"""
AI prompts for project planning and assistant functionality.
This module contains all prompt templates used for generating project plans.
Supports multiple AI providers: OpenAI, OpenRouter, etc.
"""


def get_project_planning_prompt(**kwargs):
    """
    Generate comprehensive project planning prompt for OpenAI.
    
    Args:
        prompt (str): Project requirements description
        role (str): User's role/expertise (default: 'Project Manager')
        language (str): Language for the plan (default: 'English')
        duration (str): Project duration (default: '3 months')
    
    Returns:
        str: Formatted prompt for OpenAI API
    """
    prompt = kwargs.get('prompt', 'Undefined project')
    role = kwargs.get('role', 'Project Manager')
    language = kwargs.get('language', 'English')
    duration = kwargs.get('duration', '3 months')

    return (
        f"I want you to create a detailed, realistic, and actionable step-by-step plan for my project. "
        f"Act as an expert project manager with deep domain knowledge in {role}'s field.\n\n"

        f"- Project requirements: {prompt}.\n"
        f"- My role: {role}.\n"
        f"- Language: {language}.\n"
        f"- Project duration: {duration}.\n\n"

        f"- Plan structure: \n"
        f"    - The plan must have at least 5 major milestones (minimum 5, can be more) representing comprehensive coverage of the entire project lifecycle.\n"
        f"    - EVERY SINGLE MILESTONE MUST have at least 5 main tasks (minimum 5 tasks per milestone) that specifically describe what needs to be done in that stage.\n"
        f"    - EVERY SINGLE TASK MUST have at least 5 subtasks (minimum 5 subtasks per task) that detail specific action steps.\n"
        f"    - If a milestone has fewer than 5 tasks, the response is INVALID and REJECTED.\n"
        f"    - If any task has fewer than 5 subtasks, the response is INVALID and REJECTED.\n"
        f"    - Include realistic estimated time frames for each milestone and task (in days or weeks).\n"

        f"- Description quality: \n"
        f"    - Each milestone must have a detailed description (150-200 words) that explains its purpose, objectives, expected outcomes, and key challenges.\n"
        f"    - Each task name must be concise (7-12 words) describing exactly what needs to be accomplished, written as a clear, actionable statement.\n"
        f"    - Each task needs to have a comprehensive description (120-150 words) that explains what needs to be done, including specific methodologies, tools, resources required, and expected deliverables.\n"
        f"    - Each subtask name must be a full sentence (15-25 words) describing exactly what specific action needs to be taken, written as a step-by-step instruction.\n"
        f"    - Each subtask must have a specific description (80-100 words) with practical implementation steps, best practices, and technical details when relevant.\n"

        f"- Domain-specific content: \n"
        f"    - Include industry-specific terminology, methodologies, and best practices relevant to {role}'s field.\n"
        f"    - Reference specific tools, technologies, or frameworks that would be used in this type of project.\n"
        f"    - Incorporate domain knowledge that demonstrates expertise in this field.\n"

        f"- Risk management: \n"
        f"    - For each milestone, identify 2-3 potential risks that are specific to this type of project.\n"
        f"    - Provide detailed mitigation strategies for each risk, explaining exactly how to prevent or address the issue.\n"
        f"    - Include contingency plans for critical tasks that might face obstacles.\n"

        f"- Success metrics: \n"
        f"    - Define 3-5 clear, measurable success criteria for each milestone.\n"
        f"    - Include quantifiable indicators to track progress (e.g., completion percentages, quality metrics).\n"
        f"    - Specify how to validate that each milestone has been successfully completed.\n"

        f"- Dependencies and resources: \n"
        f"    - Identify dependencies between tasks and milestones.\n"
        f"    - Specify key resources, skills, or expertise needed for each milestone.\n"

        f"- Plan flow: \n"
        f"    - The plan must be presented in chronological order, starting with setting goals and ending with evaluating results after {duration}.\n"
        f"    - Ensure logical dependencies between tasks are considered.\n"
        f"    - Make sure the plan is realistic and achievable within the given timeframe.\n\n"

        f"Finally, please format the return result as JSON with the following structure:\n"
        f"{get_json_structure_template()}"
    )


def get_json_structure_template():
    """
    Returns the JSON structure template for OpenAI response.

    Returns:
        str: JSON structure template
    """
    return (
        "{\n"
        "  'name': 'overview description of the plan',\n"
        "  'description': 'detailed introduction to the plan',\n"
        "  'milestones': [\n"
        "    {\n"
        "      'name': 'name of this milestone',\n"
        "      'description': 'detailed description of this milestone',\n"
        "      'estimated_duration': 'estimated time to complete this milestone',\n"
        "      'success_criteria': 'measurable criteria to determine milestone completion',\n"
        "      'risks': [\n"
        "        {\n"
        "          'risk': 'potential risk description',\n"
        "          'mitigation': 'strategy to mitigate this risk'\n"
        "        }\n"
        "      ],\n"
        "      'tasks': [\n"
        "        {\n"
        "          'name': 'concise task name (7-12 words) describing what needs to be accomplished',\n"
        "          'description': 'detailed description of this task (120-150 words)',\n"
        "          'estimated_duration': 'estimated time to complete this task',\n"
        "          'subtasks': [\n"
        "            {\n"
        "              'name': 'full sentence (15-25 words) describing exactly what specific action needs to be taken',\n"
        "              'description': 'detailed description of this subtask (80-100 words)'\n"
        "            },\n"
        "            // MINIMUM 5 subtasks per task - add more as needed\n"
        "          ]\n"
        "        },\n"
        "        // MINIMUM 5 tasks per milestone - add more as needed\n"
        "      ]\n"
        "    }\n"
        "  ]\n"
        "}"
    )


def get_simple_planning_prompt(**kwargs):
    """
    Generate a simpler project planning prompt for basic use cases.

    Args:
        prompt (str): Project requirements description
        role (str): User's role/expertise
        language (str): Language for the plan
        duration (str): Project duration

    Returns:
        str: Simplified prompt for OpenAI API
    """
    prompt = kwargs.get('prompt', 'Undefined project')
    role = kwargs.get('role', 'Project Manager')
    language = kwargs.get('language', 'English')
    duration = kwargs.get('duration', '3 months')

    return (
        f"Create a project plan for: {prompt}\n"
        f"Role: {role}\n"
        f"Language: {language}\n"
        f"Duration: {duration}\n\n"
        f"Please provide a structured plan with milestones, tasks, and subtasks in JSON format:\n"
        f"{get_json_structure_template()}"
    )


def get_assistant_system_prompt():
    """
    Returns the system prompt for OpenAI assistant.

    Returns:
        str: System prompt for assistant
    """
    return (
        "You are a professional project planning assistant with expertise across multiple domains. "
        "Your role is to create detailed, realistic, and actionable project plans that help users "
        "achieve their goals efficiently. You have deep knowledge of project management methodologies, "
        "industry best practices, and risk management strategies. Always provide practical, "
        "implementable advice with specific timelines and measurable outcomes."
    )


# Prompt templates for different project types
PROJECT_TYPE_PROMPTS = {
    'software_development': {
        'additional_context': (
            "Focus on software development lifecycle, including requirements gathering, "
            "design, development, testing, deployment, and maintenance phases. "
            "Include considerations for code quality, version control, CI/CD, and security."
        ),
        'tools_and_technologies': [
            'Git/GitHub', 'JIRA/Trello', 'Docker', 'CI/CD pipelines', 
            'Testing frameworks', 'Code review tools'
        ]
    },
    'marketing_campaign': {
        'additional_context': (
            "Focus on marketing strategy, target audience analysis, content creation, "
            "campaign execution, and performance measurement. Include digital marketing "
            "channels, budget allocation, and ROI tracking."
        ),
        'tools_and_technologies': [
            'Google Analytics', 'Social media platforms', 'Email marketing tools',
            'Content management systems', 'A/B testing tools'
        ]
    },
    'business_launch': {
        'additional_context': (
            "Focus on business planning, market research, legal requirements, "
            "funding, team building, and go-to-market strategy. Include financial "
            "projections and risk assessment."
        ),
        'tools_and_technologies': [
            'Business plan software', 'Financial modeling tools', 'CRM systems',
            'Accounting software', 'Legal compliance tools'
        ]
    }
}


def get_project_type_specific_prompt(project_type, **kwargs):
    """
    Generate project-type specific prompt with additional context.

    Args:
        project_type (str): Type of project (software_development, marketing_campaign, etc.)
        **kwargs: Other prompt parameters

    Returns:
        str: Enhanced prompt with project-type specific guidance
    """
    base_prompt = get_project_planning_prompt(**kwargs)

    if project_type in PROJECT_TYPE_PROMPTS:
        type_info = PROJECT_TYPE_PROMPTS[project_type]
        additional_context = (
            f"\n\nAdditional context for {project_type.replace('_', ' ').title()}:\n"
            f"{type_info['additional_context']}\n\n"
            f"Recommended tools and technologies to consider:\n"
            f"{', '.join(type_info['tools_and_technologies'])}\n"
        )
        # Insert additional context before the JSON structure
        base_prompt = base_prompt.replace(
            "Finally, please format the return result as JSON",
            additional_context + "Finally, please format the return result as JSON"
        )

    return base_prompt

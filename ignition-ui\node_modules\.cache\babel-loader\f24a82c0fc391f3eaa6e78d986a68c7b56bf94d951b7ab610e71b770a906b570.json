{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\AgentTab.js\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Typography, Paper, TextField, IconButton, Avatar, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport { useLocation } from 'react-router-dom';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\nimport { getHeaders } from \"helpers/functions\";\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AgentTab = _ref => {\n  _s();\n  let {\n    planInfo,\n    onPlanUpdate\n  } = _ref;\n  const [conversations, setConversations] = useState([]);\n  const [currentMessage, setCurrentMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const [, setError] = useState(null);\n  const location = useLocation();\n\n  // Load conversations from localStorage\n  useEffect(() => {\n    var _location$state;\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n    const planConversations = savedConversations.filter(conv => conv.planId === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n    setConversations(planConversations);\n\n    // Check for pending message from ChatbotBar\n    const pendingMessage = localStorage.getItem('pending_agent_message');\n    if (pendingMessage) {\n      try {\n        var _messageData$planInfo;\n        const messageData = JSON.parse(pendingMessage);\n        if (((_messageData$planInfo = messageData.planInfo) === null || _messageData$planInfo === void 0 ? void 0 : _messageData$planInfo.id) === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id)) {\n          handleSendMessage(messageData.message);\n        }\n        // Clear the pending message\n        localStorage.removeItem('pending_agent_message');\n      } catch (error) {\n        console.error('Error processing pending message:', error);\n        localStorage.removeItem('pending_agent_message');\n      }\n    }\n\n    // If coming from chatbot bar via navigation state, add the initial message\n    if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.message) {\n      handleSendMessage(location.state.message);\n    }\n  }, [planInfo === null || planInfo === void 0 ? void 0 : planInfo.id, location.state]);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [conversations]);\n  const handleSendMessage = async function () {\n    let messageText = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : currentMessage;\n    if (!messageText.trim() || isLoading) return;\n    setIsLoading(true);\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageText.trim(),\n      timestamp: new Date().toISOString()\n    };\n    const newConversations = [...conversations, userMessage];\n    setConversations(newConversations);\n    setCurrentMessage('');\n    try {\n      // Call the new AI agent chat endpoint with plan context\n      const response = await axios.post(`${APIURL}/api/assistant/agent-chat`, {\n        message: messageText.trim(),\n        plan_slug: planInfo.slug\n      }, {\n        headers: getHeaders()\n      });\n      const aiResponseData = response.data;\n\n      // Create AI response message\n      const aiResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: aiResponseData.message || 'I received your message but had trouble generating a response.',\n        timestamp: new Date().toISOString(),\n        actions: aiResponseData.actions || [],\n        metadata: aiResponseData.metadata || {}\n      };\n      const updatedConversations = [...newConversations, aiResponse];\n      setConversations(updatedConversations);\n\n      // Execute any actions returned by the AI\n      if (aiResponseData.actions && aiResponseData.actions.length > 0) {\n        let actionResults = [];\n        for (const action of aiResponseData.actions) {\n          try {\n            const result = await executeAIAction(action);\n            actionResults.push(result);\n          } catch (error) {\n            console.error('Error executing AI action:', error);\n            actionResults.push({\n              success: false,\n              error: error.message || 'Unknown error'\n            });\n          }\n        }\n\n        // If any actions were successful, trigger plan refresh\n        if (actionResults.some(result => result.success) && onPlanUpdate) {\n          onPlanUpdate();\n        }\n\n        // Add action results to the conversation if there were any issues\n        const failedActions = actionResults.filter(result => !result.success);\n        if (failedActions.length > 0) {\n          const errorMessage = {\n            id: Date.now() + 2,\n            type: 'assistant',\n            content: `Note: I encountered some issues executing the requested actions:\\n${failedActions.map(result => `• ${result.error}`).join('\\n')}`,\n            timestamp: new Date().toISOString(),\n            isError: true\n          };\n          setConversations(prev => [...prev, errorMessage]);\n        }\n      }\n\n      // Save to localStorage\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n      const planConversations = updatedConversations.map(conv => ({\n        ...conv,\n        planId: planInfo === null || planInfo === void 0 ? void 0 : planInfo.id,\n        planName: planInfo === null || planInfo === void 0 ? void 0 : planInfo.name\n      }));\n      localStorage.setItem('agent_conversations', JSON.stringify([...otherPlanConversations, ...planConversations]));\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error processing message:', error);\n      const errorResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: `Sorry, I encountered an error while processing your request: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || error.message || 'Unknown error'}. Please try again.`,\n        timestamp: new Date().toISOString(),\n        isError: true\n      };\n      setConversations([...newConversations, errorResponse]);\n      setError(error.message || 'Unknown error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const executeAIAction = async action => {\n    try {\n      const response = await axios.post(`${APIURL}/api/assistant/plan-action`, {\n        action: action.type,\n        plan_slug: planInfo.slug,\n        data: action.data || {},\n        message: `AI-generated action: ${action.type}`\n      }, {\n        headers: getHeaders()\n      });\n      return {\n        success: true,\n        data: response.data,\n        action: action\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Error executing AI action:', error);\n      return {\n        success: false,\n        error: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || error.message || 'Unknown error',\n        action: action\n      };\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const formatTimestamp = timestamp => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '70vh',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '12px 12px 0 0',\n        border: '1px solid #f0f0f0',\n        borderBottom: 'none',\n        backgroundColor: '#fafafa'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: mainYellowColor,\n            width: 40,\n            height: 40\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 24,\n            height: 24,\n            color: \"#fff\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontWeight: 600,\n              color: '#333'\n            },\n            children: \"AI Project Agent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            },\n            children: [\"Managing: \", planInfo === null || planInfo === void 0 ? void 0 : planInfo.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Beta\",\n          size: \"small\",\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            color: mainYellowColor,\n            fontWeight: 600,\n            ml: 'auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        flex: 1,\n        border: '1px solid #f0f0f0',\n        borderTop: 'none',\n        borderBottom: 'none',\n        overflow: 'auto',\n        p: 2,\n        backgroundColor: '#fff'\n      },\n      children: conversations.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            width: 60,\n            height: 60,\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 32,\n            height: 32,\n            color: mainYellowColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            fontWeight: 600,\n            color: '#333',\n            mb: 1\n          },\n          children: \"Welcome to AI Project Agent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: '#666',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            maxWidth: 400\n          },\n          children: \"I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        children: [conversations.map(message => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxWidth: '70%',\n              display: 'flex',\n              flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: message.type === 'user' ? '#666' : '#fff'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Paper, {\n                elevation: 0,\n                sx: {\n                  p: 1.5,\n                  borderRadius: '12px',\n                  backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\n                  color: message.type === 'user' ? '#fff' : '#333',\n                  border: message.isError ? '1px solid #f44336' : 'none'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontFamily: '\"Recursive Variable\", sans-serif',\n                    lineHeight: 1.5,\n                    whiteSpace: 'pre-line'\n                  },\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 23\n                }, this), message.type === 'assistant' && message.actions && message.actions.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mt: 1.5,\n                    display: 'flex',\n                    flexWrap: 'wrap',\n                    gap: 1\n                  },\n                  children: message.actions.slice(0, 3).map((action, actionIndex) => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: action.description,\n                    size: \"small\",\n                    onClick: () => {\n                      // Handle quick action\n                      setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);\n                      if (inputRef.current) {\n                        inputRef.current.focus();\n                      }\n                    },\n                    sx: {\n                      backgroundColor: '#fff',\n                      border: `1px solid ${mainYellowColor}`,\n                      color: mainYellowColor,\n                      fontSize: '0.7rem',\n                      height: '24px',\n                      cursor: 'pointer',\n                      '&:hover': {\n                        backgroundColor: `${mainYellowColor}10`\n                      }\n                    }\n                  }, actionIndex, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: '#999',\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  fontSize: '0.7rem',\n                  mt: 0.5,\n                  display: 'block',\n                  textAlign: message.type === 'user' ? 'right' : 'left'\n                },\n                children: formatTimestamp(message.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 15\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: \"#fff\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 0,\n              sx: {\n                p: 1.5,\n                borderRadius: '12px',\n                backgroundColor: '#f5f5f5',\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 16,\n                sx: {\n                  color: mainYellowColor\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  color: '#666'\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 272,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '0 0 12px 12px',\n        border: '1px solid #f0f0f0',\n        borderTop: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          alignItems: 'flex-end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          inputRef: inputRef,\n          value: currentMessage,\n          onChange: e => setCurrentMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask me anything about your project...\",\n          multiline: true,\n          maxRows: 3,\n          fullWidth: true,\n          variant: \"outlined\",\n          disabled: isLoading,\n          sx: {\n            '& .MuiOutlinedInput-root': {\n              borderRadius: '8px',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Send message\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => handleSendMessage(),\n            disabled: !currentMessage.trim() || isLoading,\n            sx: {\n              backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n              color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\n              '&:hover': {\n                backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:send\",\n              width: 20,\n              height: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentTab, \"rQuF4l2OLxvb/G3jCFGXzIq4svw=\", false, function () {\n  return [useLocation];\n});\n_c = AgentTab;\nexport default AgentTab;\nvar _c;\n$RefreshReg$(_c, \"AgentTab\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Typography", "Paper", "TextField", "IconButton", "Avatar", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "useLocation", "Iconify", "mainYellowColor", "APIURL", "getHeaders", "axios", "jsxDEV", "_jsxDEV", "AgentTab", "_ref", "_s", "planInfo", "onPlanUpdate", "conversations", "setConversations", "currentMessage", "setCurrentMessage", "isLoading", "setIsLoading", "messagesEndRef", "inputRef", "setError", "location", "_location$state", "savedConversations", "JSON", "parse", "localStorage", "getItem", "planConversations", "filter", "conv", "planId", "id", "pendingMessage", "_messageData$planInfo", "messageData", "handleSendMessage", "message", "removeItem", "error", "console", "state", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "messageText", "arguments", "length", "undefined", "trim", "userMessage", "Date", "now", "type", "content", "timestamp", "toISOString", "newConversations", "response", "post", "plan_slug", "slug", "headers", "aiResponseData", "data", "aiResponse", "actions", "metadata", "updatedConversations", "actionResults", "action", "result", "executeAIAction", "push", "success", "some", "failedActions", "errorMessage", "map", "join", "isError", "prev", "allConversations", "otherPlanConversations", "planName", "name", "setItem", "stringify", "_error$response", "_error$response$data", "errorResponse", "_error$response2", "_error$response2$data", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "formatTimestamp", "toLocaleTimeString", "hour", "minute", "sx", "height", "display", "flexDirection", "children", "elevation", "p", "borderRadius", "border", "borderBottom", "backgroundColor", "alignItems", "gap", "width", "icon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontFamily", "fontWeight", "label", "size", "ml", "flex", "borderTop", "overflow", "justifyContent", "textAlign", "mb", "max<PERSON><PERSON><PERSON>", "lineHeight", "whiteSpace", "mt", "flexWrap", "slice", "actionIndex", "description", "onClick", "originalMessage", "toLowerCase", "focus", "fontSize", "cursor", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "multiline", "maxRows", "fullWidth", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/AgentTab.js"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  TextField,\r\n  IconButton,\r\n  Avatar,\r\n  CircularProgress,\r\n  Chip,\r\n  Tooltip\r\n} from '@mui/material';\r\nimport { useLocation } from 'react-router-dom';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\r\nimport { getHeaders } from \"helpers/functions\";\r\nimport axios from 'axios';\r\n\r\nconst AgentTab = ({ planInfo, onPlanUpdate }) => {\r\n  const [conversations, setConversations] = useState([]);\r\n  const [currentMessage, setCurrentMessage] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const messagesEndRef = useRef(null);\r\n  const inputRef = useRef(null);\r\n  const [ , setError] = useState(null);\r\n  const location = useLocation();\r\n\r\n  // Load conversations from localStorage\r\n  useEffect(() => {\r\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n    const planConversations = savedConversations.filter(conv => conv.planId === planInfo?.id);\r\n    setConversations(planConversations);\r\n\r\n    // Check for pending message from ChatbotBar\r\n    const pendingMessage = localStorage.getItem('pending_agent_message');\r\n    if (pendingMessage) {\r\n      try {\r\n        const messageData = JSON.parse(pendingMessage);\r\n        if (messageData.planInfo?.id === planInfo?.id) {\r\n          handleSendMessage(messageData.message);\r\n        }\r\n        // Clear the pending message\r\n        localStorage.removeItem('pending_agent_message');\r\n      } catch (error) {\r\n        console.error('Error processing pending message:', error);\r\n        localStorage.removeItem('pending_agent_message');\r\n      }\r\n    }\r\n\r\n    // If coming from chatbot bar via navigation state, add the initial message\r\n    if (location.state?.message) {\r\n      handleSendMessage(location.state.message);\r\n    }\r\n  }, [planInfo?.id, location.state]);\r\n\r\n  // Auto scroll to bottom\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  }, [conversations]);\r\n\r\n\r\n\r\n  const handleSendMessage = async (messageText = currentMessage) => {\r\n    if (!messageText.trim() || isLoading) return;\r\n\r\n    setIsLoading(true);\r\n    const userMessage = {\r\n      id: Date.now(),\r\n      type: 'user',\r\n      content: messageText.trim(),\r\n      timestamp: new Date().toISOString()\r\n    };\r\n\r\n    const newConversations = [...conversations, userMessage];\r\n    setConversations(newConversations);\r\n    setCurrentMessage('');\r\n\r\n    try {\r\n      // Call the new AI agent chat endpoint with plan context\r\n      const response = await axios.post(\r\n        `${APIURL}/api/assistant/agent-chat`,\r\n        {\r\n          message: messageText.trim(),\r\n          plan_slug: planInfo.slug\r\n        },\r\n        { headers: getHeaders() }\r\n      );\r\n\r\n      const aiResponseData = response.data;\r\n\r\n      // Create AI response message\r\n      const aiResponse = {\r\n        id: Date.now() + 1,\r\n        type: 'assistant',\r\n        content: aiResponseData.message || 'I received your message but had trouble generating a response.',\r\n        timestamp: new Date().toISOString(),\r\n        actions: aiResponseData.actions || [],\r\n        metadata: aiResponseData.metadata || {}\r\n      };\r\n\r\n      const updatedConversations = [...newConversations, aiResponse];\r\n      setConversations(updatedConversations);\r\n\r\n      // Execute any actions returned by the AI\r\n      if (aiResponseData.actions && aiResponseData.actions.length > 0) {\r\n        let actionResults = [];\r\n        for (const action of aiResponseData.actions) {\r\n          try {\r\n            const result = await executeAIAction(action);\r\n            actionResults.push(result);\r\n          } catch (error) {\r\n            console.error('Error executing AI action:', error);\r\n            actionResults.push({\r\n              success: false,\r\n              error: error.message || 'Unknown error'\r\n            });\r\n          }\r\n        }\r\n\r\n        // If any actions were successful, trigger plan refresh\r\n        if (actionResults.some(result => result.success) && onPlanUpdate) {\r\n          onPlanUpdate();\r\n        }\r\n\r\n        // Add action results to the conversation if there were any issues\r\n        const failedActions = actionResults.filter(result => !result.success);\r\n        if (failedActions.length > 0) {\r\n          const errorMessage = {\r\n            id: Date.now() + 2,\r\n            type: 'assistant',\r\n            content: `Note: I encountered some issues executing the requested actions:\\n${failedActions.map(result => `• ${result.error}`).join('\\n')}`,\r\n            timestamp: new Date().toISOString(),\r\n            isError: true\r\n          };\r\n          setConversations(prev => [...prev, errorMessage]);\r\n        }\r\n      }\r\n\r\n      // Save to localStorage\r\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== planInfo?.id);\r\n      const planConversations = updatedConversations.map(conv => ({\r\n        ...conv,\r\n        planId: planInfo?.id,\r\n        planName: planInfo?.name\r\n      }));\r\n\r\n      localStorage.setItem('agent_conversations', JSON.stringify([\r\n        ...otherPlanConversations,\r\n        ...planConversations\r\n      ]));\r\n\r\n    } catch (error) {\r\n      console.error('Error processing message:', error);\r\n      const errorResponse = {\r\n        id: Date.now() + 1,\r\n        type: 'assistant',\r\n        content: `Sorry, I encountered an error while processing your request: ${error.response?.data?.error || error.message || 'Unknown error'}. Please try again.`,\r\n        timestamp: new Date().toISOString(),\r\n        isError: true\r\n      };\r\n      setConversations([...newConversations, errorResponse]);\r\n      setError(error.message || 'Unknown error occurred');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const executeAIAction = async (action) => {\r\n    try {\r\n      const response = await axios.post(\r\n        `${APIURL}/api/assistant/plan-action`,\r\n        {\r\n          action: action.type,\r\n          plan_slug: planInfo.slug,\r\n          data: action.data || {},\r\n          message: `AI-generated action: ${action.type}`\r\n        },\r\n        { headers: getHeaders() }\r\n      );\r\n\r\n      return {\r\n        success: true,\r\n        data: response.data,\r\n        action: action\r\n      };\r\n    } catch (error) {\r\n      console.error('Error executing AI action:', error);\r\n      return {\r\n        success: false,\r\n        error: error.response?.data?.error || error.message || 'Unknown error',\r\n        action: action\r\n      };\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleKeyPress = (e) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const formatTimestamp = (timestamp) => {\r\n    return new Date(timestamp).toLocaleTimeString('en-US', {\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>\r\n      {/* Header */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          p: 2,\r\n          borderRadius: '12px 12px 0 0',\r\n          border: '1px solid #f0f0f0',\r\n          borderBottom: 'none',\r\n          backgroundColor: '#fafafa'\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n          <Avatar\r\n            sx={{\r\n              backgroundColor: mainYellowColor,\r\n              width: 40,\r\n              height: 40\r\n            }}\r\n          >\r\n            <Iconify icon=\"mdi:robot\" width={24} height={24} color=\"#fff\" />\r\n          </Avatar>\r\n          <Box>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                color: '#333'\r\n              }}\r\n            >\r\n              AI Project Agent\r\n            </Typography>\r\n            <Typography\r\n              variant=\"caption\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }}\r\n            >\r\n              Managing: {planInfo?.name}\r\n            </Typography>\r\n          </Box>\r\n          <Chip\r\n            label=\"Beta\"\r\n            size=\"small\"\r\n            sx={{\r\n              backgroundColor: `${mainYellowColor}20`,\r\n              color: mainYellowColor,\r\n              fontWeight: 600,\r\n              ml: 'auto'\r\n            }}\r\n          />\r\n        </Box>\r\n      </Paper>\r\n\r\n      {/* Messages Area */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          flex: 1,\r\n          border: '1px solid #f0f0f0',\r\n          borderTop: 'none',\r\n          borderBottom: 'none',\r\n          overflow: 'auto',\r\n          p: 2,\r\n          backgroundColor: '#fff'\r\n        }}\r\n      >\r\n        {conversations.length === 0 ? (\r\n          <Box\r\n            sx={{\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              height: '100%',\r\n              textAlign: 'center'\r\n            }}\r\n          >\r\n            <Avatar\r\n              sx={{\r\n                backgroundColor: `${mainYellowColor}20`,\r\n                width: 60,\r\n                height: 60,\r\n                mb: 2\r\n              }}\r\n            >\r\n              <Iconify icon=\"mdi:robot\" width={32} height={32} color={mainYellowColor} />\r\n            </Avatar>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                color: '#333',\r\n                mb: 1\r\n              }}\r\n            >\r\n              Welcome to AI Project Agent\r\n            </Typography>\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                maxWidth: 400\r\n              }}\r\n            >\r\n              I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\r\n            </Typography>\r\n          </Box>\r\n        ) : (\r\n          <Box>\r\n            {conversations.map((message) => (\r\n              <Box\r\n                key={message.id}\r\n                sx={{\r\n                  display: 'flex',\r\n                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\r\n                  mb: 2\r\n                }}\r\n              >\r\n                <Box\r\n                  sx={{\r\n                    maxWidth: '70%',\r\n                    display: 'flex',\r\n                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\r\n                    alignItems: 'flex-start',\r\n                    gap: 1\r\n                  }}\r\n                >\r\n                  <Avatar\r\n                    sx={{\r\n                      width: 32,\r\n                      height: 32,\r\n                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\r\n                    }}\r\n                  >\r\n                    <Iconify\r\n                      icon={message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\"}\r\n                      width={18}\r\n                      height={18}\r\n                      color={message.type === 'user' ? '#666' : '#fff'}\r\n                    />\r\n                  </Avatar>\r\n                  <Box>\r\n                    <Paper\r\n                      elevation={0}\r\n                      sx={{\r\n                        p: 1.5,\r\n                        borderRadius: '12px',\r\n                        backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\r\n                        color: message.type === 'user' ? '#fff' : '#333',\r\n                        border: message.isError ? '1px solid #f44336' : 'none'\r\n                      }}\r\n                    >\r\n                      <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                          fontFamily: '\"Recursive Variable\", sans-serif',\r\n                          lineHeight: 1.5,\r\n                          whiteSpace: 'pre-line'\r\n                        }}\r\n                      >\r\n                        {message.content}\r\n                      </Typography>\r\n\r\n                      {/* Quick Action Buttons for AI responses */}\r\n                      {message.type === 'assistant' && message.actions && message.actions.length > 0 && (\r\n                        <Box sx={{ mt: 1.5, display: 'flex', flexWrap: 'wrap', gap: 1 }}>\r\n                          {message.actions.slice(0, 3).map((action, actionIndex) => (\r\n                            <Chip\r\n                              key={actionIndex}\r\n                              label={action.description}\r\n                              size=\"small\"\r\n                              onClick={() => {\r\n                                // Handle quick action\r\n                                setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);\r\n                                if (inputRef.current) {\r\n                                  inputRef.current.focus();\r\n                                }\r\n                              }}\r\n                              sx={{\r\n                                backgroundColor: '#fff',\r\n                                border: `1px solid ${mainYellowColor}`,\r\n                                color: mainYellowColor,\r\n                                fontSize: '0.7rem',\r\n                                height: '24px',\r\n                                cursor: 'pointer',\r\n                                '&:hover': {\r\n                                  backgroundColor: `${mainYellowColor}10`\r\n                                }\r\n                              }}\r\n                            />\r\n                          ))}\r\n                        </Box>\r\n                      )}\r\n                    </Paper>\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        color: '#999',\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        fontSize: '0.7rem',\r\n                        mt: 0.5,\r\n                        display: 'block',\r\n                        textAlign: message.type === 'user' ? 'right' : 'left'\r\n                      }}\r\n                    >\r\n                      {formatTimestamp(message.timestamp)}\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n            ))}\r\n            {isLoading && (\r\n              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>\r\n                  <Avatar\r\n                    sx={{\r\n                      width: 32,\r\n                      height: 32,\r\n                      backgroundColor: mainYellowColor\r\n                    }}\r\n                  >\r\n                    <Iconify icon=\"mdi:robot\" width={18} height={18} color=\"#fff\" />\r\n                  </Avatar>\r\n                  <Paper\r\n                    elevation={0}\r\n                    sx={{\r\n                      p: 1.5,\r\n                      borderRadius: '12px',\r\n                      backgroundColor: '#f5f5f5',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: 1\r\n                    }}\r\n                  >\r\n                    <CircularProgress size={16} sx={{ color: mainYellowColor }} />\r\n                    <Typography\r\n                      variant=\"body2\"\r\n                      sx={{\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        color: '#666'\r\n                      }}\r\n                    >\r\n                      Thinking...\r\n                    </Typography>\r\n                  </Paper>\r\n                </Box>\r\n              </Box>\r\n            )}\r\n            <div ref={messagesEndRef} />\r\n          </Box>\r\n        )}\r\n      </Paper>\r\n\r\n      {/* Input Area */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          p: 2,\r\n          borderRadius: '0 0 12px 12px',\r\n          border: '1px solid #f0f0f0',\r\n          borderTop: 'none'\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\r\n          <TextField\r\n            inputRef={inputRef}\r\n            value={currentMessage}\r\n            onChange={(e) => setCurrentMessage(e.target.value)}\r\n            onKeyPress={handleKeyPress}\r\n            placeholder=\"Ask me anything about your project...\"\r\n            multiline\r\n            maxRows={3}\r\n            fullWidth\r\n            variant=\"outlined\"\r\n            disabled={isLoading}\r\n            sx={{\r\n              '& .MuiOutlinedInput-root': {\r\n                borderRadius: '8px',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }\r\n            }}\r\n          />\r\n          <Tooltip title=\"Send message\">\r\n            <IconButton\r\n              onClick={() => handleSendMessage()}\r\n              disabled={!currentMessage.trim() || isLoading}\r\n              sx={{\r\n                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\r\n                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\r\n                '&:hover': {\r\n                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\r\n                }\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:send\" width={20} height={20} />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Box>\r\n      </Paper>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default AgentTab;\r\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,EAAEC,MAAM,QAAQ,mBAAmB;AAC3D,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGC,IAAA,IAAgC;EAAAC,EAAA;EAAA,IAA/B;IAAEC,QAAQ;IAAEC;EAAa,CAAC,GAAAH,IAAA;EAC1C,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM+B,cAAc,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM8B,QAAQ,GAAG9B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM,GAAI+B,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACpC,MAAMkC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;;EAE9B;EACAX,SAAS,CAAC,MAAM;IAAA,IAAAkC,eAAA;IACd,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;IAC1F,MAAMC,iBAAiB,GAAGL,kBAAkB,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKrB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAC;IACzFnB,gBAAgB,CAACe,iBAAiB,CAAC;;IAEnC;IACA,MAAMK,cAAc,GAAGP,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;IACpE,IAAIM,cAAc,EAAE;MAClB,IAAI;QAAA,IAAAC,qBAAA;QACF,MAAMC,WAAW,GAAGX,IAAI,CAACC,KAAK,CAACQ,cAAc,CAAC;QAC9C,IAAI,EAAAC,qBAAA,GAAAC,WAAW,CAACzB,QAAQ,cAAAwB,qBAAA,uBAApBA,qBAAA,CAAsBF,EAAE,OAAKtB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,GAAE;UAC7CI,iBAAiB,CAACD,WAAW,CAACE,OAAO,CAAC;QACxC;QACA;QACAX,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC;MAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDb,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC;MAClD;IACF;;IAEA;IACA,KAAAhB,eAAA,GAAID,QAAQ,CAACoB,KAAK,cAAAnB,eAAA,eAAdA,eAAA,CAAgBe,OAAO,EAAE;MAC3BD,iBAAiB,CAACf,QAAQ,CAACoB,KAAK,CAACJ,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAAC3B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAEX,QAAQ,CAACoB,KAAK,CAAC,CAAC;;EAElC;EACArD,SAAS,CAAC,MAAM;IAAA,IAAAsD,qBAAA;IACd,CAAAA,qBAAA,GAAAxB,cAAc,CAACyB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACjC,aAAa,CAAC,CAAC;EAInB,MAAMwB,iBAAiB,GAAG,eAAAA,CAAA,EAAwC;IAAA,IAAjCU,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGjC,cAAc;IAC3D,IAAI,CAACgC,WAAW,CAACI,IAAI,CAAC,CAAC,IAAIlC,SAAS,EAAE;IAEtCC,YAAY,CAAC,IAAI,CAAC;IAClB,MAAMkC,WAAW,GAAG;MAClBnB,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAET,WAAW,CAACI,IAAI,CAAC,CAAC;MAC3BM,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACpC,CAAC;IAED,MAAMC,gBAAgB,GAAG,CAAC,GAAG9C,aAAa,EAAEuC,WAAW,CAAC;IACxDtC,gBAAgB,CAAC6C,gBAAgB,CAAC;IAClC3C,iBAAiB,CAAC,EAAE,CAAC;IAErB,IAAI;MACF;MACA,MAAM4C,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,IAAI,CAC/B,GAAG1D,MAAM,2BAA2B,EACpC;QACEmC,OAAO,EAAES,WAAW,CAACI,IAAI,CAAC,CAAC;QAC3BW,SAAS,EAAEnD,QAAQ,CAACoD;MACtB,CAAC,EACD;QAAEC,OAAO,EAAE5D,UAAU,CAAC;MAAE,CAC1B,CAAC;MAED,MAAM6D,cAAc,GAAGL,QAAQ,CAACM,IAAI;;MAEpC;MACA,MAAMC,UAAU,GAAG;QACjBlC,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAES,cAAc,CAAC3B,OAAO,IAAI,gEAAgE;QACnGmB,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QACnCU,OAAO,EAAEH,cAAc,CAACG,OAAO,IAAI,EAAE;QACrCC,QAAQ,EAAEJ,cAAc,CAACI,QAAQ,IAAI,CAAC;MACxC,CAAC;MAED,MAAMC,oBAAoB,GAAG,CAAC,GAAGX,gBAAgB,EAAEQ,UAAU,CAAC;MAC9DrD,gBAAgB,CAACwD,oBAAoB,CAAC;;MAEtC;MACA,IAAIL,cAAc,CAACG,OAAO,IAAIH,cAAc,CAACG,OAAO,CAACnB,MAAM,GAAG,CAAC,EAAE;QAC/D,IAAIsB,aAAa,GAAG,EAAE;QACtB,KAAK,MAAMC,MAAM,IAAIP,cAAc,CAACG,OAAO,EAAE;UAC3C,IAAI;YACF,MAAMK,MAAM,GAAG,MAAMC,eAAe,CAACF,MAAM,CAAC;YAC5CD,aAAa,CAACI,IAAI,CAACF,MAAM,CAAC;UAC5B,CAAC,CAAC,OAAOjC,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;YAClD+B,aAAa,CAACI,IAAI,CAAC;cACjBC,OAAO,EAAE,KAAK;cACdpC,KAAK,EAAEA,KAAK,CAACF,OAAO,IAAI;YAC1B,CAAC,CAAC;UACJ;QACF;;QAEA;QACA,IAAIiC,aAAa,CAACM,IAAI,CAACJ,MAAM,IAAIA,MAAM,CAACG,OAAO,CAAC,IAAIhE,YAAY,EAAE;UAChEA,YAAY,CAAC,CAAC;QAChB;;QAEA;QACA,MAAMkE,aAAa,GAAGP,aAAa,CAACzC,MAAM,CAAC2C,MAAM,IAAI,CAACA,MAAM,CAACG,OAAO,CAAC;QACrE,IAAIE,aAAa,CAAC7B,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAM8B,YAAY,GAAG;YACnB9C,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;YAClBC,IAAI,EAAE,WAAW;YACjBC,OAAO,EAAE,qEAAqEsB,aAAa,CAACE,GAAG,CAACP,MAAM,IAAI,KAAKA,MAAM,CAACjC,KAAK,EAAE,CAAC,CAACyC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC3IxB,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;YACnCwB,OAAO,EAAE;UACX,CAAC;UACDpE,gBAAgB,CAACqE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEJ,YAAY,CAAC,CAAC;QACnD;MACF;;MAEA;MACA,MAAMK,gBAAgB,GAAG3D,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;MACxF,MAAMyD,sBAAsB,GAAGD,gBAAgB,CAACtD,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKrB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAC;MAC5F,MAAMJ,iBAAiB,GAAGyC,oBAAoB,CAACU,GAAG,CAACjD,IAAI,KAAK;QAC1D,GAAGA,IAAI;QACPC,MAAM,EAAErB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE;QACpBqD,QAAQ,EAAE3E,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4E;MACtB,CAAC,CAAC,CAAC;MAEH5D,YAAY,CAAC6D,OAAO,CAAC,qBAAqB,EAAE/D,IAAI,CAACgE,SAAS,CAAC,CACzD,GAAGJ,sBAAsB,EACzB,GAAGxD,iBAAiB,CACrB,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOW,KAAK,EAAE;MAAA,IAAAkD,eAAA,EAAAC,oBAAA;MACdlD,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMoD,aAAa,GAAG;QACpB3D,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,gEAAgE,EAAAkC,eAAA,GAAAlD,KAAK,CAACoB,QAAQ,cAAA8B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBxB,IAAI,cAAAyB,oBAAA,uBAApBA,oBAAA,CAAsBnD,KAAK,KAAIA,KAAK,CAACF,OAAO,IAAI,eAAe,qBAAqB;QAC7JmB,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QACnCwB,OAAO,EAAE;MACX,CAAC;MACDpE,gBAAgB,CAAC,CAAC,GAAG6C,gBAAgB,EAAEiC,aAAa,CAAC,CAAC;MACtDvE,QAAQ,CAACmB,KAAK,CAACF,OAAO,IAAI,wBAAwB,CAAC;IACrD,CAAC,SAAS;MACRpB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMwD,eAAe,GAAG,MAAOF,MAAM,IAAK;IACxC,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,IAAI,CAC/B,GAAG1D,MAAM,4BAA4B,EACrC;QACEqE,MAAM,EAAEA,MAAM,CAACjB,IAAI;QACnBO,SAAS,EAAEnD,QAAQ,CAACoD,IAAI;QACxBG,IAAI,EAAEM,MAAM,CAACN,IAAI,IAAI,CAAC,CAAC;QACvB5B,OAAO,EAAE,wBAAwBkC,MAAM,CAACjB,IAAI;MAC9C,CAAC,EACD;QAAES,OAAO,EAAE5D,UAAU,CAAC;MAAE,CAC1B,CAAC;MAED,OAAO;QACLwE,OAAO,EAAE,IAAI;QACbV,IAAI,EAAEN,QAAQ,CAACM,IAAI;QACnBM,MAAM,EAAEA;MACV,CAAC;IACH,CAAC,CAAC,OAAOhC,KAAK,EAAE;MAAA,IAAAqD,gBAAA,EAAAC,qBAAA;MACdrD,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO;QACLoC,OAAO,EAAE,KAAK;QACdpC,KAAK,EAAE,EAAAqD,gBAAA,GAAArD,KAAK,CAACoB,QAAQ,cAAAiC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3B,IAAI,cAAA4B,qBAAA,uBAApBA,qBAAA,CAAsBtD,KAAK,KAAIA,KAAK,CAACF,OAAO,IAAI,eAAe;QACtEkC,MAAM,EAAEA;MACV,CAAC;IACH;EACF,CAAC;EAID,MAAMuB,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClB9D,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAM+D,eAAe,GAAI3C,SAAS,IAAK;IACrC,OAAO,IAAIJ,IAAI,CAACI,SAAS,CAAC,CAAC4C,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACEhG,OAAA,CAAChB,GAAG;IAACiH,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpErG,OAAA,CAACd,KAAK;MACJoH,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,MAAM;QACpBC,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,eAEFrG,OAAA,CAAChB,GAAG;QAACiH,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAES,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACzDrG,OAAA,CAACX,MAAM;UACL4G,EAAE,EAAE;YACFU,eAAe,EAAEhH,eAAe;YAChCmH,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE;UACV,CAAE;UAAAG,QAAA,eAEFrG,OAAA,CAACN,OAAO;YAACqH,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACTpH,OAAA,CAAChB,GAAG;UAAAqH,QAAA,gBACFrG,OAAA,CAACf,UAAU;YACToI,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFqB,UAAU,EAAE,kCAAkC;cAC9CC,UAAU,EAAE,GAAG;cACfP,KAAK,EAAE;YACT,CAAE;YAAAX,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpH,OAAA,CAACf,UAAU;YACToI,OAAO,EAAC,SAAS;YACjBpB,EAAE,EAAE;cACFe,KAAK,EAAE,MAAM;cACbM,UAAU,EAAE;YACd,CAAE;YAAAjB,QAAA,GACH,YACW,EAACjG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4E,IAAI;UAAA;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNpH,OAAA,CAACT,IAAI;UACHiI,KAAK,EAAC,MAAM;UACZC,IAAI,EAAC,OAAO;UACZxB,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGhH,eAAe,IAAI;YACvCqH,KAAK,EAAErH,eAAe;YACtB4H,UAAU,EAAE,GAAG;YACfG,EAAE,EAAE;UACN;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRpH,OAAA,CAACd,KAAK;MACJoH,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACF0B,IAAI,EAAE,CAAC;QACPlB,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE,MAAM;QACjBlB,YAAY,EAAE,MAAM;QACpBmB,QAAQ,EAAE,MAAM;QAChBtB,CAAC,EAAE,CAAC;QACJI,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,EAED/F,aAAa,CAACoC,MAAM,KAAK,CAAC,gBACzB1C,OAAA,CAAChB,GAAG;QACFiH,EAAE,EAAE;UACFE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBQ,UAAU,EAAE,QAAQ;UACpBkB,cAAc,EAAE,QAAQ;UACxB5B,MAAM,EAAE,MAAM;UACd6B,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,gBAEFrG,OAAA,CAACX,MAAM;UACL4G,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGhH,eAAe,IAAI;YACvCmH,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE,EAAE;YACV8B,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEFrG,OAAA,CAACN,OAAO;YAACqH,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAErH;UAAgB;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACTpH,OAAA,CAACf,UAAU;UACToI,OAAO,EAAC,IAAI;UACZpB,EAAE,EAAE;YACFqB,UAAU,EAAE,kCAAkC;YAC9CC,UAAU,EAAE,GAAG;YACfP,KAAK,EAAE,MAAM;YACbgB,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpH,OAAA,CAACf,UAAU;UACToI,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YACFe,KAAK,EAAE,MAAM;YACbM,UAAU,EAAE,kCAAkC;YAC9CW,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAENpH,OAAA,CAAChB,GAAG;QAAAqH,QAAA,GACD/F,aAAa,CAACmE,GAAG,CAAE1C,OAAO,iBACzB/B,OAAA,CAAChB,GAAG;UAEFiH,EAAE,EAAE;YACFE,OAAO,EAAE,MAAM;YACf2B,cAAc,EAAE/F,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,YAAY;YACnEgF,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEFrG,OAAA,CAAChB,GAAG;YACFiH,EAAE,EAAE;cACFgC,QAAQ,EAAE,KAAK;cACf9B,OAAO,EAAE,MAAM;cACfC,aAAa,EAAErE,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,KAAK;cAC9D4D,UAAU,EAAE,YAAY;cACxBC,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBAEFrG,OAAA,CAACX,MAAM;cACL4G,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAE5E,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,SAAS,GAAGrD;cACzD,CAAE;cAAA0G,QAAA,eAEFrG,OAAA,CAACN,OAAO;gBACNqH,IAAI,EAAEhF,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,yBAAyB,GAAG,WAAY;gBACxE8D,KAAK,EAAE,EAAG;gBACVZ,MAAM,EAAE,EAAG;gBACXc,KAAK,EAAEjF,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG;cAAO;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTpH,OAAA,CAAChB,GAAG;cAAAqH,QAAA,gBACFrG,OAAA,CAACd,KAAK;gBACJoH,SAAS,EAAE,CAAE;gBACbL,EAAE,EAAE;kBACFM,CAAC,EAAE,GAAG;kBACNC,YAAY,EAAE,MAAM;kBACpBG,eAAe,EAAE5E,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAGrD,eAAe,GAAG,SAAS;kBACtEqH,KAAK,EAAEjF,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;kBAChDyD,MAAM,EAAE1E,OAAO,CAAC4C,OAAO,GAAG,mBAAmB,GAAG;gBAClD,CAAE;gBAAA0B,QAAA,gBAEFrG,OAAA,CAACf,UAAU;kBACToI,OAAO,EAAC,OAAO;kBACfpB,EAAE,EAAE;oBACFqB,UAAU,EAAE,kCAAkC;oBAC9CY,UAAU,EAAE,GAAG;oBACfC,UAAU,EAAE;kBACd,CAAE;kBAAA9B,QAAA,EAEDtE,OAAO,CAACkB;gBAAO;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAGZrF,OAAO,CAACiB,IAAI,KAAK,WAAW,IAAIjB,OAAO,CAAC8B,OAAO,IAAI9B,OAAO,CAAC8B,OAAO,CAACnB,MAAM,GAAG,CAAC,iBAC5E1C,OAAA,CAAChB,GAAG;kBAACiH,EAAE,EAAE;oBAAEmC,EAAE,EAAE,GAAG;oBAAEjC,OAAO,EAAE,MAAM;oBAAEkC,QAAQ,EAAE,MAAM;oBAAExB,GAAG,EAAE;kBAAE,CAAE;kBAAAR,QAAA,EAC7DtE,OAAO,CAAC8B,OAAO,CAACyE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC7D,GAAG,CAAC,CAACR,MAAM,EAAEsE,WAAW,kBACnDvI,OAAA,CAACT,IAAI;oBAEHiI,KAAK,EAAEvD,MAAM,CAACuE,WAAY;oBAC1Bf,IAAI,EAAC,OAAO;oBACZgB,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACAhI,iBAAiB,CAACwD,MAAM,CAACyE,eAAe,IAAI,UAAUzE,MAAM,CAACuE,WAAW,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC;sBACzF,IAAI9H,QAAQ,CAACwB,OAAO,EAAE;wBACpBxB,QAAQ,CAACwB,OAAO,CAACuG,KAAK,CAAC,CAAC;sBAC1B;oBACF,CAAE;oBACF3C,EAAE,EAAE;sBACFU,eAAe,EAAE,MAAM;sBACvBF,MAAM,EAAE,aAAa9G,eAAe,EAAE;sBACtCqH,KAAK,EAAErH,eAAe;sBACtBkJ,QAAQ,EAAE,QAAQ;sBAClB3C,MAAM,EAAE,MAAM;sBACd4C,MAAM,EAAE,SAAS;sBACjB,SAAS,EAAE;wBACTnC,eAAe,EAAE,GAAGhH,eAAe;sBACrC;oBACF;kBAAE,GApBG4I,WAAW;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqBjB,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACRpH,OAAA,CAACf,UAAU;gBACToI,OAAO,EAAC,SAAS;gBACjBpB,EAAE,EAAE;kBACFe,KAAK,EAAE,MAAM;kBACbM,UAAU,EAAE,kCAAkC;kBAC9CuB,QAAQ,EAAE,QAAQ;kBAClBT,EAAE,EAAE,GAAG;kBACPjC,OAAO,EAAE,OAAO;kBAChB4B,SAAS,EAAEhG,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG;gBACjD,CAAE;gBAAAqD,QAAA,EAEDR,eAAe,CAAC9D,OAAO,CAACmB,SAAS;cAAC;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjGDrF,OAAO,CAACL,EAAE;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkGZ,CACN,CAAC,EACD1G,SAAS,iBACRV,OAAA,CAAChB,GAAG;UAACiH,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,YAAY;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eAChErG,OAAA,CAAChB,GAAG;YAACiH,EAAE,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAES,UAAU,EAAE,YAAY;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAR,QAAA,gBAC7DrG,OAAA,CAACX,MAAM;cACL4G,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAEhH;cACnB,CAAE;cAAA0G,QAAA,eAEFrG,OAAA,CAACN,OAAO;gBAACqH,IAAI,EAAC,WAAW;gBAACD,KAAK,EAAE,EAAG;gBAACZ,MAAM,EAAE,EAAG;gBAACc,KAAK,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACTpH,OAAA,CAACd,KAAK;cACJoH,SAAS,EAAE,CAAE;cACbL,EAAE,EAAE;gBACFM,CAAC,EAAE,GAAG;gBACNC,YAAY,EAAE,MAAM;gBACpBG,eAAe,EAAE,SAAS;gBAC1BR,OAAO,EAAE,MAAM;gBACfS,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,gBAEFrG,OAAA,CAACV,gBAAgB;gBAACmI,IAAI,EAAE,EAAG;gBAACxB,EAAE,EAAE;kBAAEe,KAAK,EAAErH;gBAAgB;cAAE;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DpH,OAAA,CAACf,UAAU;gBACToI,OAAO,EAAC,OAAO;gBACfpB,EAAE,EAAE;kBACFqB,UAAU,EAAE,kCAAkC;kBAC9CN,KAAK,EAAE;gBACT,CAAE;gBAAAX,QAAA,EACH;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eACDpH,OAAA;UAAK+I,GAAG,EAAEnI;QAAe;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGRpH,OAAA,CAACd,KAAK;MACJoH,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE;MACb,CAAE;MAAAvB,QAAA,eAEFrG,OAAA,CAAChB,GAAG;QAACiH,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEU,GAAG,EAAE,CAAC;UAAED,UAAU,EAAE;QAAW,CAAE;QAAAP,QAAA,gBAC3DrG,OAAA,CAACb,SAAS;UACR0B,QAAQ,EAAEA,QAAS;UACnBmI,KAAK,EAAExI,cAAe;UACtByI,QAAQ,EAAGxD,CAAC,IAAKhF,iBAAiB,CAACgF,CAAC,CAACyD,MAAM,CAACF,KAAK,CAAE;UACnDG,UAAU,EAAE3D,cAAe;UAC3B4D,WAAW,EAAC,uCAAuC;UACnDC,SAAS;UACTC,OAAO,EAAE,CAAE;UACXC,SAAS;UACTlC,OAAO,EAAC,UAAU;UAClBmC,QAAQ,EAAE9I,SAAU;UACpBuF,EAAE,EAAE;YACF,0BAA0B,EAAE;cAC1BO,YAAY,EAAE,KAAK;cACnBc,UAAU,EAAE;YACd;UACF;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFpH,OAAA,CAACR,OAAO;UAACiK,KAAK,EAAC,cAAc;UAAApD,QAAA,eAC3BrG,OAAA,CAACZ,UAAU;YACTqJ,OAAO,EAAEA,CAAA,KAAM3G,iBAAiB,CAAC,CAAE;YACnC0H,QAAQ,EAAE,CAAChJ,cAAc,CAACoC,IAAI,CAAC,CAAC,IAAIlC,SAAU;YAC9CuF,EAAE,EAAE;cACFU,eAAe,EAAEnG,cAAc,CAACoC,IAAI,CAAC,CAAC,IAAI,CAAClC,SAAS,GAAGf,eAAe,GAAG,SAAS;cAClFqH,KAAK,EAAExG,cAAc,CAACoC,IAAI,CAAC,CAAC,IAAI,CAAClC,SAAS,GAAG,MAAM,GAAG,MAAM;cAC5D,SAAS,EAAE;gBACTiG,eAAe,EAAEnG,cAAc,CAACoC,IAAI,CAAC,CAAC,IAAI,CAAClC,SAAS,GAAG,SAAS,GAAG;cACrE;YACF,CAAE;YAAA2F,QAAA,eAEFrG,OAAA,CAACN,OAAO;cAACqH,IAAI,EAAC,uBAAuB;cAACD,KAAK,EAAE,EAAG;cAACZ,MAAM,EAAE;YAAG;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACjH,EAAA,CArfIF,QAAQ;EAAA,QAOKR,WAAW;AAAA;AAAAiK,EAAA,GAPxBzJ,QAAQ;AAufd,eAAeA,QAAQ;AAAC,IAAAyJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
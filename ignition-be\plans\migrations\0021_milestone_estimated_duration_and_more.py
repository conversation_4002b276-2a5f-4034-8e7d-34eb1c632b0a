# Generated by Django 5.1.7 on 2025-03-07 16:49

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('plans', '0020_milestone_description_subtask_description_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='milestone',
            name='estimated_duration',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='milestone',
            name='success_criteria',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='task',
            name='estimated_duration',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.CreateModel(
            name='Risk',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('risk', models.TextField(blank=True, null=True)),
                ('mitigation', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('milestone', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='risks', to='plans.milestone')),
            ],
            options={
                'db_table': 'risks',
            },
        ),
    ]

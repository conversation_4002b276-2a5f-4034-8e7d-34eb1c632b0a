# Generated by Django 5.0.2 on 2024-05-13 11:47

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("plans", "0007_plan_assignees"),
    ]

    operations = [
        migrations.AddField(
            model_name="subtask",
            name="end_date",
            field=models.DateField(null=True),
        ),
        migrations.AddField(
            model_name="subtask",
            name="progress",
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name="subtask",
            name="slug",
            field=models.SlugField(default=uuid.uuid4, max_length=255, unique=True),
        ),
        migrations.AddField(
            model_name="subtask",
            name="start_date",
            field=models.DateField(null=True),
        ),
        migrations.AddField(
            model_name="subtask",
            name="status",
            field=models.IntegerField(
                choices=[(1, "Todo"), (2, "In Progress"), (3, "Done")], default=1
            ),
        ),
    ]

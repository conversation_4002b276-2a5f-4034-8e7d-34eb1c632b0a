{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\routes\\\\index.js\";\nimport Home from \"views/home/<USER>\";\nimport ConnectFriend from \"views/users/connect_friend/index\";\nimport CalendarComponent from \"views/calendar/index.js\";\nimport C<PERSON><PERSON><PERSON> from \"views/plan/create.js\";\nimport PlanDetail from \"views/plan/detail\";\nimport Login from \"views/auth/Login.js\";\nimport Register from \"views/auth/Register.js\";\nimport ForgotPassword from \"views/auth/ForgotPassword.js\";\nimport Activate from \"views/auth/Activate.js\";\nimport Reset from \"views/auth/Reset.js\";\nimport GoogleAuthHandle from \"views/auth/GoogleAuthHandle\";\nimport Profile from \"views/users/profile/index.js\";\nimport OtherProfile from \"views/users/other_profile/index\";\nimport TodoComponent from \"views/todo/index.js\";\nimport Notifications from \"views/notifications/index.js\";\nimport AcceptInvitation from \"views/plan/invitation\";\nimport RegisterByInvitation from \"views/auth/RegisterByInvitation\";\nimport PrivacyPolicies from \"views/public/policies\";\nimport { AUTH_PAGE_KEY, ADMIN_PAGE_KEY, PUBLIC_PAGE_KEY } from \"helpers/constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nvar routes = [{\n  path: \"/\",\n  layout: ADMIN_PAGE_KEY,\n  name: \"HOME\",\n  component: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 16\n  }, this),\n  private: true\n}, {\n  path: \"/plan/create\",\n  layout: ADMIN_PAGE_KEY,\n  name: \"CREATE_A_PLAN\",\n  component: /*#__PURE__*/_jsxDEV(CreatePlan, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 16\n  }, this),\n  private: true\n}, {\n  path: \"/plan/:param\",\n  layout: ADMIN_PAGE_KEY,\n  name: \"PLAN_INFOMATION\",\n  component: /*#__PURE__*/_jsxDEV(PlanDetail, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 16\n  }, this),\n  private: true\n}, {\n  path: \"/connect\",\n  layout: ADMIN_PAGE_KEY,\n  name: \"CONTACT_DIRECTORY\",\n  component: /*#__PURE__*/_jsxDEV(ConnectFriend, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 16\n  }, this),\n  private: true\n}, {\n  path: \"/profile\",\n  layout: ADMIN_PAGE_KEY,\n  name: \"CURRENT_USER_PROFILE\",\n  component: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 16\n  }, this),\n  private: true\n}, {\n  path: \"/other/profile/:param\",\n  layout: ADMIN_PAGE_KEY,\n  name: \"OTHER_USER_PROFILE\",\n  component: /*#__PURE__*/_jsxDEV(OtherProfile, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 16\n  }, this),\n  private: true\n}, {\n  path: \"/my/tasks/calendar\",\n  layout: ADMIN_PAGE_KEY,\n  name: \"MY_TASKS_CALENDAR\",\n  component: /*#__PURE__*/_jsxDEV(CalendarComponent, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 16\n  }, this),\n  private: true\n}, {\n  path: \"/my/tasks/table\",\n  layout: ADMIN_PAGE_KEY,\n  name: \"MY_TASKS_TABLE\",\n  component: /*#__PURE__*/_jsxDEV(TodoComponent, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 16\n  }, this),\n  private: true\n}, {\n  path: \"/notifications\",\n  layout: ADMIN_PAGE_KEY,\n  name: \"MY_NOTIFICATIONS\",\n  component: /*#__PURE__*/_jsxDEV(Notifications, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 16\n  }, this),\n  private: true\n},\n// No Auth page\n{\n  path: \"/login\",\n  layout: AUTH_PAGE_KEY,\n  name: \"LOGIN\",\n  component: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 16\n  }, this),\n  private: false\n}, {\n  path: \"/register\",\n  layout: AUTH_PAGE_KEY,\n  name: \"REGISTER\",\n  component: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 16\n  }, this),\n  private: false\n}, {\n  path: \"/register-by-invitation/:signedId/:email\",\n  layout: AUTH_PAGE_KEY,\n  name: \"REGISTER_BY_INVITATION\",\n  component: /*#__PURE__*/_jsxDEV(RegisterByInvitation, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 16\n  }, this),\n  private: false\n}, {\n  path: \"/activate/:param1/:param2\",\n  layout: AUTH_PAGE_KEY,\n  name: \"ACTIVATE\",\n  component: /*#__PURE__*/_jsxDEV(Activate, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 16\n  }, this),\n  private: false\n}, {\n  path: \"/reset/:uid/:token\",\n  layout: AUTH_PAGE_KEY,\n  name: \"RESET_PASSWORD\",\n  component: /*#__PURE__*/_jsxDEV(Reset, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 16\n  }, this),\n  private: false\n}, {\n  path: \"/forgot-password\",\n  layout: AUTH_PAGE_KEY,\n  name: \"FORGOT_PASSWORD\",\n  component: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 16\n  }, this),\n  private: false\n}, {\n  path: \"/google\",\n  layout: AUTH_PAGE_KEY,\n  name: \"GOOGLE_AUTH_HANDLE\",\n  component: /*#__PURE__*/_jsxDEV(GoogleAuthHandle, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 16\n  }, this),\n  private: false\n},\n// Both types\n{\n  path: \"/accept-invitation/:param\",\n  layout: PUBLIC_PAGE_KEY,\n  name: \"ACCEPT_INVITATION\",\n  component: /*#__PURE__*/_jsxDEV(AcceptInvitation, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 16\n  }, this),\n  private: true\n}, {\n  path: \"/policies\",\n  layout: PUBLIC_PAGE_KEY,\n  name: \"POLICIES\",\n  component: /*#__PURE__*/_jsxDEV(PrivacyPolicies, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 16\n  }, this),\n  private: false\n}];\nexport default routes;", "map": {"version": 3, "names": ["Home", "ConnectFriend", "CalendarComponent", "CreatePlan", "PlanDetail", "<PERSON><PERSON>", "Register", "ForgotPassword", "Activate", "Reset", "GoogleAuthHandle", "Profile", "OtherProfile", "TodoComponent", "Notifications", "AcceptInvitation", "RegisterByInvitation", "PrivacyPolicies", "AUTH_PAGE_KEY", "ADMIN_PAGE_KEY", "PUBLIC_PAGE_KEY", "jsxDEV", "_jsxDEV", "routes", "path", "layout", "name", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "private"], "sources": ["C:/ignition/ignition-ui/src/routes/index.js"], "sourcesContent": ["import Home from \"views/home/<USER>\";\r\nimport Connect<PERSON>riend from \"views/users/connect_friend/index\";\r\nimport CalendarComponent from \"views/calendar/index.js\";\r\nimport CreatePlan from \"views/plan/create.js\";\r\nimport PlanDetail from \"views/plan/detail\";\r\nimport Login from \"views/auth/Login.js\";\r\nimport Register from \"views/auth/Register.js\";\r\nimport ForgotPassword from \"views/auth/ForgotPassword.js\";\r\nimport Activate from \"views/auth/Activate.js\";\r\nimport Reset from \"views/auth/Reset.js\";\r\nimport GoogleAuthHandle from \"views/auth/GoogleAuthHandle\";\r\nimport Profile from \"views/users/profile/index.js\";\r\nimport OtherProfile from \"views/users/other_profile/index\";\r\nimport TodoComponent from \"views/todo/index.js\";\r\nimport Notifications from \"views/notifications/index.js\";\r\nimport AcceptInvitation from \"views/plan/invitation\";\r\nimport RegisterByInvitation from \"views/auth/RegisterByInvitation\";\r\nimport PrivacyPolicies from \"views/public/policies\"\r\n\r\nimport { AUTH_PAGE_KEY, ADMIN_PAGE_KEY, PUBLIC_PAGE_KEY } from \"helpers/constants\";\r\n\r\nvar routes = [\r\n  {\r\n    path: \"/\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"HOME\",\r\n    component: <Home />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/plan/create\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"CREATE_A_PLAN\",\r\n    component: <CreatePlan />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/plan/:param\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"PLAN_INFOMATION\",\r\n    component: <PlanDetail />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/connect\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"CONTACT_DIRECTORY\",\r\n    component: <ConnectFriend />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/profile\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"CURRENT_USER_PROFILE\",\r\n    component: <Profile />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/other/profile/:param\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"OTHER_USER_PROFILE\",\r\n    component: <OtherProfile />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/my/tasks/calendar\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"MY_TASKS_CALENDAR\",\r\n    component: <CalendarComponent />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/my/tasks/table\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"MY_TASKS_TABLE\",\r\n    component: <TodoComponent />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/notifications\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"MY_NOTIFICATIONS\",\r\n    component: <Notifications />,\r\n    private: true,\r\n  },\r\n  // No Auth page\r\n  {\r\n    path: \"/login\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"LOGIN\",\r\n    component: <Login />,\r\n    private: false,\r\n  },\r\n  {\r\n    path: \"/register\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"REGISTER\",\r\n    component: <Register />,\r\n    private: false,\r\n  },\r\n  {\r\n    path: \"/register-by-invitation/:signedId/:email\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"REGISTER_BY_INVITATION\",\r\n    component: <RegisterByInvitation />,\r\n    private: false,\r\n  },\r\n  {\r\n    path: \"/activate/:param1/:param2\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"ACTIVATE\",\r\n    component: <Activate />,\r\n    private: false,\r\n  },\r\n  {\r\n    path: \"/reset/:uid/:token\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"RESET_PASSWORD\",\r\n    component: <Reset />,\r\n    private: false,\r\n  },\r\n  {\r\n    path: \"/forgot-password\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"FORGOT_PASSWORD\",\r\n    component: <ForgotPassword />,\r\n    private: false,\r\n  },\r\n  {\r\n    path: \"/google\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"GOOGLE_AUTH_HANDLE\",\r\n    component: <GoogleAuthHandle />,\r\n    private: false,\r\n  },\r\n  // Both types\r\n  {\r\n    path: \"/accept-invitation/:param\",\r\n    layout: PUBLIC_PAGE_KEY,\r\n    name: \"ACCEPT_INVITATION\",\r\n    component: <AcceptInvitation />,\r\n    private: true,\r\n  },\r\n  {\r\n    path:\"/policies\",\r\n    layout: PUBLIC_PAGE_KEY,\r\n    name: \"POLICIES\",\r\n    component: <PrivacyPolicies />,\r\n    private: false,\r\n  }\r\n];\r\n\r\nexport default routes;\r\n\r\n\r\n"], "mappings": ";AAAA,OAAOA,IAAI,MAAM,qBAAqB;AACtC,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,eAAe,MAAM,uBAAuB;AAEnD,SAASC,aAAa,EAAEC,cAAc,EAAEC,eAAe,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnF,IAAIC,MAAM,GAAG,CACX;EACEC,IAAI,EAAE,GAAG;EACTC,MAAM,EAAEN,cAAc;EACtBO,IAAI,EAAE,MAAM;EACZC,SAAS,eAAEL,OAAA,CAACtB,IAAI;IAAA4B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACnBC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,cAAc;EACpBC,MAAM,EAAEN,cAAc;EACtBO,IAAI,EAAE,eAAe;EACrBC,SAAS,eAAEL,OAAA,CAACnB,UAAU;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACzBC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,cAAc;EACpBC,MAAM,EAAEN,cAAc;EACtBO,IAAI,EAAE,iBAAiB;EACvBC,SAAS,eAAEL,OAAA,CAAClB,UAAU;IAAAwB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACzBC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAEN,cAAc;EACtBO,IAAI,EAAE,mBAAmB;EACzBC,SAAS,eAAEL,OAAA,CAACrB,aAAa;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC5BC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAEN,cAAc;EACtBO,IAAI,EAAE,sBAAsB;EAC5BC,SAAS,eAAEL,OAAA,CAACX,OAAO;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACtBC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,uBAAuB;EAC7BC,MAAM,EAAEN,cAAc;EACtBO,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,eAAEL,OAAA,CAACV,YAAY;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC3BC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,oBAAoB;EAC1BC,MAAM,EAAEN,cAAc;EACtBO,IAAI,EAAE,mBAAmB;EACzBC,SAAS,eAAEL,OAAA,CAACpB,iBAAiB;IAAA0B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAChCC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,iBAAiB;EACvBC,MAAM,EAAEN,cAAc;EACtBO,IAAI,EAAE,gBAAgB;EACtBC,SAAS,eAAEL,OAAA,CAACT,aAAa;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC5BC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,gBAAgB;EACtBC,MAAM,EAAEN,cAAc;EACtBO,IAAI,EAAE,kBAAkB;EACxBC,SAAS,eAAEL,OAAA,CAACR,aAAa;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC5BC,OAAO,EAAE;AACX,CAAC;AACD;AACA;EACER,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAEP,aAAa;EACrBQ,IAAI,EAAE,OAAO;EACbC,SAAS,eAAEL,OAAA,CAACjB,KAAK;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACpBC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAEP,aAAa;EACrBQ,IAAI,EAAE,UAAU;EAChBC,SAAS,eAAEL,OAAA,CAAChB,QAAQ;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACvBC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,0CAA0C;EAChDC,MAAM,EAAEP,aAAa;EACrBQ,IAAI,EAAE,wBAAwB;EAC9BC,SAAS,eAAEL,OAAA,CAACN,oBAAoB;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACnCC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,2BAA2B;EACjCC,MAAM,EAAEP,aAAa;EACrBQ,IAAI,EAAE,UAAU;EAChBC,SAAS,eAAEL,OAAA,CAACd,QAAQ;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACvBC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,oBAAoB;EAC1BC,MAAM,EAAEP,aAAa;EACrBQ,IAAI,EAAE,gBAAgB;EACtBC,SAAS,eAAEL,OAAA,CAACb,KAAK;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACpBC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,kBAAkB;EACxBC,MAAM,EAAEP,aAAa;EACrBQ,IAAI,EAAE,iBAAiB;EACvBC,SAAS,eAAEL,OAAA,CAACf,cAAc;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC7BC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAE,SAAS;EACfC,MAAM,EAAEP,aAAa;EACrBQ,IAAI,EAAE,oBAAoB;EAC1BC,SAAS,eAAEL,OAAA,CAACZ,gBAAgB;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC/BC,OAAO,EAAE;AACX,CAAC;AACD;AACA;EACER,IAAI,EAAE,2BAA2B;EACjCC,MAAM,EAAEL,eAAe;EACvBM,IAAI,EAAE,mBAAmB;EACzBC,SAAS,eAAEL,OAAA,CAACP,gBAAgB;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC/BC,OAAO,EAAE;AACX,CAAC,EACD;EACER,IAAI,EAAC,WAAW;EAChBC,MAAM,EAAEL,eAAe;EACvBM,IAAI,EAAE,UAAU;EAChBC,SAAS,eAAEL,OAAA,CAACL,eAAe;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC9BC,OAAO,EAAE;AACX,CAAC,CACF;AAED,eAAeT,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import React from "react";
import { Route, Routes, Navigate } from "react-router-dom";
import { Container, Grid } from "@mui/material";
import routes from "routes/index";
import { AUTH_PAGE_KEY } from "helpers/constants";
import { getRoutes } from "helpers/auth";

//--------------------------------------------------------------------------------------------------

const AuthLayout = () => {
  const mainContent = React.useRef(null);

  return (
    <div className="main-content" ref={mainContent}>
      <Container className="pb-5" fluid
        sx={{
          height: '100vh',
          display: 'flex',
          alignItems: 'center'
        }}>
        <Grid container justifyContent="center">
          <Routes>
            {getRoutes(routes, AUTH_PAGE_KEY)}
            <Route path="*" element={<Navigate to="/login" replace />}
            />
          </Routes>
        </Grid>
      </Container>
    </div>
  );
};

export default AuthLayout;

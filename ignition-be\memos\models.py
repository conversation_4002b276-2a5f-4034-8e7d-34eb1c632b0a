from django.db import models
from users.models import User
from django.utils import timezone

COMMENT_TYPE = (
    (1, 'Tasks'),
    (2, 'Subtasks')
)


class Memos(models.Model):
    content = models.TextField(null=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    type = models.IntegerField(choices=COMMENT_TYPE)
    created_at = models.DateTimeField(default=timezone.now)
    target_id = models.BigIntegerField(null=True)

    class Meta:
        db_table = 'memos'

{"ast": null, "code": "import React,{useEffect,useState,useCallback}from\"react\";import{useLocation,Route,Routes,Navigate}from\"react-router-dom\";import MobileHeader from\"components/Sidebar/MobileHeader\";import AdminFooter from\"components/Footers/AdminFooter\";import SidebarComponent from\"components/Sidebar/SidebarComponent\";import RightSidebar from\"components/Sidebar/RightSidebar\";import{ADMIN_PAGE_KEY,SIDEBAR_COLLAPSED_LEFT_PX,SIDEBAR_EXPANED_LEFT_PX}from\"helpers/constants\";import{getRoutes}from\"helpers/auth\";import routes from\"routes/index\";//--------------------------------------------------------------------------------------------------\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AdminLayout=()=>{const mainContent=React.useRef(null);const location=useLocation();const[sidebarCollapsed,setSidebarCollapsed]=useState(true);const[isSmartphone,setIsSmartphone]=useState(window.innerWidth<=1024);const[rightSidebarOpen,setRightSidebarOpen]=useState(true);// Kiểm tra xem có nên hiển thị right sidebar hay không dựa trên đường dẫn hiện tại\nconst shouldShowRightSidebar=useCallback(()=>{const path=location.pathname;console.log(\"Current path:\",path);// Danh sách các đường dẫn cần ẩn right sidebar\nconst hiddenPaths=[// Trang Calendar\n'/my/tasks/calendar','/d/my/tasks/calendar',// Trang Profile\n'/profile','/d/profile',// Trang Plan Creation\n'/plan/create','/d/plan/create'// Không ẩn ở trang Home (index.js)\n// Removed todo table paths since we removed the todo list page\n];// Kiểm tra xem đường dẫn hiện tại có nằm trong danh sách cần ẩn không\nconst shouldHide=hiddenPaths.some(hiddenPath=>{if(hiddenPath.endsWith('/')){return path===hiddenPath||path===hiddenPath.slice(0,-1);}return path===hiddenPath||path===hiddenPath+'/';});console.log(\"Should hide right sidebar:\",shouldHide);return!shouldHide;},[location.pathname]);useEffect(()=>{document.documentElement.scrollTop=0;document.scrollingElement.scrollTop=0;mainContent.current.scrollTop=0;},[location]);useEffect(()=>{const handleResize=()=>{const newIsSmartphone=window.innerWidth<=1024;setIsSmartphone(newIsSmartphone);if(newIsSmartphone){setRightSidebarOpen(false);}else{setRightSidebarOpen(shouldShowRightSidebar());}};window.addEventListener('resize',handleResize);return()=>window.removeEventListener('resize',handleResize);},[location,shouldShowRightSidebar]);// Cập nhật trạng thái right sidebar khi đường dẫn thay đổi\nuseEffect(()=>{if(!isSmartphone){const show=shouldShowRightSidebar();console.log(\"Setting rightSidebarOpen to:\",show);setRightSidebarOpen(show);}},[location,isSmartphone,shouldShowRightSidebar]);const toggleRightSidebar=()=>{setRightSidebarOpen(!rightSidebarOpen);};// Tính toán margin và width cho main content\nconst leftMargin=isSmartphone?'0':sidebarCollapsed?SIDEBAR_COLLAPSED_LEFT_PX:SIDEBAR_EXPANED_LEFT_PX;const rightMargin=isSmartphone?'0':rightSidebarOpen&&shouldShowRightSidebar()?'300px':'0';const mainContentStyle={marginLeft:leftMargin,marginRight:rightMargin,transition:'margin-left 0.3s ease, margin-right 0.3s ease',width:isSmartphone?'100%':`calc(100% - ${parseInt(leftMargin)+parseInt(rightMargin)}px)`};// Log để debug\nconsole.log(\"rightSidebarOpen:\",rightSidebarOpen);console.log(\"shouldShowRightSidebar:\",shouldShowRightSidebar());console.log(\"rightMargin:\",rightMargin);return/*#__PURE__*/_jsxs(_Fragment,{children:[isSmartphone?/*#__PURE__*/_jsx(MobileHeader,{onCollapseChange:setSidebarCollapsed,logo:{innerLink:\"/d/\",imgSrc:require(\"../assets/main_logo.png\"),imgAlt:\"Logo\"}}):/*#__PURE__*/_jsx(SidebarComponent,{onCollapseChange:setSidebarCollapsed,logo:{innerLink:\"/d\",imgSrc:require(sidebarCollapsed?\"../assets/fire_logo.png\":\"../assets/main_logo.png\"),imgAlt:\"Logo\"}}),/*#__PURE__*/_jsxs(\"div\",{className:\"main-content\",ref:mainContent,style:mainContentStyle,children:[/*#__PURE__*/_jsxs(Routes,{children:[getRoutes(routes,ADMIN_PAGE_KEY),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/d/\",replace:true})})]}),/*#__PURE__*/_jsx(AdminFooter,{})]}),shouldShowRightSidebar()&&/*#__PURE__*/_jsx(RightSidebar,{isOpen:rightSidebarOpen,onToggle:toggleRightSidebar})]});};export default AdminLayout;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useCallback", "useLocation", "Route", "Routes", "Navigate", "MobileHeader", "<PERSON><PERSON><PERSON><PERSON>er", "SidebarComponent", "RightSidebar", "ADMIN_PAGE_KEY", "SIDEBAR_COLLAPSED_LEFT_PX", "SIDEBAR_EXPANED_LEFT_PX", "getRoutes", "routes", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AdminLayout", "mainContent", "useRef", "location", "sidebarCollapsed", "setSidebarCollapsed", "isSmartphone", "setIsSmartphone", "window", "innerWidth", "rightSidebarOpen", "setRightSidebarOpen", "shouldShowRightSidebar", "path", "pathname", "console", "log", "hiddenPaths", "shouldHide", "some", "<PERSON><PERSON><PERSON>", "endsWith", "slice", "document", "documentElement", "scrollTop", "scrollingElement", "current", "handleResize", "newIsSmartphone", "addEventListener", "removeEventListener", "show", "toggleRightSidebar", "leftMargin", "<PERSON><PERSON><PERSON><PERSON>", "mainContentStyle", "marginLeft", "marginRight", "transition", "width", "parseInt", "children", "onCollapseChange", "logo", "innerLink", "imgSrc", "require", "imgAlt", "className", "ref", "style", "element", "to", "replace", "isOpen", "onToggle"], "sources": ["C:/ignition/ignition-ui/src/layouts/Admin.js"], "sourcesContent": ["import React, { useEffect, useState, useCallback } from \"react\";\r\nimport { useLocation, Route, Routes, Navigate } from \"react-router-dom\";\r\nimport MobileHeader from \"components/Sidebar/MobileHeader\";\r\nimport AdminFooter from \"components/Footers/AdminFooter\";\r\nimport SidebarComponent from \"components/Sidebar/SidebarComponent\";\r\nimport RightSidebar from \"components/Sidebar/RightSidebar\";\r\nimport { ADMIN_PAGE_KEY, SIDEBAR_COLLAPSED_LEFT_PX, SIDEBAR_EXPANED_LEFT_PX } from \"helpers/constants\";\r\nimport { getRoutes } from \"helpers/auth\";\r\nimport routes from \"routes/index\";\r\n\r\n//--------------------------------------------------------------------------------------------------\r\n\r\nconst AdminLayout = () => {\r\n  const mainContent = React.useRef(null);\r\n  const location = useLocation();\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);\r\n  const [isSmartphone, setIsSmartphone] = useState(window.innerWidth <= 1024);\r\n  const [rightSidebarOpen, setRightSidebarOpen] = useState(true);\r\n\r\n  // Kiểm tra xem có nên hiển thị right sidebar hay không dựa trên đường dẫn hiện tại\r\n  const shouldShowRightSidebar = useCallback(() => {\r\n    const path = location.pathname;\r\n    console.log(\"Current path:\", path);\r\n    \r\n    // Danh sách các đường dẫn cần ẩn right sidebar\r\n    const hiddenPaths = [\r\n      // Trang Calendar\r\n      '/my/tasks/calendar',\r\n      '/d/my/tasks/calendar',\r\n\r\n      // Trang Profile\r\n      '/profile',\r\n      '/d/profile',\r\n\r\n      // Trang Plan Creation\r\n      '/plan/create',\r\n      '/d/plan/create'\r\n\r\n      // Không ẩn ở trang Home (index.js)\r\n      // Removed todo table paths since we removed the todo list page\r\n    ];\r\n    \r\n    // Kiểm tra xem đường dẫn hiện tại có nằm trong danh sách cần ẩn không\r\n    const shouldHide = hiddenPaths.some(hiddenPath => {\r\n      if (hiddenPath.endsWith('/')) {\r\n        return path === hiddenPath || path === hiddenPath.slice(0, -1);\r\n      }\r\n      return path === hiddenPath || path === hiddenPath + '/';\r\n    });\r\n    \r\n    console.log(\"Should hide right sidebar:\", shouldHide);\r\n    return !shouldHide;\r\n  }, [location.pathname]);\r\n\r\n  useEffect(() => {\r\n    document.documentElement.scrollTop = 0;\r\n    document.scrollingElement.scrollTop = 0;\r\n    mainContent.current.scrollTop = 0;\r\n  }, [location]);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      const newIsSmartphone = window.innerWidth <= 1024;\r\n      setIsSmartphone(newIsSmartphone);\r\n      \r\n      if (newIsSmartphone) {\r\n        setRightSidebarOpen(false);\r\n      } else {\r\n        setRightSidebarOpen(shouldShowRightSidebar());\r\n      }\r\n    };\r\n    \r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, [location, shouldShowRightSidebar]);\r\n\r\n  // Cập nhật trạng thái right sidebar khi đường dẫn thay đổi\r\n  useEffect(() => {\r\n    if (!isSmartphone) {\r\n      const show = shouldShowRightSidebar();\r\n      console.log(\"Setting rightSidebarOpen to:\", show);\r\n      setRightSidebarOpen(show);\r\n    }\r\n  }, [location, isSmartphone, shouldShowRightSidebar]);\r\n\r\n  const toggleRightSidebar = () => {\r\n    setRightSidebarOpen(!rightSidebarOpen);\r\n  };\r\n\r\n  // Tính toán margin và width cho main content\r\n  const leftMargin = isSmartphone ? '0' : sidebarCollapsed ? SIDEBAR_COLLAPSED_LEFT_PX : SIDEBAR_EXPANED_LEFT_PX;\r\n  const rightMargin = isSmartphone ? '0' : (rightSidebarOpen && shouldShowRightSidebar()) ? '300px' : '0';\r\n  \r\n  const mainContentStyle = {\r\n    marginLeft: leftMargin,\r\n    marginRight: rightMargin,\r\n    transition: 'margin-left 0.3s ease, margin-right 0.3s ease',\r\n    width: isSmartphone ? '100%' : `calc(100% - ${parseInt(leftMargin) + parseInt(rightMargin)}px)`,\r\n  };\r\n\r\n  // Log để debug\r\n  console.log(\"rightSidebarOpen:\", rightSidebarOpen);\r\n  console.log(\"shouldShowRightSidebar:\", shouldShowRightSidebar());\r\n  console.log(\"rightMargin:\", rightMargin);\r\n\r\n  return (\r\n    <>\r\n      {isSmartphone ? (<MobileHeader\r\n        onCollapseChange={setSidebarCollapsed}\r\n        logo={{\r\n          innerLink: \"/d/\",\r\n          imgSrc: require(\"../assets/main_logo.png\"),\r\n          imgAlt: \"Logo\",\r\n        }} />\r\n      ) : (\r\n        <SidebarComponent onCollapseChange={setSidebarCollapsed}\r\n          logo={{\r\n            innerLink: \"/d\",\r\n            imgSrc: require(sidebarCollapsed ? \"../assets/fire_logo.png\" : \"../assets/main_logo.png\"),\r\n            imgAlt: \"Logo\",\r\n          }} />\r\n      )}\r\n      <div className=\"main-content\" ref={mainContent} style={mainContentStyle}>\r\n        <Routes>\r\n          {getRoutes(routes, ADMIN_PAGE_KEY)}\r\n          <Route path=\"*\" element={<Navigate to=\"/d/\" replace />} />\r\n        </Routes>\r\n        <AdminFooter />\r\n      </div>\r\n      \r\n      {/* Right Sidebar for Today's Tasks - Chỉ hiển thị ở các trang được chỉ định */}\r\n      {shouldShowRightSidebar() && (\r\n        <RightSidebar isOpen={rightSidebarOpen} onToggle={toggleRightSidebar} />\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AdminLayout;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OAASC,WAAW,CAAEC,KAAK,CAAEC,MAAM,CAAEC,QAAQ,KAAQ,kBAAkB,CACvE,MAAO,CAAAC,YAAY,KAAM,iCAAiC,CAC1D,MAAO,CAAAC,WAAW,KAAM,gCAAgC,CACxD,MAAO,CAAAC,gBAAgB,KAAM,qCAAqC,CAClE,MAAO,CAAAC,YAAY,KAAM,iCAAiC,CAC1D,OAASC,cAAc,CAAEC,yBAAyB,CAAEC,uBAAuB,KAAQ,mBAAmB,CACtG,OAASC,SAAS,KAAQ,cAAc,CACxC,MAAO,CAAAC,MAAM,KAAM,cAAc,CAEjC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEA,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,WAAW,CAAGxB,KAAK,CAACyB,MAAM,CAAC,IAAI,CAAC,CACtC,KAAM,CAAAC,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACuB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CAC9D,KAAM,CAAC2B,YAAY,CAAEC,eAAe,CAAC,CAAG5B,QAAQ,CAAC6B,MAAM,CAACC,UAAU,EAAI,IAAI,CAAC,CAC3E,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGhC,QAAQ,CAAC,IAAI,CAAC,CAE9D;AACA,KAAM,CAAAiC,sBAAsB,CAAGhC,WAAW,CAAC,IAAM,CAC/C,KAAM,CAAAiC,IAAI,CAAGV,QAAQ,CAACW,QAAQ,CAC9BC,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEH,IAAI,CAAC,CAElC;AACA,KAAM,CAAAI,WAAW,CAAG,CAClB;AACA,oBAAoB,CACpB,sBAAsB,CAEtB;AACA,UAAU,CACV,YAAY,CAEZ;AACA,cAAc,CACd,gBAEA;AACA;AAAA,CACD,CAED;AACA,KAAM,CAAAC,UAAU,CAAGD,WAAW,CAACE,IAAI,CAACC,UAAU,EAAI,CAChD,GAAIA,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC,CAAE,CAC5B,MAAO,CAAAR,IAAI,GAAKO,UAAU,EAAIP,IAAI,GAAKO,UAAU,CAACE,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAChE,CACA,MAAO,CAAAT,IAAI,GAAKO,UAAU,EAAIP,IAAI,GAAKO,UAAU,CAAG,GAAG,CACzD,CAAC,CAAC,CAEFL,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEE,UAAU,CAAC,CACrD,MAAO,CAACA,UAAU,CACpB,CAAC,CAAE,CAACf,QAAQ,CAACW,QAAQ,CAAC,CAAC,CAEvBpC,SAAS,CAAC,IAAM,CACd6C,QAAQ,CAACC,eAAe,CAACC,SAAS,CAAG,CAAC,CACtCF,QAAQ,CAACG,gBAAgB,CAACD,SAAS,CAAG,CAAC,CACvCxB,WAAW,CAAC0B,OAAO,CAACF,SAAS,CAAG,CAAC,CACnC,CAAC,CAAE,CAACtB,QAAQ,CAAC,CAAC,CAEdzB,SAAS,CAAC,IAAM,CACd,KAAM,CAAAkD,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,eAAe,CAAGrB,MAAM,CAACC,UAAU,EAAI,IAAI,CACjDF,eAAe,CAACsB,eAAe,CAAC,CAEhC,GAAIA,eAAe,CAAE,CACnBlB,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAC,IAAM,CACLA,mBAAmB,CAACC,sBAAsB,CAAC,CAAC,CAAC,CAC/C,CACF,CAAC,CAEDJ,MAAM,CAACsB,gBAAgB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CAC/C,MAAO,IAAMpB,MAAM,CAACuB,mBAAmB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CACjE,CAAC,CAAE,CAACzB,QAAQ,CAAES,sBAAsB,CAAC,CAAC,CAEtC;AACAlC,SAAS,CAAC,IAAM,CACd,GAAI,CAAC4B,YAAY,CAAE,CACjB,KAAM,CAAA0B,IAAI,CAAGpB,sBAAsB,CAAC,CAAC,CACrCG,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEgB,IAAI,CAAC,CACjDrB,mBAAmB,CAACqB,IAAI,CAAC,CAC3B,CACF,CAAC,CAAE,CAAC7B,QAAQ,CAAEG,YAAY,CAAEM,sBAAsB,CAAC,CAAC,CAEpD,KAAM,CAAAqB,kBAAkB,CAAGA,CAAA,GAAM,CAC/BtB,mBAAmB,CAAC,CAACD,gBAAgB,CAAC,CACxC,CAAC,CAED;AACA,KAAM,CAAAwB,UAAU,CAAG5B,YAAY,CAAG,GAAG,CAAGF,gBAAgB,CAAGd,yBAAyB,CAAGC,uBAAuB,CAC9G,KAAM,CAAA4C,WAAW,CAAG7B,YAAY,CAAG,GAAG,CAAII,gBAAgB,EAAIE,sBAAsB,CAAC,CAAC,CAAI,OAAO,CAAG,GAAG,CAEvG,KAAM,CAAAwB,gBAAgB,CAAG,CACvBC,UAAU,CAAEH,UAAU,CACtBI,WAAW,CAAEH,WAAW,CACxBI,UAAU,CAAE,+CAA+C,CAC3DC,KAAK,CAAElC,YAAY,CAAG,MAAM,CAAG,eAAemC,QAAQ,CAACP,UAAU,CAAC,CAAGO,QAAQ,CAACN,WAAW,CAAC,KAC5F,CAAC,CAED;AACApB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEN,gBAAgB,CAAC,CAClDK,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAEJ,sBAAsB,CAAC,CAAC,CAAC,CAChEG,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEmB,WAAW,CAAC,CAExC,mBACEtC,KAAA,CAAAE,SAAA,EAAA2C,QAAA,EACGpC,YAAY,cAAIX,IAAA,CAACV,YAAY,EAC5B0D,gBAAgB,CAAEtC,mBAAoB,CACtCuC,IAAI,CAAE,CACJC,SAAS,CAAE,KAAK,CAChBC,MAAM,CAAEC,OAAO,CAAC,yBAAyB,CAAC,CAC1CC,MAAM,CAAE,MACV,CAAE,CAAE,CAAC,cAELrD,IAAA,CAACR,gBAAgB,EAACwD,gBAAgB,CAAEtC,mBAAoB,CACtDuC,IAAI,CAAE,CACJC,SAAS,CAAE,IAAI,CACfC,MAAM,CAAEC,OAAO,CAAC3C,gBAAgB,CAAG,yBAAyB,CAAG,yBAAyB,CAAC,CACzF4C,MAAM,CAAE,MACV,CAAE,CAAE,CACP,cACDnD,KAAA,QAAKoD,SAAS,CAAC,cAAc,CAACC,GAAG,CAAEjD,WAAY,CAACkD,KAAK,CAAEf,gBAAiB,CAAAM,QAAA,eACtE7C,KAAA,CAACd,MAAM,EAAA2D,QAAA,EACJlD,SAAS,CAACC,MAAM,CAAEJ,cAAc,CAAC,cAClCM,IAAA,CAACb,KAAK,EAAC+B,IAAI,CAAC,GAAG,CAACuC,OAAO,cAAEzD,IAAA,CAACX,QAAQ,EAACqE,EAAE,CAAC,KAAK,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EACpD,CAAC,cACT3D,IAAA,CAACT,WAAW,GAAE,CAAC,EACZ,CAAC,CAGL0B,sBAAsB,CAAC,CAAC,eACvBjB,IAAA,CAACP,YAAY,EAACmE,MAAM,CAAE7C,gBAAiB,CAAC8C,QAAQ,CAAEvB,kBAAmB,CAAE,CACxE,EACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAjC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
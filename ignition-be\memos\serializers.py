from rest_framework import serializers
import os
from .models import Memos
from users.serializers import UserSerializer
class MemosSerializer(serializers.ModelSerializer):

    class Meta:
        model = Memos
        fields = '__all__'
class MemosListWithUserSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    created_at = serializers.SerializerMethodField()

    class Meta:
        model = Memos
        fields = ['id', 'content', 'type', 'user', 'target_id','created_at']

    def get_created_at(self, instance):
        print(instance.created_at)
        return instance.created_at.strftime("%H:%M %d/%m ")

import React from 'react';
import { Box, Typography, Grid, CircularProgress } from '@mui/material';
import { mainYellowColor } from "helpers/constants";
import styles from "../styles.module.scss";
import PlanCard from './PlanCard';

const PlanList = ({
  groupedPlans,
  viewMode,
  currentUser,
  loadingMore,
  hasMore,
  observerRef
}) => {
  return (
    <>
      {Object.entries(groupedPlans).map(([groupName, groupPlans]) => (
        <Box key={groupName} className={styles.planGroup}>
          <Typography
            variant="h6"
            className={styles.groupTitle}
            sx={{
              fontFamily: '"Recursive Variable", sans-serif',
              fontWeight: 600,
              color: '#333',
              marginBottom: '16px',
              paddingBottom: '8px',
              borderBottom: '2px solid #f0f0f0',
              display: 'flex',
              alignItems: 'center',
              '&::before': {
                content: '""',
                display: 'inline-block',
                width: '4px',
                height: '16px',
                backgroundColor: '#F0A500',
                marginRight: '8px',
                borderRadius: '2px'
              }
            }}
          >
            {groupName} ({groupPlans.length})
          </Typography>

          <Grid container spacing={viewMode === 'grid' ? 2 : 1} className={styles.planGrid}>
            {groupPlans.map((plan, index) => (
              <Grid item xs={12} sm={viewMode === 'grid' ? 6 : 12} md={viewMode === 'grid' ? 4 : 12} key={index}>
                <PlanCard plan={plan} currentUser={currentUser} index={index} />
              </Grid>
            ))}
          </Grid>
        </Box>
      ))}

      {/* Lazy Loading Trigger */}
      <Box ref={observerRef} className={styles.lazyLoadTrigger}></Box>

      {/* Loading Indicator */}
      {loadingMore && (
        <Box className={styles.loadingMoreIndicator}>
          <CircularProgress size={32} sx={{ color: mainYellowColor }} />
          <Typography
            variant="body2"
            sx={{
              marginTop: '12px',
              fontFamily: '"Recursive Variable", sans-serif',
              color: '#666'
            }}
          >
            Loading more plans...
          </Typography>
        </Box>
      )}
    </>
  );
};

export default PlanList;

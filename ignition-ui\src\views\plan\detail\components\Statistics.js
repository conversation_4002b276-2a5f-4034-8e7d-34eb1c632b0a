import React from 'react';
import { Box, Typography, Grid, Paper } from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import styles from '../styles.module.scss';

const Statistics = ({ stats }) => {
  const statItems = [
    {
      icon: "material-symbols:flag",
      label: "Milestones",
      value: stats.milestones,
      color: mainYellowColor
    },
    {
      icon: "material-symbols:task",
      label: "Tasks",
      value: stats.tasks,
      color: "#4CAF50"
    },
    {
      icon: "material-symbols:checklist",
      label: "Subtasks",
      value: stats.subtasks,
      color: "#2196F3"
    },
    {
      icon: "material-symbols:comment",
      label: "Comments",
      value: stats.comments || 0,
      color: "#9C27B0"
    }
  ];

  return (
    <Paper 
      elevation={0}
      className={styles.statisticsCard}
      sx={{ 
        flex: 1,
        borderRadius: '12px',
        mb: 0.5,
        py: 1,
        px: 1.5
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', mb: 1 }}>
        <Iconify icon="material-symbols:analytics" width={20} height={20} color={mainYellowColor} />
        <Typography 
          variant="h6" 
          sx={{ 
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            fontSize: '1.2rem',
            color: '#555',
            margin: 0
          }}
        >
          Statistics
        </Typography>
      </Box>

      <Grid container spacing={1}>
        {statItems.map((item, index) => (
          <Grid item xs={3} key={index}>
            <Box 
              className={styles.statItem}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                padding: '6px 4px',
                borderRadius: '6px',
                backgroundColor: '#f9f9f9',
                transition: 'all 0.2s',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Iconify icon={item.icon} width={16} height={16} color={item.color} />
                <Typography variant="h5" sx={{ fontWeight: 700, color: '#333', fontFamily: '"Recursive Variable", sans-serif', fontSize: '1.1rem' }}>
                  {item.value}
                </Typography>
              </Box>
              <Typography variant="body2" sx={{ color: '#666', fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.9rem', mt: 0.5 }}>
                {item.label}
              </Typography>
            </Box>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

export default Statistics; 
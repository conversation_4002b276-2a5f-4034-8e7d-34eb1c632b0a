{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{Box,TextField,IconButton,Paper,Typography,Chip,Tooltip,Collapse,CircularProgress}from'@mui/material';import Iconify from'components/Iconify/index';import{mainYellowColor}from\"helpers/constants\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ChatbotBar=_ref=>{let{planInfo,onPlanUpdate,onSwitchToAgent}=_ref;const[isExpanded,setIsExpanded]=useState(false);const[message,setMessage]=useState('');const[isLoading,setIsLoading]=useState(false);// Generate contextual suggestions based on project data\nconst generateSuggestions=()=>{var _planInfo$milestones;const baseSuggestions=[\"Show project progress\",\"What should I work on next?\",\"Add a new milestone\",\"Help me organize tasks\"];if((planInfo===null||planInfo===void 0?void 0:(_planInfo$milestones=planInfo.milestones)===null||_planInfo$milestones===void 0?void 0:_planInfo$milestones.length)>0){var _milestone$tasks;const milestone=planInfo.milestones[0];if(((_milestone$tasks=milestone.tasks)===null||_milestone$tasks===void 0?void 0:_milestone$tasks.length)>0){const incompleteTasks=milestone.tasks.filter(task=>task.status!=='completed');if(incompleteTasks.length>0){baseSuggestions.unshift(`Mark \"${incompleteTasks[0].name}\" as completed`);}}baseSuggestions.push(`Add task to \"${milestone.name}\"`);}return baseSuggestions.slice(0,5);// Limit to 5 suggestions\n};const[suggestions]=useState(generateSuggestions());const inputRef=useRef(null);// Focus input when expanded\nuseEffect(()=>{if(isExpanded&&inputRef.current){inputRef.current.focus();}},[isExpanded]);const handleToggleExpand=()=>{setIsExpanded(!isExpanded);};const handleSendMessage=async()=>{if(!message.trim()||isLoading)return;console.log('ChatbotBar - planInfo:',planInfo);// Debug log\nsetIsLoading(true);try{// Validate planInfo before proceeding\nif(!(planInfo!==null&&planInfo!==void 0&&planInfo.id)){console.error('ChatbotBar - Missing planInfo or planInfo.id:',planInfo);return;}// Save conversation to localStorage for Agent tab\nconst conversationHistory=JSON.parse(localStorage.getItem('agent_conversations')||'[]');const newConversation={id:Date.now(),planId:planInfo.id,planName:planInfo.name,message:message.trim(),timestamp:new Date().toISOString(),response:null// Will be filled by AI response\n};conversationHistory.push(newConversation);localStorage.setItem('agent_conversations',JSON.stringify(conversationHistory));console.log('ChatbotBar - Conversation saved, switching to agent tab');// Debug log\n// Switch to Agent tab with the conversation\nif(onSwitchToAgent){onSwitchToAgent({message:message.trim(),planInfo:planInfo,conversationId:newConversation.id});}else{console.error('ChatbotBar - onSwitchToAgent callback not provided');}}catch(error){console.error('Error sending message:',error);}finally{setIsLoading(false);setMessage('');setIsExpanded(false);}};const handleKeyPress=e=>{if(e.key==='Enter'&&!e.shiftKey){e.preventDefault();handleSendMessage();}};const handleSuggestionClick=suggestion=>{setMessage(suggestion);if(inputRef.current){inputRef.current.focus();}};return/*#__PURE__*/_jsxs(Paper,{elevation:0,sx:{mb:2,borderRadius:'12px',border:'1px solid #f0f0f0',overflow:'hidden',transition:'all 0.3s ease',backgroundColor:'#fafafa'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between',p:2,cursor:'pointer','&:hover':{backgroundColor:'#f5f5f5'}},onClick:handleToggleExpand,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1.5},children:[/*#__PURE__*/_jsx(Box,{sx:{width:32,height:32,borderRadius:'50%',backgroundColor:mainYellowColor,display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:robot\",width:18,height:18,color:\"#fff\"})}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',fontWeight:600,fontSize:'1rem',color:'#333',mb:0.5},children:\"AI Project Assistant\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{color:'#666',fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.8rem'},children:\"Ask questions or make changes to your project\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(Chip,{label:\"Beta\",size:\"small\",sx:{backgroundColor:`${mainYellowColor}20`,color:mainYellowColor,fontWeight:600,fontSize:'0.7rem',height:'20px'}}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",children:/*#__PURE__*/_jsx(Iconify,{icon:isExpanded?\"material-symbols:expand-less\":\"material-symbols:expand-more\",width:20,height:20,color:\"#666\"})})]})]}),/*#__PURE__*/_jsx(Collapse,{in:isExpanded,children:/*#__PURE__*/_jsxs(Box,{sx:{px:2,pb:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{color:'#666',fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.8rem',mb:1,display:'block'},children:\"Quick suggestions:\"}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexWrap:'wrap',gap:1},children:suggestions.map((suggestion,index)=>/*#__PURE__*/_jsx(Chip,{label:suggestion,size:\"small\",onClick:()=>handleSuggestionClick(suggestion),sx:{backgroundColor:'#fff',border:'1px solid #e0e0e0',cursor:'pointer',fontSize:'0.75rem','&:hover':{backgroundColor:`${mainYellowColor}10`,borderColor:mainYellowColor}}},index))})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1,alignItems:'flex-end'},children:[/*#__PURE__*/_jsx(TextField,{inputRef:inputRef,value:message,onChange:e=>setMessage(e.target.value),onKeyPress:handleKeyPress,placeholder:\"Ask me anything about your project or request changes...\",multiline:true,maxRows:3,fullWidth:true,variant:\"outlined\",disabled:isLoading,sx:{'& .MuiOutlinedInput-root':{borderRadius:'8px',backgroundColor:'#fff',fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.9rem'}}}),/*#__PURE__*/_jsx(Tooltip,{title:\"Send message\",children:/*#__PURE__*/_jsx(IconButton,{onClick:handleSendMessage,disabled:!message.trim()||isLoading,sx:{backgroundColor:message.trim()&&!isLoading?mainYellowColor:'#f0f0f0',color:message.trim()&&!isLoading?'#fff':'#999','&:hover':{backgroundColor:message.trim()&&!isLoading?'#E69500':'#f0f0f0'},mb:0.5},children:isLoading?/*#__PURE__*/_jsx(CircularProgress,{size:20,sx:{color:'#999'}}):/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:send\",width:20,height:20})})})]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{color:'#999',fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.75rem',mt:1,display:'block'},children:\"\\uD83D\\uDCA1 I can help you mark tasks as complete, add new tasks, update descriptions, and more!\"})]})})]});};export default ChatbotBar;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "TextField", "IconButton", "Paper", "Typography", "Chip", "<PERSON><PERSON><PERSON>", "Collapse", "CircularProgress", "Iconify", "mainYellowColor", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "planInfo", "onPlanUpdate", "onSwitchToAgent", "isExpanded", "setIsExpanded", "message", "setMessage", "isLoading", "setIsLoading", "generateSuggestions", "_planInfo$milestones", "baseSuggestions", "milestones", "length", "_milestone$tasks", "milestone", "tasks", "incompleteTasks", "filter", "task", "status", "unshift", "name", "push", "slice", "suggestions", "inputRef", "current", "focus", "handleToggleExpand", "handleSendMessage", "trim", "console", "log", "id", "error", "conversationHistory", "JSON", "parse", "localStorage", "getItem", "newConversation", "Date", "now", "planId", "planName", "timestamp", "toISOString", "response", "setItem", "stringify", "conversationId", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "handleSuggestionClick", "suggestion", "elevation", "sx", "mb", "borderRadius", "border", "overflow", "transition", "backgroundColor", "children", "display", "alignItems", "justifyContent", "p", "cursor", "onClick", "gap", "width", "height", "icon", "color", "variant", "fontFamily", "fontWeight", "fontSize", "label", "size", "in", "px", "pb", "flexWrap", "map", "index", "borderColor", "value", "onChange", "target", "onKeyPress", "placeholder", "multiline", "maxRows", "fullWidth", "disabled", "title", "mt"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/ChatbotBar.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  TextField,\r\n  IconButton,\r\n  Paper,\r\n  Typography,\r\n  Chip,\r\n  Tooltip,\r\n  Collapse,\r\n  CircularProgress\r\n} from '@mui/material';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor } from \"helpers/constants\";\r\n\r\nconst ChatbotBar = ({ planInfo, onPlanUpdate, onSwitchToAgent }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n  const [message, setMessage] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  // Generate contextual suggestions based on project data\r\n  const generateSuggestions = () => {\r\n    const baseSuggestions = [\r\n      \"Show project progress\",\r\n      \"What should I work on next?\",\r\n      \"Add a new milestone\",\r\n      \"Help me organize tasks\"\r\n    ];\r\n\r\n    if (planInfo?.milestones?.length > 0) {\r\n      const milestone = planInfo.milestones[0];\r\n      if (milestone.tasks?.length > 0) {\r\n        const incompleteTasks = milestone.tasks.filter(task => task.status !== 'completed');\r\n        if (incompleteTasks.length > 0) {\r\n          baseSuggestions.unshift(`Mark \"${incompleteTasks[0].name}\" as completed`);\r\n        }\r\n      }\r\n      baseSuggestions.push(`Add task to \"${milestone.name}\"`);\r\n    }\r\n\r\n    return baseSuggestions.slice(0, 5); // Limit to 5 suggestions\r\n  };\r\n\r\n  const [suggestions] = useState(generateSuggestions());\r\n  \r\n  const inputRef = useRef(null);\r\n\r\n  // Focus input when expanded\r\n  useEffect(() => {\r\n    if (isExpanded && inputRef.current) {\r\n      inputRef.current.focus();\r\n    }\r\n  }, [isExpanded]);\r\n\r\n  const handleToggleExpand = () => {\r\n    setIsExpanded(!isExpanded);\r\n  };\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!message.trim() || isLoading) return;\r\n\r\n    console.log('ChatbotBar - planInfo:', planInfo); // Debug log\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      // Validate planInfo before proceeding\r\n      if (!planInfo?.id) {\r\n        console.error('ChatbotBar - Missing planInfo or planInfo.id:', planInfo);\r\n        return;\r\n      }\r\n\r\n      // Save conversation to localStorage for Agent tab\r\n      const conversationHistory = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n      const newConversation = {\r\n        id: Date.now(),\r\n        planId: planInfo.id,\r\n        planName: planInfo.name,\r\n        message: message.trim(),\r\n        timestamp: new Date().toISOString(),\r\n        response: null // Will be filled by AI response\r\n      };\r\n\r\n      conversationHistory.push(newConversation);\r\n      localStorage.setItem('agent_conversations', JSON.stringify(conversationHistory));\r\n\r\n      console.log('ChatbotBar - Conversation saved, switching to agent tab'); // Debug log\r\n\r\n      // Switch to Agent tab with the conversation\r\n      if (onSwitchToAgent) {\r\n        onSwitchToAgent({\r\n          message: message.trim(),\r\n          planInfo: planInfo,\r\n          conversationId: newConversation.id\r\n        });\r\n      } else {\r\n        console.error('ChatbotBar - onSwitchToAgent callback not provided');\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error('Error sending message:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n      setMessage('');\r\n      setIsExpanded(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (e) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const handleSuggestionClick = (suggestion) => {\r\n    setMessage(suggestion);\r\n    if (inputRef.current) {\r\n      inputRef.current.focus();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Paper\r\n      elevation={0}\r\n      sx={{\r\n        mb: 2,\r\n        borderRadius: '12px',\r\n        border: '1px solid #f0f0f0',\r\n        overflow: 'hidden',\r\n        transition: 'all 0.3s ease',\r\n        backgroundColor: '#fafafa'\r\n      }}\r\n    >\r\n      {/* Chatbot Header */}\r\n      <Box\r\n        sx={{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'space-between',\r\n          p: 2,\r\n          cursor: 'pointer',\r\n          '&:hover': {\r\n            backgroundColor: '#f5f5f5'\r\n          }\r\n        }}\r\n        onClick={handleToggleExpand}\r\n      >\r\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>\r\n          <Box\r\n            sx={{\r\n              width: 32,\r\n              height: 32,\r\n              borderRadius: '50%',\r\n              backgroundColor: mainYellowColor,\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'center'\r\n            }}\r\n          >\r\n            <Iconify icon=\"mdi:robot\" width={18} height={18} color=\"#fff\" />\r\n          </Box>\r\n          <Box>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                fontSize: '1rem',\r\n                color: '#333',\r\n                mb: 0.5\r\n              }}\r\n            >\r\n              AI Project Assistant\r\n            </Typography>\r\n            <Typography\r\n              variant=\"caption\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontSize: '0.8rem'\r\n              }}\r\n            >\r\n              Ask questions or make changes to your project\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n\r\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n          <Chip\r\n            label=\"Beta\"\r\n            size=\"small\"\r\n            sx={{\r\n              backgroundColor: `${mainYellowColor}20`,\r\n              color: mainYellowColor,\r\n              fontWeight: 600,\r\n              fontSize: '0.7rem',\r\n              height: '20px'\r\n            }}\r\n          />\r\n          <IconButton size=\"small\">\r\n            <Iconify \r\n              icon={isExpanded ? \"material-symbols:expand-less\" : \"material-symbols:expand-more\"} \r\n              width={20} \r\n              height={20} \r\n              color=\"#666\"\r\n            />\r\n          </IconButton>\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Expanded Content */}\r\n      <Collapse in={isExpanded}>\r\n        <Box sx={{ px: 2, pb: 2 }}>\r\n          {/* Quick Suggestions */}\r\n          <Box sx={{ mb: 2 }}>\r\n            <Typography\r\n              variant=\"caption\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontSize: '0.8rem',\r\n                mb: 1,\r\n                display: 'block'\r\n              }}\r\n            >\r\n              Quick suggestions:\r\n            </Typography>\r\n            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\r\n              {suggestions.map((suggestion, index) => (\r\n                <Chip\r\n                  key={index}\r\n                  label={suggestion}\r\n                  size=\"small\"\r\n                  onClick={() => handleSuggestionClick(suggestion)}\r\n                  sx={{\r\n                    backgroundColor: '#fff',\r\n                    border: '1px solid #e0e0e0',\r\n                    cursor: 'pointer',\r\n                    fontSize: '0.75rem',\r\n                    '&:hover': {\r\n                      backgroundColor: `${mainYellowColor}10`,\r\n                      borderColor: mainYellowColor\r\n                    }\r\n                  }}\r\n                />\r\n              ))}\r\n            </Box>\r\n          </Box>\r\n\r\n          {/* Input Field */}\r\n          <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\r\n            <TextField\r\n              inputRef={inputRef}\r\n              value={message}\r\n              onChange={(e) => setMessage(e.target.value)}\r\n              onKeyPress={handleKeyPress}\r\n              placeholder=\"Ask me anything about your project or request changes...\"\r\n              multiline\r\n              maxRows={3}\r\n              fullWidth\r\n              variant=\"outlined\"\r\n              disabled={isLoading}\r\n              sx={{\r\n                '& .MuiOutlinedInput-root': {\r\n                  borderRadius: '8px',\r\n                  backgroundColor: '#fff',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  fontSize: '0.9rem'\r\n                }\r\n              }}\r\n            />\r\n            <Tooltip title=\"Send message\">\r\n              <IconButton\r\n                onClick={handleSendMessage}\r\n                disabled={!message.trim() || isLoading}\r\n                sx={{\r\n                  backgroundColor: message.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\r\n                  color: message.trim() && !isLoading ? '#fff' : '#999',\r\n                  '&:hover': {\r\n                    backgroundColor: message.trim() && !isLoading ? '#E69500' : '#f0f0f0'\r\n                  },\r\n                  mb: 0.5\r\n                }}\r\n              >\r\n                {isLoading ? (\r\n                  <CircularProgress size={20} sx={{ color: '#999' }} />\r\n                ) : (\r\n                  <Iconify icon=\"material-symbols:send\" width={20} height={20} />\r\n                )}\r\n              </IconButton>\r\n            </Tooltip>\r\n          </Box>\r\n\r\n          {/* Helper Text */}\r\n          <Typography\r\n            variant=\"caption\"\r\n            sx={{\r\n              color: '#999',\r\n              fontFamily: '\"Recursive Variable\", sans-serif',\r\n              fontSize: '0.75rem',\r\n              mt: 1,\r\n              display: 'block'\r\n            }}\r\n          >\r\n            💡 I can help you mark tasks as complete, add new tasks, update descriptions, and more!\r\n          </Typography>\r\n        </Box>\r\n      </Collapse>\r\n    </Paper>\r\n  );\r\n};\r\n\r\nexport default ChatbotBar;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OACEC,GAAG,CACHC,SAAS,CACTC,UAAU,CACVC,KAAK,CACLC,UAAU,CACVC,IAAI,CACJC,OAAO,CACPC,QAAQ,CACRC,gBAAgB,KACX,eAAe,CACtB,MAAO,CAAAC,OAAO,KAAM,0BAA0B,CAC9C,OAASC,eAAe,KAAQ,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAAiD,IAAhD,CAAEC,QAAQ,CAAEC,YAAY,CAAEC,eAAgB,CAAC,CAAAH,IAAA,CAC7D,KAAM,CAACI,UAAU,CAAEC,aAAa,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACyB,OAAO,CAAEC,UAAU,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC2B,SAAS,CAAEC,YAAY,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CACjD;AACA,KAAM,CAAA6B,mBAAmB,CAAGA,CAAA,GAAM,KAAAC,oBAAA,CAChC,KAAM,CAAAC,eAAe,CAAG,CACtB,uBAAuB,CACvB,6BAA6B,CAC7B,qBAAqB,CACrB,wBAAwB,CACzB,CAED,GAAI,CAAAX,QAAQ,SAARA,QAAQ,kBAAAU,oBAAA,CAARV,QAAQ,CAAEY,UAAU,UAAAF,oBAAA,iBAApBA,oBAAA,CAAsBG,MAAM,EAAG,CAAC,CAAE,KAAAC,gBAAA,CACpC,KAAM,CAAAC,SAAS,CAAGf,QAAQ,CAACY,UAAU,CAAC,CAAC,CAAC,CACxC,GAAI,EAAAE,gBAAA,CAAAC,SAAS,CAACC,KAAK,UAAAF,gBAAA,iBAAfA,gBAAA,CAAiBD,MAAM,EAAG,CAAC,CAAE,CAC/B,KAAM,CAAAI,eAAe,CAAGF,SAAS,CAACC,KAAK,CAACE,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACC,MAAM,GAAK,WAAW,CAAC,CACnF,GAAIH,eAAe,CAACJ,MAAM,CAAG,CAAC,CAAE,CAC9BF,eAAe,CAACU,OAAO,CAAC,SAASJ,eAAe,CAAC,CAAC,CAAC,CAACK,IAAI,gBAAgB,CAAC,CAC3E,CACF,CACAX,eAAe,CAACY,IAAI,CAAC,gBAAgBR,SAAS,CAACO,IAAI,GAAG,CAAC,CACzD,CAEA,MAAO,CAAAX,eAAe,CAACa,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE;AACtC,CAAC,CAED,KAAM,CAACC,WAAW,CAAC,CAAG7C,QAAQ,CAAC6B,mBAAmB,CAAC,CAAC,CAAC,CAErD,KAAM,CAAAiB,QAAQ,CAAG7C,MAAM,CAAC,IAAI,CAAC,CAE7B;AACAC,SAAS,CAAC,IAAM,CACd,GAAIqB,UAAU,EAAIuB,QAAQ,CAACC,OAAO,CAAE,CAClCD,QAAQ,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC,CAC1B,CACF,CAAC,CAAE,CAACzB,UAAU,CAAC,CAAC,CAEhB,KAAM,CAAA0B,kBAAkB,CAAGA,CAAA,GAAM,CAC/BzB,aAAa,CAAC,CAACD,UAAU,CAAC,CAC5B,CAAC,CAED,KAAM,CAAA2B,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAACzB,OAAO,CAAC0B,IAAI,CAAC,CAAC,EAAIxB,SAAS,CAAE,OAElCyB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEjC,QAAQ,CAAC,CAAE;AACjDQ,YAAY,CAAC,IAAI,CAAC,CAElB,GAAI,CACF;AACA,GAAI,EAACR,QAAQ,SAARA,QAAQ,WAARA,QAAQ,CAAEkC,EAAE,EAAE,CACjBF,OAAO,CAACG,KAAK,CAAC,+CAA+C,CAAEnC,QAAQ,CAAC,CACxE,OACF,CAEA;AACA,KAAM,CAAAoC,mBAAmB,CAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,EAAI,IAAI,CAAC,CAC3F,KAAM,CAAAC,eAAe,CAAG,CACtBP,EAAE,CAAEQ,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,MAAM,CAAE5C,QAAQ,CAACkC,EAAE,CACnBW,QAAQ,CAAE7C,QAAQ,CAACsB,IAAI,CACvBjB,OAAO,CAAEA,OAAO,CAAC0B,IAAI,CAAC,CAAC,CACvBe,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,CACnCC,QAAQ,CAAE,IAAK;AACjB,CAAC,CAEDZ,mBAAmB,CAACb,IAAI,CAACkB,eAAe,CAAC,CACzCF,YAAY,CAACU,OAAO,CAAC,qBAAqB,CAAEZ,IAAI,CAACa,SAAS,CAACd,mBAAmB,CAAC,CAAC,CAEhFJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC,CAAE;AAExE;AACA,GAAI/B,eAAe,CAAE,CACnBA,eAAe,CAAC,CACdG,OAAO,CAAEA,OAAO,CAAC0B,IAAI,CAAC,CAAC,CACvB/B,QAAQ,CAAEA,QAAQ,CAClBmD,cAAc,CAAEV,eAAe,CAACP,EAClC,CAAC,CAAC,CACJ,CAAC,IAAM,CACLF,OAAO,CAACG,KAAK,CAAC,oDAAoD,CAAC,CACrE,CAEF,CAAE,MAAOA,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CAAC,OAAS,CACR3B,YAAY,CAAC,KAAK,CAAC,CACnBF,UAAU,CAAC,EAAE,CAAC,CACdF,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAAgD,cAAc,CAAIC,CAAC,EAAK,CAC5B,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,EAAI,CAACD,CAAC,CAACE,QAAQ,CAAE,CACpCF,CAAC,CAACG,cAAc,CAAC,CAAC,CAClB1B,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAA2B,qBAAqB,CAAIC,UAAU,EAAK,CAC5CpD,UAAU,CAACoD,UAAU,CAAC,CACtB,GAAIhC,QAAQ,CAACC,OAAO,CAAE,CACpBD,QAAQ,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC,CAC1B,CACF,CAAC,CAED,mBACE/B,KAAA,CAACX,KAAK,EACJyE,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,MAAM,CACpBC,MAAM,CAAE,mBAAmB,CAC3BC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,eAAe,CAC3BC,eAAe,CAAE,SACnB,CAAE,CAAAC,QAAA,eAGFtE,KAAA,CAACd,GAAG,EACF6E,EAAE,CAAE,CACFQ,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,eAAe,CAC/BC,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,SAAS,CACjB,SAAS,CAAE,CACTN,eAAe,CAAE,SACnB,CACF,CAAE,CACFO,OAAO,CAAE5C,kBAAmB,CAAAsC,QAAA,eAE5BtE,KAAA,CAACd,GAAG,EAAC6E,EAAE,CAAE,CAAEQ,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEK,GAAG,CAAE,GAAI,CAAE,CAAAP,QAAA,eAC3DxE,IAAA,CAACZ,GAAG,EACF6E,EAAE,CAAE,CACFe,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVd,YAAY,CAAE,KAAK,CACnBI,eAAe,CAAEzE,eAAe,CAChC2E,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAAH,QAAA,cAEFxE,IAAA,CAACH,OAAO,EAACqF,IAAI,CAAC,WAAW,CAACF,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACE,KAAK,CAAC,MAAM,CAAE,CAAC,CAC7D,CAAC,cACNjF,KAAA,CAACd,GAAG,EAAAoF,QAAA,eACFxE,IAAA,CAACR,UAAU,EACT4F,OAAO,CAAC,IAAI,CACZnB,EAAE,CAAE,CACFoB,UAAU,CAAE,kCAAkC,CAC9CC,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,MAAM,CAChBJ,KAAK,CAAE,MAAM,CACbjB,EAAE,CAAE,GACN,CAAE,CAAAM,QAAA,CACH,sBAED,CAAY,CAAC,cACbxE,IAAA,CAACR,UAAU,EACT4F,OAAO,CAAC,SAAS,CACjBnB,EAAE,CAAE,CACFkB,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,kCAAkC,CAC9CE,QAAQ,CAAE,QACZ,CAAE,CAAAf,QAAA,CACH,+CAED,CAAY,CAAC,EACV,CAAC,EACH,CAAC,cAENtE,KAAA,CAACd,GAAG,EAAC6E,EAAE,CAAE,CAAEQ,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEK,GAAG,CAAE,CAAE,CAAE,CAAAP,QAAA,eACzDxE,IAAA,CAACP,IAAI,EACH+F,KAAK,CAAC,MAAM,CACZC,IAAI,CAAC,OAAO,CACZxB,EAAE,CAAE,CACFM,eAAe,CAAE,GAAGzE,eAAe,IAAI,CACvCqF,KAAK,CAAErF,eAAe,CACtBwF,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,QAAQ,CAClBN,MAAM,CAAE,MACV,CAAE,CACH,CAAC,cACFjF,IAAA,CAACV,UAAU,EAACmG,IAAI,CAAC,OAAO,CAAAjB,QAAA,cACtBxE,IAAA,CAACH,OAAO,EACNqF,IAAI,CAAE1E,UAAU,CAAG,8BAA8B,CAAG,8BAA+B,CACnFwE,KAAK,CAAE,EAAG,CACVC,MAAM,CAAE,EAAG,CACXE,KAAK,CAAC,MAAM,CACb,CAAC,CACQ,CAAC,EACV,CAAC,EACH,CAAC,cAGNnF,IAAA,CAACL,QAAQ,EAAC+F,EAAE,CAAElF,UAAW,CAAAgE,QAAA,cACvBtE,KAAA,CAACd,GAAG,EAAC6E,EAAE,CAAE,CAAE0B,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAApB,QAAA,eAExBtE,KAAA,CAACd,GAAG,EAAC6E,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAM,QAAA,eACjBxE,IAAA,CAACR,UAAU,EACT4F,OAAO,CAAC,SAAS,CACjBnB,EAAE,CAAE,CACFkB,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,kCAAkC,CAC9CE,QAAQ,CAAE,QAAQ,CAClBrB,EAAE,CAAE,CAAC,CACLO,OAAO,CAAE,OACX,CAAE,CAAAD,QAAA,CACH,oBAED,CAAY,CAAC,cACbxE,IAAA,CAACZ,GAAG,EAAC6E,EAAE,CAAE,CAAEQ,OAAO,CAAE,MAAM,CAAEoB,QAAQ,CAAE,MAAM,CAAEd,GAAG,CAAE,CAAE,CAAE,CAAAP,QAAA,CACpD1C,WAAW,CAACgE,GAAG,CAAC,CAAC/B,UAAU,CAAEgC,KAAK,gBACjC/F,IAAA,CAACP,IAAI,EAEH+F,KAAK,CAAEzB,UAAW,CAClB0B,IAAI,CAAC,OAAO,CACZX,OAAO,CAAEA,CAAA,GAAMhB,qBAAqB,CAACC,UAAU,CAAE,CACjDE,EAAE,CAAE,CACFM,eAAe,CAAE,MAAM,CACvBH,MAAM,CAAE,mBAAmB,CAC3BS,MAAM,CAAE,SAAS,CACjBU,QAAQ,CAAE,SAAS,CACnB,SAAS,CAAE,CACThB,eAAe,CAAE,GAAGzE,eAAe,IAAI,CACvCkG,WAAW,CAAElG,eACf,CACF,CAAE,EAbGiG,KAcN,CACF,CAAC,CACC,CAAC,EACH,CAAC,cAGN7F,KAAA,CAACd,GAAG,EAAC6E,EAAE,CAAE,CAAEQ,OAAO,CAAE,MAAM,CAAEM,GAAG,CAAE,CAAC,CAAEL,UAAU,CAAE,UAAW,CAAE,CAAAF,QAAA,eAC3DxE,IAAA,CAACX,SAAS,EACR0C,QAAQ,CAAEA,QAAS,CACnBkE,KAAK,CAAEvF,OAAQ,CACfwF,QAAQ,CAAGxC,CAAC,EAAK/C,UAAU,CAAC+C,CAAC,CAACyC,MAAM,CAACF,KAAK,CAAE,CAC5CG,UAAU,CAAE3C,cAAe,CAC3B4C,WAAW,CAAC,0DAA0D,CACtEC,SAAS,MACTC,OAAO,CAAE,CAAE,CACXC,SAAS,MACTpB,OAAO,CAAC,UAAU,CAClBqB,QAAQ,CAAE7F,SAAU,CACpBqD,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BE,YAAY,CAAE,KAAK,CACnBI,eAAe,CAAE,MAAM,CACvBc,UAAU,CAAE,kCAAkC,CAC9CE,QAAQ,CAAE,QACZ,CACF,CAAE,CACH,CAAC,cACFvF,IAAA,CAACN,OAAO,EAACgH,KAAK,CAAC,cAAc,CAAAlC,QAAA,cAC3BxE,IAAA,CAACV,UAAU,EACTwF,OAAO,CAAE3C,iBAAkB,CAC3BsE,QAAQ,CAAE,CAAC/F,OAAO,CAAC0B,IAAI,CAAC,CAAC,EAAIxB,SAAU,CACvCqD,EAAE,CAAE,CACFM,eAAe,CAAE7D,OAAO,CAAC0B,IAAI,CAAC,CAAC,EAAI,CAACxB,SAAS,CAAGd,eAAe,CAAG,SAAS,CAC3EqF,KAAK,CAAEzE,OAAO,CAAC0B,IAAI,CAAC,CAAC,EAAI,CAACxB,SAAS,CAAG,MAAM,CAAG,MAAM,CACrD,SAAS,CAAE,CACT2D,eAAe,CAAE7D,OAAO,CAAC0B,IAAI,CAAC,CAAC,EAAI,CAACxB,SAAS,CAAG,SAAS,CAAG,SAC9D,CAAC,CACDsD,EAAE,CAAE,GACN,CAAE,CAAAM,QAAA,CAED5D,SAAS,cACRZ,IAAA,CAACJ,gBAAgB,EAAC6F,IAAI,CAAE,EAAG,CAACxB,EAAE,CAAE,CAAEkB,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,cAErDnF,IAAA,CAACH,OAAO,EAACqF,IAAI,CAAC,uBAAuB,CAACF,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAC/D,CACS,CAAC,CACN,CAAC,EACP,CAAC,cAGNjF,IAAA,CAACR,UAAU,EACT4F,OAAO,CAAC,SAAS,CACjBnB,EAAE,CAAE,CACFkB,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,kCAAkC,CAC9CE,QAAQ,CAAE,SAAS,CACnBoB,EAAE,CAAE,CAAC,CACLlC,OAAO,CAAE,OACX,CAAE,CAAAD,QAAA,CACH,mGAED,CAAY,CAAC,EACV,CAAC,CACE,CAAC,EACN,CAAC,CAEZ,CAAC,CAED,cAAe,CAAArE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Box, Typography, Button, Paper, CircularProgress, Container } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import Iconify from 'components/Iconify';
import { successSnackbar } from 'components/Snackbar';
import { mockNotifications, getNotificationConfig } from './mockData';
import styles from './styles.module.scss';

dayjs.extend(relativeTime);

//-------------------------------------------------------------------------------------------------

const Notifications = () => {
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);
  const observerRef = useRef(null);
  const observer = useRef(null);
  const pageSize = 8;
  const totalNotifications = mockNotifications.length;

  const handleObserver = useCallback((entries) => {
    const target = entries[0];
    if (target.isIntersecting && !loadingMore && notifications.length < totalNotifications) {
      setPage((prevPage) => prevPage + 1);
    }
  }, [loadingMore, notifications.length, totalNotifications]);

  const fetchNotifications = useCallback((append = false) => {
    const start = (page - 1) * pageSize;
    const newNotifications = mockNotifications.slice(start, start + pageSize);

    if (append) {
      setNotifications((prevNotifications) => [...prevNotifications, ...newNotifications]);
    } else {
      setNotifications(newNotifications);
    }

    setLoading(false);
    setLoadingMore(false);
  }, [page]);

  useEffect(() => {
    if (observer.current) observer.current.disconnect();
    observer.current = new IntersectionObserver(handleObserver, { threshold: 0.1 });

    if (observerRef.current) {
      observer.current.observe(observerRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [handleObserver]);

  useEffect(() => {
    setLoading(true);
    setPage(1);
    fetchNotifications();
  }, [fetchNotifications]);

  useEffect(() => {
    if (page > 1) {
      setLoadingMore(true);
      fetchNotifications(true);
    }
  }, [page, fetchNotifications]);

  const handleRead = (id) => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification =>
        notification.id === id ? { ...notification, is_read: true } : notification
      )
    );
    successSnackbar('Notification marked as read');
  };

  const handleReadAll = () => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification => ({ ...notification, is_read: true }))
    );
    successSnackbar('All notifications marked as read');
  };

  const handleDelete = (id) => {
    setNotifications(prevNotifications =>
      prevNotifications.filter(notification => notification.id !== id)
    );
    successSnackbar('Notification deleted');
  };

  const handleAction = (actionUrl) => {
    navigate(actionUrl);
  };

  const renderNotificationCard = (notification) => {
    const config = getNotificationConfig(notification.type);

    return (
      <Paper
        key={notification.id}
        className={`${styles.notificationCard} ${notification.is_read ? styles.read : ''}`}
        elevation={notification.is_read ? 0 : 1}
      >
        <Box className={styles.notificationContent}>
          <Box
            className={styles.iconBox}
            sx={{ backgroundColor: `${config.color}15` }}
          >
            <Iconify
              icon={config.icon}
              width={24}
              height={24}
              sx={{ color: config.color }}
            />
          </Box>

          <Box className={styles.messageContent}>
            <Typography className={styles.message}>
              {notification.message}
            </Typography>
            <Typography className={styles.timestamp}>
              {dayjs(notification.created_at).fromNow()}
            </Typography>
          </Box>
        </Box>

        <Box className={styles.actionButtons}>
          {!notification.is_read && (
            <Button
              size="small"
              className={styles.markAsReadButton}
              onClick={() => handleRead(notification.id)}
              startIcon={<Iconify icon="mdi:check" />}
            >
              Mark as read
            </Button>
          )}

          <Button
            size="small"
            className={styles.actionButton}
            onClick={() => handleAction(notification.action_url)}
            sx={{ backgroundColor: config.color }}
            startIcon={<Iconify icon="mdi:arrow-right" />}
          >
            {config.actionText}
          </Button>

          <Button
            size="small"
            variant="outlined"
            className={styles.deleteButton}
            onClick={() => handleDelete(notification.id)}
            startIcon={<Iconify icon="mdi:delete-outline" />}
          >
            Delete
          </Button>
        </Box>
      </Paper>
    );
  };

  const renderContent = () => {
    if (loading) {
      return Array(3).fill(0).map((_, index) => (
        <Paper key={index} className={styles.notificationSkeleton} />
      ));
    }

    if (notifications.length === 0) {
      return (
        <Box className={styles.emptyState}>
          <Iconify
            icon="mdi:bell-off-outline"
            className={styles.emptyIcon}
          />
          <Typography className={styles.emptyText}>
            No notifications to display
          </Typography>
        </Box>
      );
    }

    return (
      <>
        {notifications.map(renderNotificationCard)}
        <Box ref={observerRef} />
        {loadingMore && (
          <Box className={styles.loadingContainer}>
            <CircularProgress size={32} />
          </Box>
        )}
      </>
    );
  };

  return (
    <Container maxWidth="md">
      <Box className={styles.mainPage}>
        <Box className={styles.notificationHeader}>
          <Typography className={styles.title}>
            Notifications
          </Typography>
          {notifications.length > 0 && (
            <Button
              variant="contained"
              onClick={handleReadAll}
              startIcon={<Iconify icon="mdi:check-all" />}
              sx={{
                backgroundColor: '#333',
                '&:hover': { backgroundColor: '#444' }
              }}
            >
              Mark all as read
            </Button>
          )}
        </Box>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {renderContent()}
        </Box>
      </Box>
    </Container>
  );
};

export default Notifications;

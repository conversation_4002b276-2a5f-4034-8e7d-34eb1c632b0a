// Urls
export const APIURL = process.env.REACT_APP_API_URL || "http://127.0.0.1:8000";
// Page types
export const AUTH_PAGE_KEY = "auth";
export const ADMIN_PAGE_KEY = "dashboard";
export const PUBLIC_PAGE_KEY = "public";
// Pixel
export const SIDEBAR_COLLAPSED_LEFT_PX = "80px";
export const SIDEBAR_EXPANED_LEFT_PX = "250px";
// Color
export const primaryColor = "#0F52BA";
export const btnPrimaryColor = "#333333";
export const iconPrimaryColor = "#4B4B4B";
export const mainYellowColor = "#F0A500";
export const mainRedColor = "#D3302F";
export const mainDarkGrayScaleColor = "#D3302F";
export const mainDarkGrayColor = "darkgray";
export const mainBluePupleColor = "#32327F";
export const mainLightGrayColor = "#e8e8e8";
export const primaryFont = 'Recursive Variable';
export const snackbarColor = {
  successColor: '#0F52BA',
  successBgColor: '#DDEBFF',
  successborderColor: '#DDEBFF',
  errorColor: '#C41C1C',
  errorBgColor: 'white',
  errorborderColor: '#F09898',
  warningColor: '#EA9A3E',
  warningBgColor: '#F09898',
};
export const brightColors = [
  '#FF5733',
  '#FF8D1A',
  '#FFC300',
  '#28B463',
  '#1ABC9C',
  '#3498DB',
  '#9B59B6',
  '#E74C3C',
  '#FF69B4',
  '#FF4500',
  '#FF6347',
  '#8A2BE2',
  '#FF1493',
  '#00BFFF',
  '#FFB6C1',
  '#CD5C5C',
];
// Language
export const MAIN_LANGUES_OPTION = [
  { value: 'English', label: 'English' },
  { value: 'Tiếng Việt', label: 'Tiếng Việt' },
  { value: '日本語', label: '日本語' }
]
// Status
export const ACCEPT_STATUS = 1;
export const DECLINE_STATUS = -1;
export const DEFAULTCOMMENTTYPE = 1;
export const DONE_STATUS = 3;
export const TODO_STATUS = 1;

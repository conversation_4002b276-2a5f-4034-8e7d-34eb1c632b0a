{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Container, Box, Tabs, Tab, CircularProgress, Alert } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\nimport { successSnackbar, errorSnackbar } from 'components/Snackbar/index';\nimport { toast } from 'react-toastify';\nimport { getHeaders } from \"helpers/functions\";\n\n// Components\nimport Header from './components/Header';\nimport Description from './components/Description';\nimport Statistics from './components/Statistics';\nimport Progress from './components/Progress';\nimport MilestoneList from './components/MilestoneList';\nimport MilestoneOverview from './components/MilestoneOverview';\nimport AccessManagement from './components/AccessManagement';\nimport ChatbotBar from './components/ChatbotBar';\nimport AgentTab from './components/AgentTab';\n\n// Hooks\nimport usePlanData from './hooks/usePlanData';\nimport useViewMode from './hooks/useViewMode';\n\n// Dialogs\nimport InviteDialog from './dialogs/InviteDialog';\nimport DeleteDialog from './dialogs/DeleteDialog';\nimport OptOutDialog from './dialogs/OptOutDialog';\nimport ConfirmDialog from './dialogs/ConfirmDialog';\n\n// Services\nimport { updateMilestone, updateTask, updateSubtask, addTask, addSubtask, deleteTask, deleteSubtask, assignMembersToTask } from '../services';\n\n// Styles\nimport styles from './styles.module.scss';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PlanDetail = () => {\n  _s();\n  var _planInfo$user_access, _planInfo$user_access2, _planInfo$user_access3;\n  const {\n    param\n  } = useParams();\n  const [activeTab, setActiveTab] = useState(() => {\n    // Get active tab value from localStorage, default to 'overview' if not found\n    return localStorage.getItem(`plan_${param}_activeTab`) || 'overview';\n  });\n  const [plan, setPlan] = useState(null);\n  const [dialogState, setDialogState] = useState({\n    invite: false,\n    delete: false,\n    optOut: false,\n    deleteTask: false,\n    deleteSubtask: false\n  });\n  const [selectedTaskToDelete] = useState(null);\n  const [selectedSubtaskToDelete] = useState(null);\n\n  // Custom hooks\n  const {\n    planInfo,\n    loading,\n    error,\n    handleDeletePlan,\n    handleOptOutPlan,\n    handleInviteUser,\n    calculatePlanStats,\n    calculateSubtaskProgress,\n    getSubtaskStatus,\n    calculateTaskProgress,\n    getTaskStatus,\n    calculateMilestoneProgress,\n    getMilestoneStatus\n  } = usePlanData(param);\n  const {\n    viewMode,\n    handleViewModeChange\n  } = useViewMode();\n\n  // Update plan state when planInfo changes\n  useEffect(() => {\n    if (planInfo) {\n      setPlan(planInfo);\n    }\n  }, [planInfo]);\n\n  // Remove active tab from localStorage when component unmounts\n  useEffect(() => {\n    return () => {\n      localStorage.removeItem(`plan_${param}_activeTab`);\n    };\n  }, [param]);\n\n  // Calculate plan statistics\n  const stats = calculatePlanStats ? calculatePlanStats(planInfo) : null;\n\n  // Dialog handlers\n  const openDialog = dialogName => {\n    setDialogState(prev => ({\n      ...prev,\n      [dialogName]: true\n    }));\n  };\n  const closeDialog = dialogName => {\n    setDialogState(prev => ({\n      ...prev,\n      [dialogName]: false\n    }));\n  };\n\n  // Tab change handler\n  const handleTabChange = (_, newValue) => {\n    setActiveTab(newValue);\n    // Save active tab to localStorage\n    localStorage.setItem(`plan_${param}_activeTab`, newValue);\n  };\n\n  // Handle switching to Agent tab from ChatbotBar\n  const handleSwitchToAgent = conversationData => {\n    console.log('PlanDetail - Switching to agent tab with data:', conversationData); // Debug log\n    setActiveTab('agent');\n    localStorage.setItem(`plan_${param}_activeTab`, 'agent');\n    // Store the conversation data for the Agent tab to pick up\n    localStorage.setItem('pending_agent_message', JSON.stringify(conversationData));\n  };\n\n  // Refresh plan data\n  const refreshPlanData = async () => {\n    try {\n      window.location.reload(); // Simple refresh for now\n    } catch (error) {\n      console.error('Error refreshing plan data:', error);\n    }\n  };\n\n  // Access management handlers\n  const handleAddAccess = async (email, accessLevel) => {\n    try {\n      const response = await fetch(`${APIURL}/api/plans/${param}/access`, {\n        method: 'POST',\n        headers: {\n          ...getHeaders(),\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          email,\n          access_level: accessLevel\n        })\n      });\n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.error || 'Failed to add access');\n      }\n\n      // Refresh plan data\n      await refreshPlanData();\n      toast.success('Access granted successfully');\n    } catch (error) {\n      console.error('Error adding access:', error);\n      toast.error(error.message || 'Failed to add access');\n      throw error;\n    }\n  };\n  const handleUpdateAccess = async function (accessId, accessLevel) {\n    let isHeadOwner = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    try {\n      const response = await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`, {\n        method: 'PUT',\n        headers: {\n          ...getHeaders(),\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          access_level: accessLevel,\n          is_head_owner: isHeadOwner\n        })\n      });\n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.error || 'Failed to update access');\n      }\n\n      // Refresh plan data\n      await refreshPlanData();\n      toast.success('Access updated successfully');\n    } catch (error) {\n      console.error('Error updating access:', error);\n      toast.error(error.message || 'Failed to update access');\n      throw error;\n    }\n  };\n  const handleRemoveAccess = async accessId => {\n    try {\n      const response = await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`, {\n        method: 'DELETE',\n        headers: getHeaders()\n      });\n      if (!response.ok) {\n        const error = await response.json();\n        throw new Error(error.error || 'Failed to remove access');\n      }\n\n      // Refresh plan data\n      await refreshPlanData();\n      toast.success('Access removed successfully');\n    } catch (error) {\n      console.error('Error removing access:', error);\n      toast.error(error.message || 'Failed to remove access');\n      throw error;\n    }\n  };\n\n  // Handle milestone update\n  const handleUpdateMilestone = async updatedMilestone => {\n    try {\n      console.log('Updating milestone:', updatedMilestone);\n      await updateMilestone(updatedMilestone);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === updatedMilestone.id);\n      if (milestoneIndex !== -1) {\n        updatedPlan.milestones[milestoneIndex] = {\n          ...updatedPlan.milestones[milestoneIndex],\n          ...updatedMilestone\n        };\n        setPlan(updatedPlan);\n      }\n      successSnackbar('Milestone updated successfully');\n    } catch (error) {\n      console.error('Error updating milestone:', error);\n      errorSnackbar('Failed to update milestone');\n    }\n  };\n\n  // Handle task update\n  const handleUpdateTask = async updatedTask => {\n    try {\n      console.log('Updating task:', updatedTask);\n      await updateTask(updatedTask);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.tasks && m.tasks.some(t => t.id === updatedTask.id));\n      if (milestoneIndex !== -1) {\n        const taskIndex = updatedPlan.milestones[milestoneIndex].tasks.findIndex(t => t.id === updatedTask.id);\n        if (taskIndex !== -1) {\n          updatedPlan.milestones[milestoneIndex].tasks[taskIndex] = {\n            ...updatedPlan.milestones[milestoneIndex].tasks[taskIndex],\n            ...updatedTask\n          };\n          setPlan(updatedPlan);\n        }\n      }\n      successSnackbar('Task updated successfully');\n    } catch (error) {\n      console.error('Error updating task:', error);\n      errorSnackbar('Failed to update task');\n    }\n  };\n\n  // Handle subtask update\n  const handleUpdateSubtask = async updatedSubtask => {\n    try {\n      console.log('Updating subtask:', updatedSubtask);\n\n      // Call API to update subtask\n      await updateSubtask(updatedSubtask);\n\n      // Create a copy of current plan to update\n      const updatedPlan = {\n        ...plan\n      };\n\n      // Find the task containing the subtask\n      let taskFound = false;\n\n      // Update subtask in state\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\n        const milestone = updatedPlan.milestones[i];\n        if (!milestone.tasks) continue;\n        for (let j = 0; j < milestone.tasks.length; j++) {\n          const task = milestone.tasks[j];\n          if (!task.subtasks) continue;\n\n          // Update subtask in task\n          const subtaskIndex = task.subtasks.findIndex(s => s.id === updatedSubtask.id);\n          if (subtaskIndex !== -1) {\n            // Update subtask\n            task.subtasks[subtaskIndex] = {\n              ...task.subtasks[subtaskIndex],\n              ...updatedSubtask\n            };\n            taskFound = true;\n            break;\n          }\n        }\n        if (taskFound) break;\n      }\n\n      // Update state with new plan\n      setPlan(updatedPlan);\n      successSnackbar('Subtask updated successfully');\n    } catch (error) {\n      console.error('Error updating subtask:', error);\n      errorSnackbar('Failed to update subtask');\n    }\n  };\n\n  // Handle add task\n  const handleAddTask = async newTask => {\n    try {\n      console.log('Adding new task:', newTask);\n      const response = await addTask(newTask);\n      console.log('Add task response:', response);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n\n      // Find milestone to add new task\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === newTask.milestone);\n      if (milestoneIndex !== -1) {\n        // Add new task to milestone\n        if (!updatedPlan.milestones[milestoneIndex].tasks) {\n          updatedPlan.milestones[milestoneIndex].tasks = [];\n        }\n\n        // Add the new task with data from response\n        const taskToAdd = response.data || {\n          ...newTask,\n          id: Date.now(),\n          // Temporary ID if response doesn't provide one\n          subtasks: []\n        };\n        updatedPlan.milestones[milestoneIndex].tasks.push(taskToAdd);\n        setPlan(updatedPlan);\n      }\n      successSnackbar('Task added successfully');\n    } catch (error) {\n      console.error('Error adding task:', error);\n      errorSnackbar('Failed to add task');\n    }\n  };\n\n  // Handle add subtask\n  const handleAddSubtask = async newSubtask => {\n    try {\n      console.log('Adding new subtask:', newSubtask);\n      const response = await addSubtask(newSubtask);\n      console.log('Add subtask response:', response);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n\n      // Find task to add new subtask\n      let taskFound = false;\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\n        const milestone = updatedPlan.milestones[i];\n        if (!milestone.tasks) continue;\n        for (let j = 0; j < milestone.tasks.length; j++) {\n          const task = milestone.tasks[j];\n          if (task.slug === newSubtask.task) {\n            // Add new subtask to task\n            if (!task.subtasks) {\n              task.subtasks = [];\n            }\n\n            // Add the new subtask with data from response\n            const subtaskToAdd = response.data || {\n              ...newSubtask,\n              id: Date.now() // Temporary ID if response doesn't provide one\n            };\n            task.subtasks.push(subtaskToAdd);\n            taskFound = true;\n            break;\n          }\n        }\n        if (taskFound) break;\n      }\n      setPlan(updatedPlan);\n      successSnackbar('Subtask added successfully');\n    } catch (error) {\n      console.error('Error adding subtask:', error);\n      errorSnackbar('Failed to add subtask');\n    }\n  };\n\n  // Handle delete task\n  const handleDeleteTask = async taskToDelete => {\n    try {\n      console.log('Deleting task:', taskToDelete);\n      await deleteTask(taskToDelete.slug);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n\n      // Find milestone containing the task to delete\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.tasks && m.tasks.some(t => t.id === taskToDelete.id));\n      if (milestoneIndex !== -1) {\n        // Filter out the task to delete\n        updatedPlan.milestones[milestoneIndex].tasks = updatedPlan.milestones[milestoneIndex].tasks.filter(t => t.id !== taskToDelete.id);\n        setPlan(updatedPlan);\n      }\n      successSnackbar('Task deleted successfully');\n    } catch (error) {\n      console.error('Error deleting task:', error);\n      errorSnackbar('Failed to delete task');\n    }\n  };\n\n  // Handle delete subtask\n  const handleDeleteSubtask = async subtaskToDelete => {\n    try {\n      console.log('Deleting subtask:', subtaskToDelete);\n      await deleteSubtask(subtaskToDelete.slug);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n\n      // Find task containing the subtask to delete\n      let taskFound = false;\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\n        const milestone = updatedPlan.milestones[i];\n        if (!milestone.tasks) continue;\n        for (let j = 0; j < milestone.tasks.length; j++) {\n          const task = milestone.tasks[j];\n          if (!task.subtasks) continue;\n\n          // Filter out the subtask to delete\n          const originalLength = task.subtasks.length;\n          task.subtasks = task.subtasks.filter(s => s.id !== subtaskToDelete.id);\n          if (task.subtasks.length < originalLength) {\n            taskFound = true;\n            break;\n          }\n        }\n        if (taskFound) break;\n      }\n      setPlan(updatedPlan);\n      toast.success('Subtask deleted successfully');\n    } catch (error) {\n      console.error('Error deleting subtask:', error);\n      toast.error('Failed to delete subtask');\n    }\n  };\n\n  // Handle assign members to task\n  const handleAssignMembers = async (taskToAssign, memberIds) => {\n    try {\n      console.log('Assigning members to task:', taskToAssign, memberIds);\n      await assignMembersToTask(taskToAssign.slug, memberIds);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n\n      // Find the task to update\n      let taskFound = false;\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\n        const milestone = updatedPlan.milestones[i];\n        if (!milestone.tasks) continue;\n        for (let j = 0; j < milestone.tasks.length; j++) {\n          const task = milestone.tasks[j];\n          if (task.id === taskToAssign.id) {\n            // Update assignees\n            // Find user objects for the selected member IDs\n            const assignedMembers = memberIds.map(memberId => {\n              var _planInfo$invited_use;\n              // Check if it's the plan owner\n              if (planInfo.owner && planInfo.owner.id === memberId) {\n                return {\n                  id: planInfo.owner.id,\n                  first_name: planInfo.owner.first_name,\n                  last_name: planInfo.owner.last_name,\n                  email: planInfo.owner.email,\n                  avatar: planInfo.owner.avatar\n                };\n              }\n\n              // Check in invited users\n              const invitedUser = (_planInfo$invited_use = planInfo.invited_users) === null || _planInfo$invited_use === void 0 ? void 0 : _planInfo$invited_use.find(user => user.invited_user_info && user.invited_user_info.id === memberId);\n              if (invitedUser) {\n                return {\n                  id: invitedUser.invited_user_info.id,\n                  first_name: invitedUser.invited_user_info.first_name,\n                  last_name: invitedUser.invited_user_info.last_name,\n                  email: invitedUser.email,\n                  avatar: invitedUser.invited_user_info.avatar\n                };\n              }\n              return null;\n            }).filter(member => member !== null);\n            task.assignees = assignedMembers;\n            taskFound = true;\n            break;\n          }\n        }\n        if (taskFound) break;\n      }\n      setPlan(updatedPlan);\n      toast.success('Members assigned successfully');\n    } catch (error) {\n      console.error('Error assigning members to task:', error);\n      toast.error('Failed to assign members to task');\n    }\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: styles.container,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mt: 4\n        },\n        children: \"An error occurred while loading plan information. Please try again later.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    className: styles.container,\n    sx: {\n      padding: '20px',\n      minHeight: 'calc(100vh - 65px)',\n      fontFamily: '\"Recursive Variable\", sans-serif'\n    },\n    children: [loading ? /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.loadingContainer,\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60,\n        sx: {\n          color: mainYellowColor\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        planInfo: planInfo,\n        viewMode: viewMode,\n        onViewModeChange: handleViewModeChange,\n        onOpenInviteDialog: () => openDialog('invite'),\n        onOpenDeleteDialog: () => openDialog('delete'),\n        onOpenOptOutDialog: () => openDialog('optOut')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider',\n          mb: 0.5,\n          mt: -1\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: handleTabChange,\n          variant: \"scrollable\",\n          scrollButtons: \"auto\",\n          sx: {\n            minHeight: '36px',\n            '& .MuiTab-root': {\n              textTransform: 'none',\n              fontWeight: 600,\n              fontSize: '0.9rem',\n              minWidth: 'auto',\n              minHeight: '36px',\n              px: 2,\n              py: 0.5,\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            },\n            '& .Mui-selected': {\n              color: `${mainYellowColor} !important`\n            },\n            '& .MuiTabs-indicator': {\n              backgroundColor: mainYellowColor,\n              height: '2px'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:dashboard\",\n              width: 16,\n              height: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 23\n            }, this),\n            iconPosition: \"start\",\n            label: \"Overview\",\n            value: \"overview\",\n            sx: {\n              gap: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:view-list\",\n              width: 16,\n              height: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 23\n            }, this),\n            iconPosition: \"start\",\n            label: \"Project Details\",\n            value: \"milestones\",\n            sx: {\n              gap: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"mdi:robot\",\n              width: 16,\n              height: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 23\n            }, this),\n            iconPosition: \"start\",\n            label: \"Agent\",\n            value: \"agent\",\n            sx: {\n              gap: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 15\n          }, this), (planInfo === null || planInfo === void 0 ? void 0 : (_planInfo$user_access = planInfo.user_access_level) === null || _planInfo$user_access === void 0 ? void 0 : _planInfo$user_access.access_level) === 'owner' && /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:security\",\n              width: 16,\n              height: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 25\n            }, this),\n            iconPosition: \"start\",\n            label: \"Access\",\n            value: \"access\",\n            sx: {\n              gap: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.tabContent,\n        children: [activeTab === 'overview' && /*#__PURE__*/_jsxDEV(Box, {\n          className: styles.overviewTab,\n          sx: {\n            gap: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(Description, {\n            planInfo: planInfo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexDirection: {\n                xs: 'column',\n                md: 'row'\n              },\n              gap: 1,\n              mb: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(Statistics, {\n              stats: stats\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              stats: stats\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 17\n          }, this), (planInfo === null || planInfo === void 0 ? void 0 : (_planInfo$user_access2 = planInfo.user_access_level) === null || _planInfo$user_access2 === void 0 ? void 0 : _planInfo$user_access2.access_level) === 'owner' && /*#__PURE__*/_jsxDEV(AccessManagement, {\n            planInfo: planInfo,\n            userAccessLevel: planInfo === null || planInfo === void 0 ? void 0 : planInfo.user_access_level,\n            onAddAccess: handleAddAccess,\n            onUpdateAccess: handleUpdateAccess,\n            onRemoveAccess: handleRemoveAccess\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(MilestoneOverview, {\n            milestones: planInfo === null || planInfo === void 0 ? void 0 : planInfo.milestones,\n            calculateMilestoneProgress: calculateMilestoneProgress,\n            getMilestoneStatus: getMilestoneStatus,\n            calculateTaskProgress: calculateTaskProgress,\n            getTaskStatus: getTaskStatus,\n            calculateSubtaskProgress: calculateSubtaskProgress,\n            getSubtaskStatus: getSubtaskStatus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 15\n        }, this), activeTab === 'milestones' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(ChatbotBar, {\n            planInfo: planInfo,\n            onPlanUpdate: updatedPlan => {\n              // Handle plan updates from AI agent\n              console.log('Plan updated by AI:', updatedPlan);\n            },\n            onSwitchToAgent: handleSwitchToAgent,\n            sx: {\n              marginBottom: '20px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(MilestoneList, {\n            milestones: planInfo === null || planInfo === void 0 ? void 0 : planInfo.milestones,\n            viewMode: viewMode,\n            compact: false,\n            showSubtasks: true,\n            calculateMilestoneProgress: calculateMilestoneProgress,\n            getMilestoneStatus: getMilestoneStatus,\n            calculateTaskProgress: calculateTaskProgress,\n            getTaskStatus: getTaskStatus,\n            calculateSubtaskProgress: calculateSubtaskProgress,\n            getSubtaskStatus: getSubtaskStatus,\n            onUpdateMilestone: handleUpdateMilestone,\n            onUpdateTask: handleUpdateTask,\n            onUpdateSubtask: handleUpdateSubtask,\n            onAddTask: handleAddTask,\n            onAddSubtask: handleAddSubtask,\n            onDeleteTask: handleDeleteTask,\n            onDeleteSubtask: handleDeleteSubtask,\n            onAssignMembers: handleAssignMembers,\n            invitedUsers: (planInfo === null || planInfo === void 0 ? void 0 : planInfo.invited_users) || [],\n            planOwner: planInfo === null || planInfo === void 0 ? void 0 : planInfo.owner\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), activeTab === 'agent' && /*#__PURE__*/_jsxDEV(AgentTab, {\n          planInfo: planInfo,\n          onPlanUpdate: () => {\n            // Refresh plan data when AI agent makes changes\n            console.log('Plan updated by AI agent, refreshing data...');\n            refreshPlanData();\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 15\n        }, this), activeTab === 'access' && (planInfo === null || planInfo === void 0 ? void 0 : (_planInfo$user_access3 = planInfo.user_access_level) === null || _planInfo$user_access3 === void 0 ? void 0 : _planInfo$user_access3.access_level) === 'owner' && /*#__PURE__*/_jsxDEV(AccessManagement, {\n          planInfo: planInfo,\n          userAccessLevel: planInfo === null || planInfo === void 0 ? void 0 : planInfo.user_access_level,\n          onAddAccess: handleAddAccess,\n          onUpdateAccess: handleUpdateAccess,\n          onRemoveAccess: handleRemoveAccess\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(InviteDialog, {\n      open: dialogState.invite,\n      onClose: () => closeDialog('invite'),\n      onInvite: handleInviteUser,\n      planInfo: planInfo\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DeleteDialog, {\n      open: dialogState.delete,\n      onClose: () => closeDialog('delete'),\n      onDelete: handleDeletePlan\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 739,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(OptOutDialog, {\n      open: dialogState.optOut,\n      onClose: () => closeDialog('optOut'),\n      onOptOut: handleOptOutPlan\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 745,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmDialog, {\n      open: dialogState.deleteTask,\n      onClose: () => setDialogState(prev => ({\n        ...prev,\n        deleteTask: false\n      })),\n      onConfirm: () => handleDeleteTask(selectedTaskToDelete),\n      title: \"Delete Task\",\n      description: `Are you sure you want to delete the task \"${selectedTaskToDelete === null || selectedTaskToDelete === void 0 ? void 0 : selectedTaskToDelete.name}\"? This action cannot be undone.`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 751,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmDialog, {\n      open: dialogState.deleteSubtask,\n      onClose: () => setDialogState(prev => ({\n        ...prev,\n        deleteSubtask: false\n      })),\n      onConfirm: () => handleDeleteSubtask(selectedSubtaskToDelete),\n      title: \"Delete Subtask\",\n      description: `Are you sure you want to delete the subtask \"${selectedSubtaskToDelete === null || selectedSubtaskToDelete === void 0 ? void 0 : selectedSubtaskToDelete.name}\"? This action cannot be undone.`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 759,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 555,\n    columnNumber: 5\n  }, this);\n};\n_s(PlanDetail, \"jrOyk3XWIWdt8pXb9olTAlsij6k=\", false, function () {\n  return [useParams, usePlanData, useViewMode];\n});\n_c = PlanDetail;\nexport default PlanDetail;\nvar _c;\n$RefreshReg$(_c, \"PlanDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Container", "Box", "Tabs", "Tab", "CircularProgress", "<PERSON><PERSON>", "Iconify", "mainYellowColor", "APIURL", "successSnackbar", "errorSnackbar", "toast", "getHeaders", "Header", "Description", "Statistics", "Progress", "MilestoneList", "MilestoneOverview", "AccessManagement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AgentTab", "usePlanData", "useViewMode", "InviteDialog", "DeleteDialog", "OptOutDialog", "ConfirmDialog", "updateMilestone", "updateTask", "updateSubtask", "addTask", "addSubtask", "deleteTask", "deleteSubtask", "assignMembersToTask", "styles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PlanDetail", "_s", "_planInfo$user_access", "_planInfo$user_access2", "_planInfo$user_access3", "param", "activeTab", "setActiveTab", "localStorage", "getItem", "plan", "setPlan", "dialogState", "setDialogState", "invite", "delete", "optOut", "selectedTaskToDelete", "selectedSubtaskToDelete", "planInfo", "loading", "error", "handleDeletePlan", "handleOptOutPlan", "handleInviteUser", "calculatePlanStats", "calculateSubtaskProgress", "getSubtaskStatus", "calculateTaskProgress", "getTaskStatus", "calculateMilestoneProgress", "getMilestoneStatus", "viewMode", "handleViewModeChange", "removeItem", "stats", "openDialog", "dialogName", "prev", "closeDialog", "handleTabChange", "_", "newValue", "setItem", "handleSwitchToAgent", "conversationData", "console", "log", "JSON", "stringify", "refreshPlanData", "window", "location", "reload", "handleAddAccess", "email", "accessLevel", "response", "fetch", "method", "headers", "body", "access_level", "ok", "json", "Error", "success", "message", "handleUpdateAccess", "accessId", "isHeadOwner", "arguments", "length", "undefined", "is_head_owner", "handleRemoveAccess", "handleUpdateMilestone", "updatedMilestone", "updatedPlan", "milestoneIndex", "milestones", "findIndex", "m", "id", "handleUpdateTask", "updatedTask", "tasks", "some", "t", "taskIndex", "handleUpdateSubtask", "updatedSubtask", "taskFound", "i", "milestone", "j", "task", "subtasks", "subtaskIndex", "s", "handleAddTask", "newTask", "taskToAdd", "data", "Date", "now", "push", "handleAddSubtask", "newSubtask", "slug", "subtaskToAdd", "handleDeleteTask", "taskToDelete", "filter", "handleDeleteSubtask", "subtaskToDelete", "original<PERSON>ength", "handleAssignMembers", "taskToAssign", "memberIds", "assignedMembers", "map", "memberId", "_planInfo$invited_use", "owner", "first_name", "last_name", "avatar", "invitedUser", "invited_users", "find", "user", "invited_user_info", "member", "assignees", "className", "container", "children", "severity", "sx", "mt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "padding", "minHeight", "fontFamily", "loadingContainer", "size", "color", "onViewModeChange", "onOpenInviteDialog", "onOpenDeleteDialog", "onOpenOptOutDialog", "borderBottom", "borderColor", "mb", "value", "onChange", "variant", "scrollButtons", "textTransform", "fontWeight", "fontSize", "min<PERSON><PERSON><PERSON>", "px", "py", "backgroundColor", "height", "icon", "width", "iconPosition", "label", "gap", "user_access_level", "tab<PERSON>ontent", "overviewTab", "display", "flexDirection", "xs", "md", "userAccessLevel", "onAddAccess", "onUpdateAccess", "onRemoveAccess", "onPlanUpdate", "onSwitchToAgent", "marginBottom", "compact", "showSubtasks", "onUpdateMilestone", "onUpdateTask", "onUpdateSubtask", "onAddTask", "onAddSubtask", "onDeleteTask", "onDeleteSubtask", "onAssignMembers", "invitedUsers", "<PERSON><PERSON><PERSON><PERSON>", "open", "onClose", "onInvite", "onDelete", "onOptOut", "onConfirm", "title", "description", "name", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useParams } from 'react-router-dom';\r\nimport { Container, Box, Tabs, Tab, CircularProgress, Alert } from '@mui/material';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\r\nimport { successSnackbar, errorSnackbar } from 'components/Snackbar/index';\r\nimport { toast } from 'react-toastify';\r\nimport { getHeaders } from \"helpers/functions\";\r\n\r\n// Components\r\nimport Header from './components/Header';\r\nimport Description from './components/Description';\r\nimport Statistics from './components/Statistics';\r\nimport Progress from './components/Progress';\r\nimport MilestoneList from './components/MilestoneList';\r\nimport MilestoneOverview from './components/MilestoneOverview';\r\nimport AccessManagement from './components/AccessManagement';\r\nimport ChatbotBar from './components/ChatbotBar';\r\nimport AgentTab from './components/AgentTab';\r\n\r\n// Hooks\r\nimport usePlanData from './hooks/usePlanData';\r\nimport useViewMode from './hooks/useViewMode';\r\n\r\n// Dialogs\r\nimport InviteDialog from './dialogs/InviteDialog';\r\nimport DeleteDialog from './dialogs/DeleteDialog';\r\nimport OptOutDialog from './dialogs/OptOutDialog';\r\nimport ConfirmDialog from './dialogs/ConfirmDialog';\r\n\r\n// Services\r\nimport {\r\n  updateMilestone,\r\n  updateTask,\r\n  updateSubtask,\r\n  addTask,\r\n  addSubtask,\r\n  deleteTask,\r\n  deleteSubtask,\r\n  assignMembersToTask\r\n} from '../services';\r\n\r\n// Styles\r\nimport styles from './styles.module.scss';\r\n\r\nconst PlanDetail = () => {\r\n  const { param } = useParams();\r\n  const [activeTab, setActiveTab] = useState(() => {\r\n    // Get active tab value from localStorage, default to 'overview' if not found\r\n    return localStorage.getItem(`plan_${param}_activeTab`) || 'overview';\r\n  });\r\n  const [plan, setPlan] = useState(null);\r\n  const [dialogState, setDialogState] = useState({\r\n    invite: false,\r\n    delete: false,\r\n    optOut: false,\r\n    deleteTask: false,\r\n    deleteSubtask: false\r\n  });\r\n  const [selectedTaskToDelete, ] = useState(null);\r\n  const [selectedSubtaskToDelete, ] = useState(null);\r\n\r\n  // Custom hooks\r\n  const {\r\n    planInfo,\r\n    loading,\r\n    error,\r\n    handleDeletePlan,\r\n    handleOptOutPlan,\r\n    handleInviteUser,\r\n    calculatePlanStats,\r\n    calculateSubtaskProgress,\r\n    getSubtaskStatus,\r\n    calculateTaskProgress,\r\n    getTaskStatus,\r\n    calculateMilestoneProgress,\r\n    getMilestoneStatus\r\n  } = usePlanData(param);\r\n\r\n  const {\r\n    viewMode,\r\n    handleViewModeChange\r\n  } = useViewMode();\r\n\r\n  // Update plan state when planInfo changes\r\n  useEffect(() => {\r\n    if (planInfo) {\r\n      setPlan(planInfo);\r\n    }\r\n  }, [planInfo]);\r\n\r\n  // Remove active tab from localStorage when component unmounts\r\n  useEffect(() => {\r\n    return () => {\r\n      localStorage.removeItem(`plan_${param}_activeTab`);\r\n    };\r\n  }, [param]);\r\n\r\n  // Calculate plan statistics\r\n  const stats = calculatePlanStats ? calculatePlanStats(planInfo) : null;\r\n\r\n  // Dialog handlers\r\n  const openDialog = (dialogName) => {\r\n    setDialogState(prev => ({ ...prev, [dialogName]: true }));\r\n  };\r\n\r\n  const closeDialog = (dialogName) => {\r\n    setDialogState(prev => ({ ...prev, [dialogName]: false }));\r\n  };\r\n\r\n  // Tab change handler\r\n  const handleTabChange = (_, newValue) => {\r\n    setActiveTab(newValue);\r\n    // Save active tab to localStorage\r\n    localStorage.setItem(`plan_${param}_activeTab`, newValue);\r\n  };\r\n\r\n  // Handle switching to Agent tab from ChatbotBar\r\n  const handleSwitchToAgent = (conversationData) => {\r\n    console.log('PlanDetail - Switching to agent tab with data:', conversationData); // Debug log\r\n    setActiveTab('agent');\r\n    localStorage.setItem(`plan_${param}_activeTab`, 'agent');\r\n    // Store the conversation data for the Agent tab to pick up\r\n    localStorage.setItem('pending_agent_message', JSON.stringify(conversationData));\r\n  };\r\n\r\n  // Refresh plan data\r\n  const refreshPlanData = async () => {\r\n    try {\r\n      window.location.reload(); // Simple refresh for now\r\n    } catch (error) {\r\n      console.error('Error refreshing plan data:', error);\r\n    }\r\n  };\r\n\r\n  // Access management handlers\r\n  const handleAddAccess = async (email, accessLevel) => {\r\n    try {\r\n      const response = await fetch(`${APIURL}/api/plans/${param}/access`, {\r\n        method: 'POST',\r\n        headers: {\r\n          ...getHeaders(),\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify({ email, access_level: accessLevel })\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const error = await response.json();\r\n        throw new Error(error.error || 'Failed to add access');\r\n      }\r\n\r\n      // Refresh plan data\r\n      await refreshPlanData();\r\n      toast.success('Access granted successfully');\r\n    } catch (error) {\r\n      console.error('Error adding access:', error);\r\n      toast.error(error.message || 'Failed to add access');\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const handleUpdateAccess = async (accessId, accessLevel, isHeadOwner = false) => {\r\n    try {\r\n      const response = await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          ...getHeaders(),\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify({\r\n          access_level: accessLevel,\r\n          is_head_owner: isHeadOwner\r\n        })\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const error = await response.json();\r\n        throw new Error(error.error || 'Failed to update access');\r\n      }\r\n\r\n      // Refresh plan data\r\n      await refreshPlanData();\r\n      toast.success('Access updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating access:', error);\r\n      toast.error(error.message || 'Failed to update access');\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const handleRemoveAccess = async (accessId) => {\r\n    try {\r\n      const response = await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`, {\r\n        method: 'DELETE',\r\n        headers: getHeaders()\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const error = await response.json();\r\n        throw new Error(error.error || 'Failed to remove access');\r\n      }\r\n\r\n      // Refresh plan data\r\n      await refreshPlanData();\r\n      toast.success('Access removed successfully');\r\n    } catch (error) {\r\n      console.error('Error removing access:', error);\r\n      toast.error(error.message || 'Failed to remove access');\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Handle milestone update\r\n  const handleUpdateMilestone = async (updatedMilestone) => {\r\n    try {\r\n      console.log('Updating milestone:', updatedMilestone);\r\n      await updateMilestone(updatedMilestone);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === updatedMilestone.id);\r\n\r\n      if (milestoneIndex !== -1) {\r\n        updatedPlan.milestones[milestoneIndex] = {\r\n          ...updatedPlan.milestones[milestoneIndex],\r\n          ...updatedMilestone\r\n        };\r\n        setPlan(updatedPlan);\r\n      }\r\n\r\n      successSnackbar('Milestone updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating milestone:', error);\r\n      errorSnackbar('Failed to update milestone');\r\n    }\r\n  };\r\n\r\n  // Handle task update\r\n  const handleUpdateTask = async (updatedTask) => {\r\n    try {\r\n      console.log('Updating task:', updatedTask);\r\n      await updateTask(updatedTask);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m =>\r\n        m.tasks && m.tasks.some(t => t.id === updatedTask.id)\r\n      );\r\n\r\n      if (milestoneIndex !== -1) {\r\n        const taskIndex = updatedPlan.milestones[milestoneIndex].tasks.findIndex(t => t.id === updatedTask.id);\r\n\r\n        if (taskIndex !== -1) {\r\n          updatedPlan.milestones[milestoneIndex].tasks[taskIndex] = {\r\n            ...updatedPlan.milestones[milestoneIndex].tasks[taskIndex],\r\n            ...updatedTask\r\n          };\r\n          setPlan(updatedPlan);\r\n        }\r\n      }\r\n\r\n      successSnackbar('Task updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating task:', error);\r\n      errorSnackbar('Failed to update task');\r\n    }\r\n  };\r\n\r\n  // Handle subtask update\r\n  const handleUpdateSubtask = async (updatedSubtask) => {\r\n    try {\r\n      console.log('Updating subtask:', updatedSubtask);\r\n\r\n      // Call API to update subtask\r\n      await updateSubtask(updatedSubtask);\r\n\r\n      // Create a copy of current plan to update\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find the task containing the subtask\r\n      let taskFound = false;\r\n\r\n      // Update subtask in state\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n          if (!task.subtasks) continue;\r\n\r\n          // Update subtask in task\r\n          const subtaskIndex = task.subtasks.findIndex(s => s.id === updatedSubtask.id);\r\n          if (subtaskIndex !== -1) {\r\n            // Update subtask\r\n            task.subtasks[subtaskIndex] = {\r\n              ...task.subtasks[subtaskIndex],\r\n              ...updatedSubtask\r\n            };\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      // Update state with new plan\r\n      setPlan(updatedPlan);\r\n\r\n      successSnackbar('Subtask updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating subtask:', error);\r\n      errorSnackbar('Failed to update subtask');\r\n    }\r\n  };\r\n\r\n  // Handle add task\r\n  const handleAddTask = async (newTask) => {\r\n    try {\r\n      console.log('Adding new task:', newTask);\r\n      const response = await addTask(newTask);\r\n      console.log('Add task response:', response);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find milestone to add new task\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === newTask.milestone);\r\n\r\n      if (milestoneIndex !== -1) {\r\n        // Add new task to milestone\r\n        if (!updatedPlan.milestones[milestoneIndex].tasks) {\r\n          updatedPlan.milestones[milestoneIndex].tasks = [];\r\n        }\r\n\r\n        // Add the new task with data from response\r\n        const taskToAdd = response.data || {\r\n          ...newTask,\r\n          id: Date.now(), // Temporary ID if response doesn't provide one\r\n          subtasks: []\r\n        };\r\n\r\n        updatedPlan.milestones[milestoneIndex].tasks.push(taskToAdd);\r\n        setPlan(updatedPlan);\r\n      }\r\n\r\n      successSnackbar('Task added successfully');\r\n    } catch (error) {\r\n      console.error('Error adding task:', error);\r\n      errorSnackbar('Failed to add task');\r\n    }\r\n  };\r\n\r\n  // Handle add subtask\r\n  const handleAddSubtask = async (newSubtask) => {\r\n    try {\r\n      console.log('Adding new subtask:', newSubtask);\r\n      const response = await addSubtask(newSubtask);\r\n      console.log('Add subtask response:', response);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find task to add new subtask\r\n      let taskFound = false;\r\n\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n\r\n          if (task.slug === newSubtask.task) {\r\n            // Add new subtask to task\r\n            if (!task.subtasks) {\r\n              task.subtasks = [];\r\n            }\r\n\r\n            // Add the new subtask with data from response\r\n            const subtaskToAdd = response.data || {\r\n              ...newSubtask,\r\n              id: Date.now() // Temporary ID if response doesn't provide one\r\n            };\r\n\r\n            task.subtasks.push(subtaskToAdd);\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      setPlan(updatedPlan);\r\n      successSnackbar('Subtask added successfully');\r\n    } catch (error) {\r\n      console.error('Error adding subtask:', error);\r\n      errorSnackbar('Failed to add subtask');\r\n    }\r\n  };\r\n\r\n  // Handle delete task\r\n  const handleDeleteTask = async (taskToDelete) => {\r\n    try {\r\n      console.log('Deleting task:', taskToDelete);\r\n      await deleteTask(taskToDelete.slug);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find milestone containing the task to delete\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m =>\r\n        m.tasks && m.tasks.some(t => t.id === taskToDelete.id)\r\n      );\r\n\r\n      if (milestoneIndex !== -1) {\r\n        // Filter out the task to delete\r\n        updatedPlan.milestones[milestoneIndex].tasks = updatedPlan.milestones[milestoneIndex].tasks.filter(\r\n          t => t.id !== taskToDelete.id\r\n        );\r\n\r\n        setPlan(updatedPlan);\r\n      }\r\n\r\n      successSnackbar('Task deleted successfully');\r\n    } catch (error) {\r\n      console.error('Error deleting task:', error);\r\n      errorSnackbar('Failed to delete task');\r\n    }\r\n  };\r\n\r\n  // Handle delete subtask\r\n  const handleDeleteSubtask = async (subtaskToDelete) => {\r\n    try {\r\n      console.log('Deleting subtask:', subtaskToDelete);\r\n      await deleteSubtask(subtaskToDelete.slug);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find task containing the subtask to delete\r\n      let taskFound = false;\r\n\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n          if (!task.subtasks) continue;\r\n\r\n          // Filter out the subtask to delete\r\n          const originalLength = task.subtasks.length;\r\n          task.subtasks = task.subtasks.filter(s => s.id !== subtaskToDelete.id);\r\n\r\n          if (task.subtasks.length < originalLength) {\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      setPlan(updatedPlan);\r\n      toast.success('Subtask deleted successfully');\r\n    } catch (error) {\r\n      console.error('Error deleting subtask:', error);\r\n      toast.error('Failed to delete subtask');\r\n    }\r\n  };\r\n\r\n  // Handle assign members to task\r\n  const handleAssignMembers = async (taskToAssign, memberIds) => {\r\n    try {\r\n      console.log('Assigning members to task:', taskToAssign, memberIds);\r\n      await assignMembersToTask(taskToAssign.slug, memberIds);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find the task to update\r\n      let taskFound = false;\r\n\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n          if (task.id === taskToAssign.id) {\r\n            // Update assignees\r\n            // Find user objects for the selected member IDs\r\n            const assignedMembers = memberIds.map(memberId => {\r\n              // Check if it's the plan owner\r\n              if (planInfo.owner && planInfo.owner.id === memberId) {\r\n                return {\r\n                  id: planInfo.owner.id,\r\n                  first_name: planInfo.owner.first_name,\r\n                  last_name: planInfo.owner.last_name,\r\n                  email: planInfo.owner.email,\r\n                  avatar: planInfo.owner.avatar\r\n                };\r\n              }\r\n\r\n              // Check in invited users\r\n              const invitedUser = planInfo.invited_users?.find(\r\n                user => user.invited_user_info && user.invited_user_info.id === memberId\r\n              );\r\n\r\n              if (invitedUser) {\r\n                return {\r\n                  id: invitedUser.invited_user_info.id,\r\n                  first_name: invitedUser.invited_user_info.first_name,\r\n                  last_name: invitedUser.invited_user_info.last_name,\r\n                  email: invitedUser.email,\r\n                  avatar: invitedUser.invited_user_info.avatar\r\n                };\r\n              }\r\n\r\n              return null;\r\n            }).filter(member => member !== null);\r\n\r\n            task.assignees = assignedMembers;\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      setPlan(updatedPlan);\r\n      toast.success('Members assigned successfully');\r\n    } catch (error) {\r\n      console.error('Error assigning members to task:', error);\r\n      toast.error('Failed to assign members to task');\r\n    }\r\n  };\r\n\r\n  if (error) {\r\n    return (\r\n      <Container className={styles.container}>\r\n        <Alert severity=\"error\" sx={{ mt: 4 }}>\r\n          An error occurred while loading plan information. Please try again later.\r\n        </Alert>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Container\r\n      maxWidth=\"lg\"\r\n      className={styles.container}\r\n      sx={{\r\n        padding: '20px',\r\n        minHeight: 'calc(100vh - 65px)',\r\n        fontFamily: '\"Recursive Variable\", sans-serif'\r\n      }}\r\n    >\r\n      {loading ? (\r\n        <Box className={styles.loadingContainer}>\r\n          <CircularProgress size={60} sx={{ color: mainYellowColor }} />\r\n        </Box>\r\n      ) : (\r\n        <>\r\n          {/* Header Section */}\r\n          <Header\r\n            planInfo={planInfo}\r\n            viewMode={viewMode}\r\n            onViewModeChange={handleViewModeChange}\r\n            onOpenInviteDialog={() => openDialog('invite')}\r\n            onOpenDeleteDialog={() => openDialog('delete')}\r\n            onOpenOptOutDialog={() => openDialog('optOut')}\r\n          />\r\n\r\n          {/* Tabs Navigation */}\r\n          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 0.5, mt: -1 }}>\r\n            <Tabs\r\n              value={activeTab}\r\n              onChange={handleTabChange}\r\n              variant=\"scrollable\"\r\n              scrollButtons=\"auto\"\r\n              sx={{\r\n                minHeight: '36px',\r\n                '& .MuiTab-root': {\r\n                  textTransform: 'none',\r\n                  fontWeight: 600,\r\n                  fontSize: '0.9rem',\r\n                  minWidth: 'auto',\r\n                  minHeight: '36px',\r\n                  px: 2,\r\n                  py: 0.5,\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                },\r\n                '& .Mui-selected': {\r\n                  color: `${mainYellowColor} !important`,\r\n                },\r\n                '& .MuiTabs-indicator': {\r\n                  backgroundColor: mainYellowColor,\r\n                  height: '2px'\r\n                }\r\n              }}\r\n            >\r\n              <Tab\r\n                icon={<Iconify icon=\"material-symbols:dashboard\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Overview\"\r\n                value=\"overview\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              <Tab\r\n                icon={<Iconify icon=\"material-symbols:view-list\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Project Details\"\r\n                value=\"milestones\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              <Tab\r\n                icon={<Iconify icon=\"mdi:robot\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Agent\"\r\n                value=\"agent\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              {planInfo?.user_access_level?.access_level === 'owner' && (\r\n                <Tab\r\n                  icon={<Iconify icon=\"material-symbols:security\" width={16} height={16} />}\r\n                  iconPosition=\"start\"\r\n                  label=\"Access\"\r\n                  value=\"access\"\r\n                  sx={{ gap: '4px' }}\r\n                />\r\n              )}\r\n            </Tabs>\r\n          </Box>\r\n\r\n          {/* Tab Content */}\r\n          <Box className={styles.tabContent}>\r\n            {activeTab === 'overview' && (\r\n              <Box className={styles.overviewTab} sx={{ gap: 0.5 }}>\r\n                <Description planInfo={planInfo} />\r\n                <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 1, mb: 0.5 }}>\r\n                  <Statistics stats={stats} />\r\n                  <Progress stats={stats} />\r\n                </Box>\r\n                {planInfo?.user_access_level?.access_level === 'owner' && (\r\n                  <AccessManagement\r\n                    planInfo={planInfo}\r\n                    userAccessLevel={planInfo?.user_access_level}\r\n                    onAddAccess={handleAddAccess}\r\n                    onUpdateAccess={handleUpdateAccess}\r\n                    onRemoveAccess={handleRemoveAccess}\r\n                  />\r\n                )}\r\n                <MilestoneOverview\r\n                  milestones={planInfo?.milestones}\r\n                  calculateMilestoneProgress={calculateMilestoneProgress}\r\n                  getMilestoneStatus={getMilestoneStatus}\r\n                  calculateTaskProgress={calculateTaskProgress}\r\n                  getTaskStatus={getTaskStatus}\r\n                  calculateSubtaskProgress={calculateSubtaskProgress}\r\n                  getSubtaskStatus={getSubtaskStatus}\r\n                />\r\n              </Box>\r\n            )}\r\n\r\n            {activeTab === 'milestones' && (\r\n              <>\r\n                <ChatbotBar\r\n                  planInfo={planInfo}\r\n                  onPlanUpdate={(updatedPlan) => {\r\n                    // Handle plan updates from AI agent\r\n                    console.log('Plan updated by AI:', updatedPlan);\r\n                  }}\r\n                  onSwitchToAgent={handleSwitchToAgent}\r\n                  sx={{ marginBottom: '20px'}}\r\n                />\r\n                <MilestoneList\r\n                  milestones={planInfo?.milestones}\r\n                  viewMode={viewMode}\r\n                  compact={false}\r\n                  showSubtasks={true}\r\n                  calculateMilestoneProgress={calculateMilestoneProgress}\r\n                  getMilestoneStatus={getMilestoneStatus}\r\n                  calculateTaskProgress={calculateTaskProgress}\r\n                  getTaskStatus={getTaskStatus}\r\n                  calculateSubtaskProgress={calculateSubtaskProgress}\r\n                  getSubtaskStatus={getSubtaskStatus}\r\n                  onUpdateMilestone={handleUpdateMilestone}\r\n                  onUpdateTask={handleUpdateTask}\r\n                  onUpdateSubtask={handleUpdateSubtask}\r\n                  onAddTask={handleAddTask}\r\n                  onAddSubtask={handleAddSubtask}\r\n                  onDeleteTask={handleDeleteTask}\r\n                  onDeleteSubtask={handleDeleteSubtask}\r\n                  onAssignMembers={handleAssignMembers}\r\n                  invitedUsers={planInfo?.invited_users || []}\r\n                  planOwner={planInfo?.owner}\r\n                />\r\n              </>\r\n            )}\r\n\r\n            {activeTab === 'agent' && (\r\n              <AgentTab\r\n                planInfo={planInfo}\r\n                onPlanUpdate={() => {\r\n                  // Refresh plan data when AI agent makes changes\r\n                  console.log('Plan updated by AI agent, refreshing data...');\r\n                  refreshPlanData();\r\n                }}\r\n              />\r\n            )}\r\n\r\n            {activeTab === 'access' && planInfo?.user_access_level?.access_level === 'owner' && (\r\n              <AccessManagement\r\n                planInfo={planInfo}\r\n                userAccessLevel={planInfo?.user_access_level}\r\n                onAddAccess={handleAddAccess}\r\n                onUpdateAccess={handleUpdateAccess}\r\n                onRemoveAccess={handleRemoveAccess}\r\n              />\r\n            )}\r\n          </Box>\r\n        </>\r\n      )}\r\n\r\n      {/* Dialogs */}\r\n      <InviteDialog\r\n        open={dialogState.invite}\r\n        onClose={() => closeDialog('invite')}\r\n        onInvite={handleInviteUser}\r\n        planInfo={planInfo}\r\n      />\r\n\r\n      <DeleteDialog\r\n        open={dialogState.delete}\r\n        onClose={() => closeDialog('delete')}\r\n        onDelete={handleDeletePlan}\r\n      />\r\n\r\n      <OptOutDialog\r\n        open={dialogState.optOut}\r\n        onClose={() => closeDialog('optOut')}\r\n        onOptOut={handleOptOutPlan}\r\n      />\r\n\r\n      <ConfirmDialog\r\n        open={dialogState.deleteTask}\r\n        onClose={() => setDialogState(prev => ({ ...prev, deleteTask: false }))}\r\n        onConfirm={() => handleDeleteTask(selectedTaskToDelete)}\r\n        title=\"Delete Task\"\r\n        description={`Are you sure you want to delete the task \"${selectedTaskToDelete?.name}\"? This action cannot be undone.`}\r\n      />\r\n\r\n      <ConfirmDialog\r\n        open={dialogState.deleteSubtask}\r\n        onClose={() => setDialogState(prev => ({ ...prev, deleteSubtask: false }))}\r\n        onConfirm={() => handleDeleteSubtask(selectedSubtaskToDelete)}\r\n        title=\"Delete Subtask\"\r\n        description={`Are you sure you want to delete the subtask \"${selectedSubtaskToDelete?.name}\"? This action cannot be undone.`}\r\n      />\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default PlanDetail;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,SAAS,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,gBAAgB,EAAEC,KAAK,QAAQ,eAAe;AAClF,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,EAAEC,MAAM,QAAQ,mBAAmB;AAC3D,SAASC,eAAe,EAAEC,aAAa,QAAQ,2BAA2B;AAC1E,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,UAAU,QAAQ,mBAAmB;;AAE9C;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,QAAQ,MAAM,uBAAuB;;AAE5C;AACA,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,WAAW,MAAM,qBAAqB;;AAE7C;AACA,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,aAAa,MAAM,yBAAyB;;AAEnD;AACA,SACEC,eAAe,EACfC,UAAU,EACVC,aAAa,EACbC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,mBAAmB,QACd,aAAa;;AAEpB;AACA,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACvB,MAAM;IAAEC;EAAM,CAAC,GAAG/C,SAAS,CAAC,CAAC;EAC7B,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,MAAM;IAC/C;IACA,OAAOoD,YAAY,CAACC,OAAO,CAAC,QAAQJ,KAAK,YAAY,CAAC,IAAI,UAAU;EACtE,CAAC,CAAC;EACF,MAAM,CAACK,IAAI,EAAEC,OAAO,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC;IAC7C0D,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,KAAK;IACbxB,UAAU,EAAE,KAAK;IACjBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACwB,oBAAoB,CAAG,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAC/C,MAAM,CAAC8D,uBAAuB,CAAG,GAAG9D,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM;IACJ+D,QAAQ;IACRC,OAAO;IACPC,KAAK;IACLC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,kBAAkB;IAClBC,wBAAwB;IACxBC,gBAAgB;IAChBC,qBAAqB;IACrBC,aAAa;IACbC,0BAA0B;IAC1BC;EACF,CAAC,GAAGlD,WAAW,CAACwB,KAAK,CAAC;EAEtB,MAAM;IACJ2B,QAAQ;IACRC;EACF,CAAC,GAAGnD,WAAW,CAAC,CAAC;;EAEjB;EACAzB,SAAS,CAAC,MAAM;IACd,IAAI8D,QAAQ,EAAE;MACZR,OAAO,CAACQ,QAAQ,CAAC;IACnB;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACA9D,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXmD,YAAY,CAAC0B,UAAU,CAAC,QAAQ7B,KAAK,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;;EAEX;EACA,MAAM8B,KAAK,GAAGV,kBAAkB,GAAGA,kBAAkB,CAACN,QAAQ,CAAC,GAAG,IAAI;;EAEtE;EACA,MAAMiB,UAAU,GAAIC,UAAU,IAAK;IACjCxB,cAAc,CAACyB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACD,UAAU,GAAG;IAAK,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAME,WAAW,GAAIF,UAAU,IAAK;IAClCxB,cAAc,CAACyB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACD,UAAU,GAAG;IAAM,CAAC,CAAC,CAAC;EAC5D,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAACC,CAAC,EAAEC,QAAQ,KAAK;IACvCnC,YAAY,CAACmC,QAAQ,CAAC;IACtB;IACAlC,YAAY,CAACmC,OAAO,CAAC,QAAQtC,KAAK,YAAY,EAAEqC,QAAQ,CAAC;EAC3D,CAAC;;EAED;EACA,MAAME,mBAAmB,GAAIC,gBAAgB,IAAK;IAChDC,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEF,gBAAgB,CAAC,CAAC,CAAC;IACjFtC,YAAY,CAAC,OAAO,CAAC;IACrBC,YAAY,CAACmC,OAAO,CAAC,QAAQtC,KAAK,YAAY,EAAE,OAAO,CAAC;IACxD;IACAG,YAAY,CAACmC,OAAO,CAAC,uBAAuB,EAAEK,IAAI,CAACC,SAAS,CAACJ,gBAAgB,CAAC,CAAC;EACjF,CAAC;;EAED;EACA,MAAMK,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMiC,eAAe,GAAG,MAAAA,CAAOC,KAAK,EAAEC,WAAW,KAAK;IACpD,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG3F,MAAM,cAAcsC,KAAK,SAAS,EAAE;QAClEsD,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,GAAGzF,UAAU,CAAC,CAAC;UACf,cAAc,EAAE;QAClB,CAAC;QACD0F,IAAI,EAAEb,IAAI,CAACC,SAAS,CAAC;UAAEM,KAAK;UAAEO,YAAY,EAAEN;QAAY,CAAC;MAC3D,CAAC,CAAC;MAEF,IAAI,CAACC,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAM1C,KAAK,GAAG,MAAMoC,QAAQ,CAACO,IAAI,CAAC,CAAC;QACnC,MAAM,IAAIC,KAAK,CAAC5C,KAAK,CAACA,KAAK,IAAI,sBAAsB,CAAC;MACxD;;MAEA;MACA,MAAM6B,eAAe,CAAC,CAAC;MACvBhF,KAAK,CAACgG,OAAO,CAAC,6BAA6B,CAAC;IAC9C,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CnD,KAAK,CAACmD,KAAK,CAACA,KAAK,CAAC8C,OAAO,IAAI,sBAAsB,CAAC;MACpD,MAAM9C,KAAK;IACb;EACF,CAAC;EAED,MAAM+C,kBAAkB,GAAG,eAAAA,CAAOC,QAAQ,EAAEb,WAAW,EAA0B;IAAA,IAAxBc,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAC1E,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG3F,MAAM,cAAcsC,KAAK,WAAWgE,QAAQ,EAAE,EAAE;QAC9EV,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,GAAGzF,UAAU,CAAC,CAAC;UACf,cAAc,EAAE;QAClB,CAAC;QACD0F,IAAI,EAAEb,IAAI,CAACC,SAAS,CAAC;UACnBa,YAAY,EAAEN,WAAW;UACzBkB,aAAa,EAAEJ;QACjB,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACb,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAM1C,KAAK,GAAG,MAAMoC,QAAQ,CAACO,IAAI,CAAC,CAAC;QACnC,MAAM,IAAIC,KAAK,CAAC5C,KAAK,CAACA,KAAK,IAAI,yBAAyB,CAAC;MAC3D;;MAEA;MACA,MAAM6B,eAAe,CAAC,CAAC;MACvBhF,KAAK,CAACgG,OAAO,CAAC,6BAA6B,CAAC;IAC9C,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CnD,KAAK,CAACmD,KAAK,CAACA,KAAK,CAAC8C,OAAO,IAAI,yBAAyB,CAAC;MACvD,MAAM9C,KAAK;IACb;EACF,CAAC;EAED,MAAMsD,kBAAkB,GAAG,MAAON,QAAQ,IAAK;IAC7C,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG3F,MAAM,cAAcsC,KAAK,WAAWgE,QAAQ,EAAE,EAAE;QAC9EV,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAEzF,UAAU,CAAC;MACtB,CAAC,CAAC;MAEF,IAAI,CAACsF,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAM1C,KAAK,GAAG,MAAMoC,QAAQ,CAACO,IAAI,CAAC,CAAC;QACnC,MAAM,IAAIC,KAAK,CAAC5C,KAAK,CAACA,KAAK,IAAI,yBAAyB,CAAC;MAC3D;;MAEA;MACA,MAAM6B,eAAe,CAAC,CAAC;MACvBhF,KAAK,CAACgG,OAAO,CAAC,6BAA6B,CAAC;IAC9C,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CnD,KAAK,CAACmD,KAAK,CAACA,KAAK,CAAC8C,OAAO,IAAI,yBAAyB,CAAC;MACvD,MAAM9C,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMuD,qBAAqB,GAAG,MAAOC,gBAAgB,IAAK;IACxD,IAAI;MACF/B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE8B,gBAAgB,CAAC;MACpD,MAAM1F,eAAe,CAAC0F,gBAAgB,CAAC;;MAEvC;MACA,MAAMC,WAAW,GAAG;QAAE,GAAGpE;MAAK,CAAC;MAC/B,MAAMqE,cAAc,GAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKN,gBAAgB,CAACM,EAAE,CAAC;MAE1F,IAAIJ,cAAc,KAAK,CAAC,CAAC,EAAE;QACzBD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,GAAG;UACvC,GAAGD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC;UACzC,GAAGF;QACL,CAAC;QACDlE,OAAO,CAACmE,WAAW,CAAC;MACtB;MAEA9G,eAAe,CAAC,gCAAgC,CAAC;IACnD,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDpD,aAAa,CAAC,4BAA4B,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMmH,gBAAgB,GAAG,MAAOC,WAAW,IAAK;IAC9C,IAAI;MACFvC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEsC,WAAW,CAAC;MAC1C,MAAMjG,UAAU,CAACiG,WAAW,CAAC;;MAE7B;MACA,MAAMP,WAAW,GAAG;QAAE,GAAGpE;MAAK,CAAC;MAC/B,MAAMqE,cAAc,GAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,IACvDA,CAAC,CAACI,KAAK,IAAIJ,CAAC,CAACI,KAAK,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKE,WAAW,CAACF,EAAE,CACtD,CAAC;MAED,IAAIJ,cAAc,KAAK,CAAC,CAAC,EAAE;QACzB,MAAMU,SAAS,GAAGX,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACL,SAAS,CAACO,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKE,WAAW,CAACF,EAAE,CAAC;QAEtG,IAAIM,SAAS,KAAK,CAAC,CAAC,EAAE;UACpBX,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACG,SAAS,CAAC,GAAG;YACxD,GAAGX,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACG,SAAS,CAAC;YAC1D,GAAGJ;UACL,CAAC;UACD1E,OAAO,CAACmE,WAAW,CAAC;QACtB;MACF;MAEA9G,eAAe,CAAC,2BAA2B,CAAC;IAC9C,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CpD,aAAa,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMyH,mBAAmB,GAAG,MAAOC,cAAc,IAAK;IACpD,IAAI;MACF7C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE4C,cAAc,CAAC;;MAEhD;MACA,MAAMtG,aAAa,CAACsG,cAAc,CAAC;;MAEnC;MACA,MAAMb,WAAW,GAAG;QAAE,GAAGpE;MAAK,CAAC;;MAE/B;MACA,IAAIkF,SAAS,GAAG,KAAK;;MAErB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,WAAW,CAACE,UAAU,CAACR,MAAM,EAAEqB,CAAC,EAAE,EAAE;QACtD,MAAMC,SAAS,GAAGhB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC;QAC3C,IAAI,CAACC,SAAS,CAACR,KAAK,EAAE;QAEtB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACR,KAAK,CAACd,MAAM,EAAEuB,CAAC,EAAE,EAAE;UAC/C,MAAMC,IAAI,GAAGF,SAAS,CAACR,KAAK,CAACS,CAAC,CAAC;UAC/B,IAAI,CAACC,IAAI,CAACC,QAAQ,EAAE;;UAEpB;UACA,MAAMC,YAAY,GAAGF,IAAI,CAACC,QAAQ,CAAChB,SAAS,CAACkB,CAAC,IAAIA,CAAC,CAAChB,EAAE,KAAKQ,cAAc,CAACR,EAAE,CAAC;UAC7E,IAAIe,YAAY,KAAK,CAAC,CAAC,EAAE;YACvB;YACAF,IAAI,CAACC,QAAQ,CAACC,YAAY,CAAC,GAAG;cAC5B,GAAGF,IAAI,CAACC,QAAQ,CAACC,YAAY,CAAC;cAC9B,GAAGP;YACL,CAAC;YACDC,SAAS,GAAG,IAAI;YAChB;UACF;QACF;QAEA,IAAIA,SAAS,EAAE;MACjB;;MAEA;MACAjF,OAAO,CAACmE,WAAW,CAAC;MAEpB9G,eAAe,CAAC,8BAA8B,CAAC;IACjD,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CpD,aAAa,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMmI,aAAa,GAAG,MAAOC,OAAO,IAAK;IACvC,IAAI;MACFvD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEsD,OAAO,CAAC;MACxC,MAAM5C,QAAQ,GAAG,MAAMnE,OAAO,CAAC+G,OAAO,CAAC;MACvCvD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEU,QAAQ,CAAC;;MAE3C;MACA,MAAMqB,WAAW,GAAG;QAAE,GAAGpE;MAAK,CAAC;;MAE/B;MACA,MAAMqE,cAAc,GAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKkB,OAAO,CAACP,SAAS,CAAC;MAExF,IAAIf,cAAc,KAAK,CAAC,CAAC,EAAE;QACzB;QACA,IAAI,CAACD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,EAAE;UACjDR,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,GAAG,EAAE;QACnD;;QAEA;QACA,MAAMgB,SAAS,GAAG7C,QAAQ,CAAC8C,IAAI,IAAI;UACjC,GAAGF,OAAO;UACVlB,EAAE,EAAEqB,IAAI,CAACC,GAAG,CAAC,CAAC;UAAE;UAChBR,QAAQ,EAAE;QACZ,CAAC;QAEDnB,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACoB,IAAI,CAACJ,SAAS,CAAC;QAC5D3F,OAAO,CAACmE,WAAW,CAAC;MACtB;MAEA9G,eAAe,CAAC,yBAAyB,CAAC;IAC5C,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CpD,aAAa,CAAC,oBAAoB,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAM0I,gBAAgB,GAAG,MAAOC,UAAU,IAAK;IAC7C,IAAI;MACF9D,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE6D,UAAU,CAAC;MAC9C,MAAMnD,QAAQ,GAAG,MAAMlE,UAAU,CAACqH,UAAU,CAAC;MAC7C9D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEU,QAAQ,CAAC;;MAE9C;MACA,MAAMqB,WAAW,GAAG;QAAE,GAAGpE;MAAK,CAAC;;MAE/B;MACA,IAAIkF,SAAS,GAAG,KAAK;MAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,WAAW,CAACE,UAAU,CAACR,MAAM,EAAEqB,CAAC,EAAE,EAAE;QACtD,MAAMC,SAAS,GAAGhB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC;QAC3C,IAAI,CAACC,SAAS,CAACR,KAAK,EAAE;QAEtB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACR,KAAK,CAACd,MAAM,EAAEuB,CAAC,EAAE,EAAE;UAC/C,MAAMC,IAAI,GAAGF,SAAS,CAACR,KAAK,CAACS,CAAC,CAAC;UAE/B,IAAIC,IAAI,CAACa,IAAI,KAAKD,UAAU,CAACZ,IAAI,EAAE;YACjC;YACA,IAAI,CAACA,IAAI,CAACC,QAAQ,EAAE;cAClBD,IAAI,CAACC,QAAQ,GAAG,EAAE;YACpB;;YAEA;YACA,MAAMa,YAAY,GAAGrD,QAAQ,CAAC8C,IAAI,IAAI;cACpC,GAAGK,UAAU;cACbzB,EAAE,EAAEqB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;YACjB,CAAC;YAEDT,IAAI,CAACC,QAAQ,CAACS,IAAI,CAACI,YAAY,CAAC;YAChClB,SAAS,GAAG,IAAI;YAChB;UACF;QACF;QAEA,IAAIA,SAAS,EAAE;MACjB;MAEAjF,OAAO,CAACmE,WAAW,CAAC;MACpB9G,eAAe,CAAC,4BAA4B,CAAC;IAC/C,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CpD,aAAa,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAM8I,gBAAgB,GAAG,MAAOC,YAAY,IAAK;IAC/C,IAAI;MACFlE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiE,YAAY,CAAC;MAC3C,MAAMxH,UAAU,CAACwH,YAAY,CAACH,IAAI,CAAC;;MAEnC;MACA,MAAM/B,WAAW,GAAG;QAAE,GAAGpE;MAAK,CAAC;;MAE/B;MACA,MAAMqE,cAAc,GAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,IACvDA,CAAC,CAACI,KAAK,IAAIJ,CAAC,CAACI,KAAK,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAK6B,YAAY,CAAC7B,EAAE,CACvD,CAAC;MAED,IAAIJ,cAAc,KAAK,CAAC,CAAC,EAAE;QACzB;QACAD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,GAAGR,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAAC2B,MAAM,CAChGzB,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAK6B,YAAY,CAAC7B,EAC7B,CAAC;QAEDxE,OAAO,CAACmE,WAAW,CAAC;MACtB;MAEA9G,eAAe,CAAC,2BAA2B,CAAC;IAC9C,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CpD,aAAa,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMiJ,mBAAmB,GAAG,MAAOC,eAAe,IAAK;IACrD,IAAI;MACFrE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoE,eAAe,CAAC;MACjD,MAAM1H,aAAa,CAAC0H,eAAe,CAACN,IAAI,CAAC;;MAEzC;MACA,MAAM/B,WAAW,GAAG;QAAE,GAAGpE;MAAK,CAAC;;MAE/B;MACA,IAAIkF,SAAS,GAAG,KAAK;MAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,WAAW,CAACE,UAAU,CAACR,MAAM,EAAEqB,CAAC,EAAE,EAAE;QACtD,MAAMC,SAAS,GAAGhB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC;QAC3C,IAAI,CAACC,SAAS,CAACR,KAAK,EAAE;QAEtB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACR,KAAK,CAACd,MAAM,EAAEuB,CAAC,EAAE,EAAE;UAC/C,MAAMC,IAAI,GAAGF,SAAS,CAACR,KAAK,CAACS,CAAC,CAAC;UAC/B,IAAI,CAACC,IAAI,CAACC,QAAQ,EAAE;;UAEpB;UACA,MAAMmB,cAAc,GAAGpB,IAAI,CAACC,QAAQ,CAACzB,MAAM;UAC3CwB,IAAI,CAACC,QAAQ,GAAGD,IAAI,CAACC,QAAQ,CAACgB,MAAM,CAACd,CAAC,IAAIA,CAAC,CAAChB,EAAE,KAAKgC,eAAe,CAAChC,EAAE,CAAC;UAEtE,IAAIa,IAAI,CAACC,QAAQ,CAACzB,MAAM,GAAG4C,cAAc,EAAE;YACzCxB,SAAS,GAAG,IAAI;YAChB;UACF;QACF;QAEA,IAAIA,SAAS,EAAE;MACjB;MAEAjF,OAAO,CAACmE,WAAW,CAAC;MACpB5G,KAAK,CAACgG,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CnD,KAAK,CAACmD,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMgG,mBAAmB,GAAG,MAAAA,CAAOC,YAAY,EAAEC,SAAS,KAAK;IAC7D,IAAI;MACFzE,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEuE,YAAY,EAAEC,SAAS,CAAC;MAClE,MAAM7H,mBAAmB,CAAC4H,YAAY,CAACT,IAAI,EAAEU,SAAS,CAAC;;MAEvD;MACA,MAAMzC,WAAW,GAAG;QAAE,GAAGpE;MAAK,CAAC;;MAE/B;MACA,IAAIkF,SAAS,GAAG,KAAK;MAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,WAAW,CAACE,UAAU,CAACR,MAAM,EAAEqB,CAAC,EAAE,EAAE;QACtD,MAAMC,SAAS,GAAGhB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC;QAC3C,IAAI,CAACC,SAAS,CAACR,KAAK,EAAE;QAEtB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACR,KAAK,CAACd,MAAM,EAAEuB,CAAC,EAAE,EAAE;UAC/C,MAAMC,IAAI,GAAGF,SAAS,CAACR,KAAK,CAACS,CAAC,CAAC;UAC/B,IAAIC,IAAI,CAACb,EAAE,KAAKmC,YAAY,CAACnC,EAAE,EAAE;YAC/B;YACA;YACA,MAAMqC,eAAe,GAAGD,SAAS,CAACE,GAAG,CAACC,QAAQ,IAAI;cAAA,IAAAC,qBAAA;cAChD;cACA,IAAIxG,QAAQ,CAACyG,KAAK,IAAIzG,QAAQ,CAACyG,KAAK,CAACzC,EAAE,KAAKuC,QAAQ,EAAE;gBACpD,OAAO;kBACLvC,EAAE,EAAEhE,QAAQ,CAACyG,KAAK,CAACzC,EAAE;kBACrB0C,UAAU,EAAE1G,QAAQ,CAACyG,KAAK,CAACC,UAAU;kBACrCC,SAAS,EAAE3G,QAAQ,CAACyG,KAAK,CAACE,SAAS;kBACnCvE,KAAK,EAAEpC,QAAQ,CAACyG,KAAK,CAACrE,KAAK;kBAC3BwE,MAAM,EAAE5G,QAAQ,CAACyG,KAAK,CAACG;gBACzB,CAAC;cACH;;cAEA;cACA,MAAMC,WAAW,IAAAL,qBAAA,GAAGxG,QAAQ,CAAC8G,aAAa,cAAAN,qBAAA,uBAAtBA,qBAAA,CAAwBO,IAAI,CAC9CC,IAAI,IAAIA,IAAI,CAACC,iBAAiB,IAAID,IAAI,CAACC,iBAAiB,CAACjD,EAAE,KAAKuC,QAClE,CAAC;cAED,IAAIM,WAAW,EAAE;gBACf,OAAO;kBACL7C,EAAE,EAAE6C,WAAW,CAACI,iBAAiB,CAACjD,EAAE;kBACpC0C,UAAU,EAAEG,WAAW,CAACI,iBAAiB,CAACP,UAAU;kBACpDC,SAAS,EAAEE,WAAW,CAACI,iBAAiB,CAACN,SAAS;kBAClDvE,KAAK,EAAEyE,WAAW,CAACzE,KAAK;kBACxBwE,MAAM,EAAEC,WAAW,CAACI,iBAAiB,CAACL;gBACxC,CAAC;cACH;cAEA,OAAO,IAAI;YACb,CAAC,CAAC,CAACd,MAAM,CAACoB,MAAM,IAAIA,MAAM,KAAK,IAAI,CAAC;YAEpCrC,IAAI,CAACsC,SAAS,GAAGd,eAAe;YAChC5B,SAAS,GAAG,IAAI;YAChB;UACF;QACF;QAEA,IAAIA,SAAS,EAAE;MACjB;MAEAjF,OAAO,CAACmE,WAAW,CAAC;MACpB5G,KAAK,CAACgG,OAAO,CAAC,+BAA+B,CAAC;IAChD,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDnD,KAAK,CAACmD,KAAK,CAAC,kCAAkC,CAAC;IACjD;EACF,CAAC;EAED,IAAIA,KAAK,EAAE;IACT,oBACExB,OAAA,CAACtC,SAAS;MAACgL,SAAS,EAAE5I,MAAM,CAAC6I,SAAU;MAAAC,QAAA,eACrC5I,OAAA,CAACjC,KAAK;QAAC8K,QAAQ,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAEvC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,oBACEnJ,OAAA,CAACtC,SAAS;IACR0L,QAAQ,EAAC,IAAI;IACbV,SAAS,EAAE5I,MAAM,CAAC6I,SAAU;IAC5BG,EAAE,EAAE;MACFO,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,oBAAoB;MAC/BC,UAAU,EAAE;IACd,CAAE;IAAAX,QAAA,GAEDrH,OAAO,gBACNvB,OAAA,CAACrC,GAAG;MAAC+K,SAAS,EAAE5I,MAAM,CAAC0J,gBAAiB;MAAAZ,QAAA,eACtC5I,OAAA,CAAClC,gBAAgB;QAAC2L,IAAI,EAAE,EAAG;QAACX,EAAE,EAAE;UAAEY,KAAK,EAAEzL;QAAgB;MAAE;QAAA+K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,gBAENnJ,OAAA,CAAAE,SAAA;MAAA0I,QAAA,gBAEE5I,OAAA,CAACzB,MAAM;QACL+C,QAAQ,EAAEA,QAAS;QACnBa,QAAQ,EAAEA,QAAS;QACnBwH,gBAAgB,EAAEvH,oBAAqB;QACvCwH,kBAAkB,EAAEA,CAAA,KAAMrH,UAAU,CAAC,QAAQ,CAAE;QAC/CsH,kBAAkB,EAAEA,CAAA,KAAMtH,UAAU,CAAC,QAAQ,CAAE;QAC/CuH,kBAAkB,EAAEA,CAAA,KAAMvH,UAAU,CAAC,QAAQ;MAAE;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eAGFnJ,OAAA,CAACrC,GAAG;QAACmL,EAAE,EAAE;UAAEiB,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE,SAAS;UAAEC,EAAE,EAAE,GAAG;UAAElB,EAAE,EAAE,CAAC;QAAE,CAAE;QAAAH,QAAA,eACpE5I,OAAA,CAACpC,IAAI;UACHsM,KAAK,EAAEzJ,SAAU;UACjB0J,QAAQ,EAAExH,eAAgB;UAC1ByH,OAAO,EAAC,YAAY;UACpBC,aAAa,EAAC,MAAM;UACpBvB,EAAE,EAAE;YACFQ,SAAS,EAAE,MAAM;YACjB,gBAAgB,EAAE;cAChBgB,aAAa,EAAE,MAAM;cACrBC,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE,QAAQ;cAClBC,QAAQ,EAAE,MAAM;cAChBnB,SAAS,EAAE,MAAM;cACjBoB,EAAE,EAAE,CAAC;cACLC,EAAE,EAAE,GAAG;cACPpB,UAAU,EAAE;YACd,CAAC;YACD,iBAAiB,EAAE;cACjBG,KAAK,EAAE,GAAGzL,eAAe;YAC3B,CAAC;YACD,sBAAsB,EAAE;cACtB2M,eAAe,EAAE3M,eAAe;cAChC4M,MAAM,EAAE;YACV;UACF,CAAE;UAAAjC,QAAA,gBAEF5I,OAAA,CAACnC,GAAG;YACFiN,IAAI,eAAE9K,OAAA,CAAChC,OAAO;cAAC8M,IAAI,EAAC,4BAA4B;cAACC,KAAK,EAAE,EAAG;cAACF,MAAM,EAAE;YAAG;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3E6B,YAAY,EAAC,OAAO;YACpBC,KAAK,EAAC,UAAU;YAChBf,KAAK,EAAC,UAAU;YAChBpB,EAAE,EAAE;cAAEoC,GAAG,EAAE;YAAM;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACFnJ,OAAA,CAACnC,GAAG;YACFiN,IAAI,eAAE9K,OAAA,CAAChC,OAAO;cAAC8M,IAAI,EAAC,4BAA4B;cAACC,KAAK,EAAE,EAAG;cAACF,MAAM,EAAE;YAAG;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3E6B,YAAY,EAAC,OAAO;YACpBC,KAAK,EAAC,iBAAiB;YACvBf,KAAK,EAAC,YAAY;YAClBpB,EAAE,EAAE;cAAEoC,GAAG,EAAE;YAAM;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACFnJ,OAAA,CAACnC,GAAG;YACFiN,IAAI,eAAE9K,OAAA,CAAChC,OAAO;cAAC8M,IAAI,EAAC,WAAW;cAACC,KAAK,EAAE,EAAG;cAACF,MAAM,EAAE;YAAG;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1D6B,YAAY,EAAC,OAAO;YACpBC,KAAK,EAAC,OAAO;YACbf,KAAK,EAAC,OAAO;YACbpB,EAAE,EAAE;cAAEoC,GAAG,EAAE;YAAM;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,EACD,CAAA7H,QAAQ,aAARA,QAAQ,wBAAAjB,qBAAA,GAARiB,QAAQ,CAAE6J,iBAAiB,cAAA9K,qBAAA,uBAA3BA,qBAAA,CAA6B4D,YAAY,MAAK,OAAO,iBACpDjE,OAAA,CAACnC,GAAG;YACFiN,IAAI,eAAE9K,OAAA,CAAChC,OAAO;cAAC8M,IAAI,EAAC,2BAA2B;cAACC,KAAK,EAAE,EAAG;cAACF,MAAM,EAAE;YAAG;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1E6B,YAAY,EAAC,OAAO;YACpBC,KAAK,EAAC,QAAQ;YACdf,KAAK,EAAC,QAAQ;YACdpB,EAAE,EAAE;cAAEoC,GAAG,EAAE;YAAM;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNnJ,OAAA,CAACrC,GAAG;QAAC+K,SAAS,EAAE5I,MAAM,CAACsL,UAAW;QAAAxC,QAAA,GAC/BnI,SAAS,KAAK,UAAU,iBACvBT,OAAA,CAACrC,GAAG;UAAC+K,SAAS,EAAE5I,MAAM,CAACuL,WAAY;UAACvC,EAAE,EAAE;YAAEoC,GAAG,EAAE;UAAI,CAAE;UAAAtC,QAAA,gBACnD5I,OAAA,CAACxB,WAAW;YAAC8C,QAAQ,EAAEA;UAAS;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCnJ,OAAA,CAACrC,GAAG;YAACmL,EAAE,EAAE;cAAEwC,OAAO,EAAE,MAAM;cAAEC,aAAa,EAAE;gBAAEC,EAAE,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAM,CAAC;cAAEP,GAAG,EAAE,CAAC;cAAEjB,EAAE,EAAE;YAAI,CAAE;YAAArB,QAAA,gBACxF5I,OAAA,CAACvB,UAAU;cAAC6D,KAAK,EAAEA;YAAM;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5BnJ,OAAA,CAACtB,QAAQ;cAAC4D,KAAK,EAAEA;YAAM;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,EACL,CAAA7H,QAAQ,aAARA,QAAQ,wBAAAhB,sBAAA,GAARgB,QAAQ,CAAE6J,iBAAiB,cAAA7K,sBAAA,uBAA3BA,sBAAA,CAA6B2D,YAAY,MAAK,OAAO,iBACpDjE,OAAA,CAACnB,gBAAgB;YACfyC,QAAQ,EAAEA,QAAS;YACnBoK,eAAe,EAAEpK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6J,iBAAkB;YAC7CQ,WAAW,EAAElI,eAAgB;YAC7BmI,cAAc,EAAErH,kBAAmB;YACnCsH,cAAc,EAAE/G;UAAmB;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CACF,eACDnJ,OAAA,CAACpB,iBAAiB;YAChBuG,UAAU,EAAE7D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6D,UAAW;YACjClD,0BAA0B,EAAEA,0BAA2B;YACvDC,kBAAkB,EAAEA,kBAAmB;YACvCH,qBAAqB,EAAEA,qBAAsB;YAC7CC,aAAa,EAAEA,aAAc;YAC7BH,wBAAwB,EAAEA,wBAAyB;YACnDC,gBAAgB,EAAEA;UAAiB;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEA1I,SAAS,KAAK,YAAY,iBACzBT,OAAA,CAAAE,SAAA;UAAA0I,QAAA,gBACE5I,OAAA,CAAClB,UAAU;YACTwC,QAAQ,EAAEA,QAAS;YACnBwK,YAAY,EAAG7G,WAAW,IAAK;cAC7B;cACAhC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE+B,WAAW,CAAC;YACjD,CAAE;YACF8G,eAAe,EAAEhJ,mBAAoB;YACrC+F,EAAE,EAAE;cAAEkD,YAAY,EAAE;YAAM;UAAE;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACFnJ,OAAA,CAACrB,aAAa;YACZwG,UAAU,EAAE7D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6D,UAAW;YACjChD,QAAQ,EAAEA,QAAS;YACnB8J,OAAO,EAAE,KAAM;YACfC,YAAY,EAAE,IAAK;YACnBjK,0BAA0B,EAAEA,0BAA2B;YACvDC,kBAAkB,EAAEA,kBAAmB;YACvCH,qBAAqB,EAAEA,qBAAsB;YAC7CC,aAAa,EAAEA,aAAc;YAC7BH,wBAAwB,EAAEA,wBAAyB;YACnDC,gBAAgB,EAAEA,gBAAiB;YACnCqK,iBAAiB,EAAEpH,qBAAsB;YACzCqH,YAAY,EAAE7G,gBAAiB;YAC/B8G,eAAe,EAAExG,mBAAoB;YACrCyG,SAAS,EAAE/F,aAAc;YACzBgG,YAAY,EAAEzF,gBAAiB;YAC/B0F,YAAY,EAAEtF,gBAAiB;YAC/BuF,eAAe,EAAEpF,mBAAoB;YACrCqF,eAAe,EAAElF,mBAAoB;YACrCmF,YAAY,EAAE,CAAArL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE8G,aAAa,KAAI,EAAG;YAC5CwE,SAAS,EAAEtL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEyG;UAAM;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA,eACF,CACH,EAEA1I,SAAS,KAAK,OAAO,iBACpBT,OAAA,CAACjB,QAAQ;UACPuC,QAAQ,EAAEA,QAAS;UACnBwK,YAAY,EAAEA,CAAA,KAAM;YAClB;YACA7I,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;YAC3DG,eAAe,CAAC,CAAC;UACnB;QAAE;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,EAEA1I,SAAS,KAAK,QAAQ,IAAI,CAAAa,QAAQ,aAARA,QAAQ,wBAAAf,sBAAA,GAARe,QAAQ,CAAE6J,iBAAiB,cAAA5K,sBAAA,uBAA3BA,sBAAA,CAA6B0D,YAAY,MAAK,OAAO,iBAC9EjE,OAAA,CAACnB,gBAAgB;UACfyC,QAAQ,EAAEA,QAAS;UACnBoK,eAAe,EAAEpK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6J,iBAAkB;UAC7CQ,WAAW,EAAElI,eAAgB;UAC7BmI,cAAc,EAAErH,kBAAmB;UACnCsH,cAAc,EAAE/G;QAAmB;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA,eACN,CACH,eAGDnJ,OAAA,CAACd,YAAY;MACX2N,IAAI,EAAE9L,WAAW,CAACE,MAAO;MACzB6L,OAAO,EAAEA,CAAA,KAAMpK,WAAW,CAAC,QAAQ,CAAE;MACrCqK,QAAQ,EAAEpL,gBAAiB;MAC3BL,QAAQ,EAAEA;IAAS;MAAA0H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAEFnJ,OAAA,CAACb,YAAY;MACX0N,IAAI,EAAE9L,WAAW,CAACG,MAAO;MACzB4L,OAAO,EAAEA,CAAA,KAAMpK,WAAW,CAAC,QAAQ,CAAE;MACrCsK,QAAQ,EAAEvL;IAAiB;MAAAuH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAEFnJ,OAAA,CAACZ,YAAY;MACXyN,IAAI,EAAE9L,WAAW,CAACI,MAAO;MACzB2L,OAAO,EAAEA,CAAA,KAAMpK,WAAW,CAAC,QAAQ,CAAE;MACrCuK,QAAQ,EAAEvL;IAAiB;MAAAsH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAEFnJ,OAAA,CAACX,aAAa;MACZwN,IAAI,EAAE9L,WAAW,CAACpB,UAAW;MAC7BmN,OAAO,EAAEA,CAAA,KAAM9L,cAAc,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE9C,UAAU,EAAE;MAAM,CAAC,CAAC,CAAE;MACxEuN,SAAS,EAAEA,CAAA,KAAMhG,gBAAgB,CAAC9F,oBAAoB,CAAE;MACxD+L,KAAK,EAAC,aAAa;MACnBC,WAAW,EAAE,6CAA6ChM,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEiM,IAAI;IAAmC;MAAArE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxH,CAAC,eAEFnJ,OAAA,CAACX,aAAa;MACZwN,IAAI,EAAE9L,WAAW,CAACnB,aAAc;MAChCkN,OAAO,EAAEA,CAAA,KAAM9L,cAAc,CAACyB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE7C,aAAa,EAAE;MAAM,CAAC,CAAC,CAAE;MAC3EsN,SAAS,EAAEA,CAAA,KAAM7F,mBAAmB,CAAChG,uBAAuB,CAAE;MAC9D8L,KAAK,EAAC,gBAAgB;MACtBC,WAAW,EAAE,gDAAgD/L,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEgM,IAAI;IAAmC;MAAArE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9H,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEhB,CAAC;AAAC/I,EAAA,CAltBID,UAAU;EAAA,QACI1C,SAAS,EA+BvBuB,WAAW,EAKXC,WAAW;AAAA;AAAAqO,EAAA,GArCXnN,UAAU;AAotBhB,eAAeA,UAAU;AAAC,IAAAmN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
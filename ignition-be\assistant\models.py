from django.db import models


class Assistant(models.Model):
    value = models.Char<PERSON>ield(max_length=255, null=True)

    class Meta:
        db_table = 'assistants'


class Thread(models.Model):
    value = models.Char<PERSON><PERSON>(max_length=255, null=True)
    assistant = models.<PERSON><PERSON><PERSON>(Assistant, on_delete=models.CASCADE, null=True)

    class Meta:
        db_table = 'threads'
class Message(models.Model):
    message_id = models.CharField(max_length=255, null=True)
    value = models.Char<PERSON>ield(max_length=255, null=True)
    thread = models.ForeignKey(Thread, on_delete=models.CASCADE, null=True)

    class Meta:
        db_table = 'messages'

class Run(models.Model):
    value = models.Char<PERSON>ield(max_length=255, null=True)
    thread = models.ForeignKey(Thread, on_delete=models.CASCADE, null=True)
    status = models.Char<PERSON>ield(max_length=255, null=True)

    class Meta:
        db_table = 'runs'

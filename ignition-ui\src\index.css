body {
  margin: 0;
  font-family: 'Recursive Variable' !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px !important;
}

.error-message {
  color: #dc3545;
  margin-top: 5px;
  font-size: 0.875em;
  margin-left: 5px;
}

.MuiSelect-select.MuiSelect-outlined.MuiInputBase-input.MuiOutlinedInput-input {
  display: flex;
}

/* --------- PLAN COMMENT SECTION --------- */
.ui.comments .comment .avatar {
  height: 2.5rem;
}

.ui.comments .comment .avatar img, .ui.comments .comment img.avatar {
  border-radius: 50%;
}

.css-fpqqwi-MuiCardContent-root:last-child {
  padding-bottom: 16px !important;
}

.comment {
  font-size: 1.125rem;
  font-family: 'Recursive Variable' !important;
}

.comment .content .text {
  font-size: 1.25rem !important;
  font-family: 'Recursive Variable' !important;
}
/* --------- PLAN COMMENT SECTION --------- */

/* --------- SIDEBAR SECTION --------- */
.ps-active {
  background-color: #F3F3F3;
  color: #333;
}

.ps-menu-button:hover {
  color: #333 !important;
}
/* --------- SIDEBAR SECTION --------- */

/* --------- MUI Tooltip SECTION --------- */
.MuiTooltip-tooltip {
  font-family: 'Recursive Variable' !important;
  font-size: 0.875rem !important;
  color: red !important;
  background-color: antiquewhite !important;
}
/* --------- MUI Tooltip SECTION --------- */

.avatar {
  transition: box-shadow 0.3s ease !important;
}

.avatarActive {
  box-shadow: 0 0 10px 2px white !important;
}

/* --------- Padding SECTION --------- */
.pb-16 {
  padding-bottom: 16px;
}
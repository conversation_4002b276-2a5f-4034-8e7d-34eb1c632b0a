import React from 'react';
import { Di<PERSON>, DialogContent, DialogContentText, DialogTitle, Button, Box } from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import styles from './styles.module.scss';

//--------------------------------------------------------------------------------------------------

// 3. TODO for Minh
// thêm loading cho button này
// khi loading thì sẽ có icon loading và disable button cancel và confirm

const ConfirmDialogComponent = ({ open, onClose, onConfirm, title, description }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      className={styles.mainDialogContent}>
      <Box className={styles.dialogIcon}>
        <Iconify icon="ion:help-circle-outline" width={128} height={128} color={mainYellowColor} />
      </Box>
      <DialogTitle id="alert-dialog-title" className={styles.dialogTitle}>
        {title}
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="alert-dialog-description" className={styles.dialogDescription}>
          {description}
        </DialogContentText>
      </DialogContent>
      <Box className={styles.actionDialog}>
        <Button
          onClick={onClose}
          color="primary"
          className={styles.dialogButtonCancel}>
          Cancel
        </Button>
        <Button
          onClick={onConfirm}
          color="primary"
          autoFocus
          className={styles.dialogButtonConfirm}>
          Ok
        </Button>
      </Box>
    </Dialog>
  );
};

export default ConfirmDialogComponent;

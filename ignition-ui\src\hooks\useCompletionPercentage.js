import { useEffect, useState } from 'react';

const useCompletionPercentage = (completedSubtasks, milestoneIndex, taskIndex, subtasks) => {
  const [percentage, setPercentage] = useState(0);

  useEffect(() => {
    const subtaskKey = `${milestoneIndex}-${taskIndex}`;
    const completed = completedSubtasks[subtaskKey].filter(Boolean).length;
    const total = subtasks.length;
    const newPercentage = (completed / total) * 100;
    setPercentage(newPercentage);
  }, [completedSubtasks, milestoneIndex, taskIndex, subtasks.length]);

  return percentage;
};

export default useCompletionPercentage;

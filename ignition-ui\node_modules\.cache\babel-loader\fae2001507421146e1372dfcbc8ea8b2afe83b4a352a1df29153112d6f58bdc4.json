{"ast": null, "code": "/* eslint-disable react-hooks/exhaustive-deps */import React,{useState,useEffect,useRef}from'react';import{Box,Typography,Paper,TextField,IconButton,Avatar,CircularProgress,Chip,Tooltip}from'@mui/material';import{useLocation}from'react-router-dom';import Iconify from'components/Iconify/index';import{mainYellowColor,APIURL}from\"helpers/constants\";import{getHeaders}from\"helpers/functions\";import axios from'axios';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AgentTab=_ref=>{let{planInfo,onPlanUpdate}=_ref;const[conversations,setConversations]=useState([]);const[currentMessage,setCurrentMessage]=useState('');const[isLoading,setIsLoading]=useState(false);const messagesEndRef=useRef(null);const inputRef=useRef(null);const[,setError]=useState(null);const location=useLocation();// Load conversations from localStorage\nuseEffect(()=>{var _location$state;const savedConversations=JSON.parse(localStorage.getItem('agent_conversations')||'[]');const planConversations=savedConversations.filter(conv=>conv.planId===(planInfo===null||planInfo===void 0?void 0:planInfo.id));setConversations(planConversations);// Check for pending message from ChatbotBar\nconst pendingMessage=localStorage.getItem('pending_agent_message');if(pendingMessage){try{var _messageData$planInfo;const messageData=JSON.parse(pendingMessage);if(((_messageData$planInfo=messageData.planInfo)===null||_messageData$planInfo===void 0?void 0:_messageData$planInfo.id)===(planInfo===null||planInfo===void 0?void 0:planInfo.id)){handleSendMessage(messageData.message);}// Clear the pending message\nlocalStorage.removeItem('pending_agent_message');}catch(error){console.error('Error processing pending message:',error);localStorage.removeItem('pending_agent_message');}}// If coming from chatbot bar via navigation state, add the initial message\nif((_location$state=location.state)!==null&&_location$state!==void 0&&_location$state.message){handleSendMessage(location.state.message);}},[planInfo===null||planInfo===void 0?void 0:planInfo.id,location.state]);// Auto scroll to bottom\nuseEffect(()=>{var _messagesEndRef$curre;(_messagesEndRef$curre=messagesEndRef.current)===null||_messagesEndRef$curre===void 0?void 0:_messagesEndRef$curre.scrollIntoView({behavior:'smooth'});},[conversations]);const executeAction=async(action,originalMessage)=>{try{const{type,entities}=action;if(type==='add_milestone'){// Extract milestone name from the message\nlet milestoneName='New Milestone';// Try to extract from quoted text first\nif(entities.taskNames&&entities.taskNames.length>0){milestoneName=entities.taskNames[0];}else{// Try to extract from common patterns\nconst patterns=[/add.*milestone.*[\"']([^\"']+)[\"']/i,/create.*milestone.*[\"']([^\"']+)[\"']/i,/new milestone.*[\"']([^\"']+)[\"']/i,/milestone.*[\"']([^\"']+)[\"']/i,/add.*milestone.*(?:for|about|called)\\s+([^.!?]+)/i,/create.*milestone.*(?:for|about|called)\\s+([^.!?]+)/i];for(const pattern of patterns){const match=originalMessage.match(pattern);if(match&&match[1]){milestoneName=match[1].trim();break;}}}const response=await axios.post(`${APIURL}/api/assistant/plan-action`,{action:'add_milestone',plan_slug:planInfo.slug,data:{name:milestoneName,description:`Milestone created by AI agent based on: \"${originalMessage}\"`},message:originalMessage},{headers:getHeaders()});return{success:true,message:`✅ Successfully created milestone \"${milestoneName}\"!\n\nThe new milestone has been added to your project. You can now:\n• Add tasks to this milestone\n• Set specific goals and deadlines\n• Track progress as you work\n\nWould you like me to add some initial tasks to this milestone?`,data:response.data};}// Handle other action types here...\nreturn{success:false,message:`I understand you want to ${action.description.toLowerCase()}, but I'm still learning how to do that. For now, I can help you add milestones to your project. What else can I help you with?`};}catch(error){var _error$response,_error$response$data;console.error('Error executing action:',error);return{success:false,message:`Sorry, I encountered an error while trying to ${action.description.toLowerCase()}: ${((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.error)||error.message}. Please try again or rephrase your request.`};}};const handleSendMessage=async function(){let messageText=arguments.length>0&&arguments[0]!==undefined?arguments[0]:currentMessage;if(!messageText.trim()||isLoading)return;setIsLoading(true);const userMessage={id:Date.now(),type:'user',content:messageText.trim(),timestamp:new Date().toISOString()};const newConversations=[...conversations,userMessage];setConversations(newConversations);setCurrentMessage('');try{// Call the new AI agent chat endpoint with plan context\nconst response=await axios.post(`${APIURL}/api/assistant/agent-chat`,{message:messageText.trim(),plan_slug:planInfo.slug},{headers:getHeaders()});const aiResponseData=response.data;// Create AI response message\nconst aiResponse={id:Date.now()+1,type:'assistant',content:aiResponseData.message||'I received your message but had trouble generating a response.',timestamp:new Date().toISOString(),actions:aiResponseData.actions||[],metadata:aiResponseData.metadata||{}};const updatedConversations=[...newConversations,aiResponse];setConversations(updatedConversations);// Execute any actions returned by the AI\nif(aiResponseData.actions&&aiResponseData.actions.length>0){let actionResults=[];for(const action of aiResponseData.actions){try{const result=await executeAIAction(action);actionResults.push(result);}catch(error){console.error('Error executing AI action:',error);actionResults.push({success:false,error:error.message||'Unknown error'});}}// If any actions were successful, trigger plan refresh\nif(actionResults.some(result=>result.success)&&onPlanUpdate){onPlanUpdate();}// Add action results to the conversation if there were any issues\nconst failedActions=actionResults.filter(result=>!result.success);if(failedActions.length>0){const errorMessage={id:Date.now()+2,type:'assistant',content:`Note: I encountered some issues executing the requested actions:\\n${failedActions.map(result=>`• ${result.error}`).join('\\n')}`,timestamp:new Date().toISOString(),isError:true};setConversations(prev=>[...prev,errorMessage]);}}// Save to localStorage\nconst allConversations=JSON.parse(localStorage.getItem('agent_conversations')||'[]');const otherPlanConversations=allConversations.filter(conv=>conv.planId!==(planInfo===null||planInfo===void 0?void 0:planInfo.id));const planConversations=updatedConversations.map(conv=>({...conv,planId:planInfo===null||planInfo===void 0?void 0:planInfo.id,planName:planInfo===null||planInfo===void 0?void 0:planInfo.name}));localStorage.setItem('agent_conversations',JSON.stringify([...otherPlanConversations,...planConversations]));}catch(error){var _error$response2,_error$response2$data;console.error('Error processing message:',error);const errorResponse={id:Date.now()+1,type:'assistant',content:`Sorry, I encountered an error while processing your request: ${((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.error)||error.message||'Unknown error'}. Please try again.`,timestamp:new Date().toISOString(),isError:true};setConversations([...newConversations,errorResponse]);setError(error.message||'Unknown error occurred');}finally{setIsLoading(false);}};const executeAIAction=async action=>{try{const response=await axios.post(`${APIURL}/api/assistant/plan-action`,{action:action.type,plan_slug:planInfo.slug,data:action.data||{},message:`AI-generated action: ${action.type}`},{headers:getHeaders()});return{success:true,data:response.data,action:action};}catch(error){var _error$response3,_error$response3$data;console.error('Error executing AI action:',error);return{success:false,error:((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.error)||error.message||'Unknown error',action:action};}};const generateAIResponse=(message,planInfo)=>{const lowerMessage=message.toLowerCase();const milestones=(planInfo===null||planInfo===void 0?void 0:planInfo.milestones)||[];const totalTasks=milestones.reduce((acc,milestone)=>{var _milestone$tasks;return acc+(((_milestone$tasks=milestone.tasks)===null||_milestone$tasks===void 0?void 0:_milestone$tasks.length)||0);},0);const totalSubtasks=milestones.reduce((acc,milestone)=>{var _milestone$tasks2;return acc+((_milestone$tasks2=milestone.tasks)===null||_milestone$tasks2===void 0?void 0:_milestone$tasks2.reduce((taskAcc,task)=>{var _task$subtasks;return taskAcc+(((_task$subtasks=task.subtasks)===null||_task$subtasks===void 0?void 0:_task$subtasks.length)||0);},0))||0;},0);// Analyze project context\nconst projectContext={name:(planInfo===null||planInfo===void 0?void 0:planInfo.name)||'your project',milestoneCount:milestones.length,taskCount:totalTasks,subtaskCount:totalSubtasks,milestoneNames:milestones.map(m=>m.name).slice(0,3)// First 3 milestone names\n};// Advanced intent recognition and response generation\nif(lowerMessage.includes('milestone')&&(lowerMessage.includes('add')||lowerMessage.includes('create')||lowerMessage.includes('new'))){return`I can help you add a new milestone to \"${projectContext.name}\". Currently, you have ${projectContext.milestoneCount} milestones: ${projectContext.milestoneNames.join(', ')}${projectContext.milestoneCount>3?'...':''}.\n\nTo add a new milestone, I'll need:\n1. **Milestone name** - What should we call it?\n2. **Description** - What's the main objective?\n3. **Position** - Should it come before/after a specific milestone?\n4. **Tasks** - Any initial tasks to include?\n\nPlease provide these details, or just tell me the milestone name and I'll help structure the rest!`;}if(lowerMessage.includes('progress')||lowerMessage.includes('status')||lowerMessage.includes('overview')){const progressDetails=milestones.map(milestone=>{var _milestone$tasks3,_milestone$tasks4,_milestone$tasks4$fil;const taskCount=((_milestone$tasks3=milestone.tasks)===null||_milestone$tasks3===void 0?void 0:_milestone$tasks3.length)||0;const completedTasks=((_milestone$tasks4=milestone.tasks)===null||_milestone$tasks4===void 0?void 0:(_milestone$tasks4$fil=_milestone$tasks4.filter(task=>task.status==='completed'))===null||_milestone$tasks4$fil===void 0?void 0:_milestone$tasks4$fil.length)||0;return`• ${milestone.name}: ${completedTasks}/${taskCount} tasks completed`;}).join('\\n');return`Here's your project progress for \"${projectContext.name}\":\n\n📊 **Overall Statistics:**\n• ${projectContext.milestoneCount} milestones\n• ${projectContext.taskCount} total tasks\n• ${projectContext.subtaskCount} total subtasks\n\n📋 **Milestone Progress:**\n${progressDetails}\n\nWould you like me to:\n• Show detailed progress for a specific milestone?\n• Identify overdue or at-risk tasks?\n• Suggest next actions to move the project forward?`;}if(lowerMessage.includes('complete')||lowerMessage.includes('done')||lowerMessage.includes('finish')){const availableTasks=milestones.flatMap(milestone=>{var _milestone$tasks5;return((_milestone$tasks5=milestone.tasks)===null||_milestone$tasks5===void 0?void 0:_milestone$tasks5.filter(task=>task.status!=='completed').map(task=>`\"${task.name}\" in ${milestone.name}`))||[];}).slice(0,5);return`I can mark tasks as completed for you! Here are some pending tasks I found:\n\n${availableTasks.map(task=>`• ${task}`).join('\\n')}\n\nTo mark a task as complete, just tell me:\n• \"Mark [task name] as completed\"\n• \"Complete the [task name] task\"\n• Or simply \"Done with [task name]\"\n\nWhich task would you like to mark as completed?`;}if(lowerMessage.includes('add')||lowerMessage.includes('create')||lowerMessage.includes('new')){if(lowerMessage.includes('task')){return`I can add new tasks to any of your milestones. You currently have ${projectContext.milestoneCount} milestones:\n\n${projectContext.milestoneNames.map((name,i)=>`${i+1}. ${name}`).join('\\n')}\n\nTo add a task, tell me:\n• **Which milestone** to add it to\n• **Task name** and description\n• **Any subtasks** to include\n\nExample: \"Add task 'Set up development environment' to the first milestone with subtasks for installing tools and configuring settings\"\n\nWhat task would you like to add?`;}return`I can help you add new content to \"${projectContext.name}\". I can create:\n\n🎯 **Milestones** - Major project phases\n📋 **Tasks** - Specific work items within milestones\n✅ **Subtasks** - Detailed steps for tasks\n📝 **Descriptions** - Enhanced details for any item\n\nWhat would you like to add? Just describe it naturally, like:\n• \"Add a milestone for user testing\"\n• \"Create a task for database setup in the development milestone\"\n• \"Add subtasks for the API integration task\"`;}if(lowerMessage.includes('delete')||lowerMessage.includes('remove')){return`I can help you remove items from your project. For safety, I'll always confirm before deleting anything.\n\nI can remove:\n• **Tasks** that are no longer needed\n• **Subtasks** that are redundant\n• **Completed items** to clean up the project\n• **Duplicate entries**\n\n⚠️ **Note:** I cannot delete milestones as they're core to your project structure.\n\nWhat would you like to remove? Please be specific about the item name and location.`;}if(lowerMessage.includes('update')||lowerMessage.includes('change')||lowerMessage.includes('edit')||lowerMessage.includes('modify')){return`I can update various aspects of your project \"${projectContext.name}\":\n\n📝 **Content Updates:**\n• Task and subtask descriptions\n• Milestone objectives\n• Due dates and priorities\n• Task assignments\n\n🔄 **Status Changes:**\n• Mark items as in-progress, completed, or blocked\n• Update milestone phases\n• Change task priorities\n\n📊 **Structural Changes:**\n• Move tasks between milestones\n• Reorder items\n• Split large tasks into smaller ones\n\nWhat would you like to update? Describe the change you want to make.`;}if(lowerMessage.includes('help')||lowerMessage.includes('what can you do')||lowerMessage.includes('capabilities')){return`I'm your AI Project Assistant for \"${projectContext.name}\"! Here's what I can do:\n\n🎯 **Project Management:**\n• Add/remove tasks, subtasks, and milestones\n• Update descriptions, statuses, and priorities\n• Mark items as completed or in-progress\n• Move tasks between milestones\n\n📊 **Project Analysis:**\n• Show progress reports and statistics\n• Identify bottlenecks and overdue items\n• Suggest next actions and optimizations\n• Generate project summaries\n\n🔍 **Smart Search:**\n• Find specific tasks or milestones\n• Filter by status, assignee, or due date\n• Locate related items across the project\n\n💡 **Recommendations:**\n• Suggest task breakdowns\n• Recommend milestone structures\n• Identify missing dependencies\n• Propose timeline optimizations\n\nJust tell me what you want to do in natural language - I'll understand and help you get it done!`;}// Default intelligent response\nreturn`I understand you want to \"${message}\".\n\nBased on your project \"${projectContext.name}\" with ${projectContext.milestoneCount} milestones and ${projectContext.taskCount} tasks, I can help you:\n\n🎯 **Quick Actions:**\n• \"Show me project progress\"\n• \"Add a new task to [milestone name]\"\n• \"Mark [task name] as completed\"\n• \"Update the description for [item name]\"\n\n💡 **Smart Suggestions:**\n• \"What should I work on next?\"\n• \"Show me overdue items\"\n• \"Help me organize this milestone\"\n• \"Create a timeline for this project\"\n\nWhat specific action would you like me to take? I'm here to make managing your project easier!`;};const extractActions=message=>{const actions=[];const lowerMessage=message.toLowerCase();// Enhanced action extraction with context\nconst actionPatterns=[{pattern:/(complete|done|finish|mark.*complete)/,type:'complete_task',confidence:0.9,description:'Mark task as completed'},{pattern:/(add|create|new).*milestone/,type:'add_milestone',confidence:0.9,description:'Add new milestone'},{pattern:/(add|create|new).*task/,type:'add_task',confidence:0.9,description:'Add new task'},{pattern:/(add|create|new).*subtask/,type:'add_subtask',confidence:0.9,description:'Add new subtask'},{pattern:/(delete|remove).*task/,type:'delete_task',confidence:0.8,description:'Delete task'},{pattern:/(update|change|edit|modify)/,type:'update_item',confidence:0.8,description:'Update item details'},{pattern:/(progress|status|overview)/,type:'show_progress',confidence:0.9,description:'Show project progress'},{pattern:/(move|transfer).*task/,type:'move_task',confidence:0.8,description:'Move task between milestones'},{pattern:/(assign|delegate)/,type:'assign_task',confidence:0.8,description:'Assign task to team member'},{pattern:/(due date|deadline|schedule)/,type:'set_deadline',confidence:0.8,description:'Set or update due date'}];// Extract entities (task names, milestone names, etc.)\nconst entities={taskNames:[],milestoneNames:[],dates:[],priorities:[]};// Look for quoted text (likely task/milestone names)\nconst quotedText=message.match(/\"([^\"]+)\"/g);if(quotedText){entities.taskNames=quotedText.map(q=>q.replace(/\"/g,''));}// Look for date patterns\nconst datePatterns=message.match(/\\b\\d{1,2}\\/\\d{1,2}\\/\\d{4}\\b|\\b\\d{1,2}-\\d{1,2}-\\d{4}\\b|tomorrow|today|next week|next month/gi);if(datePatterns){entities.dates=datePatterns;}// Look for priority keywords\nconst priorityPatterns=message.match(/\\b(high|low|medium|urgent|critical|normal)\\s*priority\\b/gi);if(priorityPatterns){entities.priorities=priorityPatterns;}// Match actions against patterns\nactionPatterns.forEach(_ref2=>{let{pattern,type,confidence,description}=_ref2;if(pattern.test(lowerMessage)){actions.push({type,confidence,description,entities:entities,originalMessage:message});}});return actions;};const handleKeyPress=e=>{if(e.key==='Enter'&&!e.shiftKey){e.preventDefault();handleSendMessage();}};const formatTimestamp=timestamp=>{return new Date(timestamp).toLocaleTimeString('en-US',{hour:'2-digit',minute:'2-digit'});};return/*#__PURE__*/_jsxs(Box,{sx:{height:'70vh',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsx(Paper,{elevation:0,sx:{p:2,borderRadius:'12px 12px 0 0',border:'1px solid #f0f0f0',borderBottom:'none',backgroundColor:'#fafafa'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Avatar,{sx:{backgroundColor:mainYellowColor,width:40,height:40},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:robot\",width:24,height:24,color:\"#fff\"})}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',fontWeight:600,color:'#333'},children:\"AI Project Agent\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",sx:{color:'#666',fontFamily:'\"Recursive Variable\", sans-serif'},children:[\"Managing: \",planInfo===null||planInfo===void 0?void 0:planInfo.name]})]}),/*#__PURE__*/_jsx(Chip,{label:\"Beta\",size:\"small\",sx:{backgroundColor:`${mainYellowColor}20`,color:mainYellowColor,fontWeight:600,ml:'auto'}})]})}),/*#__PURE__*/_jsx(Paper,{elevation:0,sx:{flex:1,border:'1px solid #f0f0f0',borderTop:'none',borderBottom:'none',overflow:'auto',p:2,backgroundColor:'#fff'},children:conversations.length===0?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',height:'100%',textAlign:'center'},children:[/*#__PURE__*/_jsx(Avatar,{sx:{backgroundColor:`${mainYellowColor}20`,width:60,height:60,mb:2},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:robot\",width:32,height:32,color:mainYellowColor})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',fontWeight:600,color:'#333',mb:1},children:\"Welcome to AI Project Agent\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:'#666',fontFamily:'\"Recursive Variable\", sans-serif',maxWidth:400},children:\"I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\"})]}):/*#__PURE__*/_jsxs(Box,{children:[conversations.map(message=>/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:message.type==='user'?'flex-end':'flex-start',mb:2},children:/*#__PURE__*/_jsxs(Box,{sx:{maxWidth:'70%',display:'flex',flexDirection:message.type==='user'?'row-reverse':'row',alignItems:'flex-start',gap:1},children:[/*#__PURE__*/_jsx(Avatar,{sx:{width:32,height:32,backgroundColor:message.type==='user'?'#e0e0e0':mainYellowColor},children:/*#__PURE__*/_jsx(Iconify,{icon:message.type==='user'?\"material-symbols:person\":\"mdi:robot\",width:18,height:18,color:message.type==='user'?'#666':'#fff'})}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Paper,{elevation:0,sx:{p:1.5,borderRadius:'12px',backgroundColor:message.type==='user'?mainYellowColor:'#f5f5f5',color:message.type==='user'?'#fff':'#333',border:message.isError?'1px solid #f44336':'none'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',lineHeight:1.5,whiteSpace:'pre-line'},children:message.content}),message.type==='assistant'&&message.actions&&message.actions.length>0&&/*#__PURE__*/_jsx(Box,{sx:{mt:1.5,display:'flex',flexWrap:'wrap',gap:1},children:message.actions.slice(0,3).map((action,actionIndex)=>/*#__PURE__*/_jsx(Chip,{label:action.description,size:\"small\",onClick:()=>{// Handle quick action\nsetCurrentMessage(action.originalMessage||`Please ${action.description.toLowerCase()}`);if(inputRef.current){inputRef.current.focus();}},sx:{backgroundColor:'#fff',border:`1px solid ${mainYellowColor}`,color:mainYellowColor,fontSize:'0.7rem',height:'24px',cursor:'pointer','&:hover':{backgroundColor:`${mainYellowColor}10`}}},actionIndex))})]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{color:'#999',fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.7rem',mt:0.5,display:'block',textAlign:message.type==='user'?'right':'left'},children:formatTimestamp(message.timestamp)})]})]})},message.id)),isLoading&&/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'flex-start',mb:2},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'flex-start',gap:1},children:[/*#__PURE__*/_jsx(Avatar,{sx:{width:32,height:32,backgroundColor:mainYellowColor},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:robot\",width:18,height:18,color:\"#fff\"})}),/*#__PURE__*/_jsxs(Paper,{elevation:0,sx:{p:1.5,borderRadius:'12px',backgroundColor:'#f5f5f5',display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(CircularProgress,{size:16,sx:{color:mainYellowColor}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',color:'#666'},children:\"Thinking...\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{ref:messagesEndRef})]})}),/*#__PURE__*/_jsx(Paper,{elevation:0,sx:{p:2,borderRadius:'0 0 12px 12px',border:'1px solid #f0f0f0',borderTop:'none'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1,alignItems:'flex-end'},children:[/*#__PURE__*/_jsx(TextField,{inputRef:inputRef,value:currentMessage,onChange:e=>setCurrentMessage(e.target.value),onKeyPress:handleKeyPress,placeholder:\"Ask me anything about your project...\",multiline:true,maxRows:3,fullWidth:true,variant:\"outlined\",disabled:isLoading,sx:{'& .MuiOutlinedInput-root':{borderRadius:'8px',fontFamily:'\"Recursive Variable\", sans-serif'}}}),/*#__PURE__*/_jsx(Tooltip,{title:\"Send message\",children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleSendMessage(),disabled:!currentMessage.trim()||isLoading,sx:{backgroundColor:currentMessage.trim()&&!isLoading?mainYellowColor:'#f0f0f0',color:currentMessage.trim()&&!isLoading?'#fff':'#999','&:hover':{backgroundColor:currentMessage.trim()&&!isLoading?'#E69500':'#f0f0f0'}},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:send\",width:20,height:20})})})]})})]});};export default AgentTab;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Typography", "Paper", "TextField", "IconButton", "Avatar", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "useLocation", "Iconify", "mainYellowColor", "APIURL", "getHeaders", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "AgentTab", "_ref", "planInfo", "onPlanUpdate", "conversations", "setConversations", "currentMessage", "setCurrentMessage", "isLoading", "setIsLoading", "messagesEndRef", "inputRef", "setError", "location", "_location$state", "savedConversations", "JSON", "parse", "localStorage", "getItem", "planConversations", "filter", "conv", "planId", "id", "pendingMessage", "_messageData$planInfo", "messageData", "handleSendMessage", "message", "removeItem", "error", "console", "state", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "executeAction", "action", "originalMessage", "type", "entities", "milestoneName", "taskNames", "length", "patterns", "pattern", "match", "trim", "response", "post", "plan_slug", "slug", "data", "name", "description", "headers", "success", "toLowerCase", "_error$response", "_error$response$data", "messageText", "arguments", "undefined", "userMessage", "Date", "now", "content", "timestamp", "toISOString", "newConversations", "aiResponseData", "aiResponse", "actions", "metadata", "updatedConversations", "actionResults", "result", "executeAIAction", "push", "some", "failedActions", "errorMessage", "map", "join", "isError", "prev", "allConversations", "otherPlanConversations", "planName", "setItem", "stringify", "_error$response2", "_error$response2$data", "errorResponse", "_error$response3", "_error$response3$data", "generateAIResponse", "lowerMessage", "milestones", "totalTasks", "reduce", "acc", "milestone", "_milestone$tasks", "tasks", "totalSubtasks", "_milestone$tasks2", "taskAcc", "task", "_task$subtasks", "subtasks", "projectContext", "milestoneCount", "taskCount", "subtaskCount", "milestoneNames", "m", "slice", "includes", "progressDetails", "_milestone$tasks3", "_milestone$tasks4", "_milestone$tasks4$fil", "completedTasks", "status", "availableTasks", "flatMap", "_milestone$tasks5", "i", "extractActions", "actionPatterns", "confidence", "dates", "priorities", "quotedText", "q", "replace", "datePatterns", "priorityPatterns", "for<PERSON>ach", "_ref2", "test", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "formatTimestamp", "toLocaleTimeString", "hour", "minute", "sx", "height", "display", "flexDirection", "children", "elevation", "p", "borderRadius", "border", "borderBottom", "backgroundColor", "alignItems", "gap", "width", "icon", "color", "variant", "fontFamily", "fontWeight", "label", "size", "ml", "flex", "borderTop", "overflow", "justifyContent", "textAlign", "mb", "max<PERSON><PERSON><PERSON>", "lineHeight", "whiteSpace", "mt", "flexWrap", "actionIndex", "onClick", "focus", "fontSize", "cursor", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "multiline", "maxRows", "fullWidth", "disabled", "title"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/AgentTab.js"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  TextField,\r\n  IconButton,\r\n  Avatar,\r\n  CircularProgress,\r\n  Chip,\r\n  Tooltip\r\n} from '@mui/material';\r\nimport { useLocation } from 'react-router-dom';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\r\nimport { getHeaders } from \"helpers/functions\";\r\nimport axios from 'axios';\r\n\r\nconst AgentTab = ({ planInfo, onPlanUpdate }) => {\r\n  const [conversations, setConversations] = useState([]);\r\n  const [currentMessage, setCurrentMessage] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const messagesEndRef = useRef(null);\r\n  const inputRef = useRef(null);\r\n  const [ , setError] = useState(null);\r\n  const location = useLocation();\r\n\r\n  // Load conversations from localStorage\r\n  useEffect(() => {\r\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n    const planConversations = savedConversations.filter(conv => conv.planId === planInfo?.id);\r\n    setConversations(planConversations);\r\n\r\n    // Check for pending message from ChatbotBar\r\n    const pendingMessage = localStorage.getItem('pending_agent_message');\r\n    if (pendingMessage) {\r\n      try {\r\n        const messageData = JSON.parse(pendingMessage);\r\n        if (messageData.planInfo?.id === planInfo?.id) {\r\n          handleSendMessage(messageData.message);\r\n        }\r\n        // Clear the pending message\r\n        localStorage.removeItem('pending_agent_message');\r\n      } catch (error) {\r\n        console.error('Error processing pending message:', error);\r\n        localStorage.removeItem('pending_agent_message');\r\n      }\r\n    }\r\n\r\n    // If coming from chatbot bar via navigation state, add the initial message\r\n    if (location.state?.message) {\r\n      handleSendMessage(location.state.message);\r\n    }\r\n  }, [planInfo?.id, location.state]);\r\n\r\n  // Auto scroll to bottom\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  }, [conversations]);\r\n\r\n  const executeAction = async (action, originalMessage) => {\r\n    try {\r\n      const { type, entities } = action;\r\n\r\n      if (type === 'add_milestone') {\r\n        // Extract milestone name from the message\r\n        let milestoneName = 'New Milestone';\r\n\r\n        // Try to extract from quoted text first\r\n        if (entities.taskNames && entities.taskNames.length > 0) {\r\n          milestoneName = entities.taskNames[0];\r\n        } else {\r\n          // Try to extract from common patterns\r\n          const patterns = [\r\n            /add.*milestone.*[\"']([^\"']+)[\"']/i,\r\n            /create.*milestone.*[\"']([^\"']+)[\"']/i,\r\n            /new milestone.*[\"']([^\"']+)[\"']/i,\r\n            /milestone.*[\"']([^\"']+)[\"']/i,\r\n            /add.*milestone.*(?:for|about|called)\\s+([^.!?]+)/i,\r\n            /create.*milestone.*(?:for|about|called)\\s+([^.!?]+)/i\r\n          ];\r\n\r\n          for (const pattern of patterns) {\r\n            const match = originalMessage.match(pattern);\r\n            if (match && match[1]) {\r\n              milestoneName = match[1].trim();\r\n              break;\r\n            }\r\n          }\r\n        }\r\n\r\n        const response = await axios.post(\r\n          `${APIURL}/api/assistant/plan-action`,\r\n          {\r\n            action: 'add_milestone',\r\n            plan_slug: planInfo.slug,\r\n            data: {\r\n              name: milestoneName,\r\n              description: `Milestone created by AI agent based on: \"${originalMessage}\"`\r\n            },\r\n            message: originalMessage\r\n          },\r\n          { headers: getHeaders() }\r\n        );\r\n\r\n        return {\r\n          success: true,\r\n          message: `✅ Successfully created milestone \"${milestoneName}\"!\r\n\r\nThe new milestone has been added to your project. You can now:\r\n• Add tasks to this milestone\r\n• Set specific goals and deadlines\r\n• Track progress as you work\r\n\r\nWould you like me to add some initial tasks to this milestone?`,\r\n          data: response.data\r\n        };\r\n      }\r\n\r\n      // Handle other action types here...\r\n      return {\r\n        success: false,\r\n        message: `I understand you want to ${action.description.toLowerCase()}, but I'm still learning how to do that. For now, I can help you add milestones to your project. What else can I help you with?`\r\n      };\r\n\r\n    } catch (error) {\r\n      console.error('Error executing action:', error);\r\n      return {\r\n        success: false,\r\n        message: `Sorry, I encountered an error while trying to ${action.description.toLowerCase()}: ${error.response?.data?.error || error.message}. Please try again or rephrase your request.`\r\n      };\r\n    }\r\n  };\r\n\r\n  const handleSendMessage = async (messageText = currentMessage) => {\r\n    if (!messageText.trim() || isLoading) return;\r\n\r\n    setIsLoading(true);\r\n    const userMessage = {\r\n      id: Date.now(),\r\n      type: 'user',\r\n      content: messageText.trim(),\r\n      timestamp: new Date().toISOString()\r\n    };\r\n\r\n    const newConversations = [...conversations, userMessage];\r\n    setConversations(newConversations);\r\n    setCurrentMessage('');\r\n\r\n    try {\r\n      // Call the new AI agent chat endpoint with plan context\r\n      const response = await axios.post(\r\n        `${APIURL}/api/assistant/agent-chat`,\r\n        {\r\n          message: messageText.trim(),\r\n          plan_slug: planInfo.slug\r\n        },\r\n        { headers: getHeaders() }\r\n      );\r\n\r\n      const aiResponseData = response.data;\r\n\r\n      // Create AI response message\r\n      const aiResponse = {\r\n        id: Date.now() + 1,\r\n        type: 'assistant',\r\n        content: aiResponseData.message || 'I received your message but had trouble generating a response.',\r\n        timestamp: new Date().toISOString(),\r\n        actions: aiResponseData.actions || [],\r\n        metadata: aiResponseData.metadata || {}\r\n      };\r\n\r\n      const updatedConversations = [...newConversations, aiResponse];\r\n      setConversations(updatedConversations);\r\n\r\n      // Execute any actions returned by the AI\r\n      if (aiResponseData.actions && aiResponseData.actions.length > 0) {\r\n        let actionResults = [];\r\n        for (const action of aiResponseData.actions) {\r\n          try {\r\n            const result = await executeAIAction(action);\r\n            actionResults.push(result);\r\n          } catch (error) {\r\n            console.error('Error executing AI action:', error);\r\n            actionResults.push({\r\n              success: false,\r\n              error: error.message || 'Unknown error'\r\n            });\r\n          }\r\n        }\r\n\r\n        // If any actions were successful, trigger plan refresh\r\n        if (actionResults.some(result => result.success) && onPlanUpdate) {\r\n          onPlanUpdate();\r\n        }\r\n\r\n        // Add action results to the conversation if there were any issues\r\n        const failedActions = actionResults.filter(result => !result.success);\r\n        if (failedActions.length > 0) {\r\n          const errorMessage = {\r\n            id: Date.now() + 2,\r\n            type: 'assistant',\r\n            content: `Note: I encountered some issues executing the requested actions:\\n${failedActions.map(result => `• ${result.error}`).join('\\n')}`,\r\n            timestamp: new Date().toISOString(),\r\n            isError: true\r\n          };\r\n          setConversations(prev => [...prev, errorMessage]);\r\n        }\r\n      }\r\n\r\n      // Save to localStorage\r\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== planInfo?.id);\r\n      const planConversations = updatedConversations.map(conv => ({\r\n        ...conv,\r\n        planId: planInfo?.id,\r\n        planName: planInfo?.name\r\n      }));\r\n\r\n      localStorage.setItem('agent_conversations', JSON.stringify([\r\n        ...otherPlanConversations,\r\n        ...planConversations\r\n      ]));\r\n\r\n    } catch (error) {\r\n      console.error('Error processing message:', error);\r\n      const errorResponse = {\r\n        id: Date.now() + 1,\r\n        type: 'assistant',\r\n        content: `Sorry, I encountered an error while processing your request: ${error.response?.data?.error || error.message || 'Unknown error'}. Please try again.`,\r\n        timestamp: new Date().toISOString(),\r\n        isError: true\r\n      };\r\n      setConversations([...newConversations, errorResponse]);\r\n      setError(error.message || 'Unknown error occurred');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const executeAIAction = async (action) => {\r\n    try {\r\n      const response = await axios.post(\r\n        `${APIURL}/api/assistant/plan-action`,\r\n        {\r\n          action: action.type,\r\n          plan_slug: planInfo.slug,\r\n          data: action.data || {},\r\n          message: `AI-generated action: ${action.type}`\r\n        },\r\n        { headers: getHeaders() }\r\n      );\r\n\r\n      return {\r\n        success: true,\r\n        data: response.data,\r\n        action: action\r\n      };\r\n    } catch (error) {\r\n      console.error('Error executing AI action:', error);\r\n      return {\r\n        success: false,\r\n        error: error.response?.data?.error || error.message || 'Unknown error',\r\n        action: action\r\n      };\r\n    }\r\n  };\r\n\r\n  const generateAIResponse = (message, planInfo) => {\r\n    const lowerMessage = message.toLowerCase();\r\n    const milestones = planInfo?.milestones || [];\r\n    const totalTasks = milestones.reduce((acc, milestone) => acc + (milestone.tasks?.length || 0), 0);\r\n    const totalSubtasks = milestones.reduce((acc, milestone) =>\r\n      acc + milestone.tasks?.reduce((taskAcc, task) => taskAcc + (task.subtasks?.length || 0), 0) || 0, 0);\r\n\r\n    // Analyze project context\r\n    const projectContext = {\r\n      name: planInfo?.name || 'your project',\r\n      milestoneCount: milestones.length,\r\n      taskCount: totalTasks,\r\n      subtaskCount: totalSubtasks,\r\n      milestoneNames: milestones.map(m => m.name).slice(0, 3), // First 3 milestone names\r\n    };\r\n\r\n    // Advanced intent recognition and response generation\r\n    if (lowerMessage.includes('milestone') && (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new'))) {\r\n      return `I can help you add a new milestone to \"${projectContext.name}\". Currently, you have ${projectContext.milestoneCount} milestones: ${projectContext.milestoneNames.join(', ')}${projectContext.milestoneCount > 3 ? '...' : ''}.\r\n\r\nTo add a new milestone, I'll need:\r\n1. **Milestone name** - What should we call it?\r\n2. **Description** - What's the main objective?\r\n3. **Position** - Should it come before/after a specific milestone?\r\n4. **Tasks** - Any initial tasks to include?\r\n\r\nPlease provide these details, or just tell me the milestone name and I'll help structure the rest!`;\r\n    }\r\n\r\n    if (lowerMessage.includes('progress') || lowerMessage.includes('status') || lowerMessage.includes('overview')) {\r\n      const progressDetails = milestones.map(milestone => {\r\n        const taskCount = milestone.tasks?.length || 0;\r\n        const completedTasks = milestone.tasks?.filter(task => task.status === 'completed')?.length || 0;\r\n        return `• ${milestone.name}: ${completedTasks}/${taskCount} tasks completed`;\r\n      }).join('\\n');\r\n\r\n      return `Here's your project progress for \"${projectContext.name}\":\r\n\r\n📊 **Overall Statistics:**\r\n• ${projectContext.milestoneCount} milestones\r\n• ${projectContext.taskCount} total tasks\r\n• ${projectContext.subtaskCount} total subtasks\r\n\r\n📋 **Milestone Progress:**\r\n${progressDetails}\r\n\r\nWould you like me to:\r\n• Show detailed progress for a specific milestone?\r\n• Identify overdue or at-risk tasks?\r\n• Suggest next actions to move the project forward?`;\r\n    }\r\n\r\n    if (lowerMessage.includes('complete') || lowerMessage.includes('done') || lowerMessage.includes('finish')) {\r\n      const availableTasks = milestones.flatMap(milestone =>\r\n        milestone.tasks?.filter(task => task.status !== 'completed').map(task =>\r\n          `\"${task.name}\" in ${milestone.name}`\r\n        ) || []\r\n      ).slice(0, 5);\r\n\r\n      return `I can mark tasks as completed for you! Here are some pending tasks I found:\r\n\r\n${availableTasks.map(task => `• ${task}`).join('\\n')}\r\n\r\nTo mark a task as complete, just tell me:\r\n• \"Mark [task name] as completed\"\r\n• \"Complete the [task name] task\"\r\n• Or simply \"Done with [task name]\"\r\n\r\nWhich task would you like to mark as completed?`;\r\n    }\r\n\r\n    if (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new')) {\r\n      if (lowerMessage.includes('task')) {\r\n        return `I can add new tasks to any of your milestones. You currently have ${projectContext.milestoneCount} milestones:\r\n\r\n${projectContext.milestoneNames.map((name, i) => `${i + 1}. ${name}`).join('\\n')}\r\n\r\nTo add a task, tell me:\r\n• **Which milestone** to add it to\r\n• **Task name** and description\r\n• **Any subtasks** to include\r\n\r\nExample: \"Add task 'Set up development environment' to the first milestone with subtasks for installing tools and configuring settings\"\r\n\r\nWhat task would you like to add?`;\r\n      }\r\n\r\n      return `I can help you add new content to \"${projectContext.name}\". I can create:\r\n\r\n🎯 **Milestones** - Major project phases\r\n📋 **Tasks** - Specific work items within milestones\r\n✅ **Subtasks** - Detailed steps for tasks\r\n📝 **Descriptions** - Enhanced details for any item\r\n\r\nWhat would you like to add? Just describe it naturally, like:\r\n• \"Add a milestone for user testing\"\r\n• \"Create a task for database setup in the development milestone\"\r\n• \"Add subtasks for the API integration task\"`;\r\n    }\r\n\r\n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\r\n      return `I can help you remove items from your project. For safety, I'll always confirm before deleting anything.\r\n\r\nI can remove:\r\n• **Tasks** that are no longer needed\r\n• **Subtasks** that are redundant\r\n• **Completed items** to clean up the project\r\n• **Duplicate entries**\r\n\r\n⚠️ **Note:** I cannot delete milestones as they're core to your project structure.\r\n\r\nWhat would you like to remove? Please be specific about the item name and location.`;\r\n    }\r\n\r\n    if (lowerMessage.includes('update') || lowerMessage.includes('change') || lowerMessage.includes('edit') || lowerMessage.includes('modify')) {\r\n      return `I can update various aspects of your project \"${projectContext.name}\":\r\n\r\n📝 **Content Updates:**\r\n• Task and subtask descriptions\r\n• Milestone objectives\r\n• Due dates and priorities\r\n• Task assignments\r\n\r\n🔄 **Status Changes:**\r\n• Mark items as in-progress, completed, or blocked\r\n• Update milestone phases\r\n• Change task priorities\r\n\r\n📊 **Structural Changes:**\r\n• Move tasks between milestones\r\n• Reorder items\r\n• Split large tasks into smaller ones\r\n\r\nWhat would you like to update? Describe the change you want to make.`;\r\n    }\r\n\r\n    if (lowerMessage.includes('help') || lowerMessage.includes('what can you do') || lowerMessage.includes('capabilities')) {\r\n      return `I'm your AI Project Assistant for \"${projectContext.name}\"! Here's what I can do:\r\n\r\n🎯 **Project Management:**\r\n• Add/remove tasks, subtasks, and milestones\r\n• Update descriptions, statuses, and priorities\r\n• Mark items as completed or in-progress\r\n• Move tasks between milestones\r\n\r\n📊 **Project Analysis:**\r\n• Show progress reports and statistics\r\n• Identify bottlenecks and overdue items\r\n• Suggest next actions and optimizations\r\n• Generate project summaries\r\n\r\n🔍 **Smart Search:**\r\n• Find specific tasks or milestones\r\n• Filter by status, assignee, or due date\r\n• Locate related items across the project\r\n\r\n💡 **Recommendations:**\r\n• Suggest task breakdowns\r\n• Recommend milestone structures\r\n• Identify missing dependencies\r\n• Propose timeline optimizations\r\n\r\nJust tell me what you want to do in natural language - I'll understand and help you get it done!`;\r\n    }\r\n\r\n    // Default intelligent response\r\n    return `I understand you want to \"${message}\".\r\n\r\nBased on your project \"${projectContext.name}\" with ${projectContext.milestoneCount} milestones and ${projectContext.taskCount} tasks, I can help you:\r\n\r\n🎯 **Quick Actions:**\r\n• \"Show me project progress\"\r\n• \"Add a new task to [milestone name]\"\r\n• \"Mark [task name] as completed\"\r\n• \"Update the description for [item name]\"\r\n\r\n💡 **Smart Suggestions:**\r\n• \"What should I work on next?\"\r\n• \"Show me overdue items\"\r\n• \"Help me organize this milestone\"\r\n• \"Create a timeline for this project\"\r\n\r\nWhat specific action would you like me to take? I'm here to make managing your project easier!`;\r\n  };\r\n\r\n  const extractActions = (message) => {\r\n    const actions = [];\r\n    const lowerMessage = message.toLowerCase();\r\n\r\n    // Enhanced action extraction with context\r\n    const actionPatterns = [\r\n      {\r\n        pattern: /(complete|done|finish|mark.*complete)/,\r\n        type: 'complete_task',\r\n        confidence: 0.9,\r\n        description: 'Mark task as completed'\r\n      },\r\n      {\r\n        pattern: /(add|create|new).*milestone/,\r\n        type: 'add_milestone',\r\n        confidence: 0.9,\r\n        description: 'Add new milestone'\r\n      },\r\n      {\r\n        pattern: /(add|create|new).*task/,\r\n        type: 'add_task',\r\n        confidence: 0.9,\r\n        description: 'Add new task'\r\n      },\r\n      {\r\n        pattern: /(add|create|new).*subtask/,\r\n        type: 'add_subtask',\r\n        confidence: 0.9,\r\n        description: 'Add new subtask'\r\n      },\r\n      {\r\n        pattern: /(delete|remove).*task/,\r\n        type: 'delete_task',\r\n        confidence: 0.8,\r\n        description: 'Delete task'\r\n      },\r\n      {\r\n        pattern: /(update|change|edit|modify)/,\r\n        type: 'update_item',\r\n        confidence: 0.8,\r\n        description: 'Update item details'\r\n      },\r\n      {\r\n        pattern: /(progress|status|overview)/,\r\n        type: 'show_progress',\r\n        confidence: 0.9,\r\n        description: 'Show project progress'\r\n      },\r\n      {\r\n        pattern: /(move|transfer).*task/,\r\n        type: 'move_task',\r\n        confidence: 0.8,\r\n        description: 'Move task between milestones'\r\n      },\r\n      {\r\n        pattern: /(assign|delegate)/,\r\n        type: 'assign_task',\r\n        confidence: 0.8,\r\n        description: 'Assign task to team member'\r\n      },\r\n      {\r\n        pattern: /(due date|deadline|schedule)/,\r\n        type: 'set_deadline',\r\n        confidence: 0.8,\r\n        description: 'Set or update due date'\r\n      }\r\n    ];\r\n\r\n    // Extract entities (task names, milestone names, etc.)\r\n    const entities = {\r\n      taskNames: [],\r\n      milestoneNames: [],\r\n      dates: [],\r\n      priorities: []\r\n    };\r\n\r\n    // Look for quoted text (likely task/milestone names)\r\n    const quotedText = message.match(/\"([^\"]+)\"/g);\r\n    if (quotedText) {\r\n      entities.taskNames = quotedText.map(q => q.replace(/\"/g, ''));\r\n    }\r\n\r\n    // Look for date patterns\r\n    const datePatterns = message.match(/\\b\\d{1,2}\\/\\d{1,2}\\/\\d{4}\\b|\\b\\d{1,2}-\\d{1,2}-\\d{4}\\b|tomorrow|today|next week|next month/gi);\r\n    if (datePatterns) {\r\n      entities.dates = datePatterns;\r\n    }\r\n\r\n    // Look for priority keywords\r\n    const priorityPatterns = message.match(/\\b(high|low|medium|urgent|critical|normal)\\s*priority\\b/gi);\r\n    if (priorityPatterns) {\r\n      entities.priorities = priorityPatterns;\r\n    }\r\n\r\n    // Match actions against patterns\r\n    actionPatterns.forEach(({ pattern, type, confidence, description }) => {\r\n      if (pattern.test(lowerMessage)) {\r\n        actions.push({\r\n          type,\r\n          confidence,\r\n          description,\r\n          entities: entities,\r\n          originalMessage: message\r\n        });\r\n      }\r\n    });\r\n\r\n    return actions;\r\n  };\r\n\r\n  const handleKeyPress = (e) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const formatTimestamp = (timestamp) => {\r\n    return new Date(timestamp).toLocaleTimeString('en-US', {\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>\r\n      {/* Header */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          p: 2,\r\n          borderRadius: '12px 12px 0 0',\r\n          border: '1px solid #f0f0f0',\r\n          borderBottom: 'none',\r\n          backgroundColor: '#fafafa'\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n          <Avatar\r\n            sx={{\r\n              backgroundColor: mainYellowColor,\r\n              width: 40,\r\n              height: 40\r\n            }}\r\n          >\r\n            <Iconify icon=\"mdi:robot\" width={24} height={24} color=\"#fff\" />\r\n          </Avatar>\r\n          <Box>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                color: '#333'\r\n              }}\r\n            >\r\n              AI Project Agent\r\n            </Typography>\r\n            <Typography\r\n              variant=\"caption\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }}\r\n            >\r\n              Managing: {planInfo?.name}\r\n            </Typography>\r\n          </Box>\r\n          <Chip\r\n            label=\"Beta\"\r\n            size=\"small\"\r\n            sx={{\r\n              backgroundColor: `${mainYellowColor}20`,\r\n              color: mainYellowColor,\r\n              fontWeight: 600,\r\n              ml: 'auto'\r\n            }}\r\n          />\r\n        </Box>\r\n      </Paper>\r\n\r\n      {/* Messages Area */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          flex: 1,\r\n          border: '1px solid #f0f0f0',\r\n          borderTop: 'none',\r\n          borderBottom: 'none',\r\n          overflow: 'auto',\r\n          p: 2,\r\n          backgroundColor: '#fff'\r\n        }}\r\n      >\r\n        {conversations.length === 0 ? (\r\n          <Box\r\n            sx={{\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              height: '100%',\r\n              textAlign: 'center'\r\n            }}\r\n          >\r\n            <Avatar\r\n              sx={{\r\n                backgroundColor: `${mainYellowColor}20`,\r\n                width: 60,\r\n                height: 60,\r\n                mb: 2\r\n              }}\r\n            >\r\n              <Iconify icon=\"mdi:robot\" width={32} height={32} color={mainYellowColor} />\r\n            </Avatar>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                color: '#333',\r\n                mb: 1\r\n              }}\r\n            >\r\n              Welcome to AI Project Agent\r\n            </Typography>\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                maxWidth: 400\r\n              }}\r\n            >\r\n              I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\r\n            </Typography>\r\n          </Box>\r\n        ) : (\r\n          <Box>\r\n            {conversations.map((message) => (\r\n              <Box\r\n                key={message.id}\r\n                sx={{\r\n                  display: 'flex',\r\n                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\r\n                  mb: 2\r\n                }}\r\n              >\r\n                <Box\r\n                  sx={{\r\n                    maxWidth: '70%',\r\n                    display: 'flex',\r\n                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\r\n                    alignItems: 'flex-start',\r\n                    gap: 1\r\n                  }}\r\n                >\r\n                  <Avatar\r\n                    sx={{\r\n                      width: 32,\r\n                      height: 32,\r\n                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\r\n                    }}\r\n                  >\r\n                    <Iconify\r\n                      icon={message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\"}\r\n                      width={18}\r\n                      height={18}\r\n                      color={message.type === 'user' ? '#666' : '#fff'}\r\n                    />\r\n                  </Avatar>\r\n                  <Box>\r\n                    <Paper\r\n                      elevation={0}\r\n                      sx={{\r\n                        p: 1.5,\r\n                        borderRadius: '12px',\r\n                        backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\r\n                        color: message.type === 'user' ? '#fff' : '#333',\r\n                        border: message.isError ? '1px solid #f44336' : 'none'\r\n                      }}\r\n                    >\r\n                      <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                          fontFamily: '\"Recursive Variable\", sans-serif',\r\n                          lineHeight: 1.5,\r\n                          whiteSpace: 'pre-line'\r\n                        }}\r\n                      >\r\n                        {message.content}\r\n                      </Typography>\r\n\r\n                      {/* Quick Action Buttons for AI responses */}\r\n                      {message.type === 'assistant' && message.actions && message.actions.length > 0 && (\r\n                        <Box sx={{ mt: 1.5, display: 'flex', flexWrap: 'wrap', gap: 1 }}>\r\n                          {message.actions.slice(0, 3).map((action, actionIndex) => (\r\n                            <Chip\r\n                              key={actionIndex}\r\n                              label={action.description}\r\n                              size=\"small\"\r\n                              onClick={() => {\r\n                                // Handle quick action\r\n                                setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);\r\n                                if (inputRef.current) {\r\n                                  inputRef.current.focus();\r\n                                }\r\n                              }}\r\n                              sx={{\r\n                                backgroundColor: '#fff',\r\n                                border: `1px solid ${mainYellowColor}`,\r\n                                color: mainYellowColor,\r\n                                fontSize: '0.7rem',\r\n                                height: '24px',\r\n                                cursor: 'pointer',\r\n                                '&:hover': {\r\n                                  backgroundColor: `${mainYellowColor}10`\r\n                                }\r\n                              }}\r\n                            />\r\n                          ))}\r\n                        </Box>\r\n                      )}\r\n                    </Paper>\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        color: '#999',\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        fontSize: '0.7rem',\r\n                        mt: 0.5,\r\n                        display: 'block',\r\n                        textAlign: message.type === 'user' ? 'right' : 'left'\r\n                      }}\r\n                    >\r\n                      {formatTimestamp(message.timestamp)}\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n            ))}\r\n            {isLoading && (\r\n              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>\r\n                  <Avatar\r\n                    sx={{\r\n                      width: 32,\r\n                      height: 32,\r\n                      backgroundColor: mainYellowColor\r\n                    }}\r\n                  >\r\n                    <Iconify icon=\"mdi:robot\" width={18} height={18} color=\"#fff\" />\r\n                  </Avatar>\r\n                  <Paper\r\n                    elevation={0}\r\n                    sx={{\r\n                      p: 1.5,\r\n                      borderRadius: '12px',\r\n                      backgroundColor: '#f5f5f5',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: 1\r\n                    }}\r\n                  >\r\n                    <CircularProgress size={16} sx={{ color: mainYellowColor }} />\r\n                    <Typography\r\n                      variant=\"body2\"\r\n                      sx={{\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        color: '#666'\r\n                      }}\r\n                    >\r\n                      Thinking...\r\n                    </Typography>\r\n                  </Paper>\r\n                </Box>\r\n              </Box>\r\n            )}\r\n            <div ref={messagesEndRef} />\r\n          </Box>\r\n        )}\r\n      </Paper>\r\n\r\n      {/* Input Area */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          p: 2,\r\n          borderRadius: '0 0 12px 12px',\r\n          border: '1px solid #f0f0f0',\r\n          borderTop: 'none'\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\r\n          <TextField\r\n            inputRef={inputRef}\r\n            value={currentMessage}\r\n            onChange={(e) => setCurrentMessage(e.target.value)}\r\n            onKeyPress={handleKeyPress}\r\n            placeholder=\"Ask me anything about your project...\"\r\n            multiline\r\n            maxRows={3}\r\n            fullWidth\r\n            variant=\"outlined\"\r\n            disabled={isLoading}\r\n            sx={{\r\n              '& .MuiOutlinedInput-root': {\r\n                borderRadius: '8px',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }\r\n            }}\r\n          />\r\n          <Tooltip title=\"Send message\">\r\n            <IconButton\r\n              onClick={() => handleSendMessage()}\r\n              disabled={!currentMessage.trim() || isLoading}\r\n              sx={{\r\n                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\r\n                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\r\n                '&:hover': {\r\n                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\r\n                }\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:send\" width={20} height={20} />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Box>\r\n      </Paper>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default AgentTab;\r\n"], "mappings": "AAAA,gDACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,SAAS,CACTC,UAAU,CACVC,MAAM,CACNC,gBAAgB,CAChBC,IAAI,CACJC,OAAO,KACF,eAAe,CACtB,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,OAAO,KAAM,0BAA0B,CAC9C,OAASC,eAAe,CAAEC,MAAM,KAAQ,mBAAmB,CAC3D,OAASC,UAAU,KAAQ,mBAAmB,CAC9C,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,QAAQ,CAAGC,IAAA,EAAgC,IAA/B,CAAEC,QAAQ,CAAEC,YAAa,CAAC,CAAAF,IAAA,CAC1C,KAAM,CAACG,aAAa,CAAEC,gBAAgB,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC4B,cAAc,CAAEC,iBAAiB,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC8B,SAAS,CAAEC,YAAY,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAAgC,cAAc,CAAG9B,MAAM,CAAC,IAAI,CAAC,CACnC,KAAM,CAAA+B,QAAQ,CAAG/B,MAAM,CAAC,IAAI,CAAC,CAC7B,KAAM,EAAIgC,QAAQ,CAAC,CAAGlC,QAAQ,CAAC,IAAI,CAAC,CACpC,KAAM,CAAAmC,QAAQ,CAAGvB,WAAW,CAAC,CAAC,CAE9B;AACAX,SAAS,CAAC,IAAM,KAAAmC,eAAA,CACd,KAAM,CAAAC,kBAAkB,CAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,EAAI,IAAI,CAAC,CAC1F,KAAM,CAAAC,iBAAiB,CAAGL,kBAAkB,CAACM,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACC,MAAM,IAAKrB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,EAAE,EAAC,CACzFnB,gBAAgB,CAACe,iBAAiB,CAAC,CAEnC;AACA,KAAM,CAAAK,cAAc,CAAGP,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,CACpE,GAAIM,cAAc,CAAE,CAClB,GAAI,KAAAC,qBAAA,CACF,KAAM,CAAAC,WAAW,CAAGX,IAAI,CAACC,KAAK,CAACQ,cAAc,CAAC,CAC9C,GAAI,EAAAC,qBAAA,CAAAC,WAAW,CAACzB,QAAQ,UAAAwB,qBAAA,iBAApBA,qBAAA,CAAsBF,EAAE,KAAKtB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,EAAE,EAAE,CAC7CI,iBAAiB,CAACD,WAAW,CAACE,OAAO,CAAC,CACxC,CACA;AACAX,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC,CAClD,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzDb,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC,CAClD,CACF,CAEA;AACA,IAAAhB,eAAA,CAAID,QAAQ,CAACoB,KAAK,UAAAnB,eAAA,WAAdA,eAAA,CAAgBe,OAAO,CAAE,CAC3BD,iBAAiB,CAACf,QAAQ,CAACoB,KAAK,CAACJ,OAAO,CAAC,CAC3C,CACF,CAAC,CAAE,CAAC3B,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,EAAE,CAAEX,QAAQ,CAACoB,KAAK,CAAC,CAAC,CAElC;AACAtD,SAAS,CAAC,IAAM,KAAAuD,qBAAA,CACd,CAAAA,qBAAA,CAAAxB,cAAc,CAACyB,OAAO,UAAAD,qBAAA,iBAAtBA,qBAAA,CAAwBE,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChE,CAAC,CAAE,CAACjC,aAAa,CAAC,CAAC,CAEnB,KAAM,CAAAkC,aAAa,CAAG,KAAAA,CAAOC,MAAM,CAAEC,eAAe,GAAK,CACvD,GAAI,CACF,KAAM,CAAEC,IAAI,CAAEC,QAAS,CAAC,CAAGH,MAAM,CAEjC,GAAIE,IAAI,GAAK,eAAe,CAAE,CAC5B;AACA,GAAI,CAAAE,aAAa,CAAG,eAAe,CAEnC;AACA,GAAID,QAAQ,CAACE,SAAS,EAAIF,QAAQ,CAACE,SAAS,CAACC,MAAM,CAAG,CAAC,CAAE,CACvDF,aAAa,CAAGD,QAAQ,CAACE,SAAS,CAAC,CAAC,CAAC,CACvC,CAAC,IAAM,CACL;AACA,KAAM,CAAAE,QAAQ,CAAG,CACf,mCAAmC,CACnC,sCAAsC,CACtC,kCAAkC,CAClC,8BAA8B,CAC9B,mDAAmD,CACnD,sDAAsD,CACvD,CAED,IAAK,KAAM,CAAAC,OAAO,GAAI,CAAAD,QAAQ,CAAE,CAC9B,KAAM,CAAAE,KAAK,CAAGR,eAAe,CAACQ,KAAK,CAACD,OAAO,CAAC,CAC5C,GAAIC,KAAK,EAAIA,KAAK,CAAC,CAAC,CAAC,CAAE,CACrBL,aAAa,CAAGK,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAC/B,MACF,CACF,CACF,CAEA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAvD,KAAK,CAACwD,IAAI,CAC/B,GAAG1D,MAAM,4BAA4B,CACrC,CACE8C,MAAM,CAAE,eAAe,CACvBa,SAAS,CAAElD,QAAQ,CAACmD,IAAI,CACxBC,IAAI,CAAE,CACJC,IAAI,CAAEZ,aAAa,CACnBa,WAAW,CAAE,4CAA4ChB,eAAe,GAC1E,CAAC,CACDX,OAAO,CAAEW,eACX,CAAC,CACD,CAAEiB,OAAO,CAAE/D,UAAU,CAAC,CAAE,CAC1B,CAAC,CAED,MAAO,CACLgE,OAAO,CAAE,IAAI,CACb7B,OAAO,CAAE,qCAAqCc,aAAa;AACrE;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,CACrDW,IAAI,CAAEJ,QAAQ,CAACI,IACjB,CAAC,CACH,CAEA;AACA,MAAO,CACLI,OAAO,CAAE,KAAK,CACd7B,OAAO,CAAE,4BAA4BU,MAAM,CAACiB,WAAW,CAACG,WAAW,CAAC,CAAC,iIACvE,CAAC,CAEH,CAAE,MAAO5B,KAAK,CAAE,KAAA6B,eAAA,CAAAC,oBAAA,CACd7B,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,MAAO,CACL2B,OAAO,CAAE,KAAK,CACd7B,OAAO,CAAE,iDAAiDU,MAAM,CAACiB,WAAW,CAACG,WAAW,CAAC,CAAC,KAAK,EAAAC,eAAA,CAAA7B,KAAK,CAACmB,QAAQ,UAAAU,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBN,IAAI,UAAAO,oBAAA,iBAApBA,oBAAA,CAAsB9B,KAAK,GAAIA,KAAK,CAACF,OAAO,8CAC7I,CAAC,CACH,CACF,CAAC,CAED,KAAM,CAAAD,iBAAiB,CAAG,cAAAA,CAAA,CAAwC,IAAjC,CAAAkC,WAAW,CAAAC,SAAA,CAAAlB,MAAA,IAAAkB,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAGzD,cAAc,CAC3D,GAAI,CAACwD,WAAW,CAACb,IAAI,CAAC,CAAC,EAAIzC,SAAS,CAAE,OAEtCC,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAwD,WAAW,CAAG,CAClBzC,EAAE,CAAE0C,IAAI,CAACC,GAAG,CAAC,CAAC,CACd1B,IAAI,CAAE,MAAM,CACZ2B,OAAO,CAAEN,WAAW,CAACb,IAAI,CAAC,CAAC,CAC3BoB,SAAS,CAAE,GAAI,CAAAH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CACpC,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAG,CAAC,GAAGnE,aAAa,CAAE6D,WAAW,CAAC,CACxD5D,gBAAgB,CAACkE,gBAAgB,CAAC,CAClChE,iBAAiB,CAAC,EAAE,CAAC,CAErB,GAAI,CACF;AACA,KAAM,CAAA2C,QAAQ,CAAG,KAAM,CAAAvD,KAAK,CAACwD,IAAI,CAC/B,GAAG1D,MAAM,2BAA2B,CACpC,CACEoC,OAAO,CAAEiC,WAAW,CAACb,IAAI,CAAC,CAAC,CAC3BG,SAAS,CAAElD,QAAQ,CAACmD,IACtB,CAAC,CACD,CAAEI,OAAO,CAAE/D,UAAU,CAAC,CAAE,CAC1B,CAAC,CAED,KAAM,CAAA8E,cAAc,CAAGtB,QAAQ,CAACI,IAAI,CAEpC;AACA,KAAM,CAAAmB,UAAU,CAAG,CACjBjD,EAAE,CAAE0C,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,CAAC,CAClB1B,IAAI,CAAE,WAAW,CACjB2B,OAAO,CAAEI,cAAc,CAAC3C,OAAO,EAAI,gEAAgE,CACnGwC,SAAS,CAAE,GAAI,CAAAH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CACnCI,OAAO,CAAEF,cAAc,CAACE,OAAO,EAAI,EAAE,CACrCC,QAAQ,CAAEH,cAAc,CAACG,QAAQ,EAAI,CAAC,CACxC,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAG,CAAC,GAAGL,gBAAgB,CAAEE,UAAU,CAAC,CAC9DpE,gBAAgB,CAACuE,oBAAoB,CAAC,CAEtC;AACA,GAAIJ,cAAc,CAACE,OAAO,EAAIF,cAAc,CAACE,OAAO,CAAC7B,MAAM,CAAG,CAAC,CAAE,CAC/D,GAAI,CAAAgC,aAAa,CAAG,EAAE,CACtB,IAAK,KAAM,CAAAtC,MAAM,GAAI,CAAAiC,cAAc,CAACE,OAAO,CAAE,CAC3C,GAAI,CACF,KAAM,CAAAI,MAAM,CAAG,KAAM,CAAAC,eAAe,CAACxC,MAAM,CAAC,CAC5CsC,aAAa,CAACG,IAAI,CAACF,MAAM,CAAC,CAC5B,CAAE,MAAO/C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD8C,aAAa,CAACG,IAAI,CAAC,CACjBtB,OAAO,CAAE,KAAK,CACd3B,KAAK,CAAEA,KAAK,CAACF,OAAO,EAAI,eAC1B,CAAC,CAAC,CACJ,CACF,CAEA;AACA,GAAIgD,aAAa,CAACI,IAAI,CAACH,MAAM,EAAIA,MAAM,CAACpB,OAAO,CAAC,EAAIvD,YAAY,CAAE,CAChEA,YAAY,CAAC,CAAC,CAChB,CAEA;AACA,KAAM,CAAA+E,aAAa,CAAGL,aAAa,CAACxD,MAAM,CAACyD,MAAM,EAAI,CAACA,MAAM,CAACpB,OAAO,CAAC,CACrE,GAAIwB,aAAa,CAACrC,MAAM,CAAG,CAAC,CAAE,CAC5B,KAAM,CAAAsC,YAAY,CAAG,CACnB3D,EAAE,CAAE0C,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,CAAC,CAClB1B,IAAI,CAAE,WAAW,CACjB2B,OAAO,CAAE,qEAAqEc,aAAa,CAACE,GAAG,CAACN,MAAM,EAAI,KAAKA,MAAM,CAAC/C,KAAK,EAAE,CAAC,CAACsD,IAAI,CAAC,IAAI,CAAC,EAAE,CAC3IhB,SAAS,CAAE,GAAI,CAAAH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CACnCgB,OAAO,CAAE,IACX,CAAC,CACDjF,gBAAgB,CAACkF,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEJ,YAAY,CAAC,CAAC,CACnD,CACF,CAEA;AACA,KAAM,CAAAK,gBAAgB,CAAGxE,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,EAAI,IAAI,CAAC,CACxF,KAAM,CAAAsE,sBAAsB,CAAGD,gBAAgB,CAACnE,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACC,MAAM,IAAKrB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,EAAE,EAAC,CAC5F,KAAM,CAAAJ,iBAAiB,CAAGwD,oBAAoB,CAACQ,GAAG,CAAC9D,IAAI,GAAK,CAC1D,GAAGA,IAAI,CACPC,MAAM,CAAErB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,EAAE,CACpBkE,QAAQ,CAAExF,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEqD,IACtB,CAAC,CAAC,CAAC,CAEHrC,YAAY,CAACyE,OAAO,CAAC,qBAAqB,CAAE3E,IAAI,CAAC4E,SAAS,CAAC,CACzD,GAAGH,sBAAsB,CACzB,GAAGrE,iBAAiB,CACrB,CAAC,CAAC,CAEL,CAAE,MAAOW,KAAK,CAAE,KAAA8D,gBAAA,CAAAC,qBAAA,CACd9D,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,KAAM,CAAAgE,aAAa,CAAG,CACpBvE,EAAE,CAAE0C,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,CAAC,CAClB1B,IAAI,CAAE,WAAW,CACjB2B,OAAO,CAAE,gEAAgE,EAAAyB,gBAAA,CAAA9D,KAAK,CAACmB,QAAQ,UAAA2C,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBvC,IAAI,UAAAwC,qBAAA,iBAApBA,qBAAA,CAAsB/D,KAAK,GAAIA,KAAK,CAACF,OAAO,EAAI,eAAe,qBAAqB,CAC7JwC,SAAS,CAAE,GAAI,CAAAH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CACnCgB,OAAO,CAAE,IACX,CAAC,CACDjF,gBAAgB,CAAC,CAAC,GAAGkE,gBAAgB,CAAEwB,aAAa,CAAC,CAAC,CACtDnF,QAAQ,CAACmB,KAAK,CAACF,OAAO,EAAI,wBAAwB,CAAC,CACrD,CAAC,OAAS,CACRpB,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAsE,eAAe,CAAG,KAAO,CAAAxC,MAAM,EAAK,CACxC,GAAI,CACF,KAAM,CAAAW,QAAQ,CAAG,KAAM,CAAAvD,KAAK,CAACwD,IAAI,CAC/B,GAAG1D,MAAM,4BAA4B,CACrC,CACE8C,MAAM,CAAEA,MAAM,CAACE,IAAI,CACnBW,SAAS,CAAElD,QAAQ,CAACmD,IAAI,CACxBC,IAAI,CAAEf,MAAM,CAACe,IAAI,EAAI,CAAC,CAAC,CACvBzB,OAAO,CAAE,wBAAwBU,MAAM,CAACE,IAAI,EAC9C,CAAC,CACD,CAAEgB,OAAO,CAAE/D,UAAU,CAAC,CAAE,CAC1B,CAAC,CAED,MAAO,CACLgE,OAAO,CAAE,IAAI,CACbJ,IAAI,CAAEJ,QAAQ,CAACI,IAAI,CACnBf,MAAM,CAAEA,MACV,CAAC,CACH,CAAE,MAAOR,KAAK,CAAE,KAAAiE,gBAAA,CAAAC,qBAAA,CACdjE,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,CACL2B,OAAO,CAAE,KAAK,CACd3B,KAAK,CAAE,EAAAiE,gBAAA,CAAAjE,KAAK,CAACmB,QAAQ,UAAA8C,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB1C,IAAI,UAAA2C,qBAAA,iBAApBA,qBAAA,CAAsBlE,KAAK,GAAIA,KAAK,CAACF,OAAO,EAAI,eAAe,CACtEU,MAAM,CAAEA,MACV,CAAC,CACH,CACF,CAAC,CAED,KAAM,CAAA2D,kBAAkB,CAAGA,CAACrE,OAAO,CAAE3B,QAAQ,GAAK,CAChD,KAAM,CAAAiG,YAAY,CAAGtE,OAAO,CAAC8B,WAAW,CAAC,CAAC,CAC1C,KAAM,CAAAyC,UAAU,CAAG,CAAAlG,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEkG,UAAU,GAAI,EAAE,CAC7C,KAAM,CAAAC,UAAU,CAAGD,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,CAAEC,SAAS,QAAAC,gBAAA,OAAK,CAAAF,GAAG,EAAI,EAAAE,gBAAA,CAAAD,SAAS,CAACE,KAAK,UAAAD,gBAAA,iBAAfA,gBAAA,CAAiB5D,MAAM,GAAI,CAAC,CAAC,GAAE,CAAC,CAAC,CACjG,KAAM,CAAA8D,aAAa,CAAGP,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,CAAEC,SAAS,QAAAI,iBAAA,OACrD,CAAAL,GAAG,GAAAK,iBAAA,CAAGJ,SAAS,CAACE,KAAK,UAAAE,iBAAA,iBAAfA,iBAAA,CAAiBN,MAAM,CAAC,CAACO,OAAO,CAAEC,IAAI,QAAAC,cAAA,OAAK,CAAAF,OAAO,EAAI,EAAAE,cAAA,CAAAD,IAAI,CAACE,QAAQ,UAAAD,cAAA,iBAAbA,cAAA,CAAelE,MAAM,GAAI,CAAC,CAAC,GAAE,CAAC,CAAC,GAAI,CAAC,GAAE,CAAC,CAAC,CAEtG;AACA,KAAM,CAAAoE,cAAc,CAAG,CACrB1D,IAAI,CAAE,CAAArD,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEqD,IAAI,GAAI,cAAc,CACtC2D,cAAc,CAAEd,UAAU,CAACvD,MAAM,CACjCsE,SAAS,CAAEd,UAAU,CACrBe,YAAY,CAAET,aAAa,CAC3BU,cAAc,CAAEjB,UAAU,CAAChB,GAAG,CAACkC,CAAC,EAAIA,CAAC,CAAC/D,IAAI,CAAC,CAACgE,KAAK,CAAC,CAAC,CAAE,CAAC,CAAG;AAC3D,CAAC,CAED;AACA,GAAIpB,YAAY,CAACqB,QAAQ,CAAC,WAAW,CAAC,GAAKrB,YAAY,CAACqB,QAAQ,CAAC,KAAK,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE,CAC3I,MAAO,0CAA0CP,cAAc,CAAC1D,IAAI,0BAA0B0D,cAAc,CAACC,cAAc,gBAAgBD,cAAc,CAACI,cAAc,CAAChC,IAAI,CAAC,IAAI,CAAC,GAAG4B,cAAc,CAACC,cAAc,CAAG,CAAC,CAAG,KAAK,CAAG,EAAE;AAC1O;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mGAAmG,CAC/F,CAEA,GAAIf,YAAY,CAACqB,QAAQ,CAAC,UAAU,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,UAAU,CAAC,CAAE,CAC7G,KAAM,CAAAC,eAAe,CAAGrB,UAAU,CAAChB,GAAG,CAACoB,SAAS,EAAI,KAAAkB,iBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAClD,KAAM,CAAAT,SAAS,CAAG,EAAAO,iBAAA,CAAAlB,SAAS,CAACE,KAAK,UAAAgB,iBAAA,iBAAfA,iBAAA,CAAiB7E,MAAM,GAAI,CAAC,CAC9C,KAAM,CAAAgF,cAAc,CAAG,EAAAF,iBAAA,CAAAnB,SAAS,CAACE,KAAK,UAAAiB,iBAAA,kBAAAC,qBAAA,CAAfD,iBAAA,CAAiBtG,MAAM,CAACyF,IAAI,EAAIA,IAAI,CAACgB,MAAM,GAAK,WAAW,CAAC,UAAAF,qBAAA,iBAA5DA,qBAAA,CAA8D/E,MAAM,GAAI,CAAC,CAChG,MAAO,KAAK2D,SAAS,CAACjD,IAAI,KAAKsE,cAAc,IAAIV,SAAS,kBAAkB,CAC9E,CAAC,CAAC,CAAC9B,IAAI,CAAC,IAAI,CAAC,CAEb,MAAO,qCAAqC4B,cAAc,CAAC1D,IAAI;AACrE;AACA;AACA,IAAI0D,cAAc,CAACC,cAAc;AACjC,IAAID,cAAc,CAACE,SAAS;AAC5B,IAAIF,cAAc,CAACG,YAAY;AAC/B;AACA;AACA,EAAEK,eAAe;AACjB;AACA;AACA;AACA;AACA,oDAAoD,CAChD,CAEA,GAAItB,YAAY,CAACqB,QAAQ,CAAC,UAAU,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,MAAM,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,CAAE,CACzG,KAAM,CAAAO,cAAc,CAAG3B,UAAU,CAAC4B,OAAO,CAACxB,SAAS,OAAAyB,iBAAA,OACjD,EAAAA,iBAAA,CAAAzB,SAAS,CAACE,KAAK,UAAAuB,iBAAA,iBAAfA,iBAAA,CAAiB5G,MAAM,CAACyF,IAAI,EAAIA,IAAI,CAACgB,MAAM,GAAK,WAAW,CAAC,CAAC1C,GAAG,CAAC0B,IAAI,EACnE,IAAIA,IAAI,CAACvD,IAAI,QAAQiD,SAAS,CAACjD,IAAI,EACrC,CAAC,GAAI,EAAE,EACT,CAAC,CAACgE,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAEb,MAAO;AACb;AACA,EAAEQ,cAAc,CAAC3C,GAAG,CAAC0B,IAAI,EAAI,KAAKA,IAAI,EAAE,CAAC,CAACzB,IAAI,CAAC,IAAI,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD,CAC5C,CAEA,GAAIc,YAAY,CAACqB,QAAQ,CAAC,KAAK,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,KAAK,CAAC,CAAE,CACnG,GAAIrB,YAAY,CAACqB,QAAQ,CAAC,MAAM,CAAC,CAAE,CACjC,MAAO,qEAAqEP,cAAc,CAACC,cAAc;AACjH;AACA,EAAED,cAAc,CAACI,cAAc,CAACjC,GAAG,CAAC,CAAC7B,IAAI,CAAE2E,CAAC,GAAK,GAAGA,CAAC,CAAG,CAAC,KAAK3E,IAAI,EAAE,CAAC,CAAC8B,IAAI,CAAC,IAAI,CAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,CAC3B,CAEA,MAAO,sCAAsC4B,cAAc,CAAC1D,IAAI;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,CAC1C,CAEA,GAAI4C,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,CAAE,CACtE,MAAO;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF,CAChF,CAEA,GAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,MAAM,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,CAAE,CAC1I,MAAO,iDAAiDP,cAAc,CAAC1D,IAAI;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,CACjE,CAEA,GAAI4C,YAAY,CAACqB,QAAQ,CAAC,MAAM,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,iBAAiB,CAAC,EAAIrB,YAAY,CAACqB,QAAQ,CAAC,cAAc,CAAC,CAAE,CACtH,MAAO,sCAAsCP,cAAc,CAAC1D,IAAI;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iGAAiG,CAC7F,CAEA;AACA,MAAO,6BAA6B1B,OAAO;AAC/C;AACA,yBAAyBoF,cAAc,CAAC1D,IAAI,UAAU0D,cAAc,CAACC,cAAc,mBAAmBD,cAAc,CAACE,SAAS;AAC9H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+FAA+F,CAC7F,CAAC,CAED,KAAM,CAAAgB,cAAc,CAAItG,OAAO,EAAK,CAClC,KAAM,CAAA6C,OAAO,CAAG,EAAE,CAClB,KAAM,CAAAyB,YAAY,CAAGtE,OAAO,CAAC8B,WAAW,CAAC,CAAC,CAE1C;AACA,KAAM,CAAAyE,cAAc,CAAG,CACrB,CACErF,OAAO,CAAE,uCAAuC,CAChDN,IAAI,CAAE,eAAe,CACrB4F,UAAU,CAAE,GAAG,CACf7E,WAAW,CAAE,wBACf,CAAC,CACD,CACET,OAAO,CAAE,6BAA6B,CACtCN,IAAI,CAAE,eAAe,CACrB4F,UAAU,CAAE,GAAG,CACf7E,WAAW,CAAE,mBACf,CAAC,CACD,CACET,OAAO,CAAE,wBAAwB,CACjCN,IAAI,CAAE,UAAU,CAChB4F,UAAU,CAAE,GAAG,CACf7E,WAAW,CAAE,cACf,CAAC,CACD,CACET,OAAO,CAAE,2BAA2B,CACpCN,IAAI,CAAE,aAAa,CACnB4F,UAAU,CAAE,GAAG,CACf7E,WAAW,CAAE,iBACf,CAAC,CACD,CACET,OAAO,CAAE,uBAAuB,CAChCN,IAAI,CAAE,aAAa,CACnB4F,UAAU,CAAE,GAAG,CACf7E,WAAW,CAAE,aACf,CAAC,CACD,CACET,OAAO,CAAE,6BAA6B,CACtCN,IAAI,CAAE,aAAa,CACnB4F,UAAU,CAAE,GAAG,CACf7E,WAAW,CAAE,qBACf,CAAC,CACD,CACET,OAAO,CAAE,4BAA4B,CACrCN,IAAI,CAAE,eAAe,CACrB4F,UAAU,CAAE,GAAG,CACf7E,WAAW,CAAE,uBACf,CAAC,CACD,CACET,OAAO,CAAE,uBAAuB,CAChCN,IAAI,CAAE,WAAW,CACjB4F,UAAU,CAAE,GAAG,CACf7E,WAAW,CAAE,8BACf,CAAC,CACD,CACET,OAAO,CAAE,mBAAmB,CAC5BN,IAAI,CAAE,aAAa,CACnB4F,UAAU,CAAE,GAAG,CACf7E,WAAW,CAAE,4BACf,CAAC,CACD,CACET,OAAO,CAAE,8BAA8B,CACvCN,IAAI,CAAE,cAAc,CACpB4F,UAAU,CAAE,GAAG,CACf7E,WAAW,CAAE,wBACf,CAAC,CACF,CAED;AACA,KAAM,CAAAd,QAAQ,CAAG,CACfE,SAAS,CAAE,EAAE,CACbyE,cAAc,CAAE,EAAE,CAClBiB,KAAK,CAAE,EAAE,CACTC,UAAU,CAAE,EACd,CAAC,CAED;AACA,KAAM,CAAAC,UAAU,CAAG3G,OAAO,CAACmB,KAAK,CAAC,YAAY,CAAC,CAC9C,GAAIwF,UAAU,CAAE,CACd9F,QAAQ,CAACE,SAAS,CAAG4F,UAAU,CAACpD,GAAG,CAACqD,CAAC,EAAIA,CAAC,CAACC,OAAO,CAAC,IAAI,CAAE,EAAE,CAAC,CAAC,CAC/D,CAEA;AACA,KAAM,CAAAC,YAAY,CAAG9G,OAAO,CAACmB,KAAK,CAAC,6FAA6F,CAAC,CACjI,GAAI2F,YAAY,CAAE,CAChBjG,QAAQ,CAAC4F,KAAK,CAAGK,YAAY,CAC/B,CAEA;AACA,KAAM,CAAAC,gBAAgB,CAAG/G,OAAO,CAACmB,KAAK,CAAC,2DAA2D,CAAC,CACnG,GAAI4F,gBAAgB,CAAE,CACpBlG,QAAQ,CAAC6F,UAAU,CAAGK,gBAAgB,CACxC,CAEA;AACAR,cAAc,CAACS,OAAO,CAACC,KAAA,EAAgD,IAA/C,CAAE/F,OAAO,CAAEN,IAAI,CAAE4F,UAAU,CAAE7E,WAAY,CAAC,CAAAsF,KAAA,CAChE,GAAI/F,OAAO,CAACgG,IAAI,CAAC5C,YAAY,CAAC,CAAE,CAC9BzB,OAAO,CAACM,IAAI,CAAC,CACXvC,IAAI,CACJ4F,UAAU,CACV7E,WAAW,CACXd,QAAQ,CAAEA,QAAQ,CAClBF,eAAe,CAAEX,OACnB,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF,MAAO,CAAA6C,OAAO,CAChB,CAAC,CAED,KAAM,CAAAsE,cAAc,CAAIC,CAAC,EAAK,CAC5B,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,EAAI,CAACD,CAAC,CAACE,QAAQ,CAAE,CACpCF,CAAC,CAACG,cAAc,CAAC,CAAC,CAClBxH,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAyH,eAAe,CAAIhF,SAAS,EAAK,CACrC,MAAO,IAAI,CAAAH,IAAI,CAACG,SAAS,CAAC,CAACiF,kBAAkB,CAAC,OAAO,CAAE,CACrDC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CACJ,CAAC,CAED,mBACEzJ,KAAA,CAAClB,GAAG,EAAC4K,EAAE,CAAE,CAAEC,MAAM,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAS,CAAE,CAAAC,QAAA,eAEpEhK,IAAA,CAACd,KAAK,EACJ+K,SAAS,CAAE,CAAE,CACbL,EAAE,CAAE,CACFM,CAAC,CAAE,CAAC,CACJC,YAAY,CAAE,eAAe,CAC7BC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,MAAM,CACpBC,eAAe,CAAE,SACnB,CAAE,CAAAN,QAAA,cAEF9J,KAAA,CAAClB,GAAG,EAAC4K,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAES,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAR,QAAA,eACzDhK,IAAA,CAACX,MAAM,EACLuK,EAAE,CAAE,CACFU,eAAe,CAAE3K,eAAe,CAChC8K,KAAK,CAAE,EAAE,CACTZ,MAAM,CAAE,EACV,CAAE,CAAAG,QAAA,cAEFhK,IAAA,CAACN,OAAO,EAACgL,IAAI,CAAC,WAAW,CAACD,KAAK,CAAE,EAAG,CAACZ,MAAM,CAAE,EAAG,CAACc,KAAK,CAAC,MAAM,CAAE,CAAC,CAC1D,CAAC,cACTzK,KAAA,CAAClB,GAAG,EAAAgL,QAAA,eACFhK,IAAA,CAACf,UAAU,EACT2L,OAAO,CAAC,IAAI,CACZhB,EAAE,CAAE,CACFiB,UAAU,CAAE,kCAAkC,CAC9CC,UAAU,CAAE,GAAG,CACfH,KAAK,CAAE,MACT,CAAE,CAAAX,QAAA,CACH,kBAED,CAAY,CAAC,cACb9J,KAAA,CAACjB,UAAU,EACT2L,OAAO,CAAC,SAAS,CACjBhB,EAAE,CAAE,CACFe,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,kCACd,CAAE,CAAAb,QAAA,EACH,YACW,CAAC3J,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEqD,IAAI,EACf,CAAC,EACV,CAAC,cACN1D,IAAA,CAACT,IAAI,EACHwL,KAAK,CAAC,MAAM,CACZC,IAAI,CAAC,OAAO,CACZpB,EAAE,CAAE,CACFU,eAAe,CAAE,GAAG3K,eAAe,IAAI,CACvCgL,KAAK,CAAEhL,eAAe,CACtBmL,UAAU,CAAE,GAAG,CACfG,EAAE,CAAE,MACN,CAAE,CACH,CAAC,EACC,CAAC,CACD,CAAC,cAGRjL,IAAA,CAACd,KAAK,EACJ+K,SAAS,CAAE,CAAE,CACbL,EAAE,CAAE,CACFsB,IAAI,CAAE,CAAC,CACPd,MAAM,CAAE,mBAAmB,CAC3Be,SAAS,CAAE,MAAM,CACjBd,YAAY,CAAE,MAAM,CACpBe,QAAQ,CAAE,MAAM,CAChBlB,CAAC,CAAE,CAAC,CACJI,eAAe,CAAE,MACnB,CAAE,CAAAN,QAAA,CAEDzJ,aAAa,CAACyC,MAAM,GAAK,CAAC,cACzB9C,KAAA,CAAClB,GAAG,EACF4K,EAAE,CAAE,CACFE,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBQ,UAAU,CAAE,QAAQ,CACpBc,cAAc,CAAE,QAAQ,CACxBxB,MAAM,CAAE,MAAM,CACdyB,SAAS,CAAE,QACb,CAAE,CAAAtB,QAAA,eAEFhK,IAAA,CAACX,MAAM,EACLuK,EAAE,CAAE,CACFU,eAAe,CAAE,GAAG3K,eAAe,IAAI,CACvC8K,KAAK,CAAE,EAAE,CACTZ,MAAM,CAAE,EAAE,CACV0B,EAAE,CAAE,CACN,CAAE,CAAAvB,QAAA,cAEFhK,IAAA,CAACN,OAAO,EAACgL,IAAI,CAAC,WAAW,CAACD,KAAK,CAAE,EAAG,CAACZ,MAAM,CAAE,EAAG,CAACc,KAAK,CAAEhL,eAAgB,CAAE,CAAC,CACrE,CAAC,cACTK,IAAA,CAACf,UAAU,EACT2L,OAAO,CAAC,IAAI,CACZhB,EAAE,CAAE,CACFiB,UAAU,CAAE,kCAAkC,CAC9CC,UAAU,CAAE,GAAG,CACfH,KAAK,CAAE,MAAM,CACbY,EAAE,CAAE,CACN,CAAE,CAAAvB,QAAA,CACH,6BAED,CAAY,CAAC,cACbhK,IAAA,CAACf,UAAU,EACT2L,OAAO,CAAC,OAAO,CACfhB,EAAE,CAAE,CACFe,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,kCAAkC,CAC9CW,QAAQ,CAAE,GACZ,CAAE,CAAAxB,QAAA,CACH,+GAED,CAAY,CAAC,EACV,CAAC,cAEN9J,KAAA,CAAClB,GAAG,EAAAgL,QAAA,EACDzJ,aAAa,CAACgF,GAAG,CAAEvD,OAAO,eACzBhC,IAAA,CAAChB,GAAG,EAEF4K,EAAE,CAAE,CACFE,OAAO,CAAE,MAAM,CACfuB,cAAc,CAAErJ,OAAO,CAACY,IAAI,GAAK,MAAM,CAAG,UAAU,CAAG,YAAY,CACnE2I,EAAE,CAAE,CACN,CAAE,CAAAvB,QAAA,cAEF9J,KAAA,CAAClB,GAAG,EACF4K,EAAE,CAAE,CACF4B,QAAQ,CAAE,KAAK,CACf1B,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE/H,OAAO,CAACY,IAAI,GAAK,MAAM,CAAG,aAAa,CAAG,KAAK,CAC9D2H,UAAU,CAAE,YAAY,CACxBC,GAAG,CAAE,CACP,CAAE,CAAAR,QAAA,eAEFhK,IAAA,CAACX,MAAM,EACLuK,EAAE,CAAE,CACFa,KAAK,CAAE,EAAE,CACTZ,MAAM,CAAE,EAAE,CACVS,eAAe,CAAEtI,OAAO,CAACY,IAAI,GAAK,MAAM,CAAG,SAAS,CAAGjD,eACzD,CAAE,CAAAqK,QAAA,cAEFhK,IAAA,CAACN,OAAO,EACNgL,IAAI,CAAE1I,OAAO,CAACY,IAAI,GAAK,MAAM,CAAG,yBAAyB,CAAG,WAAY,CACxE6H,KAAK,CAAE,EAAG,CACVZ,MAAM,CAAE,EAAG,CACXc,KAAK,CAAE3I,OAAO,CAACY,IAAI,GAAK,MAAM,CAAG,MAAM,CAAG,MAAO,CAClD,CAAC,CACI,CAAC,cACT1C,KAAA,CAAClB,GAAG,EAAAgL,QAAA,eACF9J,KAAA,CAAChB,KAAK,EACJ+K,SAAS,CAAE,CAAE,CACbL,EAAE,CAAE,CACFM,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,MAAM,CACpBG,eAAe,CAAEtI,OAAO,CAACY,IAAI,GAAK,MAAM,CAAGjD,eAAe,CAAG,SAAS,CACtEgL,KAAK,CAAE3I,OAAO,CAACY,IAAI,GAAK,MAAM,CAAG,MAAM,CAAG,MAAM,CAChDwH,MAAM,CAAEpI,OAAO,CAACyD,OAAO,CAAG,mBAAmB,CAAG,MAClD,CAAE,CAAAuE,QAAA,eAEFhK,IAAA,CAACf,UAAU,EACT2L,OAAO,CAAC,OAAO,CACfhB,EAAE,CAAE,CACFiB,UAAU,CAAE,kCAAkC,CAC9CY,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE,UACd,CAAE,CAAA1B,QAAA,CAEDhI,OAAO,CAACuC,OAAO,CACN,CAAC,CAGZvC,OAAO,CAACY,IAAI,GAAK,WAAW,EAAIZ,OAAO,CAAC6C,OAAO,EAAI7C,OAAO,CAAC6C,OAAO,CAAC7B,MAAM,CAAG,CAAC,eAC5EhD,IAAA,CAAChB,GAAG,EAAC4K,EAAE,CAAE,CAAE+B,EAAE,CAAE,GAAG,CAAE7B,OAAO,CAAE,MAAM,CAAE8B,QAAQ,CAAE,MAAM,CAAEpB,GAAG,CAAE,CAAE,CAAE,CAAAR,QAAA,CAC7DhI,OAAO,CAAC6C,OAAO,CAAC6C,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACnC,GAAG,CAAC,CAAC7C,MAAM,CAAEmJ,WAAW,gBACnD7L,IAAA,CAACT,IAAI,EAEHwL,KAAK,CAAErI,MAAM,CAACiB,WAAY,CAC1BqH,IAAI,CAAC,OAAO,CACZc,OAAO,CAAEA,CAAA,GAAM,CACb;AACApL,iBAAiB,CAACgC,MAAM,CAACC,eAAe,EAAI,UAAUD,MAAM,CAACiB,WAAW,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,CACzF,GAAIhD,QAAQ,CAACwB,OAAO,CAAE,CACpBxB,QAAQ,CAACwB,OAAO,CAACyJ,KAAK,CAAC,CAAC,CAC1B,CACF,CAAE,CACFnC,EAAE,CAAE,CACFU,eAAe,CAAE,MAAM,CACvBF,MAAM,CAAE,aAAazK,eAAe,EAAE,CACtCgL,KAAK,CAAEhL,eAAe,CACtBqM,QAAQ,CAAE,QAAQ,CAClBnC,MAAM,CAAE,MAAM,CACdoC,MAAM,CAAE,SAAS,CACjB,SAAS,CAAE,CACT3B,eAAe,CAAE,GAAG3K,eAAe,IACrC,CACF,CAAE,EApBGkM,WAqBN,CACF,CAAC,CACC,CACN,EACI,CAAC,cACR7L,IAAA,CAACf,UAAU,EACT2L,OAAO,CAAC,SAAS,CACjBhB,EAAE,CAAE,CACFe,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,kCAAkC,CAC9CmB,QAAQ,CAAE,QAAQ,CAClBL,EAAE,CAAE,GAAG,CACP7B,OAAO,CAAE,OAAO,CAChBwB,SAAS,CAAEtJ,OAAO,CAACY,IAAI,GAAK,MAAM,CAAG,OAAO,CAAG,MACjD,CAAE,CAAAoH,QAAA,CAEDR,eAAe,CAACxH,OAAO,CAACwC,SAAS,CAAC,CACzB,CAAC,EACV,CAAC,EACH,CAAC,EAjGDxC,OAAO,CAACL,EAkGV,CACN,CAAC,CACDhB,SAAS,eACRX,IAAA,CAAChB,GAAG,EAAC4K,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEuB,cAAc,CAAE,YAAY,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAvB,QAAA,cAChE9J,KAAA,CAAClB,GAAG,EAAC4K,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAES,UAAU,CAAE,YAAY,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAR,QAAA,eAC7DhK,IAAA,CAACX,MAAM,EACLuK,EAAE,CAAE,CACFa,KAAK,CAAE,EAAE,CACTZ,MAAM,CAAE,EAAE,CACVS,eAAe,CAAE3K,eACnB,CAAE,CAAAqK,QAAA,cAEFhK,IAAA,CAACN,OAAO,EAACgL,IAAI,CAAC,WAAW,CAACD,KAAK,CAAE,EAAG,CAACZ,MAAM,CAAE,EAAG,CAACc,KAAK,CAAC,MAAM,CAAE,CAAC,CAC1D,CAAC,cACTzK,KAAA,CAAChB,KAAK,EACJ+K,SAAS,CAAE,CAAE,CACbL,EAAE,CAAE,CACFM,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,MAAM,CACpBG,eAAe,CAAE,SAAS,CAC1BR,OAAO,CAAE,MAAM,CACfS,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,CACP,CAAE,CAAAR,QAAA,eAEFhK,IAAA,CAACV,gBAAgB,EAAC0L,IAAI,CAAE,EAAG,CAACpB,EAAE,CAAE,CAAEe,KAAK,CAAEhL,eAAgB,CAAE,CAAE,CAAC,cAC9DK,IAAA,CAACf,UAAU,EACT2L,OAAO,CAAC,OAAO,CACfhB,EAAE,CAAE,CACFiB,UAAU,CAAE,kCAAkC,CAC9CF,KAAK,CAAE,MACT,CAAE,CAAAX,QAAA,CACH,aAED,CAAY,CAAC,EACR,CAAC,EACL,CAAC,CACH,CACN,cACDhK,IAAA,QAAKkM,GAAG,CAAErL,cAAe,CAAE,CAAC,EACzB,CACN,CACI,CAAC,cAGRb,IAAA,CAACd,KAAK,EACJ+K,SAAS,CAAE,CAAE,CACbL,EAAE,CAAE,CACFM,CAAC,CAAE,CAAC,CACJC,YAAY,CAAE,eAAe,CAC7BC,MAAM,CAAE,mBAAmB,CAC3Be,SAAS,CAAE,MACb,CAAE,CAAAnB,QAAA,cAEF9J,KAAA,CAAClB,GAAG,EAAC4K,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEU,GAAG,CAAE,CAAC,CAAED,UAAU,CAAE,UAAW,CAAE,CAAAP,QAAA,eAC3DhK,IAAA,CAACb,SAAS,EACR2B,QAAQ,CAAEA,QAAS,CACnBqL,KAAK,CAAE1L,cAAe,CACtB2L,QAAQ,CAAGhD,CAAC,EAAK1I,iBAAiB,CAAC0I,CAAC,CAACiD,MAAM,CAACF,KAAK,CAAE,CACnDG,UAAU,CAAEnD,cAAe,CAC3BoD,WAAW,CAAC,uCAAuC,CACnDC,SAAS,MACTC,OAAO,CAAE,CAAE,CACXC,SAAS,MACT9B,OAAO,CAAC,UAAU,CAClB+B,QAAQ,CAAEhM,SAAU,CACpBiJ,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BO,YAAY,CAAE,KAAK,CACnBU,UAAU,CAAE,kCACd,CACF,CAAE,CACH,CAAC,cACF7K,IAAA,CAACR,OAAO,EAACoN,KAAK,CAAC,cAAc,CAAA5C,QAAA,cAC3BhK,IAAA,CAACZ,UAAU,EACT0M,OAAO,CAAEA,CAAA,GAAM/J,iBAAiB,CAAC,CAAE,CACnC4K,QAAQ,CAAE,CAAClM,cAAc,CAAC2C,IAAI,CAAC,CAAC,EAAIzC,SAAU,CAC9CiJ,EAAE,CAAE,CACFU,eAAe,CAAE7J,cAAc,CAAC2C,IAAI,CAAC,CAAC,EAAI,CAACzC,SAAS,CAAGhB,eAAe,CAAG,SAAS,CAClFgL,KAAK,CAAElK,cAAc,CAAC2C,IAAI,CAAC,CAAC,EAAI,CAACzC,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5D,SAAS,CAAE,CACT2J,eAAe,CAAE7J,cAAc,CAAC2C,IAAI,CAAC,CAAC,EAAI,CAACzC,SAAS,CAAG,SAAS,CAAG,SACrE,CACF,CAAE,CAAAqJ,QAAA,cAEFhK,IAAA,CAACN,OAAO,EAACgL,IAAI,CAAC,uBAAuB,CAACD,KAAK,CAAE,EAAG,CAACZ,MAAM,CAAE,EAAG,CAAE,CAAC,CACrD,CAAC,CACN,CAAC,EACP,CAAC,CACD,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAA1J,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
# Generated by Django 5.0.1 on 2024-01-27 04:19

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assistant', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_id', models.Char<PERSON>ield(max_length=255, null=True)),
                ('value', models.Char<PERSON>ield(max_length=255, null=True)),
                ('thread', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='assistant.thread')),
            ],
            options={
                'db_table': 'messages',
            },
        ),
    ]

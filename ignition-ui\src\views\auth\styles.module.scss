.loginContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem 1rem;
  font-family: 'Recursive Variable', sans-serif !important;
}

.fontRecursive {
  font-family: 'Recursive Variable', sans-serif !important;
}

.loginCard {
  width: 100%;
  border-radius: 12px !important;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(240, 165, 0, 0.08) !important;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 12px 28px rgba(240, 165, 0, 0.12) !important;
  }
}

.cardHeader {
  padding: 1.5rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background-color: #1e1e2d;
  color: white;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('https://demos.creative-tim.com/vision-ui-dashboard-pro/static/media/curved14.12a9a9d9.jpg') no-repeat center center;
    background-size: cover;
    opacity: 0.4;
    z-index: 0;
  }
}

.headerContent {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
  width: 100%;
}

.logo {
  width: 60px;
  height: 60px;
  margin-right: 1.5rem;
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.headerTextContainer {
  display: flex;
  flex-direction: column;
}

.headerTitle {
  font-size: 1.75rem !important;
  font-weight: 700 !important;
  margin-bottom: 0.5rem !important;
  font-family: 'Recursive Variable', sans-serif !important;
  letter-spacing: 0.5px !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: white !important;
}

.headerSubtitle {
  font-size: 1rem !important;
  opacity: 0.9 !important;
  font-family: 'Recursive Variable', sans-serif !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

.divider {
  display: flex;
  align-items: center;
  margin: 1.5rem 0;
  color: #666;
  font-family: 'Recursive Variable', sans-serif !important;

  &::before,
  &::after {
    content: "";
    flex: 1;
    border-bottom: 1px solid rgba(240, 165, 0, 0.2);
  }

  &::before {
    margin-right: 1rem;
  }

  &::after {
    margin-left: 1rem;
  }
}

.formGroup {
  margin-bottom: 2rem;
}

.inputField {
  transition: all 0.3s ease;

  &:focus-within {
    transform: none;
  }
}

.errorMessage {
  color: #f44336;
  font-size: 0.8rem;
  margin-top: 0.5rem;
  margin-left: 0.5rem;
  font-family: 'Recursive Variable', sans-serif !important;
}

.submitBtn {
  width: 100%;
  max-width: 200px;
  height: 44px;
  font-size: 16px !important;
  font-family: 'Recursive Variable', sans-serif !important;
  background: linear-gradient(135deg, #F0A500 30%, #E45826 100%) !important;
  color: white !important;
  text-transform: none !important;
  font-weight: bold !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(240, 165, 0, 0.3) !important;
  transition: all 0.3s ease !important;

  &:hover {
    background: linear-gradient(135deg, #F0A500 40%, #E45826 100%) !important;
    box-shadow: 0 6px 16px rgba(240, 165, 0, 0.4) !important;
  }

  &:active {
    transform: translateY(1px);
  }
}

.googleButton {
  width: 100%;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;

  &:hover {
    box-shadow: 0 4px 12px rgba(240, 165, 0, 0.15) !important;
  }
}

.linksContainer {
  display: flex;
  justify-content: space-between;
  margin-top: 1.5rem;
  width: 100%;
  padding: 0 0.5rem;
}

.customeALink {
  font-size: 0.95rem;
  font-family: 'Recursive Variable', sans-serif !important;
  color: #555;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  background-color: rgba(240, 165, 0, 0.05);
  display: flex;
  align-items: center;

  &:hover {
    color: #F0A500;
    background-color: rgba(240, 165, 0, 0.1);
    box-shadow: 0 2px 8px rgba(240, 165, 0, 0.15);
  }
}

.linkIcon {
  margin-right: 0.5rem;
  color: #F0A500;
}

@keyframes successIconPulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  60% {
    transform: scale(1.2);
    opacity: 1;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.successIcon {
  animation: successIconPulse 1s cubic-bezier(0.19, 1, 0.22, 1);
  animation-fill-mode: forwards;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.7));
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.successTitle {
  animation: fadeInUp 0.8s ease-out 0.7s;
  animation-fill-mode: backwards;
}

.successMessage {
  animation: fadeInUp 0.8s ease-out 1s;
  animation-fill-mode: backwards;
}

@keyframes borderPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(46, 125, 50, 0.4);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(46, 125, 50, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(46, 125, 50, 0);
  }
}

.successIconContainer {
  animation: borderPulse 2s infinite;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 50%;
    border: 2px solid rgba(46, 125, 50, 0.3);
    animation: spin 10s linear infinite;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

# AI Agent Chatbot Setup Guide

## Overview

The AI Agent functionality has been enhanced to provide intelligent chatbot interactions with complete plan context. The system now uses external AI APIs (OpenRouter with DeepSeek model) instead of hardcoded responses.

## Key Features

### 1. **Complete Plan Context Integration**
- AI receives full project structure (milestones, tasks, subtasks)
- Project statistics and progress information
- Real-time plan state for accurate responses

### 2. **External AI API Integration**
- Uses OpenRouter with `deepseek/deepseek-r1-0528:free` model
- Fallback to OpenAI if needed
- Configurable AI providers

### 3. **Structured Action Execution**
- AI can generate structured actions in JSON format
- Actions are automatically executed via existing APIs
- Supports: add_milestone, add_task, add_subtask, complete_task, update_task, etc.

### 4. **Enhanced UI Integration**
- ChatbotBar redirects conversations to Agent tab
- Real-time conversation history
- Action execution feedback

## API Endpoints

### New Endpoint: `/api/assistant/agent-chat`
**POST** - Generate AI response with plan context

**Request:**
```json
{
  "message": "Add a milestone for user testing",
  "plan_slug": "project-slug-here"
}
```

**Response:**
```json
{
  "message": "I'll add a user testing milestone to your project...",
  "actions": [
    {
      "type": "add_milestone",
      "data": {
        "name": "User Testing Phase",
        "description": "Comprehensive user testing and feedback collection"
      }
    }
  ],
  "metadata": {
    "model": "deepseek/deepseek-r1-0528:free",
    "provider": "openrouter",
    "plan_context_included": true
  }
}
```

### Enhanced Endpoint: `/api/assistant/plan-action`
**POST** - Execute specific actions on plans

**Supported Actions:**
- `add_milestone` - Create new milestone
- `add_task` - Add task to milestone
- `add_subtask` - Add subtask to task
- `update_task_status` - Change task status
- `complete_task` - Mark task as completed
- `update_milestone` - Update milestone details
- `update_task` - Update task details

## Environment Configuration

Add these environment variables to your `.env` file:

```bash
# AI Provider Configuration
AI_PROVIDER=openrouter
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_DEFAULT_MODEL=deepseek/deepseek-r1-0528:free
OPENROUTER_APP_NAME=Ignition
OPENROUTER_SITE_URL=https://your-domain.com

# Fallback OpenAI (optional)
OPENAI_API_KEY=your_openai_key_here
OPENAI_DEFAULT_MODEL=gpt-4o-mini
```

## How It Works

### 1. **User Interaction Flow**
1. User types message in ChatbotBar or AgentTab
2. Message + complete plan context sent to AI API
3. AI analyzes request and generates response + actions
4. Actions automatically executed via existing APIs
5. Plan refreshed if modifications were made

### 2. **Plan Context Structure**
The AI receives complete project information:
```json
{
  "plan": {
    "id": 1,
    "name": "Project Name",
    "description": "Project description",
    "status": "completed"
  },
  "milestones": [
    {
      "id": 1,
      "name": "Milestone 1",
      "tasks": [
        {
          "id": 1,
          "name": "Task 1",
          "status": 2,
          "subtasks": [...]
        }
      ]
    }
  ],
  "statistics": {
    "total_milestones": 3,
    "total_tasks": 15,
    "completed_tasks": 8
  }
}
```

### 3. **AI Response Processing**
- AI generates conversational response
- Extracts structured actions from response
- Actions executed automatically
- Results fed back to user

## Frontend Integration

### AgentTab Component
- Updated to use new AI endpoint
- Handles action execution
- Provides real-time feedback
- Maintains conversation history

### ChatbotBar Component
- Redirects to Agent tab for processing
- Maintains context between components
- Provides quick access to AI assistant

## Error Handling

### Fallback Mechanisms
1. **AI API Failure**: Falls back to helpful error message
2. **Action Execution Failure**: Reports specific errors to user
3. **Permission Issues**: Proper access control validation
4. **Invalid Requests**: Clear error messages

### Logging
- All AI interactions logged for debugging
- Action execution results tracked
- Error details preserved for troubleshooting

## Testing

### Manual Testing
1. Create a test project with milestones and tasks
2. Use ChatbotBar to ask: "Add a milestone for deployment"
3. Verify AI response and milestone creation
4. Test various action types and edge cases

### API Testing
```bash
# Test AI chat endpoint
curl -X POST http://localhost:8000/api/assistant/agent-chat \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "Show project progress", "plan_slug": "test-project"}'
```

## Troubleshooting

### Common Issues
1. **No AI Response**: Check OPENROUTER_API_KEY configuration
2. **Actions Not Executing**: Verify plan permissions
3. **Context Missing**: Ensure plan has milestones/tasks
4. **Rate Limiting**: OpenRouter free tier has usage limits

### Debug Mode
Enable debug logging in Django settings to see detailed AI interaction logs.

## Future Enhancements

### Planned Features
- Multi-turn conversations with memory
- Advanced action types (move tasks, bulk operations)
- Integration with external tools
- Voice interaction support
- Custom AI model fine-tuning

### Performance Optimizations
- Response caching for common queries
- Batch action execution
- Optimized plan context serialization
- Background processing for complex operations

import { useState } from 'react';
import dayjs from 'dayjs';

const useGrouping = (plans) => {
  const [groupBy, setGroupBy] = useState('none');
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'grid'

  const handleViewModeChange = (event, newMode) => {
    if (newMode !== null) {
      setViewMode(newMode);
    }
  };

  const handleGroupByChange = (event, newGroupBy) => {
    if (newGroupBy !== null) {
      setGroupBy(newGroupBy);
    }
  };

  const getGroupedPlans = () => {
    if (groupBy === 'none') {
      return { 'All plans': plans };
    } else if (groupBy === 'status') {
      // Group plans by status
      const grouped = {
        'Not Started': [],
        'In Progress': [],
        'Completed': []
      };

      plans.forEach(plan => {
        // Calculate progress based on task status
        let completedTasks = 0;
        let totalTasks = 0;

        plan.milestones?.forEach(milestone => {
          milestone.tasks?.forEach(task => {
            totalTasks++;
            if (task.status === 3) { // Assuming status 3 is completed
              completedTasks++;
            }
          });
        });

        const progress = totalTasks > 0 ? Math.floor((completedTasks / totalTasks) * 100) : 0;

        if (progress === 0) {
          grouped['Not Started'].push(plan);
        } else if (progress === 100) {
          grouped['Completed'].push(plan);
        } else {
          grouped['In Progress'].push(plan);
        }
      });

      // Remove empty groups
      return Object.fromEntries(
        Object.entries(grouped).filter(([_, groupPlans]) => groupPlans.length > 0)
      );
    } else if (groupBy === 'date') {
      const grouped = {
        'Today': [],
        'This Week': [],
        'This Month': [],
        'Older': []
      };

      const today = dayjs().startOf('day');
      const startOfWeek = dayjs().startOf('week');
      const startOfMonth = dayjs().startOf('month');

      plans.forEach(plan => {
        const planDate = dayjs(plan.created_at);

        if (planDate.isAfter(today)) {
          grouped['Today'].push(plan);
        } else if (planDate.isAfter(startOfWeek)) {
          grouped['This Week'].push(plan);
        } else if (planDate.isAfter(startOfMonth)) {
          grouped['This Month'].push(plan);
        } else {
          grouped['Older'].push(plan);
        }
      });

      // Remove empty groups
      return Object.fromEntries(
        Object.entries(grouped).filter(([_, plans]) => plans.length > 0)
      );
    }

    return { 'All plans': plans };
  };

  return {
    viewMode,
    groupBy,
    handleViewModeChange,
    handleGroupByChange,
    getGroupedPlans
  };
};

export default useGrouping;

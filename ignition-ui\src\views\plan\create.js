import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { Button, Container, Box, Typography, Tooltip, IconButton, Paper, Divider } from '@mui/material';
import LinearProgress from '@mui/material/LinearProgress';
import { getHeaders } from "helpers/functions";
import { APIURL } from "helpers/constants";
import { useNavigate } from 'react-router-dom';
import TextAreaBase from 'components/Input/TextAreaBase';
import Iconify from 'components/Iconify/index';
import HexagonBallLoading from 'components/Loading/HexagonBallLoading';
import styles from './styles.module.scss';

//--------------------------------------------------------------------------------------------------

const CreatePlan = () => {
  const [promptInput, setPromptInput] = useState('');
  const [language] = useState('English'); // Default to English
  const [plannerRole] = useState('Project Manager'); // Default role
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const [planSlug, setPlanSlug] = useState('');
  const [isPlanCreated, setIsPlanCreated] = useState(false);
  const [planId, setPlanId] = useState(null);
  const [planStatus, setPlanStatus] = useState('');
  const [statusMessage, setStatusMessage] = useState('');
  const pollingIntervalRef = useRef(null);

  // Hàm kiểm tra trạng thái kế hoạch
  const checkPlanStatus = useCallback(async () => {
    if (!planId) return;

    try {
      const response = await axios.get(`${APIURL}/api/assistant/plan-status/${planId}`, {
        headers: getHeaders()
      });

      const { status, slug } = response.data;
      setPlanStatus(status);

      // Handle different statuses
      switch (status) {
        case 'pending':
          setStatusMessage('Preparing to create plan...');
          break;
        case 'processing':
          setStatusMessage('Creating plan, please wait...');
          break;
        case 'completed':
          setStatusMessage('Plan has been created successfully!');
          
          // Just use the slug from status API response
          setPlanSlug(slug);
          setIsPlanCreated(true);
          setLoading(false);
          // Stop polling when plan is completed
          clearInterval(pollingIntervalRef.current);
          break;
        case 'failed':
          setStatusMessage('An error occurred while creating the plan. Please try again.');
          setLoading(false);
          clearInterval(pollingIntervalRef.current);
          break;
        default:
          setStatusMessage('Processing...');
      }
    } catch (error) {
      console.error("Error checking plan status:", error);
      setStatusMessage('Unable to check plan status. Please try again.');
      setLoading(false);
      clearInterval(pollingIntervalRef.current);
    }
  }, [planId]);

  // Stop polling when component unmounts
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, []);

  // Set up polling when planId changes
  useEffect(() => {
    if (planId && (planStatus === 'pending' || planStatus === 'processing')) {
      // Stop old interval if exists
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }

      // Check immediately
      checkPlanStatus();

      // Set up new interval
      pollingIntervalRef.current = setInterval(checkPlanStatus, 10000); // Check every 10 seconds

      return () => {
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
        }
      };
    }
  }, [planId, planStatus, checkPlanStatus]);

  const handleCreate = async () => {
    if (!promptInput.trim()) {
      setError('Please describe what you want from this project.');
      return;
    }

    setLoading(true);
    setStatusMessage('Starting to create plan...');

    try {
      const formData = new FormData();
      formData.append("prompt", promptInput);
      formData.append("language", language);
      formData.append("role", plannerRole);

      const response = await axios.post(`${APIURL}/api/assistant/create-planner-by-chat`,
        formData,
        { headers: getHeaders() }
      );

      // Save plan_id for polling
      setPlanId(response.data.plan_id);
      setPlanStatus('pending');

      // Polling will be set up automatically through useEffect

    } catch (error) {
      console.error("Plan generation faced an error", error);
      setStatusMessage('An error occurred while creating the plan. Please try again.');
      setLoading(false);
    }
  };

  const handleNavigateToPlan = () => {
    navigate("/d/plan/" + planSlug, { replace: true });
  };

  const handleResetForm = () => {
    setLoading(false);
    setIsPlanCreated(false);
    setPlanId(null);
    setPlanStatus('');
    setStatusMessage('');
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }
  };

  return (
    <Container className={styles.mainCreateContainer}>
      <Box className={styles.boxWrapper}>
        {!loading && !isPlanCreated && (
          <Paper elevation={3} className={styles.paperContent}>
            {/* Header Section with Rocket Icon */}
            <Box sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center',
              textAlign: 'center',
              mb: 5,
              pt: 2
            }}>
              <Box sx={{ 
                backgroundColor: 'rgba(240, 165, 0, 0.1)', 
                borderRadius: '50%', 
                p: 2,
                mb: 3,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                width: 100,
                height: 100,
                boxShadow: '0 4px 20px rgba(240, 165, 0, 0.2)'
              }}>
                <Iconify icon="mdi:rocket-launch" width={60} height={60} color="#F0A500" />
              </Box>
              <Typography variant="h4" className={styles.paperTitle} sx={{ mb: 2 }}>
                Welcome to the Ignition
              </Typography>
              <Typography variant="body1" className={styles.paperBodyContent} sx={{ maxWidth: '700px' }}>
                This form will help you create a new project plan. Please describe what you want from your project in detail, and then click "Generate" to create your plan.
              </Typography>
            </Box>

            <Divider sx={{ mb: 5, borderColor: 'rgba(0,0,0,0.1)' }} />

            {/* Form Section */}
            <Box className={styles.formSection}>
              <Box className={styles.boxInputContent}>
                <Box display="flex" alignItems="center" justifyContent='space-between'>
                  <Typography variant="h6" className={styles.titleInput} sx={{ display: 'flex', alignItems: 'center' }}>
                    <Iconify icon="mdi:target" width={24} height={24} color="#F0A500" style={{ marginRight: '12px' }} />
                    What do you want out from this project?
                  </Typography>
                  <Tooltip title={<>
                    <strong>What is this field?</strong><br />
                    This field is for describing your project. Please provide as much detail as possible to help the AI understand your requirements.<br />
                    <strong>What should you include?</strong><br />
                    - Brief description of the project.<br />
                    - Key objectives and goals.<br />
                    - Any specific tasks or milestones.<br />
                    The more detailed you are, the better the generated plan will be.
                  </>}>
                    <IconButton size="small">
                      <Iconify icon="octicon:info-16" width={18} height={18} color="#F0A500" />
                    </IconButton>
                  </Tooltip>
                </Box>
                <TextAreaBase
                  id="prompt"
                  value={promptInput}
                  handleChange={setPromptInput}
                  minRows={5}
                  multiline
                  placeholder="E.g., I need a comprehensive testing plan for a new mobile app. The app has user authentication, profile management, content browsing, and payment features. I want to ensure all features work correctly across different devices and platforms..."
                  errorText={error && !promptInput ? error : ''}
                  required
                  sx={{ 
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '12px',
                      '&.Mui-focused fieldset': {
                        borderColor: '#F0A500',
                        borderWidth: '2px'
                      }
                    }
                  }}
                />
                <Typography variant="caption" sx={{ display: 'block', mt: 1, color: '#666', fontStyle: 'italic' }}>
                  Suggestion: Be specific about your project scope, timeline, and expected outcomes. Include any technical requirements or constraints.
                </Typography>
              </Box>

              <Button
                variant="contained" 
                onClick={handleCreate} 
                fullWidth 
                className={styles.genPlanBtn}
                startIcon={<Iconify icon="mingcute:ai-line" width={24} height={24} />}
                sx={{ 
                  mt: 5,
                  height: '60px',
                  borderRadius: '16px',
                  boxShadow: '0 6px 16px rgba(240, 165, 0, 0.3)',
                  fontSize: '1.2rem',
                  fontWeight: 700
                }}
              >
                GENERATE
              </Button>
            </Box>
          </Paper>
        )}

        {(loading || isPlanCreated) && (
          <Box className={styles.loadingBox}>
            {loading && (
              <Box className={styles.loadingContainer}>
                <Box className={styles.hexagonContainer}>
                  <HexagonBallLoading fromCreatePlan={true} />
                </Box>

                <Paper elevation={4} className={styles.loadingCard}>
                  <Box sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    height: '4px',
                    bgcolor: 'rgba(240, 165, 0, 0.2)'
                  }}>
                    <Box sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      height: '100%',
                      width: '30%',
                      bgcolor: '#F0A500',
                      animation: 'loadingProgress 2s infinite ease-in-out'
                    }} />
                  </Box>

                  <Typography variant="h5" align="center" className={styles.titleGenerating}>
                    {statusMessage || 'Creating plan, please wait...'}
                  </Typography>
                  
                  <Box sx={{ 
                    width: '100%', 
                    mt: 4, 
                    mb: 3, 
                    display: 'flex', 
                    justifyContent: 'center'
                  }}>
                    <Box sx={{ position: 'relative', width: '80%' }}>
                      <LinearProgress 
                        color="inherit" 
                        sx={{ 
                          height: 8, 
                          borderRadius: 4,
                          bgcolor: 'rgba(240, 165, 0, 0.15)',
                          '& .MuiLinearProgress-bar': {
                            bgcolor: '#F0A500',
                          }
                        }} 
                      />
                    </Box>
                  </Box>
                  
                  <Typography variant="body2" sx={{ 
                    mt: 3, 
                    color: '#666', 
                    fontStyle: 'italic',
                    px: 2
                  }}>
                    This may take a minute or two. We're crafting a detailed plan for you.
                  </Typography>
                </Paper>
              </Box>
            )}

            {isPlanCreated && (
              <Box sx={{ textAlign: 'center', maxWidth: '500px', p: 5, bgcolor: 'white', borderRadius: '16px', boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)' }}>
                <Box sx={{ 
                  backgroundColor: 'rgba(76, 175, 80, 0.1)', 
                  borderRadius: '50%', 
                  p: 2,
                  mb: 3,
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: 100,
                  height: 100,
                  margin: '0 auto',
                  boxShadow: '0 4px 20px rgba(76, 175, 80, 0.2)'
                }}>
                  <Iconify icon="mdi:check-circle" width={64} height={64} color="#4CAF50" />
                </Box>
                <Typography variant="h5" align="center" className={styles.titleGenerating} sx={{ color: '#333', animation: 'none' }}>
                  Plan has been created successfully!
                </Typography>
                <Typography variant="body1" sx={{ mt: 3, mb: 5, color: '#555' }}>
                  Would you like to view your new plan or create another one?
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, flexWrap: 'wrap' }}>
                  <Button 
                    variant="contained"
                    className={styles.gotoDetailPageBtn} 
                    onClick={handleNavigateToPlan}
                    startIcon={<Iconify icon="material-symbols:visibility-outline" width={22} height={22} />}
                    sx={{ 
                      borderRadius: '12px',
                      boxShadow: '0 4px 12px rgba(240, 165, 0, 0.3)',
                      padding: '12px 28px',
                      fontSize: '1.1rem',
                      fontWeight: 600
                    }}
                  >
                    View Plan
                  </Button>
                  <Button 
                    variant="contained"
                    className={styles.reRunGenBtn} 
                    onClick={handleResetForm}
                    startIcon={<Iconify icon="material-symbols:refresh" width={22} height={22} />}
                    sx={{ 
                      borderRadius: '12px',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                      padding: '12px 28px',
                      fontSize: '1.1rem',
                      fontWeight: 600
                    }}
                  >
                    Create Another
                  </Button>
                </Box>
              </Box>
            )}
          </Box>
        )}
      </Box>
    </Container>
  );
};

export default CreatePlan;

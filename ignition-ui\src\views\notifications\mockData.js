import { mainYellowColor } from 'helpers/constants';

export const NOTIFICATION_TYPES = {
  TASK_ASSIGNED: 'TASK_ASSIGNED',
  TASK_COMPLETED: 'TASK_COMPLETED',
  MILESTONE_REACHED: 'MILESTONE_REACHED',
  PLAN_UPDATED: 'PLAN_UPDATED',
  COMMENT_ADDED: 'COMMENT_ADDED',
  FRIEND_REQUEST: 'FRIEND_REQUEST',
  SKILL_ENDORSED: 'SKILL_ENDORSED',
  MILESTONE_DUE_SOON: 'MILESTONE_DUE_SOON',
  TEAM_ADDED: 'TEAM_ADDED',
  TASK_DUE_SOON: 'TASK_DUE_SOON',
  TASK_OVERDUE: 'TASK_OVERDUE'
};

export const getNotificationConfig = (type) => {
  const configs = {
    [NOTIFICATION_TYPES.TASK_ASSIGNED]: {
      icon: 'mdi:clipboard-check-outline',
      color: mainYellowColor,
      actionText: 'View Task'
    },
    [NOTIFICATION_TYPES.TASK_COMPLETED]: {
      icon: 'mdi:checkbox-marked-circle-outline',
      color: '#4CAF50',
      actionText: 'View Details'
    },
    [NOTIFICATION_TYPES.MILESTONE_REACHED]: {
      icon: 'mdi:flag-checkered',
      color: '#2196F3',
      actionText: 'View Milestone'
    },
    [NOTIFICATION_TYPES.PLAN_UPDATED]: {
      icon: 'mdi:calendar-clock',
      color: '#9C27B0',
      actionText: 'View Plan'
    },
    [NOTIFICATION_TYPES.COMMENT_ADDED]: {
      icon: 'mdi:message-text-outline',
      color: '#FF9800',
      actionText: 'View Comment'
    },
    [NOTIFICATION_TYPES.FRIEND_REQUEST]: {
      icon: 'mdi:account-plus-outline',
      color: '#E91E63',
      actionText: 'View Profile'
    },
    [NOTIFICATION_TYPES.SKILL_ENDORSED]: {
      icon: 'mdi:thumb-up-outline',
      color: '#00BCD4',
      actionText: 'View Skills'
    },
    [NOTIFICATION_TYPES.MILESTONE_DUE_SOON]: {
      icon: 'mdi:flag-checkered',
      color: '#2196F3',
      actionText: 'View Milestone'
    },
    [NOTIFICATION_TYPES.TEAM_ADDED]: {
      icon: 'mdi:account-group-outline',
      color: '#9C27B0',
      actionText: 'View Team'
    },
    [NOTIFICATION_TYPES.TASK_DUE_SOON]: {
      icon: 'mdi:clock-outline',
      color: '#FF9800',
      actionText: 'View Task'
    },
    [NOTIFICATION_TYPES.TASK_OVERDUE]: {
      icon: 'mdi:clock-outline',
      color: '#FF9800',
      actionText: 'View Task'
    }
  };
  return configs[type] || configs[NOTIFICATION_TYPES.TASK_ASSIGNED];
};

export const mockNotifications = [
  {
    id: 1,
    type: NOTIFICATION_TYPES.TASK_ASSIGNED,
    message: 'You have been assigned to task "Update User Interface" in Project Alpha',
    created_at: '2024-03-20T08:00:00Z',
    is_read: false,
    action_url: '/tasks/123',
    sender: {
      name: 'John Manager',
      avatar: null
    }
  },
  {
    id: 2,
    type: NOTIFICATION_TYPES.MILESTONE_REACHED,
    message: 'Milestone "Beta Release" has been completed in Project Beta',
    created_at: '2024-03-19T15:30:00Z',
    is_read: true,
    action_url: '/milestones/456',
    sender: {
      name: 'Project System',
      avatar: null
    }
  },
  {
    id: 3,
    type: NOTIFICATION_TYPES.COMMENT_ADDED,
    message: 'Sarah Developer commented on your task "API Integration"',
    created_at: '2024-03-19T13:45:00Z',
    is_read: false,
    action_url: '/tasks/789#comments',
    sender: {
      name: 'Sarah Developer',
      avatar: null
    }
  },
  {
    id: 4,
    type: NOTIFICATION_TYPES.PLAN_UPDATED,
    message: 'Project plan "Mobile App Development" has been updated',
    created_at: '2024-03-19T10:15:00Z',
    is_read: true,
    action_url: '/plans/101',
    sender: {
      name: 'System Update',
      avatar: null
    }
  },
  {
    id: 5,
    type: NOTIFICATION_TYPES.SKILL_ENDORSED,
    message: 'Mike Tech Lead has endorsed your React.js skill',
    created_at: '2024-03-19T09:20:00Z',
    is_read: false,
    action_url: '/profile/skills',
    sender: {
      name: 'Mike Tech',
      avatar: null
    }
  },
  {
    id: 16,
    type: NOTIFICATION_TYPES.TASK_ASSIGNED,
    message: 'You have been assigned to review "Payment Integration" module',
    created_at: '2024-03-16T16:20:00Z',
    is_read: true,
    action_url: '/tasks/901',
    sender: {
      name: 'Tech Lead',
      avatar: null
    }
  },
  {
    id: 17,
    type: NOTIFICATION_TYPES.COMMENT_ADDED,
    message: 'Emma QA found a potential issue in "User Registration Flow"',
    created_at: '2024-03-16T15:45:00Z',
    is_read: false,
    action_url: '/tasks/902#comments',
    sender: {
      name: 'Emma QA',
      avatar: null
    }
  },
  {
    id: 18,
    type: NOTIFICATION_TYPES.MILESTONE_DUE_SOON,
    message: 'Milestone "Phase 1 Completion" deadline is approaching in 3 days',
    created_at: '2024-03-16T14:30:00Z',
    is_read: false,
    action_url: '/milestones/903',
    sender: {
      name: 'System Alert',
      avatar: null
    }
  },
  {
    id: 19,
    type: NOTIFICATION_TYPES.TASK_COMPLETED,
    message: 'Task "Email Template Design" has been completed by Design Team',
    created_at: '2024-03-16T13:15:00Z',
    is_read: true,
    action_url: '/tasks/904',
    sender: {
      name: 'Design Team',
      avatar: null
    }
  },
  {
    id: 20,
    type: NOTIFICATION_TYPES.PLAN_UPDATED,
    message: 'Resource allocation for "Project Omega" has been updated',
    created_at: '2024-03-16T12:00:00Z',
    is_read: false,
    action_url: '/plans/905',
    sender: {
      name: 'Resource Manager',
      avatar: null
    }
  },
  {
    id: 21,
    type: NOTIFICATION_TYPES.TEAM_ADDED,
    message: 'You have been added to "Code Review" team',
    created_at: '2024-03-16T11:30:00Z',
    is_read: true,
    action_url: '/teams/906',
    sender: {
      name: 'Development Manager',
      avatar: null
    }
  },
  {
    id: 22,
    type: NOTIFICATION_TYPES.TASK_DUE_SOON,
    message: 'Task "Database Migration Script" is due tomorrow',
    created_at: '2024-03-16T10:45:00Z',
    is_read: false,
    action_url: '/tasks/907',
    sender: {
      name: 'System Alert',
      avatar: null
    }
  },
  {
    id: 23,
    type: NOTIFICATION_TYPES.SKILL_ENDORSED,
    message: 'David Backend has endorsed your Node.js skill',
    created_at: '2024-03-16T09:30:00Z',
    is_read: true,
    action_url: '/profile/skills',
    sender: {
      name: 'David Backend',
      avatar: null
    }
  },
  {
    id: 24,
    type: NOTIFICATION_TYPES.COMMENT_ADDED,
    message: 'New documentation update in "API Documentation" task',
    created_at: '2024-03-15T17:20:00Z',
    is_read: false,
    action_url: '/tasks/908#comments',
    sender: {
      name: 'Technical Writer',
      avatar: null
    }
  },
  {
    id: 25,
    type: NOTIFICATION_TYPES.TASK_OVERDUE,
    message: 'Task "Performance Testing" is overdue by 1 day',
    created_at: '2024-03-15T16:15:00Z',
    is_read: false,
    action_url: '/tasks/909',
    sender: {
      name: 'System Alert',
      avatar: null
    }
  },
  {
    id: 26,
    type: NOTIFICATION_TYPES.MILESTONE_REACHED,
    message: 'Milestone "Backend Infrastructure Setup" has been achieved',
    created_at: '2024-03-15T15:00:00Z',
    is_read: true,
    action_url: '/milestones/910',
    sender: {
      name: 'Infrastructure Team',
      avatar: null
    }
  },
  {
    id: 27,
    type: NOTIFICATION_TYPES.TASK_ASSIGNED,
    message: 'New bug fix task assigned: "Fix Login Page Validation"',
    created_at: '2024-03-15T14:30:00Z',
    is_read: false,
    action_url: '/tasks/911',
    sender: {
      name: 'QA Team',
      avatar: null
    }
  },
  {
    id: 28,
    type: NOTIFICATION_TYPES.PLAN_UPDATED,
    message: 'Sprint backlog has been updated with new priority items',
    created_at: '2024-03-15T13:45:00Z',
    is_read: true,
    action_url: '/plans/912',
    sender: {
      name: 'Scrum Master',
      avatar: null
    }
  },
  {
    id: 29,
    type: NOTIFICATION_TYPES.COMMENT_ADDED,
    message: 'UI/UX team provided feedback on "Dashboard Layout"',
    created_at: '2024-03-15T12:30:00Z',
    is_read: false,
    action_url: '/tasks/913#comments',
    sender: {
      name: 'UI/UX Team',
      avatar: null
    }
  },
  {
    id: 30,
    type: NOTIFICATION_TYPES.TASK_COMPLETED,
    message: 'Code review completed for "User Settings Component"',
    created_at: '2024-03-15T11:15:00Z',
    is_read: true,
    action_url: '/tasks/914',
    sender: {
      name: 'Senior Developer',
      avatar: null
    }
  },
  {
    id: 31,
    type: NOTIFICATION_TYPES.SKILL_ENDORSED,
    message: 'Lisa Frontend has endorsed your CSS/SCSS skill',
    created_at: '2024-03-15T10:00:00Z',
    is_read: false,
    action_url: '/profile/skills',
    sender: {
      name: 'Lisa Frontend',
      avatar: null
    }
  },
  {
    id: 32,
    type: NOTIFICATION_TYPES.TASK_DUE_SOON,
    message: 'Reminder: Code documentation due in 3 days',
    created_at: '2024-03-15T09:30:00Z',
    is_read: false,
    action_url: '/tasks/915',
    sender: {
      name: 'System Alert',
      avatar: null
    }
  },
  {
    id: 33,
    type: NOTIFICATION_TYPES.TEAM_ADDED,
    message: 'Welcome to "Project Architecture" team',
    created_at: '2024-03-15T08:45:00Z',
    is_read: true,
    action_url: '/teams/916',
    sender: {
      name: 'Architecture Lead',
      avatar: null
    }
  },
  {
    id: 34,
    type: NOTIFICATION_TYPES.MILESTONE_DUE_SOON,
    message: 'Final testing phase starts in 2 days',
    created_at: '2024-03-14T17:30:00Z',
    is_read: false,
    action_url: '/milestones/917',
    sender: {
      name: 'System Alert',
      avatar: null
    }
  },
  {
    id: 35,
    type: NOTIFICATION_TYPES.TASK_ASSIGNED,
    message: 'Please review "Data Analytics Dashboard" implementation',
    created_at: '2024-03-14T16:15:00Z',
    is_read: false,
    action_url: '/tasks/918',
    sender: {
      name: 'Project Manager',
      avatar: null
    }
  }
]; 
import React from 'react';
import { Box, Typography, Paper, LinearProgress, Chip, Grid } from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import styles from '../styles.module.scss';

const Progress = ({ stats }) => {
  // Xác định trạng thái dựa trên tiến độ
  let status = { color: '#FFC107', label: 'Not Started', icon: 'material-symbols:hourglass-empty' };
  
  if (stats.progress === 100) {
    status = { color: '#4CAF50', label: 'Completed', icon: 'material-symbols:check-circle' };
  } else if (stats.progress > 0) {
    status = { color: '#FF9800', label: 'In Progress', icon: 'material-symbols:pending' };
  }

  const statusItems = [
    {
      color: '#4CAF50',
      label: 'Completed',
      count: stats.completedTasks,
      icon: 'material-symbols:check-circle'
    },
    {
      color: '#FF9800',
      label: 'In Progress',
      count: stats.inProgressTasks,
      icon: 'material-symbols:pending'
    },
    {
      color: '#E0E0E0',
      label: 'Not Started',
      count: stats.notStartedTasks,
      icon: 'material-symbols:hourglass-empty'
    }
  ];

  return (
    <Paper 
      elevation={0}
      className={styles.progressCard}
      sx={{ 
        flex: 1,
        borderRadius: '12px',
        mb: 0.5,
        py: 1,
        px: 1.5
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', mb: 1 }}>
        <Iconify icon="material-symbols:progress-activity" width={20} height={20} color={mainYellowColor} />
        <Typography 
          variant="h6" 
          sx={{ 
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            fontSize: '1.2rem',
            color: '#555',
            margin: 0
          }}
        >
          Progress
        </Typography>
      </Box>

      <Box sx={{ mb: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
          <Typography variant="body2" sx={{ fontWeight: 600, fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.9rem', color: '#666' }}>
            Completion
          </Typography>
          <Chip 
            icon={<Iconify icon={status.icon} width={12} height={12} />}
            label={status.label}
            size="small"
            sx={{ 
              backgroundColor: `${status.color}20`, 
              color: status.color,
              fontWeight: 600,
              borderRadius: '4px',
              fontFamily: '"Recursive Variable", sans-serif',
              height: '18px',
              '& .MuiChip-label': {
                padding: '0 4px',
                fontSize: '0.9rem'
              }
            }} 
          />
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LinearProgress 
            variant="determinate" 
            value={stats.progress} 
            sx={{ 
              flexGrow: 1, 
              height: 6, 
              borderRadius: 3,
              backgroundColor: '#f0f0f0',
              '& .MuiLinearProgress-bar': {
                backgroundColor: status.color
              }
            }} 
          />
          <Typography variant="body2" sx={{ fontWeight: 700, minWidth: '28px', fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.9rem', color: '#666' }}>
            {stats.progress}%
          </Typography>
        </Box>
      </Box>

      <Grid container spacing={0.5}>
        {statusItems.map((item, index) => (
          <Grid item xs={4} key={index}>
            <Box 
              sx={{ 
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                padding: '6px 4px',
                borderRadius: '6px',
                backgroundColor: '#f9f9f9',
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Box sx={{ width: 8, height: 8, borderRadius: 1, backgroundColor: item.color }} />
                <Typography variant="body2" sx={{ fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.9rem', color: '#666' }}>
                  {item.label}
                </Typography>
              </Box>
              <Typography variant="body1" sx={{ fontWeight: 600, fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.9rem', mt: 0.5, color: '#333' }}>
                {item.count} tasks
              </Typography>
            </Box>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

export default Progress; 
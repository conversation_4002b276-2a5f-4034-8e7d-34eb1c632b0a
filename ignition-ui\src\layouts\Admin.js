import React, { useEffect, useState, useCallback } from "react";
import { useLocation, Route, Routes, Navigate } from "react-router-dom";
import MobileHeader from "components/Sidebar/MobileHeader";
import AdminFooter from "components/Footers/AdminFooter";
import SidebarComponent from "components/Sidebar/SidebarComponent";
import RightSidebar from "components/Sidebar/RightSidebar";
import { ADMIN_PAGE_KEY, SIDEBAR_COLLAPSED_LEFT_PX, SIDEBAR_EXPANED_LEFT_PX } from "helpers/constants";
import { getRoutes } from "helpers/auth";
import routes from "routes/index";

//--------------------------------------------------------------------------------------------------

const AdminLayout = () => {
  const mainContent = React.useRef(null);
  const location = useLocation();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);
  const [isSmartphone, setIsSmartphone] = useState(window.innerWidth <= 768);
  const [rightSidebarOpen, setRightSidebarOpen] = useState(true);

  // Kiểm tra xem có nên hiển thị right sidebar hay không dựa trên đường dẫn hiện tại
  const shouldShowRightSidebar = useCallback(() => {
    const path = location.pathname;
    console.log("Current path:", path);
    
    // Danh sách các đường dẫn cần ẩn right sidebar
    const hiddenPaths = [
      // Trang Calendar
      '/my/tasks/calendar',
      '/d/my/tasks/calendar',

      // Trang Profile
      '/profile',
      '/d/profile',

      // Trang Plan Creation
      '/plan/create',
      '/d/plan/create'

      // Không ẩn ở trang Home (index.js)
      // Removed todo table paths since we removed the todo list page
    ];
    
    // Kiểm tra xem đường dẫn hiện tại có nằm trong danh sách cần ẩn không
    const shouldHide = hiddenPaths.some(hiddenPath => {
      if (hiddenPath.endsWith('/')) {
        return path === hiddenPath || path === hiddenPath.slice(0, -1);
      }
      return path === hiddenPath || path === hiddenPath + '/';
    });
    
    console.log("Should hide right sidebar:", shouldHide);
    return !shouldHide;
  }, [location.pathname]);

  useEffect(() => {
    document.documentElement.scrollTop = 0;
    document.scrollingElement.scrollTop = 0;
    mainContent.current.scrollTop = 0;
  }, [location]);

  useEffect(() => {
    const handleResize = () => {
      const newIsSmartphone = window.innerWidth <= 768;
      setIsSmartphone(newIsSmartphone);
      
      if (newIsSmartphone) {
        setRightSidebarOpen(false);
      } else {
        setRightSidebarOpen(shouldShowRightSidebar());
      }
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [location, shouldShowRightSidebar]);

  // Cập nhật trạng thái right sidebar khi đường dẫn thay đổi
  useEffect(() => {
    if (!isSmartphone) {
      const show = shouldShowRightSidebar();
      console.log("Setting rightSidebarOpen to:", show);
      setRightSidebarOpen(show);
    }
  }, [location, isSmartphone, shouldShowRightSidebar]);

  const toggleRightSidebar = () => {
    setRightSidebarOpen(!rightSidebarOpen);
  };

  // Tính toán margin và width cho main content
  const leftMargin = isSmartphone ? '0' : sidebarCollapsed ? SIDEBAR_COLLAPSED_LEFT_PX : SIDEBAR_EXPANED_LEFT_PX;
  const rightMargin = isSmartphone ? '0' : (rightSidebarOpen && shouldShowRightSidebar()) ? '300px' : '0';
  
  const mainContentStyle = {
    marginLeft: leftMargin,
    marginRight: rightMargin,
    transition: 'margin-left 0.3s ease, margin-right 0.3s ease',
    width: isSmartphone ? '100%' : `calc(100% - ${parseInt(leftMargin) + parseInt(rightMargin)}px)`,
  };

  // Log để debug
  console.log("rightSidebarOpen:", rightSidebarOpen);
  console.log("shouldShowRightSidebar:", shouldShowRightSidebar());
  console.log("rightMargin:", rightMargin);

  return (
    <>
      {isSmartphone ? (<MobileHeader
        onCollapseChange={setSidebarCollapsed}
        logo={{
          innerLink: "/d/",
          imgSrc: require("../assets/main_logo.png"),
          imgAlt: "Logo",
        }} />
      ) : (
        <SidebarComponent onCollapseChange={setSidebarCollapsed}
          logo={{
            innerLink: "/d",
            imgSrc: require(sidebarCollapsed ? "../assets/fire_logo.png" : "../assets/main_logo.png"),
            imgAlt: "Logo",
          }} />
      )}
      <div className="main-content" ref={mainContent} style={mainContentStyle}>
        <Routes>
          {getRoutes(routes, ADMIN_PAGE_KEY)}
          <Route path="*" element={<Navigate to="/d/" replace />} />
        </Routes>
        <AdminFooter />
      </div>
      
      {/* Right Sidebar for Today's Tasks - Chỉ hiển thị ở các trang được chỉ định */}
      {shouldShowRightSidebar() && (
        <RightSidebar isOpen={rightSidebarOpen} onToggle={toggleRightSidebar} />
      )}
    </>
  );
};

export default AdminLayout;

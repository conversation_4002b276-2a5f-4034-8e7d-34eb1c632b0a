/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  IconButton,
  Avatar,
  CircularProgress,
  Chip,
  Tooltip
} from '@mui/material';
import { useLocation } from 'react-router-dom';
import Iconify from 'components/Iconify/index';
import { mainYellowColor, APIURL } from "helpers/constants";
import { getHeaders } from "helpers/functions";
import axios from 'axios';

const AgentTab = ({ planInfo, onPlanUpdate }) => {
  const [conversations, setConversations] = useState([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const [ , setError] = useState(null);
  const location = useLocation();

  // Load conversations from localStorage
  useEffect(() => {
    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');
    const planConversations = savedConversations.filter(conv => conv.planId === planInfo?.id);
    setConversations(planConversations);

    // Check for pending message from ChatbotBar
    const pendingMessage = localStorage.getItem('pending_agent_message');
    if (pendingMessage) {
      try {
        const messageData = JSON.parse(pendingMessage);
        if (messageData.planInfo?.id === planInfo?.id) {
          handleSendMessage(messageData.message);
        }
        // Clear the pending message
        localStorage.removeItem('pending_agent_message');
      } catch (error) {
        console.error('Error processing pending message:', error);
        localStorage.removeItem('pending_agent_message');
      }
    }

    // If coming from chatbot bar via navigation state, add the initial message
    if (location.state?.message) {
      handleSendMessage(location.state.message);
    }
  }, [planInfo?.id, location.state]);

  // Auto scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversations]);



  const handleSendMessage = async (messageText = currentMessage) => {
    if (!messageText.trim() || isLoading) return;

    setIsLoading(true);
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: messageText.trim(),
      timestamp: new Date().toISOString()
    };

    const newConversations = [...conversations, userMessage];
    setConversations(newConversations);
    setCurrentMessage('');

    try {
      // Call the new AI agent chat endpoint with plan context
      const response = await axios.post(
        `${APIURL}/api/assistant/agent-chat`,
        {
          message: messageText.trim(),
          plan_slug: planInfo.slug
        },
        { headers: getHeaders() }
      );

      const aiResponseData = response.data;

      // Create AI response message
      const aiResponse = {
        id: Date.now() + 1,
        type: 'assistant',
        content: aiResponseData.message || 'I received your message but had trouble generating a response.',
        timestamp: new Date().toISOString(),
        actions: aiResponseData.actions || [],
        metadata: aiResponseData.metadata || {}
      };

      const updatedConversations = [...newConversations, aiResponse];
      setConversations(updatedConversations);

      // Execute any actions returned by the AI
      if (aiResponseData.actions && aiResponseData.actions.length > 0) {
        let actionResults = [];
        for (const action of aiResponseData.actions) {
          try {
            const result = await executeAIAction(action);
            actionResults.push(result);
          } catch (error) {
            console.error('Error executing AI action:', error);
            actionResults.push({
              success: false,
              error: error.message || 'Unknown error'
            });
          }
        }

        // If any actions were successful, trigger plan refresh and show success message
        const successfulActions = actionResults.filter(result => result.success);
        if (successfulActions.length > 0 && onPlanUpdate) {
          onPlanUpdate();

          // Add success confirmation message
          const successMessage = {
            id: Date.now() + 2,
            type: 'assistant',
            content: `✅ **All done!** I've successfully made the following changes to your project:\n\n${successfulActions.map(result => {
              const action = result.action;
              const actionType = action.action || action.type;

              switch(actionType) {
                case 'add_milestone':
                  return `• Added milestone: "${result.data.result?.milestone_name || action.data?.name}"`;
                case 'add_task':
                  return `• Added task: "${result.data.result?.task_name || action.data?.name}"`;
                case 'add_subtask':
                  return `• Added subtask: "${result.data.result?.subtask_name || action.data?.name}"`;
                case 'update_milestone':
                  return `• Updated milestone: "${result.data.result?.milestone_name || action.data?.name}"`;
                case 'update_task':
                  return `• Updated task: "${result.data.result?.task_name || action.data?.name}"`;
                case 'complete_task':
                  return `• Completed task: "${result.data.result?.task_name || action.data?.name}"`;
                default:
                  return `• Completed action: ${actionType}`;
              }
            }).join('\n')}\n\n**Check it out** in your project details above! 🎉`,
            timestamp: new Date().toISOString(),
            isSuccess: true
          };
          setConversations(prev => [...prev, successMessage]);
        }

        // Add action results to the conversation if there were any issues
        const failedActions = actionResults.filter(result => !result.success);
        if (failedActions.length > 0) {
          const errorMessage = {
            id: Date.now() + 3,
            type: 'assistant',
            content: `Note: I encountered some issues executing the requested actions:\n${failedActions.map(result => `• ${result.error}`).join('\n')}`,
            timestamp: new Date().toISOString(),
            isError: true
          };
          setConversations(prev => [...prev, errorMessage]);
        }
      }

      // Save to localStorage
      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');
      const otherPlanConversations = allConversations.filter(conv => conv.planId !== planInfo?.id);
      const planConversations = updatedConversations.map(conv => ({
        ...conv,
        planId: planInfo?.id,
        planName: planInfo?.name
      }));

      localStorage.setItem('agent_conversations', JSON.stringify([
        ...otherPlanConversations,
        ...planConversations
      ]));

    } catch (error) {
      console.error('Error processing message:', error);
      const errorResponse = {
        id: Date.now() + 1,
        type: 'assistant',
        content: `Sorry, I encountered an error while processing your request: ${error.response?.data?.error || error.message || 'Unknown error'}. Please try again.`,
        timestamp: new Date().toISOString(),
        isError: true
      };
      setConversations([...newConversations, errorResponse]);
      setError(error.message || 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const executeAIAction = async (action) => {
    try {
      const response = await axios.post(
        `${APIURL}/api/assistant/plan-action`,
        {
          action: action.action || action.type, // Handle both formats
          plan_slug: planInfo.slug,
          data: action.data || {},
          message: `AI-generated action: ${action.action || action.type}`
        },
        { headers: getHeaders() }
      );

      return {
        success: true,
        data: response.data,
        action: action
      };
    } catch (error) {
      console.error('Error executing AI action:', error);
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Unknown error',
        action: action
      };
    }
  };



  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          borderRadius: '12px 12px 0 0',
          border: '1px solid #f0f0f0',
          borderBottom: 'none',
          backgroundColor: '#fafafa'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{
              backgroundColor: mainYellowColor,
              width: 40,
              height: 40
            }}
          >
            <Iconify icon="mdi:robot" width={24} height={24} color="#fff" />
          </Avatar>
          <Box>
            <Typography
              variant="h6"
              sx={{
                fontFamily: '"Recursive Variable", sans-serif',
                fontWeight: 600,
                color: '#333'
              }}
            >
              AI Project Agent
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: '#666',
                fontFamily: '"Recursive Variable", sans-serif'
              }}
            >
              Managing: {planInfo?.name}
            </Typography>
          </Box>
          <Chip
            label="Beta"
            size="small"
            sx={{
              backgroundColor: `${mainYellowColor}20`,
              color: mainYellowColor,
              fontWeight: 600,
              ml: 'auto'
            }}
          />
        </Box>
      </Paper>

      {/* Messages Area */}
      <Paper
        elevation={0}
        sx={{
          flex: 1,
          border: '1px solid #f0f0f0',
          borderTop: 'none',
          borderBottom: 'none',
          overflow: 'auto',
          p: 2,
          backgroundColor: '#fff'
        }}
      >
        {conversations.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              textAlign: 'center'
            }}
          >
            <Avatar
              sx={{
                backgroundColor: `${mainYellowColor}20`,
                width: 60,
                height: 60,
                mb: 2
              }}
            >
              <Iconify icon="mdi:robot" width={32} height={32} color={mainYellowColor} />
            </Avatar>
            <Typography
              variant="h6"
              sx={{
                fontFamily: '"Recursive Variable", sans-serif',
                fontWeight: 600,
                color: '#333',
                mb: 1
              }}
            >
              Welcome to AI Project Agent
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: '#666',
                fontFamily: '"Recursive Variable", sans-serif',
                maxWidth: 400
              }}
            >
              I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!
            </Typography>
          </Box>
        ) : (
          <Box>
            {conversations.map((message) => (
              <Box
                key={message.id}
                sx={{
                  display: 'flex',
                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',
                  mb: 2
                }}
              >
                <Box
                  sx={{
                    maxWidth: '70%',
                    display: 'flex',
                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',
                    alignItems: 'flex-start',
                    gap: 1
                  }}
                >
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor
                    }}
                  >
                    <Iconify
                      icon={message.type === 'user' ? "material-symbols:person" : "mdi:robot"}
                      width={18}
                      height={18}
                      color={message.type === 'user' ? '#666' : '#fff'}
                    />
                  </Avatar>
                  <Box>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 1.5,
                        borderRadius: '12px',
                        backgroundColor: message.type === 'user' ? mainYellowColor :
                                       message.isError ? '#ffebee' :
                                       message.isSuccess ? '#e8f5e8' : '#f5f5f5',
                        color: message.type === 'user' ? '#fff' : '#333',
                        border: message.isError ? '1px solid #f44336' :
                               message.isSuccess ? '1px solid #4caf50' : 'none'
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          fontFamily: '"Recursive Variable", sans-serif',
                          lineHeight: 1.5,
                          whiteSpace: 'pre-line',
                          '& strong': {
                            fontWeight: 600
                          }
                        }}
                      >
                        {message.content}
                      </Typography>

                      {/* Quick Action Buttons for AI responses */}
                      {message.type === 'assistant' && message.actions && message.actions.length > 0 && (
                        <Box sx={{ mt: 1.5, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {message.actions.slice(0, 3).map((action, actionIndex) => (
                            <Chip
                              key={actionIndex}
                              label={action.description}
                              size="small"
                              onClick={() => {
                                // Handle quick action
                                setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);
                                if (inputRef.current) {
                                  inputRef.current.focus();
                                }
                              }}
                              sx={{
                                backgroundColor: '#fff',
                                border: `1px solid ${mainYellowColor}`,
                                color: mainYellowColor,
                                fontSize: '0.7rem',
                                height: '24px',
                                cursor: 'pointer',
                                '&:hover': {
                                  backgroundColor: `${mainYellowColor}10`
                                }
                              }}
                            />
                          ))}
                        </Box>
                      )}
                    </Paper>
                    <Typography
                      variant="caption"
                      sx={{
                        color: '#999',
                        fontFamily: '"Recursive Variable", sans-serif',
                        fontSize: '0.7rem',
                        mt: 0.5,
                        display: 'block',
                        textAlign: message.type === 'user' ? 'right' : 'left'
                      }}
                    >
                      {formatTimestamp(message.timestamp)}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            ))}
            {isLoading && (
              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: mainYellowColor
                    }}
                  >
                    <Iconify icon="mdi:robot" width={18} height={18} color="#fff" />
                  </Avatar>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 1.5,
                      borderRadius: '12px',
                      backgroundColor: '#f5f5f5',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <CircularProgress size={16} sx={{ color: mainYellowColor }} />
                    <Typography
                      variant="body2"
                      sx={{
                        fontFamily: '"Recursive Variable", sans-serif',
                        color: '#666'
                      }}
                    >
                      Thinking...
                    </Typography>
                  </Paper>
                </Box>
              </Box>
            )}
            <div ref={messagesEndRef} />
          </Box>
        )}
      </Paper>

      {/* Input Area */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          borderRadius: '0 0 12px 12px',
          border: '1px solid #f0f0f0',
          borderTop: 'none'
        }}
      >
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
          <TextField
            inputRef={inputRef}
            value={currentMessage}
            onChange={(e) => setCurrentMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me anything about your project..."
            multiline
            maxRows={3}
            fullWidth
            variant="outlined"
            disabled={isLoading}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                fontFamily: '"Recursive Variable", sans-serif'
              }
            }}
          />
          <Tooltip title="Send message">
            <IconButton
              onClick={() => handleSendMessage()}
              disabled={!currentMessage.trim() || isLoading}
              sx={{
                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',
                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',
                '&:hover': {
                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'
                }
              }}
            >
              <Iconify icon="material-symbols:send" width={20} height={20} />
            </IconButton>
          </Tooltip>
        </Box>
      </Paper>
    </Box>
  );
};

export default AgentTab;

import Home from "views/home/<USER>";
import Connect<PERSON>riend from "views/users/connect_friend/index";
import CalendarComponent from "views/calendar/index.js";
import CreatePlan from "views/plan/create.js";
import PlanDetail from "views/plan/detail";
import Login from "views/auth/Login.js";
import Register from "views/auth/Register.js";
import ForgotPassword from "views/auth/ForgotPassword.js";
import Activate from "views/auth/Activate.js";
import Reset from "views/auth/Reset.js";
import GoogleAuthHandle from "views/auth/GoogleAuthHandle";
import Profile from "views/users/profile/index.js";
import OtherProfile from "views/users/other_profile/index";
import TodoComponent from "views/todo/index.js";
import Notifications from "views/notifications/index.js";
import AcceptInvitation from "views/plan/invitation";
import RegisterByInvitation from "views/auth/RegisterByInvitation";
import PrivacyPolicies from "views/public/policies"

import { AUTH_PAGE_KEY, ADMIN_PAGE_KEY, PUBLIC_PAGE_KEY } from "helpers/constants";

var routes = [
  {
    path: "/",
    layout: ADMIN_PAGE_KEY,
    name: "HOME",
    component: <Home />,
    private: true,
  },
  {
    path: "/plan/create",
    layout: ADMIN_PAGE_KEY,
    name: "CREATE_A_PLAN",
    component: <CreatePlan />,
    private: true,
  },
  {
    path: "/plan/:param",
    layout: ADMIN_PAGE_KEY,
    name: "PLAN_INFOMATION",
    component: <PlanDetail />,
    private: true,
  },
  {
    path: "/connect",
    layout: ADMIN_PAGE_KEY,
    name: "CONTACT_DIRECTORY",
    component: <ConnectFriend />,
    private: true,
  },
  {
    path: "/profile",
    layout: ADMIN_PAGE_KEY,
    name: "CURRENT_USER_PROFILE",
    component: <Profile />,
    private: true,
  },
  {
    path: "/other/profile/:param",
    layout: ADMIN_PAGE_KEY,
    name: "OTHER_USER_PROFILE",
    component: <OtherProfile />,
    private: true,
  },
  {
    path: "/my/tasks/calendar",
    layout: ADMIN_PAGE_KEY,
    name: "MY_TASKS_CALENDAR",
    component: <CalendarComponent />,
    private: true,
  },
  {
    path: "/my/tasks/table",
    layout: ADMIN_PAGE_KEY,
    name: "MY_TASKS_TABLE",
    component: <TodoComponent />,
    private: true,
  },
  {
    path: "/notifications",
    layout: ADMIN_PAGE_KEY,
    name: "MY_NOTIFICATIONS",
    component: <Notifications />,
    private: true,
  },
  // No Auth page
  {
    path: "/login",
    layout: AUTH_PAGE_KEY,
    name: "LOGIN",
    component: <Login />,
    private: false,
  },
  {
    path: "/register",
    layout: AUTH_PAGE_KEY,
    name: "REGISTER",
    component: <Register />,
    private: false,
  },
  {
    path: "/register-by-invitation/:signedId/:email",
    layout: AUTH_PAGE_KEY,
    name: "REGISTER_BY_INVITATION",
    component: <RegisterByInvitation />,
    private: false,
  },
  {
    path: "/activate/:param1/:param2",
    layout: AUTH_PAGE_KEY,
    name: "ACTIVATE",
    component: <Activate />,
    private: false,
  },
  {
    path: "/reset/:uid/:token",
    layout: AUTH_PAGE_KEY,
    name: "RESET_PASSWORD",
    component: <Reset />,
    private: false,
  },
  {
    path: "/forgot-password",
    layout: AUTH_PAGE_KEY,
    name: "FORGOT_PASSWORD",
    component: <ForgotPassword />,
    private: false,
  },
  {
    path: "/google",
    layout: AUTH_PAGE_KEY,
    name: "GOOGLE_AUTH_HANDLE",
    component: <GoogleAuthHandle />,
    private: false,
  },
  // Both types
  {
    path: "/accept-invitation/:param",
    layout: PUBLIC_PAGE_KEY,
    name: "ACCEPT_INVITATION",
    component: <AcceptInvitation />,
    private: true,
  },
  {
    path:"/policies",
    layout: PUBLIC_PAGE_KEY,
    name: "POLICIES",
    component: <PrivacyPolicies />,
    private: false,
  }
];

export default routes;



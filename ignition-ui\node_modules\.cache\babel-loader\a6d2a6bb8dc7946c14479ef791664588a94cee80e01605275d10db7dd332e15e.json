{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\AgentTab.js\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Typography, Paper, TextField, IconButton, Avatar, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport { useLocation } from 'react-router-dom';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\nimport { getHeaders } from \"helpers/functions\";\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AgentTab = _ref => {\n  _s();\n  let {\n    planInfo,\n    onPlanUpdate\n  } = _ref;\n  const [conversations, setConversations] = useState([]);\n  const [currentMessage, setCurrentMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const [, setError] = useState(null);\n  const location = useLocation();\n\n  // Load conversations from localStorage\n  useEffect(() => {\n    var _location$state;\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n    const planConversations = savedConversations.filter(conv => conv.planId === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n    setConversations(planConversations);\n\n    // Check for pending message from ChatbotBar\n    const pendingMessage = localStorage.getItem('pending_agent_message');\n    if (pendingMessage) {\n      try {\n        var _messageData$planInfo;\n        const messageData = JSON.parse(pendingMessage);\n        if (((_messageData$planInfo = messageData.planInfo) === null || _messageData$planInfo === void 0 ? void 0 : _messageData$planInfo.id) === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id)) {\n          handleSendMessage(messageData.message);\n        }\n        // Clear the pending message\n        localStorage.removeItem('pending_agent_message');\n      } catch (error) {\n        console.error('Error processing pending message:', error);\n        localStorage.removeItem('pending_agent_message');\n      }\n    }\n\n    // If coming from chatbot bar via navigation state, add the initial message\n    if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.message) {\n      handleSendMessage(location.state.message);\n    }\n  }, [planInfo === null || planInfo === void 0 ? void 0 : planInfo.id, location.state]);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [conversations]);\n  const executeAction = async (action, originalMessage) => {\n    try {\n      const {\n        type,\n        entities\n      } = action;\n      if (type === 'add_milestone') {\n        // Extract milestone name from the message\n        let milestoneName = 'New Milestone';\n\n        // Try to extract from quoted text first\n        if (entities.taskNames && entities.taskNames.length > 0) {\n          milestoneName = entities.taskNames[0];\n        } else {\n          // Try to extract from common patterns\n          const patterns = [/add.*milestone.*[\"']([^\"']+)[\"']/i, /create.*milestone.*[\"']([^\"']+)[\"']/i, /new milestone.*[\"']([^\"']+)[\"']/i, /milestone.*[\"']([^\"']+)[\"']/i, /add.*milestone.*(?:for|about|called)\\s+([^.!?]+)/i, /create.*milestone.*(?:for|about|called)\\s+([^.!?]+)/i];\n          for (const pattern of patterns) {\n            const match = originalMessage.match(pattern);\n            if (match && match[1]) {\n              milestoneName = match[1].trim();\n              break;\n            }\n          }\n        }\n        const response = await axios.post(`${APIURL}/api/assistant/plan-action`, {\n          action: 'add_milestone',\n          plan_slug: planInfo.slug,\n          data: {\n            name: milestoneName,\n            description: `Milestone created by AI agent based on: \"${originalMessage}\"`\n          },\n          message: originalMessage\n        }, {\n          headers: getHeaders()\n        });\n        return {\n          success: true,\n          message: `✅ Successfully created milestone \"${milestoneName}\"!\n\nThe new milestone has been added to your project. You can now:\n• Add tasks to this milestone\n• Set specific goals and deadlines\n• Track progress as you work\n\nWould you like me to add some initial tasks to this milestone?`,\n          data: response.data\n        };\n      }\n\n      // Handle other action types here...\n      return {\n        success: false,\n        message: `I understand you want to ${action.description.toLowerCase()}, but I'm still learning how to do that. For now, I can help you add milestones to your project. What else can I help you with?`\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error executing action:', error);\n      return {\n        success: false,\n        message: `Sorry, I encountered an error while trying to ${action.description.toLowerCase()}: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || error.message}. Please try again or rephrase your request.`\n      };\n    }\n  };\n  const handleSendMessage = async function () {\n    let messageText = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : currentMessage;\n    if (!messageText.trim() || isLoading) return;\n    setIsLoading(true);\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageText.trim(),\n      timestamp: new Date().toISOString()\n    };\n    const newConversations = [...conversations, userMessage];\n    setConversations(newConversations);\n    setCurrentMessage('');\n    try {\n      // Call the new AI agent chat endpoint with plan context\n      const response = await axios.post(`${APIURL}/api/assistant/agent-chat`, {\n        message: messageText.trim(),\n        plan_slug: planInfo.slug\n      }, {\n        headers: getHeaders()\n      });\n      const aiResponseData = response.data;\n\n      // Create AI response message\n      const aiResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: aiResponseData.message || 'I received your message but had trouble generating a response.',\n        timestamp: new Date().toISOString(),\n        actions: aiResponseData.actions || [],\n        metadata: aiResponseData.metadata || {}\n      };\n      const updatedConversations = [...newConversations, aiResponse];\n      setConversations(updatedConversations);\n\n      // Execute any actions returned by the AI\n      if (aiResponseData.actions && aiResponseData.actions.length > 0) {\n        let actionResults = [];\n        for (const action of aiResponseData.actions) {\n          try {\n            const result = await executeAIAction(action);\n            actionResults.push(result);\n          } catch (error) {\n            console.error('Error executing AI action:', error);\n            actionResults.push({\n              success: false,\n              error: error.message || 'Unknown error'\n            });\n          }\n        }\n\n        // If any actions were successful, trigger plan refresh\n        if (actionResults.some(result => result.success) && onPlanUpdate) {\n          onPlanUpdate();\n        }\n\n        // Add action results to the conversation if there were any issues\n        const failedActions = actionResults.filter(result => !result.success);\n        if (failedActions.length > 0) {\n          const errorMessage = {\n            id: Date.now() + 2,\n            type: 'assistant',\n            content: `Note: I encountered some issues executing the requested actions:\\n${failedActions.map(result => `• ${result.error}`).join('\\n')}`,\n            timestamp: new Date().toISOString(),\n            isError: true\n          };\n          setConversations(prev => [...prev, errorMessage]);\n        }\n      }\n      const finalConversations = conversations.length > 0 ? [...newConversations, aiResponse] : [userMessage, aiResponse];\n\n      // Save to localStorage\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n      const planConversations = updatedConversations.map(conv => ({\n        ...conv,\n        planId: planInfo === null || planInfo === void 0 ? void 0 : planInfo.id,\n        planName: planInfo === null || planInfo === void 0 ? void 0 : planInfo.name\n      }));\n      localStorage.setItem('agent_conversations', JSON.stringify([...otherPlanConversations, ...planConversations]));\n    } catch (error) {\n      console.error('Error processing message:', error);\n      const errorResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: `Sorry, I encountered an error while processing your request: ${error.message || 'Unknown error'}. Please try again.`,\n        timestamp: new Date().toISOString(),\n        isError: true\n      };\n      setConversations([...newConversations, errorResponse]);\n      setError(error.message || 'Unknown error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const generateAIResponse = (message, planInfo) => {\n    const lowerMessage = message.toLowerCase();\n    const milestones = (planInfo === null || planInfo === void 0 ? void 0 : planInfo.milestones) || [];\n    const totalTasks = milestones.reduce((acc, milestone) => {\n      var _milestone$tasks;\n      return acc + (((_milestone$tasks = milestone.tasks) === null || _milestone$tasks === void 0 ? void 0 : _milestone$tasks.length) || 0);\n    }, 0);\n    const totalSubtasks = milestones.reduce((acc, milestone) => {\n      var _milestone$tasks2;\n      return acc + ((_milestone$tasks2 = milestone.tasks) === null || _milestone$tasks2 === void 0 ? void 0 : _milestone$tasks2.reduce((taskAcc, task) => {\n        var _task$subtasks;\n        return taskAcc + (((_task$subtasks = task.subtasks) === null || _task$subtasks === void 0 ? void 0 : _task$subtasks.length) || 0);\n      }, 0)) || 0;\n    }, 0);\n\n    // Analyze project context\n    const projectContext = {\n      name: (planInfo === null || planInfo === void 0 ? void 0 : planInfo.name) || 'your project',\n      milestoneCount: milestones.length,\n      taskCount: totalTasks,\n      subtaskCount: totalSubtasks,\n      milestoneNames: milestones.map(m => m.name).slice(0, 3) // First 3 milestone names\n    };\n\n    // Advanced intent recognition and response generation\n    if (lowerMessage.includes('milestone') && (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new'))) {\n      return `I can help you add a new milestone to \"${projectContext.name}\". Currently, you have ${projectContext.milestoneCount} milestones: ${projectContext.milestoneNames.join(', ')}${projectContext.milestoneCount > 3 ? '...' : ''}.\n\nTo add a new milestone, I'll need:\n1. **Milestone name** - What should we call it?\n2. **Description** - What's the main objective?\n3. **Position** - Should it come before/after a specific milestone?\n4. **Tasks** - Any initial tasks to include?\n\nPlease provide these details, or just tell me the milestone name and I'll help structure the rest!`;\n    }\n    if (lowerMessage.includes('progress') || lowerMessage.includes('status') || lowerMessage.includes('overview')) {\n      const progressDetails = milestones.map(milestone => {\n        var _milestone$tasks3, _milestone$tasks4, _milestone$tasks4$fil;\n        const taskCount = ((_milestone$tasks3 = milestone.tasks) === null || _milestone$tasks3 === void 0 ? void 0 : _milestone$tasks3.length) || 0;\n        const completedTasks = ((_milestone$tasks4 = milestone.tasks) === null || _milestone$tasks4 === void 0 ? void 0 : (_milestone$tasks4$fil = _milestone$tasks4.filter(task => task.status === 'completed')) === null || _milestone$tasks4$fil === void 0 ? void 0 : _milestone$tasks4$fil.length) || 0;\n        return `• ${milestone.name}: ${completedTasks}/${taskCount} tasks completed`;\n      }).join('\\n');\n      return `Here's your project progress for \"${projectContext.name}\":\n\n📊 **Overall Statistics:**\n• ${projectContext.milestoneCount} milestones\n• ${projectContext.taskCount} total tasks\n• ${projectContext.subtaskCount} total subtasks\n\n📋 **Milestone Progress:**\n${progressDetails}\n\nWould you like me to:\n• Show detailed progress for a specific milestone?\n• Identify overdue or at-risk tasks?\n• Suggest next actions to move the project forward?`;\n    }\n    if (lowerMessage.includes('complete') || lowerMessage.includes('done') || lowerMessage.includes('finish')) {\n      const availableTasks = milestones.flatMap(milestone => {\n        var _milestone$tasks5;\n        return ((_milestone$tasks5 = milestone.tasks) === null || _milestone$tasks5 === void 0 ? void 0 : _milestone$tasks5.filter(task => task.status !== 'completed').map(task => `\"${task.name}\" in ${milestone.name}`)) || [];\n      }).slice(0, 5);\n      return `I can mark tasks as completed for you! Here are some pending tasks I found:\n\n${availableTasks.map(task => `• ${task}`).join('\\n')}\n\nTo mark a task as complete, just tell me:\n• \"Mark [task name] as completed\"\n• \"Complete the [task name] task\"\n• Or simply \"Done with [task name]\"\n\nWhich task would you like to mark as completed?`;\n    }\n    if (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new')) {\n      if (lowerMessage.includes('task')) {\n        return `I can add new tasks to any of your milestones. You currently have ${projectContext.milestoneCount} milestones:\n\n${projectContext.milestoneNames.map((name, i) => `${i + 1}. ${name}`).join('\\n')}\n\nTo add a task, tell me:\n• **Which milestone** to add it to\n• **Task name** and description\n• **Any subtasks** to include\n\nExample: \"Add task 'Set up development environment' to the first milestone with subtasks for installing tools and configuring settings\"\n\nWhat task would you like to add?`;\n      }\n      return `I can help you add new content to \"${projectContext.name}\". I can create:\n\n🎯 **Milestones** - Major project phases\n📋 **Tasks** - Specific work items within milestones\n✅ **Subtasks** - Detailed steps for tasks\n📝 **Descriptions** - Enhanced details for any item\n\nWhat would you like to add? Just describe it naturally, like:\n• \"Add a milestone for user testing\"\n• \"Create a task for database setup in the development milestone\"\n• \"Add subtasks for the API integration task\"`;\n    }\n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\n      return `I can help you remove items from your project. For safety, I'll always confirm before deleting anything.\n\nI can remove:\n• **Tasks** that are no longer needed\n• **Subtasks** that are redundant\n• **Completed items** to clean up the project\n• **Duplicate entries**\n\n⚠️ **Note:** I cannot delete milestones as they're core to your project structure.\n\nWhat would you like to remove? Please be specific about the item name and location.`;\n    }\n    if (lowerMessage.includes('update') || lowerMessage.includes('change') || lowerMessage.includes('edit') || lowerMessage.includes('modify')) {\n      return `I can update various aspects of your project \"${projectContext.name}\":\n\n📝 **Content Updates:**\n• Task and subtask descriptions\n• Milestone objectives\n• Due dates and priorities\n• Task assignments\n\n🔄 **Status Changes:**\n• Mark items as in-progress, completed, or blocked\n• Update milestone phases\n• Change task priorities\n\n📊 **Structural Changes:**\n• Move tasks between milestones\n• Reorder items\n• Split large tasks into smaller ones\n\nWhat would you like to update? Describe the change you want to make.`;\n    }\n    if (lowerMessage.includes('help') || lowerMessage.includes('what can you do') || lowerMessage.includes('capabilities')) {\n      return `I'm your AI Project Assistant for \"${projectContext.name}\"! Here's what I can do:\n\n🎯 **Project Management:**\n• Add/remove tasks, subtasks, and milestones\n• Update descriptions, statuses, and priorities\n• Mark items as completed or in-progress\n• Move tasks between milestones\n\n📊 **Project Analysis:**\n• Show progress reports and statistics\n• Identify bottlenecks and overdue items\n• Suggest next actions and optimizations\n• Generate project summaries\n\n🔍 **Smart Search:**\n• Find specific tasks or milestones\n• Filter by status, assignee, or due date\n• Locate related items across the project\n\n💡 **Recommendations:**\n• Suggest task breakdowns\n• Recommend milestone structures\n• Identify missing dependencies\n• Propose timeline optimizations\n\nJust tell me what you want to do in natural language - I'll understand and help you get it done!`;\n    }\n\n    // Default intelligent response\n    return `I understand you want to \"${message}\".\n\nBased on your project \"${projectContext.name}\" with ${projectContext.milestoneCount} milestones and ${projectContext.taskCount} tasks, I can help you:\n\n🎯 **Quick Actions:**\n• \"Show me project progress\"\n• \"Add a new task to [milestone name]\"\n• \"Mark [task name] as completed\"\n• \"Update the description for [item name]\"\n\n💡 **Smart Suggestions:**\n• \"What should I work on next?\"\n• \"Show me overdue items\"\n• \"Help me organize this milestone\"\n• \"Create a timeline for this project\"\n\nWhat specific action would you like me to take? I'm here to make managing your project easier!`;\n  };\n  const extractActions = message => {\n    const actions = [];\n    const lowerMessage = message.toLowerCase();\n\n    // Enhanced action extraction with context\n    const actionPatterns = [{\n      pattern: /(complete|done|finish|mark.*complete)/,\n      type: 'complete_task',\n      confidence: 0.9,\n      description: 'Mark task as completed'\n    }, {\n      pattern: /(add|create|new).*milestone/,\n      type: 'add_milestone',\n      confidence: 0.9,\n      description: 'Add new milestone'\n    }, {\n      pattern: /(add|create|new).*task/,\n      type: 'add_task',\n      confidence: 0.9,\n      description: 'Add new task'\n    }, {\n      pattern: /(add|create|new).*subtask/,\n      type: 'add_subtask',\n      confidence: 0.9,\n      description: 'Add new subtask'\n    }, {\n      pattern: /(delete|remove).*task/,\n      type: 'delete_task',\n      confidence: 0.8,\n      description: 'Delete task'\n    }, {\n      pattern: /(update|change|edit|modify)/,\n      type: 'update_item',\n      confidence: 0.8,\n      description: 'Update item details'\n    }, {\n      pattern: /(progress|status|overview)/,\n      type: 'show_progress',\n      confidence: 0.9,\n      description: 'Show project progress'\n    }, {\n      pattern: /(move|transfer).*task/,\n      type: 'move_task',\n      confidence: 0.8,\n      description: 'Move task between milestones'\n    }, {\n      pattern: /(assign|delegate)/,\n      type: 'assign_task',\n      confidence: 0.8,\n      description: 'Assign task to team member'\n    }, {\n      pattern: /(due date|deadline|schedule)/,\n      type: 'set_deadline',\n      confidence: 0.8,\n      description: 'Set or update due date'\n    }];\n\n    // Extract entities (task names, milestone names, etc.)\n    const entities = {\n      taskNames: [],\n      milestoneNames: [],\n      dates: [],\n      priorities: []\n    };\n\n    // Look for quoted text (likely task/milestone names)\n    const quotedText = message.match(/\"([^\"]+)\"/g);\n    if (quotedText) {\n      entities.taskNames = quotedText.map(q => q.replace(/\"/g, ''));\n    }\n\n    // Look for date patterns\n    const datePatterns = message.match(/\\b\\d{1,2}\\/\\d{1,2}\\/\\d{4}\\b|\\b\\d{1,2}-\\d{1,2}-\\d{4}\\b|tomorrow|today|next week|next month/gi);\n    if (datePatterns) {\n      entities.dates = datePatterns;\n    }\n\n    // Look for priority keywords\n    const priorityPatterns = message.match(/\\b(high|low|medium|urgent|critical|normal)\\s*priority\\b/gi);\n    if (priorityPatterns) {\n      entities.priorities = priorityPatterns;\n    }\n\n    // Match actions against patterns\n    actionPatterns.forEach(_ref2 => {\n      let {\n        pattern,\n        type,\n        confidence,\n        description\n      } = _ref2;\n      if (pattern.test(lowerMessage)) {\n        actions.push({\n          type,\n          confidence,\n          description,\n          entities: entities,\n          originalMessage: message\n        });\n      }\n    });\n    return actions;\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const formatTimestamp = timestamp => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '70vh',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '12px 12px 0 0',\n        border: '1px solid #f0f0f0',\n        borderBottom: 'none',\n        backgroundColor: '#fafafa'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: mainYellowColor,\n            width: 40,\n            height: 40\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 24,\n            height: 24,\n            color: \"#fff\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontWeight: 600,\n              color: '#333'\n            },\n            children: \"AI Project Agent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            },\n            children: [\"Managing: \", planInfo === null || planInfo === void 0 ? void 0 : planInfo.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Beta\",\n          size: \"small\",\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            color: mainYellowColor,\n            fontWeight: 600,\n            ml: 'auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        flex: 1,\n        border: '1px solid #f0f0f0',\n        borderTop: 'none',\n        borderBottom: 'none',\n        overflow: 'auto',\n        p: 2,\n        backgroundColor: '#fff'\n      },\n      children: conversations.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            width: 60,\n            height: 60,\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 32,\n            height: 32,\n            color: mainYellowColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            fontWeight: 600,\n            color: '#333',\n            mb: 1\n          },\n          children: \"Welcome to AI Project Agent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: '#666',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            maxWidth: 400\n          },\n          children: \"I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        children: [conversations.map(message => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxWidth: '70%',\n              display: 'flex',\n              flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: message.type === 'user' ? '#666' : '#fff'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Paper, {\n                elevation: 0,\n                sx: {\n                  p: 1.5,\n                  borderRadius: '12px',\n                  backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\n                  color: message.type === 'user' ? '#fff' : '#333',\n                  border: message.isError ? '1px solid #f44336' : 'none'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontFamily: '\"Recursive Variable\", sans-serif',\n                    lineHeight: 1.5,\n                    whiteSpace: 'pre-line'\n                  },\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 23\n                }, this), message.type === 'assistant' && message.actions && message.actions.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mt: 1.5,\n                    display: 'flex',\n                    flexWrap: 'wrap',\n                    gap: 1\n                  },\n                  children: message.actions.slice(0, 3).map((action, actionIndex) => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: action.description,\n                    size: \"small\",\n                    onClick: () => {\n                      // Handle quick action\n                      setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);\n                      if (inputRef.current) {\n                        inputRef.current.focus();\n                      }\n                    },\n                    sx: {\n                      backgroundColor: '#fff',\n                      border: `1px solid ${mainYellowColor}`,\n                      color: mainYellowColor,\n                      fontSize: '0.7rem',\n                      height: '24px',\n                      cursor: 'pointer',\n                      '&:hover': {\n                        backgroundColor: `${mainYellowColor}10`\n                      }\n                    }\n                  }, actionIndex, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: '#999',\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  fontSize: '0.7rem',\n                  mt: 0.5,\n                  display: 'block',\n                  textAlign: message.type === 'user' ? 'right' : 'left'\n                },\n                children: formatTimestamp(message.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 702,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 15\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: \"#fff\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 0,\n              sx: {\n                p: 1.5,\n                borderRadius: '12px',\n                backgroundColor: '#f5f5f5',\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 16,\n                sx: {\n                  color: mainYellowColor\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 795,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  color: '#666'\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 796,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 613,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '0 0 12px 12px',\n        border: '1px solid #f0f0f0',\n        borderTop: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          alignItems: 'flex-end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          inputRef: inputRef,\n          value: currentMessage,\n          onChange: e => setCurrentMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask me anything about your project...\",\n          multiline: true,\n          maxRows: 3,\n          fullWidth: true,\n          variant: \"outlined\",\n          disabled: isLoading,\n          sx: {\n            '& .MuiOutlinedInput-root': {\n              borderRadius: '8px',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Send message\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => handleSendMessage(),\n            disabled: !currentMessage.trim() || isLoading,\n            sx: {\n              backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n              color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\n              '&:hover': {\n                backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:send\",\n              width: 20,\n              height: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 843,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 824,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 815,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 556,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentTab, \"rQuF4l2OLxvb/G3jCFGXzIq4svw=\", false, function () {\n  return [useLocation];\n});\n_c = AgentTab;\nexport default AgentTab;\nvar _c;\n$RefreshReg$(_c, \"AgentTab\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Typography", "Paper", "TextField", "IconButton", "Avatar", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "useLocation", "Iconify", "mainYellowColor", "APIURL", "getHeaders", "axios", "jsxDEV", "_jsxDEV", "AgentTab", "_ref", "_s", "planInfo", "onPlanUpdate", "conversations", "setConversations", "currentMessage", "setCurrentMessage", "isLoading", "setIsLoading", "messagesEndRef", "inputRef", "setError", "location", "_location$state", "savedConversations", "JSON", "parse", "localStorage", "getItem", "planConversations", "filter", "conv", "planId", "id", "pendingMessage", "_messageData$planInfo", "messageData", "handleSendMessage", "message", "removeItem", "error", "console", "state", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "executeAction", "action", "originalMessage", "type", "entities", "milestoneName", "taskNames", "length", "patterns", "pattern", "match", "trim", "response", "post", "plan_slug", "slug", "data", "name", "description", "headers", "success", "toLowerCase", "_error$response", "_error$response$data", "messageText", "arguments", "undefined", "userMessage", "Date", "now", "content", "timestamp", "toISOString", "newConversations", "aiResponseData", "aiResponse", "actions", "metadata", "updatedConversations", "actionResults", "result", "executeAIAction", "push", "some", "failedActions", "errorMessage", "map", "join", "isError", "prev", "finalConversations", "allConversations", "otherPlanConversations", "planName", "setItem", "stringify", "errorResponse", "generateAIResponse", "lowerMessage", "milestones", "totalTasks", "reduce", "acc", "milestone", "_milestone$tasks", "tasks", "totalSubtasks", "_milestone$tasks2", "taskAcc", "task", "_task$subtasks", "subtasks", "projectContext", "milestoneCount", "taskCount", "subtaskCount", "milestoneNames", "m", "slice", "includes", "progressDetails", "_milestone$tasks3", "_milestone$tasks4", "_milestone$tasks4$fil", "completedTasks", "status", "availableTasks", "flatMap", "_milestone$tasks5", "i", "extractActions", "actionPatterns", "confidence", "dates", "priorities", "quotedText", "q", "replace", "datePatterns", "priorityPatterns", "for<PERSON>ach", "_ref2", "test", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "formatTimestamp", "toLocaleTimeString", "hour", "minute", "sx", "height", "display", "flexDirection", "children", "elevation", "p", "borderRadius", "border", "borderBottom", "backgroundColor", "alignItems", "gap", "width", "icon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontFamily", "fontWeight", "label", "size", "ml", "flex", "borderTop", "overflow", "justifyContent", "textAlign", "mb", "max<PERSON><PERSON><PERSON>", "lineHeight", "whiteSpace", "mt", "flexWrap", "actionIndex", "onClick", "focus", "fontSize", "cursor", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "multiline", "maxRows", "fullWidth", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/AgentTab.js"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  TextField,\r\n  IconButton,\r\n  Avatar,\r\n  CircularProgress,\r\n  Chip,\r\n  Tooltip\r\n} from '@mui/material';\r\nimport { useLocation } from 'react-router-dom';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\r\nimport { getHeaders } from \"helpers/functions\";\r\nimport axios from 'axios';\r\n\r\nconst AgentTab = ({ planInfo, onPlanUpdate }) => {\r\n  const [conversations, setConversations] = useState([]);\r\n  const [currentMessage, setCurrentMessage] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const messagesEndRef = useRef(null);\r\n  const inputRef = useRef(null);\r\n  const [ , setError] = useState(null);\r\n  const location = useLocation();\r\n\r\n  // Load conversations from localStorage\r\n  useEffect(() => {\r\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n    const planConversations = savedConversations.filter(conv => conv.planId === planInfo?.id);\r\n    setConversations(planConversations);\r\n\r\n    // Check for pending message from ChatbotBar\r\n    const pendingMessage = localStorage.getItem('pending_agent_message');\r\n    if (pendingMessage) {\r\n      try {\r\n        const messageData = JSON.parse(pendingMessage);\r\n        if (messageData.planInfo?.id === planInfo?.id) {\r\n          handleSendMessage(messageData.message);\r\n        }\r\n        // Clear the pending message\r\n        localStorage.removeItem('pending_agent_message');\r\n      } catch (error) {\r\n        console.error('Error processing pending message:', error);\r\n        localStorage.removeItem('pending_agent_message');\r\n      }\r\n    }\r\n\r\n    // If coming from chatbot bar via navigation state, add the initial message\r\n    if (location.state?.message) {\r\n      handleSendMessage(location.state.message);\r\n    }\r\n  }, [planInfo?.id, location.state]);\r\n\r\n  // Auto scroll to bottom\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  }, [conversations]);\r\n\r\n  const executeAction = async (action, originalMessage) => {\r\n    try {\r\n      const { type, entities } = action;\r\n\r\n      if (type === 'add_milestone') {\r\n        // Extract milestone name from the message\r\n        let milestoneName = 'New Milestone';\r\n\r\n        // Try to extract from quoted text first\r\n        if (entities.taskNames && entities.taskNames.length > 0) {\r\n          milestoneName = entities.taskNames[0];\r\n        } else {\r\n          // Try to extract from common patterns\r\n          const patterns = [\r\n            /add.*milestone.*[\"']([^\"']+)[\"']/i,\r\n            /create.*milestone.*[\"']([^\"']+)[\"']/i,\r\n            /new milestone.*[\"']([^\"']+)[\"']/i,\r\n            /milestone.*[\"']([^\"']+)[\"']/i,\r\n            /add.*milestone.*(?:for|about|called)\\s+([^.!?]+)/i,\r\n            /create.*milestone.*(?:for|about|called)\\s+([^.!?]+)/i\r\n          ];\r\n\r\n          for (const pattern of patterns) {\r\n            const match = originalMessage.match(pattern);\r\n            if (match && match[1]) {\r\n              milestoneName = match[1].trim();\r\n              break;\r\n            }\r\n          }\r\n        }\r\n\r\n        const response = await axios.post(\r\n          `${APIURL}/api/assistant/plan-action`,\r\n          {\r\n            action: 'add_milestone',\r\n            plan_slug: planInfo.slug,\r\n            data: {\r\n              name: milestoneName,\r\n              description: `Milestone created by AI agent based on: \"${originalMessage}\"`\r\n            },\r\n            message: originalMessage\r\n          },\r\n          { headers: getHeaders() }\r\n        );\r\n\r\n        return {\r\n          success: true,\r\n          message: `✅ Successfully created milestone \"${milestoneName}\"!\r\n\r\nThe new milestone has been added to your project. You can now:\r\n• Add tasks to this milestone\r\n• Set specific goals and deadlines\r\n• Track progress as you work\r\n\r\nWould you like me to add some initial tasks to this milestone?`,\r\n          data: response.data\r\n        };\r\n      }\r\n\r\n      // Handle other action types here...\r\n      return {\r\n        success: false,\r\n        message: `I understand you want to ${action.description.toLowerCase()}, but I'm still learning how to do that. For now, I can help you add milestones to your project. What else can I help you with?`\r\n      };\r\n\r\n    } catch (error) {\r\n      console.error('Error executing action:', error);\r\n      return {\r\n        success: false,\r\n        message: `Sorry, I encountered an error while trying to ${action.description.toLowerCase()}: ${error.response?.data?.error || error.message}. Please try again or rephrase your request.`\r\n      };\r\n    }\r\n  };\r\n\r\n  const handleSendMessage = async (messageText = currentMessage) => {\r\n    if (!messageText.trim() || isLoading) return;\r\n\r\n    setIsLoading(true);\r\n    const userMessage = {\r\n      id: Date.now(),\r\n      type: 'user',\r\n      content: messageText.trim(),\r\n      timestamp: new Date().toISOString()\r\n    };\r\n\r\n    const newConversations = [...conversations, userMessage];\r\n    setConversations(newConversations);\r\n    setCurrentMessage('');\r\n\r\n    try {\r\n      // Call the new AI agent chat endpoint with plan context\r\n      const response = await axios.post(\r\n        `${APIURL}/api/assistant/agent-chat`,\r\n        {\r\n          message: messageText.trim(),\r\n          plan_slug: planInfo.slug\r\n        },\r\n        { headers: getHeaders() }\r\n      );\r\n\r\n      const aiResponseData = response.data;\r\n\r\n      // Create AI response message\r\n      const aiResponse = {\r\n        id: Date.now() + 1,\r\n        type: 'assistant',\r\n        content: aiResponseData.message || 'I received your message but had trouble generating a response.',\r\n        timestamp: new Date().toISOString(),\r\n        actions: aiResponseData.actions || [],\r\n        metadata: aiResponseData.metadata || {}\r\n      };\r\n\r\n      const updatedConversations = [...newConversations, aiResponse];\r\n      setConversations(updatedConversations);\r\n\r\n      // Execute any actions returned by the AI\r\n      if (aiResponseData.actions && aiResponseData.actions.length > 0) {\r\n        let actionResults = [];\r\n        for (const action of aiResponseData.actions) {\r\n          try {\r\n            const result = await executeAIAction(action);\r\n            actionResults.push(result);\r\n          } catch (error) {\r\n            console.error('Error executing AI action:', error);\r\n            actionResults.push({\r\n              success: false,\r\n              error: error.message || 'Unknown error'\r\n            });\r\n          }\r\n        }\r\n\r\n        // If any actions were successful, trigger plan refresh\r\n        if (actionResults.some(result => result.success) && onPlanUpdate) {\r\n          onPlanUpdate();\r\n        }\r\n\r\n        // Add action results to the conversation if there were any issues\r\n        const failedActions = actionResults.filter(result => !result.success);\r\n        if (failedActions.length > 0) {\r\n          const errorMessage = {\r\n            id: Date.now() + 2,\r\n            type: 'assistant',\r\n            content: `Note: I encountered some issues executing the requested actions:\\n${failedActions.map(result => `• ${result.error}`).join('\\n')}`,\r\n            timestamp: new Date().toISOString(),\r\n            isError: true\r\n          };\r\n          setConversations(prev => [...prev, errorMessage]);\r\n        }\r\n      }\r\n\r\n      const finalConversations = conversations.length > 0 ?\r\n        [...newConversations, aiResponse] :\r\n        [userMessage, aiResponse];\r\n\r\n      // Save to localStorage\r\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== planInfo?.id);\r\n      const planConversations = updatedConversations.map(conv => ({\r\n        ...conv,\r\n        planId: planInfo?.id,\r\n        planName: planInfo?.name\r\n      }));\r\n      \r\n      localStorage.setItem('agent_conversations', JSON.stringify([\r\n        ...otherPlanConversations,\r\n        ...planConversations\r\n      ]));\r\n\r\n    } catch (error) {\r\n      console.error('Error processing message:', error);\r\n      const errorResponse = {\r\n        id: Date.now() + 1,\r\n        type: 'assistant',\r\n        content: `Sorry, I encountered an error while processing your request: ${error.message || 'Unknown error'}. Please try again.`,\r\n        timestamp: new Date().toISOString(),\r\n        isError: true\r\n      };\r\n      setConversations([...newConversations, errorResponse]);\r\n      setError(error.message || 'Unknown error occurred');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const generateAIResponse = (message, planInfo) => {\r\n    const lowerMessage = message.toLowerCase();\r\n    const milestones = planInfo?.milestones || [];\r\n    const totalTasks = milestones.reduce((acc, milestone) => acc + (milestone.tasks?.length || 0), 0);\r\n    const totalSubtasks = milestones.reduce((acc, milestone) =>\r\n      acc + milestone.tasks?.reduce((taskAcc, task) => taskAcc + (task.subtasks?.length || 0), 0) || 0, 0);\r\n\r\n    // Analyze project context\r\n    const projectContext = {\r\n      name: planInfo?.name || 'your project',\r\n      milestoneCount: milestones.length,\r\n      taskCount: totalTasks,\r\n      subtaskCount: totalSubtasks,\r\n      milestoneNames: milestones.map(m => m.name).slice(0, 3), // First 3 milestone names\r\n    };\r\n\r\n    // Advanced intent recognition and response generation\r\n    if (lowerMessage.includes('milestone') && (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new'))) {\r\n      return `I can help you add a new milestone to \"${projectContext.name}\". Currently, you have ${projectContext.milestoneCount} milestones: ${projectContext.milestoneNames.join(', ')}${projectContext.milestoneCount > 3 ? '...' : ''}.\r\n\r\nTo add a new milestone, I'll need:\r\n1. **Milestone name** - What should we call it?\r\n2. **Description** - What's the main objective?\r\n3. **Position** - Should it come before/after a specific milestone?\r\n4. **Tasks** - Any initial tasks to include?\r\n\r\nPlease provide these details, or just tell me the milestone name and I'll help structure the rest!`;\r\n    }\r\n\r\n    if (lowerMessage.includes('progress') || lowerMessage.includes('status') || lowerMessage.includes('overview')) {\r\n      const progressDetails = milestones.map(milestone => {\r\n        const taskCount = milestone.tasks?.length || 0;\r\n        const completedTasks = milestone.tasks?.filter(task => task.status === 'completed')?.length || 0;\r\n        return `• ${milestone.name}: ${completedTasks}/${taskCount} tasks completed`;\r\n      }).join('\\n');\r\n\r\n      return `Here's your project progress for \"${projectContext.name}\":\r\n\r\n📊 **Overall Statistics:**\r\n• ${projectContext.milestoneCount} milestones\r\n• ${projectContext.taskCount} total tasks\r\n• ${projectContext.subtaskCount} total subtasks\r\n\r\n📋 **Milestone Progress:**\r\n${progressDetails}\r\n\r\nWould you like me to:\r\n• Show detailed progress for a specific milestone?\r\n• Identify overdue or at-risk tasks?\r\n• Suggest next actions to move the project forward?`;\r\n    }\r\n\r\n    if (lowerMessage.includes('complete') || lowerMessage.includes('done') || lowerMessage.includes('finish')) {\r\n      const availableTasks = milestones.flatMap(milestone =>\r\n        milestone.tasks?.filter(task => task.status !== 'completed').map(task =>\r\n          `\"${task.name}\" in ${milestone.name}`\r\n        ) || []\r\n      ).slice(0, 5);\r\n\r\n      return `I can mark tasks as completed for you! Here are some pending tasks I found:\r\n\r\n${availableTasks.map(task => `• ${task}`).join('\\n')}\r\n\r\nTo mark a task as complete, just tell me:\r\n• \"Mark [task name] as completed\"\r\n• \"Complete the [task name] task\"\r\n• Or simply \"Done with [task name]\"\r\n\r\nWhich task would you like to mark as completed?`;\r\n    }\r\n\r\n    if (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new')) {\r\n      if (lowerMessage.includes('task')) {\r\n        return `I can add new tasks to any of your milestones. You currently have ${projectContext.milestoneCount} milestones:\r\n\r\n${projectContext.milestoneNames.map((name, i) => `${i + 1}. ${name}`).join('\\n')}\r\n\r\nTo add a task, tell me:\r\n• **Which milestone** to add it to\r\n• **Task name** and description\r\n• **Any subtasks** to include\r\n\r\nExample: \"Add task 'Set up development environment' to the first milestone with subtasks for installing tools and configuring settings\"\r\n\r\nWhat task would you like to add?`;\r\n      }\r\n\r\n      return `I can help you add new content to \"${projectContext.name}\". I can create:\r\n\r\n🎯 **Milestones** - Major project phases\r\n📋 **Tasks** - Specific work items within milestones\r\n✅ **Subtasks** - Detailed steps for tasks\r\n📝 **Descriptions** - Enhanced details for any item\r\n\r\nWhat would you like to add? Just describe it naturally, like:\r\n• \"Add a milestone for user testing\"\r\n• \"Create a task for database setup in the development milestone\"\r\n• \"Add subtasks for the API integration task\"`;\r\n    }\r\n\r\n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\r\n      return `I can help you remove items from your project. For safety, I'll always confirm before deleting anything.\r\n\r\nI can remove:\r\n• **Tasks** that are no longer needed\r\n• **Subtasks** that are redundant\r\n• **Completed items** to clean up the project\r\n• **Duplicate entries**\r\n\r\n⚠️ **Note:** I cannot delete milestones as they're core to your project structure.\r\n\r\nWhat would you like to remove? Please be specific about the item name and location.`;\r\n    }\r\n\r\n    if (lowerMessage.includes('update') || lowerMessage.includes('change') || lowerMessage.includes('edit') || lowerMessage.includes('modify')) {\r\n      return `I can update various aspects of your project \"${projectContext.name}\":\r\n\r\n📝 **Content Updates:**\r\n• Task and subtask descriptions\r\n• Milestone objectives\r\n• Due dates and priorities\r\n• Task assignments\r\n\r\n🔄 **Status Changes:**\r\n• Mark items as in-progress, completed, or blocked\r\n• Update milestone phases\r\n• Change task priorities\r\n\r\n📊 **Structural Changes:**\r\n• Move tasks between milestones\r\n• Reorder items\r\n• Split large tasks into smaller ones\r\n\r\nWhat would you like to update? Describe the change you want to make.`;\r\n    }\r\n\r\n    if (lowerMessage.includes('help') || lowerMessage.includes('what can you do') || lowerMessage.includes('capabilities')) {\r\n      return `I'm your AI Project Assistant for \"${projectContext.name}\"! Here's what I can do:\r\n\r\n🎯 **Project Management:**\r\n• Add/remove tasks, subtasks, and milestones\r\n• Update descriptions, statuses, and priorities\r\n• Mark items as completed or in-progress\r\n• Move tasks between milestones\r\n\r\n📊 **Project Analysis:**\r\n• Show progress reports and statistics\r\n• Identify bottlenecks and overdue items\r\n• Suggest next actions and optimizations\r\n• Generate project summaries\r\n\r\n🔍 **Smart Search:**\r\n• Find specific tasks or milestones\r\n• Filter by status, assignee, or due date\r\n• Locate related items across the project\r\n\r\n💡 **Recommendations:**\r\n• Suggest task breakdowns\r\n• Recommend milestone structures\r\n• Identify missing dependencies\r\n• Propose timeline optimizations\r\n\r\nJust tell me what you want to do in natural language - I'll understand and help you get it done!`;\r\n    }\r\n\r\n    // Default intelligent response\r\n    return `I understand you want to \"${message}\".\r\n\r\nBased on your project \"${projectContext.name}\" with ${projectContext.milestoneCount} milestones and ${projectContext.taskCount} tasks, I can help you:\r\n\r\n🎯 **Quick Actions:**\r\n• \"Show me project progress\"\r\n• \"Add a new task to [milestone name]\"\r\n• \"Mark [task name] as completed\"\r\n• \"Update the description for [item name]\"\r\n\r\n💡 **Smart Suggestions:**\r\n• \"What should I work on next?\"\r\n• \"Show me overdue items\"\r\n• \"Help me organize this milestone\"\r\n• \"Create a timeline for this project\"\r\n\r\nWhat specific action would you like me to take? I'm here to make managing your project easier!`;\r\n  };\r\n\r\n  const extractActions = (message) => {\r\n    const actions = [];\r\n    const lowerMessage = message.toLowerCase();\r\n\r\n    // Enhanced action extraction with context\r\n    const actionPatterns = [\r\n      {\r\n        pattern: /(complete|done|finish|mark.*complete)/,\r\n        type: 'complete_task',\r\n        confidence: 0.9,\r\n        description: 'Mark task as completed'\r\n      },\r\n      {\r\n        pattern: /(add|create|new).*milestone/,\r\n        type: 'add_milestone',\r\n        confidence: 0.9,\r\n        description: 'Add new milestone'\r\n      },\r\n      {\r\n        pattern: /(add|create|new).*task/,\r\n        type: 'add_task',\r\n        confidence: 0.9,\r\n        description: 'Add new task'\r\n      },\r\n      {\r\n        pattern: /(add|create|new).*subtask/,\r\n        type: 'add_subtask',\r\n        confidence: 0.9,\r\n        description: 'Add new subtask'\r\n      },\r\n      {\r\n        pattern: /(delete|remove).*task/,\r\n        type: 'delete_task',\r\n        confidence: 0.8,\r\n        description: 'Delete task'\r\n      },\r\n      {\r\n        pattern: /(update|change|edit|modify)/,\r\n        type: 'update_item',\r\n        confidence: 0.8,\r\n        description: 'Update item details'\r\n      },\r\n      {\r\n        pattern: /(progress|status|overview)/,\r\n        type: 'show_progress',\r\n        confidence: 0.9,\r\n        description: 'Show project progress'\r\n      },\r\n      {\r\n        pattern: /(move|transfer).*task/,\r\n        type: 'move_task',\r\n        confidence: 0.8,\r\n        description: 'Move task between milestones'\r\n      },\r\n      {\r\n        pattern: /(assign|delegate)/,\r\n        type: 'assign_task',\r\n        confidence: 0.8,\r\n        description: 'Assign task to team member'\r\n      },\r\n      {\r\n        pattern: /(due date|deadline|schedule)/,\r\n        type: 'set_deadline',\r\n        confidence: 0.8,\r\n        description: 'Set or update due date'\r\n      }\r\n    ];\r\n\r\n    // Extract entities (task names, milestone names, etc.)\r\n    const entities = {\r\n      taskNames: [],\r\n      milestoneNames: [],\r\n      dates: [],\r\n      priorities: []\r\n    };\r\n\r\n    // Look for quoted text (likely task/milestone names)\r\n    const quotedText = message.match(/\"([^\"]+)\"/g);\r\n    if (quotedText) {\r\n      entities.taskNames = quotedText.map(q => q.replace(/\"/g, ''));\r\n    }\r\n\r\n    // Look for date patterns\r\n    const datePatterns = message.match(/\\b\\d{1,2}\\/\\d{1,2}\\/\\d{4}\\b|\\b\\d{1,2}-\\d{1,2}-\\d{4}\\b|tomorrow|today|next week|next month/gi);\r\n    if (datePatterns) {\r\n      entities.dates = datePatterns;\r\n    }\r\n\r\n    // Look for priority keywords\r\n    const priorityPatterns = message.match(/\\b(high|low|medium|urgent|critical|normal)\\s*priority\\b/gi);\r\n    if (priorityPatterns) {\r\n      entities.priorities = priorityPatterns;\r\n    }\r\n\r\n    // Match actions against patterns\r\n    actionPatterns.forEach(({ pattern, type, confidence, description }) => {\r\n      if (pattern.test(lowerMessage)) {\r\n        actions.push({\r\n          type,\r\n          confidence,\r\n          description,\r\n          entities: entities,\r\n          originalMessage: message\r\n        });\r\n      }\r\n    });\r\n\r\n    return actions;\r\n  };\r\n\r\n  const handleKeyPress = (e) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const formatTimestamp = (timestamp) => {\r\n    return new Date(timestamp).toLocaleTimeString('en-US', {\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>\r\n      {/* Header */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          p: 2,\r\n          borderRadius: '12px 12px 0 0',\r\n          border: '1px solid #f0f0f0',\r\n          borderBottom: 'none',\r\n          backgroundColor: '#fafafa'\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n          <Avatar\r\n            sx={{\r\n              backgroundColor: mainYellowColor,\r\n              width: 40,\r\n              height: 40\r\n            }}\r\n          >\r\n            <Iconify icon=\"mdi:robot\" width={24} height={24} color=\"#fff\" />\r\n          </Avatar>\r\n          <Box>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                color: '#333'\r\n              }}\r\n            >\r\n              AI Project Agent\r\n            </Typography>\r\n            <Typography\r\n              variant=\"caption\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }}\r\n            >\r\n              Managing: {planInfo?.name}\r\n            </Typography>\r\n          </Box>\r\n          <Chip\r\n            label=\"Beta\"\r\n            size=\"small\"\r\n            sx={{\r\n              backgroundColor: `${mainYellowColor}20`,\r\n              color: mainYellowColor,\r\n              fontWeight: 600,\r\n              ml: 'auto'\r\n            }}\r\n          />\r\n        </Box>\r\n      </Paper>\r\n\r\n      {/* Messages Area */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          flex: 1,\r\n          border: '1px solid #f0f0f0',\r\n          borderTop: 'none',\r\n          borderBottom: 'none',\r\n          overflow: 'auto',\r\n          p: 2,\r\n          backgroundColor: '#fff'\r\n        }}\r\n      >\r\n        {conversations.length === 0 ? (\r\n          <Box\r\n            sx={{\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              height: '100%',\r\n              textAlign: 'center'\r\n            }}\r\n          >\r\n            <Avatar\r\n              sx={{\r\n                backgroundColor: `${mainYellowColor}20`,\r\n                width: 60,\r\n                height: 60,\r\n                mb: 2\r\n              }}\r\n            >\r\n              <Iconify icon=\"mdi:robot\" width={32} height={32} color={mainYellowColor} />\r\n            </Avatar>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                color: '#333',\r\n                mb: 1\r\n              }}\r\n            >\r\n              Welcome to AI Project Agent\r\n            </Typography>\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                maxWidth: 400\r\n              }}\r\n            >\r\n              I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\r\n            </Typography>\r\n          </Box>\r\n        ) : (\r\n          <Box>\r\n            {conversations.map((message) => (\r\n              <Box\r\n                key={message.id}\r\n                sx={{\r\n                  display: 'flex',\r\n                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\r\n                  mb: 2\r\n                }}\r\n              >\r\n                <Box\r\n                  sx={{\r\n                    maxWidth: '70%',\r\n                    display: 'flex',\r\n                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\r\n                    alignItems: 'flex-start',\r\n                    gap: 1\r\n                  }}\r\n                >\r\n                  <Avatar\r\n                    sx={{\r\n                      width: 32,\r\n                      height: 32,\r\n                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\r\n                    }}\r\n                  >\r\n                    <Iconify\r\n                      icon={message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\"}\r\n                      width={18}\r\n                      height={18}\r\n                      color={message.type === 'user' ? '#666' : '#fff'}\r\n                    />\r\n                  </Avatar>\r\n                  <Box>\r\n                    <Paper\r\n                      elevation={0}\r\n                      sx={{\r\n                        p: 1.5,\r\n                        borderRadius: '12px',\r\n                        backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\r\n                        color: message.type === 'user' ? '#fff' : '#333',\r\n                        border: message.isError ? '1px solid #f44336' : 'none'\r\n                      }}\r\n                    >\r\n                      <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                          fontFamily: '\"Recursive Variable\", sans-serif',\r\n                          lineHeight: 1.5,\r\n                          whiteSpace: 'pre-line'\r\n                        }}\r\n                      >\r\n                        {message.content}\r\n                      </Typography>\r\n\r\n                      {/* Quick Action Buttons for AI responses */}\r\n                      {message.type === 'assistant' && message.actions && message.actions.length > 0 && (\r\n                        <Box sx={{ mt: 1.5, display: 'flex', flexWrap: 'wrap', gap: 1 }}>\r\n                          {message.actions.slice(0, 3).map((action, actionIndex) => (\r\n                            <Chip\r\n                              key={actionIndex}\r\n                              label={action.description}\r\n                              size=\"small\"\r\n                              onClick={() => {\r\n                                // Handle quick action\r\n                                setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);\r\n                                if (inputRef.current) {\r\n                                  inputRef.current.focus();\r\n                                }\r\n                              }}\r\n                              sx={{\r\n                                backgroundColor: '#fff',\r\n                                border: `1px solid ${mainYellowColor}`,\r\n                                color: mainYellowColor,\r\n                                fontSize: '0.7rem',\r\n                                height: '24px',\r\n                                cursor: 'pointer',\r\n                                '&:hover': {\r\n                                  backgroundColor: `${mainYellowColor}10`\r\n                                }\r\n                              }}\r\n                            />\r\n                          ))}\r\n                        </Box>\r\n                      )}\r\n                    </Paper>\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        color: '#999',\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        fontSize: '0.7rem',\r\n                        mt: 0.5,\r\n                        display: 'block',\r\n                        textAlign: message.type === 'user' ? 'right' : 'left'\r\n                      }}\r\n                    >\r\n                      {formatTimestamp(message.timestamp)}\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n            ))}\r\n            {isLoading && (\r\n              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>\r\n                  <Avatar\r\n                    sx={{\r\n                      width: 32,\r\n                      height: 32,\r\n                      backgroundColor: mainYellowColor\r\n                    }}\r\n                  >\r\n                    <Iconify icon=\"mdi:robot\" width={18} height={18} color=\"#fff\" />\r\n                  </Avatar>\r\n                  <Paper\r\n                    elevation={0}\r\n                    sx={{\r\n                      p: 1.5,\r\n                      borderRadius: '12px',\r\n                      backgroundColor: '#f5f5f5',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: 1\r\n                    }}\r\n                  >\r\n                    <CircularProgress size={16} sx={{ color: mainYellowColor }} />\r\n                    <Typography\r\n                      variant=\"body2\"\r\n                      sx={{\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        color: '#666'\r\n                      }}\r\n                    >\r\n                      Thinking...\r\n                    </Typography>\r\n                  </Paper>\r\n                </Box>\r\n              </Box>\r\n            )}\r\n            <div ref={messagesEndRef} />\r\n          </Box>\r\n        )}\r\n      </Paper>\r\n\r\n      {/* Input Area */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          p: 2,\r\n          borderRadius: '0 0 12px 12px',\r\n          border: '1px solid #f0f0f0',\r\n          borderTop: 'none'\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\r\n          <TextField\r\n            inputRef={inputRef}\r\n            value={currentMessage}\r\n            onChange={(e) => setCurrentMessage(e.target.value)}\r\n            onKeyPress={handleKeyPress}\r\n            placeholder=\"Ask me anything about your project...\"\r\n            multiline\r\n            maxRows={3}\r\n            fullWidth\r\n            variant=\"outlined\"\r\n            disabled={isLoading}\r\n            sx={{\r\n              '& .MuiOutlinedInput-root': {\r\n                borderRadius: '8px',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }\r\n            }}\r\n          />\r\n          <Tooltip title=\"Send message\">\r\n            <IconButton\r\n              onClick={() => handleSendMessage()}\r\n              disabled={!currentMessage.trim() || isLoading}\r\n              sx={{\r\n                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\r\n                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\r\n                '&:hover': {\r\n                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\r\n                }\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:send\" width={20} height={20} />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Box>\r\n      </Paper>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default AgentTab;\r\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,EAAEC,MAAM,QAAQ,mBAAmB;AAC3D,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGC,IAAA,IAAgC;EAAAC,EAAA;EAAA,IAA/B;IAAEC,QAAQ;IAAEC;EAAa,CAAC,GAAAH,IAAA;EAC1C,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM+B,cAAc,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM8B,QAAQ,GAAG9B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM,GAAI+B,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACpC,MAAMkC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;;EAE9B;EACAX,SAAS,CAAC,MAAM;IAAA,IAAAkC,eAAA;IACd,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;IAC1F,MAAMC,iBAAiB,GAAGL,kBAAkB,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKrB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAC;IACzFnB,gBAAgB,CAACe,iBAAiB,CAAC;;IAEnC;IACA,MAAMK,cAAc,GAAGP,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;IACpE,IAAIM,cAAc,EAAE;MAClB,IAAI;QAAA,IAAAC,qBAAA;QACF,MAAMC,WAAW,GAAGX,IAAI,CAACC,KAAK,CAACQ,cAAc,CAAC;QAC9C,IAAI,EAAAC,qBAAA,GAAAC,WAAW,CAACzB,QAAQ,cAAAwB,qBAAA,uBAApBA,qBAAA,CAAsBF,EAAE,OAAKtB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,GAAE;UAC7CI,iBAAiB,CAACD,WAAW,CAACE,OAAO,CAAC;QACxC;QACA;QACAX,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC;MAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDb,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC;MAClD;IACF;;IAEA;IACA,KAAAhB,eAAA,GAAID,QAAQ,CAACoB,KAAK,cAAAnB,eAAA,eAAdA,eAAA,CAAgBe,OAAO,EAAE;MAC3BD,iBAAiB,CAACf,QAAQ,CAACoB,KAAK,CAACJ,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAAC3B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAEX,QAAQ,CAACoB,KAAK,CAAC,CAAC;;EAElC;EACArD,SAAS,CAAC,MAAM;IAAA,IAAAsD,qBAAA;IACd,CAAAA,qBAAA,GAAAxB,cAAc,CAACyB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACjC,aAAa,CAAC,CAAC;EAEnB,MAAMkC,aAAa,GAAG,MAAAA,CAAOC,MAAM,EAAEC,eAAe,KAAK;IACvD,IAAI;MACF,MAAM;QAAEC,IAAI;QAAEC;MAAS,CAAC,GAAGH,MAAM;MAEjC,IAAIE,IAAI,KAAK,eAAe,EAAE;QAC5B;QACA,IAAIE,aAAa,GAAG,eAAe;;QAEnC;QACA,IAAID,QAAQ,CAACE,SAAS,IAAIF,QAAQ,CAACE,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;UACvDF,aAAa,GAAGD,QAAQ,CAACE,SAAS,CAAC,CAAC,CAAC;QACvC,CAAC,MAAM;UACL;UACA,MAAME,QAAQ,GAAG,CACf,mCAAmC,EACnC,sCAAsC,EACtC,kCAAkC,EAClC,8BAA8B,EAC9B,mDAAmD,EACnD,sDAAsD,CACvD;UAED,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;YAC9B,MAAME,KAAK,GAAGR,eAAe,CAACQ,KAAK,CAACD,OAAO,CAAC;YAC5C,IAAIC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;cACrBL,aAAa,GAAGK,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;cAC/B;YACF;UACF;QACF;QAEA,MAAMC,QAAQ,GAAG,MAAMtD,KAAK,CAACuD,IAAI,CAC/B,GAAGzD,MAAM,4BAA4B,EACrC;UACE6C,MAAM,EAAE,eAAe;UACvBa,SAAS,EAAElD,QAAQ,CAACmD,IAAI;UACxBC,IAAI,EAAE;YACJC,IAAI,EAAEZ,aAAa;YACnBa,WAAW,EAAE,4CAA4ChB,eAAe;UAC1E,CAAC;UACDX,OAAO,EAAEW;QACX,CAAC,EACD;UAAEiB,OAAO,EAAE9D,UAAU,CAAC;QAAE,CAC1B,CAAC;QAED,OAAO;UACL+D,OAAO,EAAE,IAAI;UACb7B,OAAO,EAAE,qCAAqCc,aAAa;AACrE;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D;UACrDW,IAAI,EAAEJ,QAAQ,CAACI;QACjB,CAAC;MACH;;MAEA;MACA,OAAO;QACLI,OAAO,EAAE,KAAK;QACd7B,OAAO,EAAE,4BAA4BU,MAAM,CAACiB,WAAW,CAACG,WAAW,CAAC,CAAC;MACvE,CAAC;IAEH,CAAC,CAAC,OAAO5B,KAAK,EAAE;MAAA,IAAA6B,eAAA,EAAAC,oBAAA;MACd7B,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO;QACL2B,OAAO,EAAE,KAAK;QACd7B,OAAO,EAAE,iDAAiDU,MAAM,CAACiB,WAAW,CAACG,WAAW,CAAC,CAAC,KAAK,EAAAC,eAAA,GAAA7B,KAAK,CAACmB,QAAQ,cAAAU,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBN,IAAI,cAAAO,oBAAA,uBAApBA,oBAAA,CAAsB9B,KAAK,KAAIA,KAAK,CAACF,OAAO;MAC7I,CAAC;IACH;EACF,CAAC;EAED,MAAMD,iBAAiB,GAAG,eAAAA,CAAA,EAAwC;IAAA,IAAjCkC,WAAW,GAAAC,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGzD,cAAc;IAC3D,IAAI,CAACwD,WAAW,CAACb,IAAI,CAAC,CAAC,IAAIzC,SAAS,EAAE;IAEtCC,YAAY,CAAC,IAAI,CAAC;IAClB,MAAMwD,WAAW,GAAG;MAClBzC,EAAE,EAAE0C,IAAI,CAACC,GAAG,CAAC,CAAC;MACd1B,IAAI,EAAE,MAAM;MACZ2B,OAAO,EAAEN,WAAW,CAACb,IAAI,CAAC,CAAC;MAC3BoB,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC;IACpC,CAAC;IAED,MAAMC,gBAAgB,GAAG,CAAC,GAAGnE,aAAa,EAAE6D,WAAW,CAAC;IACxD5D,gBAAgB,CAACkE,gBAAgB,CAAC;IAClChE,iBAAiB,CAAC,EAAE,CAAC;IAErB,IAAI;MACF;MACA,MAAM2C,QAAQ,GAAG,MAAMtD,KAAK,CAACuD,IAAI,CAC/B,GAAGzD,MAAM,2BAA2B,EACpC;QACEmC,OAAO,EAAEiC,WAAW,CAACb,IAAI,CAAC,CAAC;QAC3BG,SAAS,EAAElD,QAAQ,CAACmD;MACtB,CAAC,EACD;QAAEI,OAAO,EAAE9D,UAAU,CAAC;MAAE,CAC1B,CAAC;MAED,MAAM6E,cAAc,GAAGtB,QAAQ,CAACI,IAAI;;MAEpC;MACA,MAAMmB,UAAU,GAAG;QACjBjD,EAAE,EAAE0C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClB1B,IAAI,EAAE,WAAW;QACjB2B,OAAO,EAAEI,cAAc,CAAC3C,OAAO,IAAI,gEAAgE;QACnGwC,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;QACnCI,OAAO,EAAEF,cAAc,CAACE,OAAO,IAAI,EAAE;QACrCC,QAAQ,EAAEH,cAAc,CAACG,QAAQ,IAAI,CAAC;MACxC,CAAC;MAED,MAAMC,oBAAoB,GAAG,CAAC,GAAGL,gBAAgB,EAAEE,UAAU,CAAC;MAC9DpE,gBAAgB,CAACuE,oBAAoB,CAAC;;MAEtC;MACA,IAAIJ,cAAc,CAACE,OAAO,IAAIF,cAAc,CAACE,OAAO,CAAC7B,MAAM,GAAG,CAAC,EAAE;QAC/D,IAAIgC,aAAa,GAAG,EAAE;QACtB,KAAK,MAAMtC,MAAM,IAAIiC,cAAc,CAACE,OAAO,EAAE;UAC3C,IAAI;YACF,MAAMI,MAAM,GAAG,MAAMC,eAAe,CAACxC,MAAM,CAAC;YAC5CsC,aAAa,CAACG,IAAI,CAACF,MAAM,CAAC;UAC5B,CAAC,CAAC,OAAO/C,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;YAClD8C,aAAa,CAACG,IAAI,CAAC;cACjBtB,OAAO,EAAE,KAAK;cACd3B,KAAK,EAAEA,KAAK,CAACF,OAAO,IAAI;YAC1B,CAAC,CAAC;UACJ;QACF;;QAEA;QACA,IAAIgD,aAAa,CAACI,IAAI,CAACH,MAAM,IAAIA,MAAM,CAACpB,OAAO,CAAC,IAAIvD,YAAY,EAAE;UAChEA,YAAY,CAAC,CAAC;QAChB;;QAEA;QACA,MAAM+E,aAAa,GAAGL,aAAa,CAACxD,MAAM,CAACyD,MAAM,IAAI,CAACA,MAAM,CAACpB,OAAO,CAAC;QACrE,IAAIwB,aAAa,CAACrC,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAMsC,YAAY,GAAG;YACnB3D,EAAE,EAAE0C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;YAClB1B,IAAI,EAAE,WAAW;YACjB2B,OAAO,EAAE,qEAAqEc,aAAa,CAACE,GAAG,CAACN,MAAM,IAAI,KAAKA,MAAM,CAAC/C,KAAK,EAAE,CAAC,CAACsD,IAAI,CAAC,IAAI,CAAC,EAAE;YAC3IhB,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;YACnCgB,OAAO,EAAE;UACX,CAAC;UACDjF,gBAAgB,CAACkF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEJ,YAAY,CAAC,CAAC;QACnD;MACF;MAEA,MAAMK,kBAAkB,GAAGpF,aAAa,CAACyC,MAAM,GAAG,CAAC,GACjD,CAAC,GAAG0B,gBAAgB,EAAEE,UAAU,CAAC,GACjC,CAACR,WAAW,EAAEQ,UAAU,CAAC;;MAE3B;MACA,MAAMgB,gBAAgB,GAAGzE,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;MACxF,MAAMuE,sBAAsB,GAAGD,gBAAgB,CAACpE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKrB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAC;MAC5F,MAAMJ,iBAAiB,GAAGwD,oBAAoB,CAACQ,GAAG,CAAC9D,IAAI,KAAK;QAC1D,GAAGA,IAAI;QACPC,MAAM,EAAErB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE;QACpBmE,QAAQ,EAAEzF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqD;MACtB,CAAC,CAAC,CAAC;MAEHrC,YAAY,CAAC0E,OAAO,CAAC,qBAAqB,EAAE5E,IAAI,CAAC6E,SAAS,CAAC,CACzD,GAAGH,sBAAsB,EACzB,GAAGtE,iBAAiB,CACrB,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAM+D,aAAa,GAAG;QACpBtE,EAAE,EAAE0C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClB1B,IAAI,EAAE,WAAW;QACjB2B,OAAO,EAAE,gEAAgErC,KAAK,CAACF,OAAO,IAAI,eAAe,qBAAqB;QAC9HwC,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;QACnCgB,OAAO,EAAE;MACX,CAAC;MACDjF,gBAAgB,CAAC,CAAC,GAAGkE,gBAAgB,EAAEuB,aAAa,CAAC,CAAC;MACtDlF,QAAQ,CAACmB,KAAK,CAACF,OAAO,IAAI,wBAAwB,CAAC;IACrD,CAAC,SAAS;MACRpB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMsF,kBAAkB,GAAGA,CAAClE,OAAO,EAAE3B,QAAQ,KAAK;IAChD,MAAM8F,YAAY,GAAGnE,OAAO,CAAC8B,WAAW,CAAC,CAAC;IAC1C,MAAMsC,UAAU,GAAG,CAAA/F,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE+F,UAAU,KAAI,EAAE;IAC7C,MAAMC,UAAU,GAAGD,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS;MAAA,IAAAC,gBAAA;MAAA,OAAKF,GAAG,IAAI,EAAAE,gBAAA,GAAAD,SAAS,CAACE,KAAK,cAAAD,gBAAA,uBAAfA,gBAAA,CAAiBzD,MAAM,KAAI,CAAC,CAAC;IAAA,GAAE,CAAC,CAAC;IACjG,MAAM2D,aAAa,GAAGP,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS;MAAA,IAAAI,iBAAA;MAAA,OACrDL,GAAG,KAAAK,iBAAA,GAAGJ,SAAS,CAACE,KAAK,cAAAE,iBAAA,uBAAfA,iBAAA,CAAiBN,MAAM,CAAC,CAACO,OAAO,EAAEC,IAAI;QAAA,IAAAC,cAAA;QAAA,OAAKF,OAAO,IAAI,EAAAE,cAAA,GAAAD,IAAI,CAACE,QAAQ,cAAAD,cAAA,uBAAbA,cAAA,CAAe/D,MAAM,KAAI,CAAC,CAAC;MAAA,GAAE,CAAC,CAAC,KAAI,CAAC;IAAA,GAAE,CAAC,CAAC;;IAEtG;IACA,MAAMiE,cAAc,GAAG;MACrBvD,IAAI,EAAE,CAAArD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqD,IAAI,KAAI,cAAc;MACtCwD,cAAc,EAAEd,UAAU,CAACpD,MAAM;MACjCmE,SAAS,EAAEd,UAAU;MACrBe,YAAY,EAAET,aAAa;MAC3BU,cAAc,EAAEjB,UAAU,CAACb,GAAG,CAAC+B,CAAC,IAAIA,CAAC,CAAC5D,IAAI,CAAC,CAAC6D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE;IAC3D,CAAC;;IAED;IACA,IAAIpB,YAAY,CAACqB,QAAQ,CAAC,WAAW,CAAC,KAAKrB,YAAY,CAACqB,QAAQ,CAAC,KAAK,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3I,OAAO,0CAA0CP,cAAc,CAACvD,IAAI,0BAA0BuD,cAAc,CAACC,cAAc,gBAAgBD,cAAc,CAACI,cAAc,CAAC7B,IAAI,CAAC,IAAI,CAAC,GAAGyB,cAAc,CAACC,cAAc,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE;AAC1O;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mGAAmG;IAC/F;IAEA,IAAIf,YAAY,CAACqB,QAAQ,CAAC,UAAU,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC7G,MAAMC,eAAe,GAAGrB,UAAU,CAACb,GAAG,CAACiB,SAAS,IAAI;QAAA,IAAAkB,iBAAA,EAAAC,iBAAA,EAAAC,qBAAA;QAClD,MAAMT,SAAS,GAAG,EAAAO,iBAAA,GAAAlB,SAAS,CAACE,KAAK,cAAAgB,iBAAA,uBAAfA,iBAAA,CAAiB1E,MAAM,KAAI,CAAC;QAC9C,MAAM6E,cAAc,GAAG,EAAAF,iBAAA,GAAAnB,SAAS,CAACE,KAAK,cAAAiB,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiBnG,MAAM,CAACsF,IAAI,IAAIA,IAAI,CAACgB,MAAM,KAAK,WAAW,CAAC,cAAAF,qBAAA,uBAA5DA,qBAAA,CAA8D5E,MAAM,KAAI,CAAC;QAChG,OAAO,KAAKwD,SAAS,CAAC9C,IAAI,KAAKmE,cAAc,IAAIV,SAAS,kBAAkB;MAC9E,CAAC,CAAC,CAAC3B,IAAI,CAAC,IAAI,CAAC;MAEb,OAAO,qCAAqCyB,cAAc,CAACvD,IAAI;AACrE;AACA;AACA,IAAIuD,cAAc,CAACC,cAAc;AACjC,IAAID,cAAc,CAACE,SAAS;AAC5B,IAAIF,cAAc,CAACG,YAAY;AAC/B;AACA;AACA,EAAEK,eAAe;AACjB;AACA;AACA;AACA;AACA,oDAAoD;IAChD;IAEA,IAAItB,YAAY,CAACqB,QAAQ,CAAC,UAAU,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,MAAM,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACzG,MAAMO,cAAc,GAAG3B,UAAU,CAAC4B,OAAO,CAACxB,SAAS;QAAA,IAAAyB,iBAAA;QAAA,OACjD,EAAAA,iBAAA,GAAAzB,SAAS,CAACE,KAAK,cAAAuB,iBAAA,uBAAfA,iBAAA,CAAiBzG,MAAM,CAACsF,IAAI,IAAIA,IAAI,CAACgB,MAAM,KAAK,WAAW,CAAC,CAACvC,GAAG,CAACuB,IAAI,IACnE,IAAIA,IAAI,CAACpD,IAAI,QAAQ8C,SAAS,CAAC9C,IAAI,EACrC,CAAC,KAAI,EAAE;MAAA,CACT,CAAC,CAAC6D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAEb,OAAO;AACb;AACA,EAAEQ,cAAc,CAACxC,GAAG,CAACuB,IAAI,IAAI,KAAKA,IAAI,EAAE,CAAC,CAACtB,IAAI,CAAC,IAAI,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD;IAC5C;IAEA,IAAIW,YAAY,CAACqB,QAAQ,CAAC,KAAK,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,KAAK,CAAC,EAAE;MACnG,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,MAAM,CAAC,EAAE;QACjC,OAAO,qEAAqEP,cAAc,CAACC,cAAc;AACjH;AACA,EAAED,cAAc,CAACI,cAAc,CAAC9B,GAAG,CAAC,CAAC7B,IAAI,EAAEwE,CAAC,KAAK,GAAGA,CAAC,GAAG,CAAC,KAAKxE,IAAI,EAAE,CAAC,CAAC8B,IAAI,CAAC,IAAI,CAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;MAC3B;MAEA,OAAO,sCAAsCyB,cAAc,CAACvD,IAAI;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C;IAC1C;IAEA,IAAIyC,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACtE,OAAO;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF;IAChF;IAEA,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,MAAM,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC1I,OAAO,iDAAiDP,cAAc,CAACvD,IAAI;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE;IACjE;IAEA,IAAIyC,YAAY,CAACqB,QAAQ,CAAC,MAAM,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,iBAAiB,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,cAAc,CAAC,EAAE;MACtH,OAAO,sCAAsCP,cAAc,CAACvD,IAAI;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iGAAiG;IAC7F;;IAEA;IACA,OAAO,6BAA6B1B,OAAO;AAC/C;AACA,yBAAyBiF,cAAc,CAACvD,IAAI,UAAUuD,cAAc,CAACC,cAAc,mBAAmBD,cAAc,CAACE,SAAS;AAC9H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+FAA+F;EAC7F,CAAC;EAED,MAAMgB,cAAc,GAAInG,OAAO,IAAK;IAClC,MAAM6C,OAAO,GAAG,EAAE;IAClB,MAAMsB,YAAY,GAAGnE,OAAO,CAAC8B,WAAW,CAAC,CAAC;;IAE1C;IACA,MAAMsE,cAAc,GAAG,CACrB;MACElF,OAAO,EAAE,uCAAuC;MAChDN,IAAI,EAAE,eAAe;MACrByF,UAAU,EAAE,GAAG;MACf1E,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,6BAA6B;MACtCN,IAAI,EAAE,eAAe;MACrByF,UAAU,EAAE,GAAG;MACf1E,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,wBAAwB;MACjCN,IAAI,EAAE,UAAU;MAChByF,UAAU,EAAE,GAAG;MACf1E,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,2BAA2B;MACpCN,IAAI,EAAE,aAAa;MACnByF,UAAU,EAAE,GAAG;MACf1E,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,uBAAuB;MAChCN,IAAI,EAAE,aAAa;MACnByF,UAAU,EAAE,GAAG;MACf1E,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,6BAA6B;MACtCN,IAAI,EAAE,aAAa;MACnByF,UAAU,EAAE,GAAG;MACf1E,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,4BAA4B;MACrCN,IAAI,EAAE,eAAe;MACrByF,UAAU,EAAE,GAAG;MACf1E,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,uBAAuB;MAChCN,IAAI,EAAE,WAAW;MACjByF,UAAU,EAAE,GAAG;MACf1E,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,mBAAmB;MAC5BN,IAAI,EAAE,aAAa;MACnByF,UAAU,EAAE,GAAG;MACf1E,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,8BAA8B;MACvCN,IAAI,EAAE,cAAc;MACpByF,UAAU,EAAE,GAAG;MACf1E,WAAW,EAAE;IACf,CAAC,CACF;;IAED;IACA,MAAMd,QAAQ,GAAG;MACfE,SAAS,EAAE,EAAE;MACbsE,cAAc,EAAE,EAAE;MAClBiB,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE;IACd,CAAC;;IAED;IACA,MAAMC,UAAU,GAAGxG,OAAO,CAACmB,KAAK,CAAC,YAAY,CAAC;IAC9C,IAAIqF,UAAU,EAAE;MACd3F,QAAQ,CAACE,SAAS,GAAGyF,UAAU,CAACjD,GAAG,CAACkD,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC/D;;IAEA;IACA,MAAMC,YAAY,GAAG3G,OAAO,CAACmB,KAAK,CAAC,6FAA6F,CAAC;IACjI,IAAIwF,YAAY,EAAE;MAChB9F,QAAQ,CAACyF,KAAK,GAAGK,YAAY;IAC/B;;IAEA;IACA,MAAMC,gBAAgB,GAAG5G,OAAO,CAACmB,KAAK,CAAC,2DAA2D,CAAC;IACnG,IAAIyF,gBAAgB,EAAE;MACpB/F,QAAQ,CAAC0F,UAAU,GAAGK,gBAAgB;IACxC;;IAEA;IACAR,cAAc,CAACS,OAAO,CAACC,KAAA,IAAgD;MAAA,IAA/C;QAAE5F,OAAO;QAAEN,IAAI;QAAEyF,UAAU;QAAE1E;MAAY,CAAC,GAAAmF,KAAA;MAChE,IAAI5F,OAAO,CAAC6F,IAAI,CAAC5C,YAAY,CAAC,EAAE;QAC9BtB,OAAO,CAACM,IAAI,CAAC;UACXvC,IAAI;UACJyF,UAAU;UACV1E,WAAW;UACXd,QAAQ,EAAEA,QAAQ;UAClBF,eAAe,EAAEX;QACnB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAO6C,OAAO;EAChB,CAAC;EAED,MAAMmE,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBrH,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMsH,eAAe,GAAI7E,SAAS,IAAK;IACrC,OAAO,IAAIH,IAAI,CAACG,SAAS,CAAC,CAAC8E,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACEvJ,OAAA,CAAChB,GAAG;IAACwK,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpE5J,OAAA,CAACd,KAAK;MACJ2K,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,MAAM;QACpBC,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,eAEF5J,OAAA,CAAChB,GAAG;QAACwK,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAES,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACzD5J,OAAA,CAACX,MAAM;UACLmK,EAAE,EAAE;YACFU,eAAe,EAAEvK,eAAe;YAChC0K,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE;UACV,CAAE;UAAAG,QAAA,eAEF5J,OAAA,CAACN,OAAO;YAAC4K,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACT3K,OAAA,CAAChB,GAAG;UAAA4K,QAAA,gBACF5J,OAAA,CAACf,UAAU;YACT2L,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFqB,UAAU,EAAE,kCAAkC;cAC9CC,UAAU,EAAE,GAAG;cACfP,KAAK,EAAE;YACT,CAAE;YAAAX,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3K,OAAA,CAACf,UAAU;YACT2L,OAAO,EAAC,SAAS;YACjBpB,EAAE,EAAE;cACFe,KAAK,EAAE,MAAM;cACbM,UAAU,EAAE;YACd,CAAE;YAAAjB,QAAA,GACH,YACW,EAACxJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqD,IAAI;UAAA;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN3K,OAAA,CAACT,IAAI;UACHwL,KAAK,EAAC,MAAM;UACZC,IAAI,EAAC,OAAO;UACZxB,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGvK,eAAe,IAAI;YACvC4K,KAAK,EAAE5K,eAAe;YACtBmL,UAAU,EAAE,GAAG;YACfG,EAAE,EAAE;UACN;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGR3K,OAAA,CAACd,KAAK;MACJ2K,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACF0B,IAAI,EAAE,CAAC;QACPlB,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE,MAAM;QACjBlB,YAAY,EAAE,MAAM;QACpBmB,QAAQ,EAAE,MAAM;QAChBtB,CAAC,EAAE,CAAC;QACJI,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,EAEDtJ,aAAa,CAACyC,MAAM,KAAK,CAAC,gBACzB/C,OAAA,CAAChB,GAAG;QACFwK,EAAE,EAAE;UACFE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBQ,UAAU,EAAE,QAAQ;UACpBkB,cAAc,EAAE,QAAQ;UACxB5B,MAAM,EAAE,MAAM;UACd6B,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,gBAEF5J,OAAA,CAACX,MAAM;UACLmK,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGvK,eAAe,IAAI;YACvC0K,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE,EAAE;YACV8B,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEF5J,OAAA,CAACN,OAAO;YAAC4K,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAE5K;UAAgB;YAAA6K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACT3K,OAAA,CAACf,UAAU;UACT2L,OAAO,EAAC,IAAI;UACZpB,EAAE,EAAE;YACFqB,UAAU,EAAE,kCAAkC;YAC9CC,UAAU,EAAE,GAAG;YACfP,KAAK,EAAE,MAAM;YACbgB,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3K,OAAA,CAACf,UAAU;UACT2L,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YACFe,KAAK,EAAE,MAAM;YACbM,UAAU,EAAE,kCAAkC;YAC9CW,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAEN3K,OAAA,CAAChB,GAAG;QAAA4K,QAAA,GACDtJ,aAAa,CAACgF,GAAG,CAAEvD,OAAO,iBACzB/B,OAAA,CAAChB,GAAG;UAEFwK,EAAE,EAAE;YACFE,OAAO,EAAE,MAAM;YACf2B,cAAc,EAAEtJ,OAAO,CAACY,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,YAAY;YACnE4I,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEF5J,OAAA,CAAChB,GAAG;YACFwK,EAAE,EAAE;cACFgC,QAAQ,EAAE,KAAK;cACf9B,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE5H,OAAO,CAACY,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,KAAK;cAC9DwH,UAAU,EAAE,YAAY;cACxBC,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBAEF5J,OAAA,CAACX,MAAM;cACLmK,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAEnI,OAAO,CAACY,IAAI,KAAK,MAAM,GAAG,SAAS,GAAGhD;cACzD,CAAE;cAAAiK,QAAA,eAEF5J,OAAA,CAACN,OAAO;gBACN4K,IAAI,EAAEvI,OAAO,CAACY,IAAI,KAAK,MAAM,GAAG,yBAAyB,GAAG,WAAY;gBACxE0H,KAAK,EAAE,EAAG;gBACVZ,MAAM,EAAE,EAAG;gBACXc,KAAK,EAAExI,OAAO,CAACY,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG;cAAO;gBAAA6H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACT3K,OAAA,CAAChB,GAAG;cAAA4K,QAAA,gBACF5J,OAAA,CAACd,KAAK;gBACJ2K,SAAS,EAAE,CAAE;gBACbL,EAAE,EAAE;kBACFM,CAAC,EAAE,GAAG;kBACNC,YAAY,EAAE,MAAM;kBACpBG,eAAe,EAAEnI,OAAO,CAACY,IAAI,KAAK,MAAM,GAAGhD,eAAe,GAAG,SAAS;kBACtE4K,KAAK,EAAExI,OAAO,CAACY,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;kBAChDqH,MAAM,EAAEjI,OAAO,CAACyD,OAAO,GAAG,mBAAmB,GAAG;gBAClD,CAAE;gBAAAoE,QAAA,gBAEF5J,OAAA,CAACf,UAAU;kBACT2L,OAAO,EAAC,OAAO;kBACfpB,EAAE,EAAE;oBACFqB,UAAU,EAAE,kCAAkC;oBAC9CY,UAAU,EAAE,GAAG;oBACfC,UAAU,EAAE;kBACd,CAAE;kBAAA9B,QAAA,EAED7H,OAAO,CAACuC;gBAAO;kBAAAkG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAGZ5I,OAAO,CAACY,IAAI,KAAK,WAAW,IAAIZ,OAAO,CAAC6C,OAAO,IAAI7C,OAAO,CAAC6C,OAAO,CAAC7B,MAAM,GAAG,CAAC,iBAC5E/C,OAAA,CAAChB,GAAG;kBAACwK,EAAE,EAAE;oBAAEmC,EAAE,EAAE,GAAG;oBAAEjC,OAAO,EAAE,MAAM;oBAAEkC,QAAQ,EAAE,MAAM;oBAAExB,GAAG,EAAE;kBAAE,CAAE;kBAAAR,QAAA,EAC7D7H,OAAO,CAAC6C,OAAO,CAAC0C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChC,GAAG,CAAC,CAAC7C,MAAM,EAAEoJ,WAAW,kBACnD7L,OAAA,CAACT,IAAI;oBAEHwL,KAAK,EAAEtI,MAAM,CAACiB,WAAY;oBAC1BsH,IAAI,EAAC,OAAO;oBACZc,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACArL,iBAAiB,CAACgC,MAAM,CAACC,eAAe,IAAI,UAAUD,MAAM,CAACiB,WAAW,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC;sBACzF,IAAIhD,QAAQ,CAACwB,OAAO,EAAE;wBACpBxB,QAAQ,CAACwB,OAAO,CAAC0J,KAAK,CAAC,CAAC;sBAC1B;oBACF,CAAE;oBACFvC,EAAE,EAAE;sBACFU,eAAe,EAAE,MAAM;sBACvBF,MAAM,EAAE,aAAarK,eAAe,EAAE;sBACtC4K,KAAK,EAAE5K,eAAe;sBACtBqM,QAAQ,EAAE,QAAQ;sBAClBvC,MAAM,EAAE,MAAM;sBACdwC,MAAM,EAAE,SAAS;sBACjB,SAAS,EAAE;wBACT/B,eAAe,EAAE,GAAGvK,eAAe;sBACrC;oBACF;kBAAE,GApBGkM,WAAW;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqBjB,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACR3K,OAAA,CAACf,UAAU;gBACT2L,OAAO,EAAC,SAAS;gBACjBpB,EAAE,EAAE;kBACFe,KAAK,EAAE,MAAM;kBACbM,UAAU,EAAE,kCAAkC;kBAC9CmB,QAAQ,EAAE,QAAQ;kBAClBL,EAAE,EAAE,GAAG;kBACPjC,OAAO,EAAE,OAAO;kBAChB4B,SAAS,EAAEvJ,OAAO,CAACY,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG;gBACjD,CAAE;gBAAAiH,QAAA,EAEDR,eAAe,CAACrH,OAAO,CAACwC,SAAS;cAAC;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjGD5I,OAAO,CAACL,EAAE;UAAA8I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkGZ,CACN,CAAC,EACDjK,SAAS,iBACRV,OAAA,CAAChB,GAAG;UAACwK,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,YAAY;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eAChE5J,OAAA,CAAChB,GAAG;YAACwK,EAAE,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAES,UAAU,EAAE,YAAY;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAR,QAAA,gBAC7D5J,OAAA,CAACX,MAAM;cACLmK,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAEvK;cACnB,CAAE;cAAAiK,QAAA,eAEF5J,OAAA,CAACN,OAAO;gBAAC4K,IAAI,EAAC,WAAW;gBAACD,KAAK,EAAE,EAAG;gBAACZ,MAAM,EAAE,EAAG;gBAACc,KAAK,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACT3K,OAAA,CAACd,KAAK;cACJ2K,SAAS,EAAE,CAAE;cACbL,EAAE,EAAE;gBACFM,CAAC,EAAE,GAAG;gBACNC,YAAY,EAAE,MAAM;gBACpBG,eAAe,EAAE,SAAS;gBAC1BR,OAAO,EAAE,MAAM;gBACfS,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,gBAEF5J,OAAA,CAACV,gBAAgB;gBAAC0L,IAAI,EAAE,EAAG;gBAACxB,EAAE,EAAE;kBAAEe,KAAK,EAAE5K;gBAAgB;cAAE;gBAAA6K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9D3K,OAAA,CAACf,UAAU;gBACT2L,OAAO,EAAC,OAAO;gBACfpB,EAAE,EAAE;kBACFqB,UAAU,EAAE,kCAAkC;kBAC9CN,KAAK,EAAE;gBACT,CAAE;gBAAAX,QAAA,EACH;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eACD3K,OAAA;UAAKkM,GAAG,EAAEtL;QAAe;UAAA4J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGR3K,OAAA,CAACd,KAAK;MACJ2K,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE;MACb,CAAE;MAAAvB,QAAA,eAEF5J,OAAA,CAAChB,GAAG;QAACwK,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEU,GAAG,EAAE,CAAC;UAAED,UAAU,EAAE;QAAW,CAAE;QAAAP,QAAA,gBAC3D5J,OAAA,CAACb,SAAS;UACR0B,QAAQ,EAAEA,QAAS;UACnBsL,KAAK,EAAE3L,cAAe;UACtB4L,QAAQ,EAAGpD,CAAC,IAAKvI,iBAAiB,CAACuI,CAAC,CAACqD,MAAM,CAACF,KAAK,CAAE;UACnDG,UAAU,EAAEvD,cAAe;UAC3BwD,WAAW,EAAC,uCAAuC;UACnDC,SAAS;UACTC,OAAO,EAAE,CAAE;UACXC,SAAS;UACT9B,OAAO,EAAC,UAAU;UAClB+B,QAAQ,EAAEjM,SAAU;UACpB8I,EAAE,EAAE;YACF,0BAA0B,EAAE;cAC1BO,YAAY,EAAE,KAAK;cACnBc,UAAU,EAAE;YACd;UACF;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF3K,OAAA,CAACR,OAAO;UAACoN,KAAK,EAAC,cAAc;UAAAhD,QAAA,eAC3B5J,OAAA,CAACZ,UAAU;YACT0M,OAAO,EAAEA,CAAA,KAAMhK,iBAAiB,CAAC,CAAE;YACnC6K,QAAQ,EAAE,CAACnM,cAAc,CAAC2C,IAAI,CAAC,CAAC,IAAIzC,SAAU;YAC9C8I,EAAE,EAAE;cACFU,eAAe,EAAE1J,cAAc,CAAC2C,IAAI,CAAC,CAAC,IAAI,CAACzC,SAAS,GAAGf,eAAe,GAAG,SAAS;cAClF4K,KAAK,EAAE/J,cAAc,CAAC2C,IAAI,CAAC,CAAC,IAAI,CAACzC,SAAS,GAAG,MAAM,GAAG,MAAM;cAC5D,SAAS,EAAE;gBACTwJ,eAAe,EAAE1J,cAAc,CAAC2C,IAAI,CAAC,CAAC,IAAI,CAACzC,SAAS,GAAG,SAAS,GAAG;cACrE;YACF,CAAE;YAAAkJ,QAAA,eAEF5J,OAAA,CAACN,OAAO;cAAC4K,IAAI,EAAC,uBAAuB;cAACD,KAAK,EAAE,EAAG;cAACZ,MAAM,EAAE;YAAG;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACxK,EAAA,CA10BIF,QAAQ;EAAA,QAOKR,WAAW;AAAA;AAAAoN,EAAA,GAPxB5M,QAAQ;AA40Bd,eAAeA,QAAQ;AAAC,IAAA4M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{Box,Typography,Paper,Chip,LinearProgress,Button,Divider,TextField,IconButton,Tooltip,Checkbox,CircularProgress,Avatar,AvatarGroup,Collapse}from'@mui/material';import Iconify from'components/Iconify/index';import{mainYellowColor}from\"helpers/constants\";import{STATUS,STATUS_CONFIG}from'../hooks/usePlanData';import styles from'../styles.module.scss';import CommentDialog from'../dialogs/CommentDialog';import DueDateDialog from'../dialogs/DueDateDialog';import AssignMemberDialog from'../dialogs/AssignMemberDialog';import{getComments,addComment,updateComment,deleteComment,updateTask,assignMembersToTask}from'../../services';import{toast}from'react-toastify';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const MilestoneCard=_ref=>{var _milestone$tasks,_milestone$tasks2,_milestone$tasks3;let{milestone,compact=false,showSubtasks=false,calculateMilestoneProgress,getMilestoneStatus,calculateTaskProgress,getTaskStatus,calculateSubtaskProgress,getSubtaskStatus,onUpdateMilestone,onUpdateTask,onUpdateSubtask,onAddTask,onAddSubtask,onDeleteTask,onDeleteSubtask,onAssignMembers,invitedUsers,planOwner}=_ref;const[isEditing,setIsEditing]=useState(false);const[milestoneName,setMilestoneName]=useState(milestone.name);const[isAddingTask,setIsAddingTask]=useState(false);const[newTaskName,setNewTaskName]=useState('');// Initialize collapse state from localStorage\nconst[isExpanded,setIsExpanded]=useState(()=>{const savedState=localStorage.getItem(`milestone_${milestone.id||milestone.name}_expanded`);return savedState!==null?JSON.parse(savedState):true;// Default to expanded\n});const inputRef=useRef(null);const newTaskInputRef=useRef(null);// Handle expand/collapse toggle with state persistence\nconst handleToggleExpanded=()=>{const newExpandedState=!isExpanded;setIsExpanded(newExpandedState);// Save state to localStorage\nlocalStorage.setItem(`milestone_${milestone.id||milestone.name}_expanded`,JSON.stringify(newExpandedState));};// Calculate milestone progress\nconst progress=calculateMilestoneProgress?calculateMilestoneProgress(milestone):0;// Determine status based on progress\nconst milestoneStatus=getMilestoneStatus?getMilestoneStatus(milestone):STATUS.NOT_STARTED;const statusConfig=STATUS_CONFIG[milestoneStatus]||STATUS_CONFIG[STATUS.NOT_STARTED];// Check if description exists and is not empty\nconst hasDescription=milestone.description&&milestone.description.trim().length>0;// Handle edit mode\nconst handleEditClick=()=>{setIsEditing(true);};// Handle save changes\nconst handleSave=()=>{// Trim the name to remove leading/trailing whitespace\nconst trimmedName=milestoneName.trim();// Only update if the name has actually changed (after trimming)\nif(trimmedName!==''&&trimmedName!==milestone.name){if(onUpdateMilestone){onUpdateMilestone({...milestone,name:trimmedName});}}else{// Reset to original if empty or unchanged\nsetMilestoneName(milestone.name);}setIsEditing(false);};// Handle key press events\nconst handleKeyPress=e=>{if(e.key==='Enter'){handleSave();}else if(e.key==='Escape'){setMilestoneName(milestone.name);setIsEditing(false);}};// Handle add task\nconst handleAddTaskClick=()=>{setIsAddingTask(true);};// Handle save new task\nconst handleSaveNewTask=()=>{const trimmedName=newTaskName.trim();if(trimmedName&&onAddTask){const newTask={name:trimmedName,milestone:milestone.id,status:STATUS.NOT_STARTED,progress:0};onAddTask(newTask);setNewTaskName('');}setIsAddingTask(false);};// Handle key press events for new task\nconst handleNewTaskKeyPress=e=>{if(e.key==='Enter'){handleSaveNewTask();}else if(e.key==='Escape'){setNewTaskName('');setIsAddingTask(false);}};// Focus input when editing starts\nuseEffect(()=>{if(isEditing&&inputRef.current){inputRef.current.focus();}},[isEditing]);// Focus input when adding task\nuseEffect(()=>{if(isAddingTask&&newTaskInputRef.current){newTaskInputRef.current.focus();}},[isAddingTask]);return/*#__PURE__*/_jsxs(Paper,{elevation:0,className:styles.milestoneCard,sx:{padding:'16px',paddingBottom:'5px'},children:[/*#__PURE__*/_jsxs(Box,{className:styles.milestoneHeader,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',flex:1},children:[/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleToggleExpanded,sx:{mr:1,color:'#666','&:hover':{backgroundColor:'rgba(0, 0, 0, 0.04)'}},children:/*#__PURE__*/_jsx(Iconify,{icon:isExpanded?\"material-symbols:expand-less\":\"material-symbols:expand-more\",width:20,height:20})}),isEditing?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',width:'100%'},children:[/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:flag\",width:20,height:20,color:mainYellowColor,sx:{mr:1}}),/*#__PURE__*/_jsx(TextField,{inputRef:inputRef,value:milestoneName,onChange:e=>setMilestoneName(e.target.value),onKeyDown:handleKeyPress,onBlur:handleSave,variant:\"standard\",fullWidth:true,autoFocus:true,sx:{fontFamily:'\"Recursive Variable\", sans-serif',fontWeight:600,fontSize:'1.1rem','& .MuiInputBase-input':{fontWeight:600,fontSize:'1.1rem',fontFamily:'\"Recursive Variable\", sans-serif'}}}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleSave,sx:{ml:1},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:check\",width:20,height:20,color:\"#4CAF50\"})})]}):/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",className:styles.milestoneTitle,sx:{fontFamily:'\"Recursive Variable\", sans-serif',display:'flex',alignItems:'center',cursor:'pointer',flex:1,'&:hover':{'& .edit-icon':{opacity:1}}},onClick:handleEditClick,children:[/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:flag\",width:20,height:20,color:mainYellowColor}),milestoneName,/*#__PURE__*/_jsx(Tooltip,{title:\"Edit milestone name\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",className:\"edit-icon\",sx:{ml:1,opacity:0,transition:'opacity 0.2s',padding:'2px'},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:edit-outline\",width:16,height:16,color:\"#666\"})})})]})]}),/*#__PURE__*/_jsx(Chip,{icon:/*#__PURE__*/_jsx(Iconify,{icon:statusConfig.icon,width:16,height:16}),label:statusConfig.label,size:\"small\",sx:{backgroundColor:`${statusConfig.color}20`,color:statusConfig.color,fontWeight:600,borderRadius:'4px',fontFamily:'\"Recursive Variable\", sans-serif'}})]}),/*#__PURE__*/_jsxs(Collapse,{in:isExpanded,timeout:\"auto\",unmountOnExit:true,children:[!compact&&hasDescription&&/*#__PURE__*/_jsx(Box,{sx:{mb:2,mt:1.5},children:/*#__PURE__*/_jsx(Box,{sx:{backgroundColor:'#f9f9f9',p:1.5,borderRadius:'8px',border:'1px solid #f0f0f0'},children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:'#333',fontFamily:'\"Recursive Variable\", sans-serif',whiteSpace:'pre-line',lineHeight:1.6,fontWeight:500},children:milestone.description})})}),/*#__PURE__*/_jsx(Divider,{sx:{my:2,opacity:0.6}}),/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:0.5},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontWeight:600,fontFamily:'\"Recursive Variable\", sans-serif',color:'#333'},children:\"Progress\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{fontWeight:700,fontFamily:'\"Recursive Variable\", sans-serif',color:'#333'},children:[progress,\"%\"]})]}),/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:progress,sx:{height:6,borderRadius:3,backgroundColor:'#f0f0f0','& .MuiLinearProgress-bar':{backgroundColor:statusConfig.color}}})]}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:1},children:milestone.estimated_duration&&/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:timer\",width:16,height:16,color:\"#333\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',color:'#333',fontWeight:500},children:milestone.estimated_duration})]})}),/*#__PURE__*/_jsxs(Box,{sx:{mb:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between',mb:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:checklist\",width:16,height:16,sx:{color:'#333'}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontWeight:600,color:'#333',fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.9rem'},children:\"Tasks\"})]}),/*#__PURE__*/_jsx(Tooltip,{title:\"Add new task\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleAddTaskClick,sx:{color:mainYellowColor,'&:hover':{backgroundColor:`${mainYellowColor}10`}},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:add-task\",width:18,height:18})})})]}),compact?/*#__PURE__*/_jsxs(Box,{className:styles.taskList,children:[(_milestone$tasks=milestone.tasks)===null||_milestone$tasks===void 0?void 0:_milestone$tasks.slice(0,2).map((task,index)=>/*#__PURE__*/_jsx(TaskItem,{task:task,showSubtasks:showSubtasks,compact:true,calculateTaskProgress:calculateTaskProgress,getTaskStatus:getTaskStatus,calculateSubtaskProgress:calculateSubtaskProgress,getSubtaskStatus:getSubtaskStatus,onUpdateTask:onUpdateTask,onUpdateSubtask:onUpdateSubtask,onAddSubtask:onAddSubtask,onDeleteTask:onDeleteTask,onDeleteSubtask:onDeleteSubtask,onAssignMembers:onAssignMembers,invitedUsers:invitedUsers,planOwner:planOwner},index)),((_milestone$tasks2=milestone.tasks)===null||_milestone$tasks2===void 0?void 0:_milestone$tasks2.length)>2&&/*#__PURE__*/_jsxs(Button,{variant:\"text\",size:\"small\",sx:{color:mainYellowColor,fontWeight:600,textTransform:'none',p:0,mt:1,fontFamily:'\"Recursive Variable\", sans-serif'},children:[\"+ \",milestone.tasks.length-2,\" more tasks\"]})]}):/*#__PURE__*/_jsxs(Box,{className:styles.taskList,children:[(_milestone$tasks3=milestone.tasks)===null||_milestone$tasks3===void 0?void 0:_milestone$tasks3.map((task,index)=>/*#__PURE__*/_jsx(TaskItem,{task:task,showSubtasks:showSubtasks,compact:false,calculateTaskProgress:calculateTaskProgress,getTaskStatus:getTaskStatus,calculateSubtaskProgress:calculateSubtaskProgress,getSubtaskStatus:getSubtaskStatus,onUpdateTask:onUpdateTask,onUpdateSubtask:onUpdateSubtask,onAddSubtask:onAddSubtask,onDeleteTask:onDeleteTask,onDeleteSubtask:onDeleteSubtask,onAssignMembers:onAssignMembers,invitedUsers:invitedUsers,planOwner:planOwner},index)),isAddingTask&&/*#__PURE__*/_jsxs(Box,{className:styles.taskItem,sx:{position:'relative',mt:1,display:'flex',alignItems:'center',backgroundColor:'#f9f9f9',borderRadius:'6px',padding:'8px 12px',border:'1px solid #f0f0f0'},children:[/*#__PURE__*/_jsx(Box,{sx:{width:10,height:10,borderRadius:'50%',backgroundColor:'#CCCCCC',mr:1.5,flexShrink:0}}),/*#__PURE__*/_jsx(TextField,{inputRef:newTaskInputRef,value:newTaskName,onChange:e=>setNewTaskName(e.target.value),onKeyDown:handleNewTaskKeyPress,placeholder:\"Enter new task name...\",variant:\"standard\",fullWidth:true,autoFocus:true,sx:{fontFamily:'\"Recursive Variable\", sans-serif','& .MuiInputBase-input':{fontSize:'0.9rem',fontFamily:'\"Recursive Variable\", sans-serif',padding:'4px 0'},'& .MuiInput-underline:before':{borderBottomColor:'rgba(0, 0, 0, 0.1)'}}}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',ml:'auto'},children:[/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleSaveNewTask,sx:{ml:1},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:check\",width:18,height:18,color:\"#4CAF50\"})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>setIsAddingTask(false),sx:{ml:0.5},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:close\",width:18,height:18,color:\"#F44336\"})})]})]})]})]})]})]});};// Component to display task and subtask\nconst TaskItem=_ref2=>{var _localTask$assignees;let{task,showSubtasks=false,compact=false,calculateTaskProgress,getTaskStatus,calculateSubtaskProgress,getSubtaskStatus,onUpdateTask,onUpdateSubtask,onAddSubtask,onDeleteTask,onDeleteSubtask,onAssignMembers,invitedUsers,planOwner}=_ref2;const[isEditing,setIsEditing]=useState(false);const[taskName,setTaskName]=useState(task.name);const taskInputRef=useRef(null);const[isAddingSubtask,setIsAddingSubtask]=useState(false);const[newSubtaskName,setNewSubtaskName]=useState('');const newSubtaskInputRef=useRef(null);// Comment functionality\nconst[commentDialogOpen,setCommentDialogOpen]=useState(false);const[comments,setComments]=useState([]);const[loadingComments,setLoadingComments]=useState(false);// Enable due date functionality\nconst[dueDateDialogOpen,setDueDateDialogOpen]=useState(false);const[updatingDueDate,setUpdatingDueDate]=useState(false);// Add state for assign member dialog\nconst[assignMemberDialogOpen,setAssignMemberDialogOpen]=useState(false);// Add state to store current task information\nconst[localTask,setLocalTask]=useState(task);// Update localTask when task changes from props\nuseEffect(()=>{setLocalTask(task);},[task]);const handleOpenComments=e=>{e.stopPropagation();setCommentDialogOpen(true);setLoadingComments(true);getComments(task.id).then(response=>{var _response$data,_response$data2;console.log('Comments data:',response.data);// Check data structure and get correct comments array\nlet commentsData=[];if(Array.isArray(response.data)){commentsData=response.data;}else if((_response$data=response.data)!==null&&_response$data!==void 0&&_response$data.data&&Array.isArray(response.data.data)){commentsData=response.data.data;}else if((_response$data2=response.data)!==null&&_response$data2!==void 0&&_response$data2.comments&&Array.isArray(response.data.comments)){commentsData=response.data.comments;}setComments(commentsData);}).catch(error=>{console.error('Error fetching comments:',error);toast.error('Failed to load comments');}).finally(()=>{setLoadingComments(false);});};const handleAddComment=async content=>{try{var _response$data3;const response=await addComment(task.id,content);console.log('Add comment response:',response);// Create new comment from response or create temporary object\nlet newComment;if((_response$data3=response.data)!==null&&_response$data3!==void 0&&_response$data3.data){newComment=response.data.data;}else{// Create temporary object if API does not return new comment\nconst currentUser=JSON.parse(localStorage.getItem('user'))||{};newComment={id:Date.now(),// Temporary ID\ncontent:content,user:{id:currentUser.id,first_name:currentUser.first_name,last_name:currentUser.last_name,avatar:currentUser.avatar},created_at:new Date().toLocaleString('en-US',{hour:'2-digit',minute:'2-digit',day:'2-digit',month:'2-digit'}).replace(',','')};}// Update state\nsetComments(prevComments=>[...prevComments,newComment]);// Refresh comments to ensure latest data\nrefreshComments();}catch(error){console.error('Error adding comment:',error);toast.error('Failed to add comment');}};const handleUpdateComment=async(commentId,content)=>{try{// Update UI first\nconst updatedCommentTemp=comments.find(c=>c.id===commentId);if(updatedCommentTemp){const updatedComments=comments.map(c=>c.id===commentId?{...c,content:content}:c);setComments(updatedComments);}// Call API\nconst response=await updateComment(commentId,content);console.log('Update comment response:',response);toast.success('Comment updated successfully');// Refresh comments to ensure latest data\nrefreshComments();}catch(error){console.error('Error updating comment:',error);toast.error('Failed to update comment');// Refresh comments to restore original state if there is an error\nrefreshComments();}};const handleDeleteComment=async commentId=>{try{// Update UI first\nsetComments(prevComments=>prevComments.filter(c=>c.id!==commentId));// Call API\nawait deleteComment(commentId);console.log('Comment deleted successfully');toast.success('Comment deleted successfully');// Refresh comments to ensure latest data\nrefreshComments();}catch(error){console.error('Error deleting comment:',error);toast.error('Failed to delete comment');// Refresh comments to restore original state if there is an error\nrefreshComments();}};// Refresh comments function\nconst refreshComments=async()=>{try{var _response$data4,_response$data5;const response=await getComments(task.id);console.log('Refreshed comments:',response.data);// Handle returned data\nlet commentsData=[];if(Array.isArray(response.data)){commentsData=response.data;}else if((_response$data4=response.data)!==null&&_response$data4!==void 0&&_response$data4.data&&Array.isArray(response.data.data)){commentsData=response.data.data;}else if((_response$data5=response.data)!==null&&_response$data5!==void 0&&_response$data5.comments&&Array.isArray(response.data.comments)){commentsData=response.data.comments;}setComments(commentsData);}catch(error){console.error('Error refreshing comments:',error);}};// Calculate task progress\nconst taskProgress=calculateTaskProgress?calculateTaskProgress(task):0;// Determine task status\nconst taskStatus=getTaskStatus?getTaskStatus(task):task.status||STATUS.NOT_STARTED;const statusConfig=STATUS_CONFIG[taskStatus]||STATUS_CONFIG[STATUS.NOT_STARTED];// Check if task has subtasks\nconst hasSubtasks=task.subtasks&&task.subtasks.length>0;// Check if task is completed\nconst isCompleted=taskStatus===STATUS.COMPLETED;// Handle edit mode for task\nconst handleTaskEditClick=()=>{setIsEditing(true);};// Handle save changes for task\nconst handleTaskSave=()=>{// Trim the name to remove leading/trailing whitespace\nconst trimmedName=taskName.trim();// Only update if the name has actually changed (after trimming)\nif(trimmedName!==''&&trimmedName!==task.name){if(onUpdateTask){onUpdateTask({...task,name:trimmedName});}}else{// Reset to original if empty or unchanged\nsetTaskName(task.name);}setIsEditing(false);};// Handle key press events for task\nconst handleTaskKeyPress=e=>{if(e.key==='Enter'){handleTaskSave();}else if(e.key==='Escape'){setTaskName(task.name);setIsEditing(false);}};// Handle add subtask\nconst handleAddSubtaskClick=()=>{setIsAddingSubtask(true);};// Handle delete task\nconst handleDeleteTaskClick=()=>{if(onDeleteTask){onDeleteTask(task);}};// Handle save new subtask\nconst handleSaveNewSubtask=()=>{const trimmedName=newSubtaskName.trim();if(trimmedName&&onAddSubtask){const newSubtask={name:trimmedName,task:task.slug,status:STATUS.NOT_STARTED,progress:0};onAddSubtask(newSubtask);setNewSubtaskName('');}setIsAddingSubtask(false);};// Handle key press events for new subtask\nconst handleNewSubtaskKeyPress=e=>{if(e.key==='Enter'){handleSaveNewSubtask();}else if(e.key==='Escape'){setNewSubtaskName('');setIsAddingSubtask(false);}};// Focus input when editing starts\nuseEffect(()=>{if(isEditing&&taskInputRef.current){taskInputRef.current.focus();}},[isEditing]);// Focus input when adding subtask\nuseEffect(()=>{if(isAddingSubtask&&newSubtaskInputRef.current){newSubtaskInputRef.current.focus();}},[isAddingSubtask]);// Check if task has due dates - use localTask instead of task\nconst hasDueDates=localTask.start_date||localTask.end_date;const handleOpenDueDateDialog=e=>{e.stopPropagation();setDueDateDialogOpen(true);};const handleUpdateDueDate=async updatedTask=>{setUpdatingDueDate(true);try{const taskToUpdate={...localTask,start_date:updatedTask.start_date,end_date:updatedTask.end_date,progress:localTask.progress||0,status:localTask.status||1};// Update local state first\nsetLocalTask(taskToUpdate);// Call API\nconst response=await updateTask(taskToUpdate);// If API returns data, update local state\nif(response&&response.data){setLocalTask(response.data);}// Update state in parent component if necessary\nif(onUpdateTask){onUpdateTask(taskToUpdate);}toast.success('Due date updated successfully');}catch(error){console.error('Error updating due date:',error);toast.error('Failed to update due date');// If there is an error, restore original state\nsetLocalTask(task);}finally{setUpdatingDueDate(false);setDueDateDialogOpen(false);// Close dialog after completion\n}};// Add handler for member assignment updates\nconst handleAssignMembers=async assignedUserIds=>{try{// Convert IDs to numbers\nconst numericIds=assignedUserIds.map(id=>Number(id));// Call API to assign members\nconst response=await assignMembersToTask(localTask.slug,numericIds);// Create updated task with new assignees\nconst updatedTask={...localTask,assignees:response.assignees||numericIds.map(id=>({id:id,first_name:'',last_name:'',email:''}))};// Update both local state and parent state\nsetLocalTask(updatedTask);if(onUpdateTask){onUpdateTask(updatedTask);}setAssignMemberDialogOpen(false);toast.success('Members assigned successfully');}catch(error){console.error('Error assigning members:',error);toast.error('Failed to assign members');}};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Box,{className:styles.taskItemContainer,children:[/*#__PURE__*/_jsxs(Box,{className:styles.taskItem,sx:{position:'relative'},children:[/*#__PURE__*/_jsx(Box,{sx:{width:10,height:10,borderRadius:'50%',backgroundColor:statusConfig.color,mr:1.5,flexShrink:0}}),isEditing?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',flexGrow:1},children:[/*#__PURE__*/_jsx(TextField,{inputRef:taskInputRef,value:taskName,onChange:e=>setTaskName(e.target.value),onKeyDown:handleTaskKeyPress,onBlur:handleTaskSave,variant:\"standard\",fullWidth:true,autoFocus:true,sx:{fontFamily:'\"Recursive Variable\", sans-serif','& .MuiInputBase-input':{fontSize:'0.9rem',fontFamily:'\"Recursive Variable\", sans-serif'}}}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleTaskSave,sx:{ml:1},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:check\",width:16,height:16,color:\"#4CAF50\"})})]}):/*#__PURE__*/_jsxs(Box,{sx:{flexGrow:1,display:'flex',flexDirection:'column',gap:0.5},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between',width:'100%'},children:[/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',gap:2,flexGrow:1},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",className:styles.taskName,sx:{fontFamily:'\"Recursive Variable\", sans-serif',cursor:'pointer',display:'flex',alignItems:'center',color:isCompleted?'#4CAF50':'inherit',fontWeight:isCompleted?500:400,flexGrow:1,'&:hover':{'& .task-edit-icon':{opacity:1}}},onClick:handleTaskEditClick,children:[taskName,/*#__PURE__*/_jsx(Tooltip,{title:\"Edit task name\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",className:\"task-edit-icon\",sx:{ml:0.5,opacity:0,transition:'opacity 0.2s',padding:'2px'},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:edit-outline\",width:14,height:14,color:\"#666\"})})})]})}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[localTask.assignees&&localTask.assignees.length>0&&/*#__PURE__*/_jsx(AvatarGroup,{max:3,sx:{'& .MuiAvatar-root':{width:24,height:24,fontSize:'0.75rem',border:'1.5px solid #fff'}},children:localTask.assignees.map((assignee,index)=>/*#__PURE__*/_jsx(Tooltip,{title:assignee.first_name&&assignee.last_name?`${assignee.first_name} ${assignee.last_name}`:assignee.email,arrow:true,children:/*#__PURE__*/_jsx(Avatar,{src:assignee.avatar,alt:assignee.first_name||assignee.email,sx:{bgcolor:mainYellowColor},children:assignee.first_name?assignee.first_name.charAt(0).toUpperCase():assignee.email?assignee.email.charAt(0).toUpperCase():''})},assignee.id))}),/*#__PURE__*/_jsxs(Box,{className:\"task-toolbar\",sx:{position:'relative',display:'flex',alignItems:'center','&:hover .toolbar-expanded':{opacity:1,visibility:'visible',transform:'translateX(0)'},'&:hover .toolbar-dots':{opacity:0,visibility:'hidden'}},children:[/*#__PURE__*/_jsx(Box,{className:\"toolbar-dots\",sx:{opacity:1,visibility:'visible',transition:'all 0.2s ease',display:'flex',alignItems:'center',cursor:'pointer',padding:'4px 8px',borderRadius:'6px','&:hover':{backgroundColor:'rgba(0, 0, 0, 0.04)'}},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:more-horiz\",width:20,height:20,color:\"#666\"})}),/*#__PURE__*/_jsxs(Box,{className:\"toolbar-expanded\",sx:{position:'absolute',right:0,top:0,opacity:0,visibility:'hidden',transform:'translateX(10px)',transition:'all 0.2s ease',display:'flex',alignItems:'center',border:'1px solid #eee',borderRadius:'8px',padding:'2px 4px',background:'#fff',boxShadow:'0 2px 8px rgba(0,0,0,0.1)',zIndex:10},children:[/*#__PURE__*/_jsx(Tooltip,{title:hasDueDates?\"Edit due date\":\"Set due date\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleOpenDueDateDialog,sx:{color:hasDueDates?mainYellowColor:'#888','&:hover':{color:mainYellowColor,bgcolor:'rgba(255, 193, 7, 0.08)'}},children:/*#__PURE__*/_jsx(Iconify,{icon:hasDueDates?\"material-symbols:calendar-month\":\"material-symbols:calendar-add-on\",width:16,height:16})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"Comments\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleOpenComments,disabled:loadingComments,sx:{color:mainYellowColor,'&:hover':{bgcolor:'rgba(255, 193, 7, 0.08)'}},children:loadingComments?/*#__PURE__*/_jsx(CircularProgress,{size:16,sx:{color:mainYellowColor}}):/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:comment-outline\",width:16,height:16})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"Assign members\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>setAssignMemberDialogOpen(true),sx:{color:((_localTask$assignees=localTask.assignees)===null||_localTask$assignees===void 0?void 0:_localTask$assignees.length)>0?mainYellowColor:'#888','&:hover':{color:mainYellowColor,bgcolor:'rgba(255, 193, 7, 0.08)'}},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:account-multiple-plus\",width:16,height:16})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"Add subtask\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleAddSubtaskClick,sx:{color:mainYellowColor,'&:hover':{bgcolor:'rgba(255, 193, 7, 0.08)'}},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:add-task\",width:16,height:16})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"Edit task\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleTaskEditClick,sx:{color:'#666','&:hover':{bgcolor:'rgba(0, 0, 0, 0.04)'}},children:/*#__PURE__*/_jsx(Iconify,{icon:\"eva:edit-fill\",width:16,height:16})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"Delete task\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleDeleteTaskClick,sx:{color:'#F44336','&:hover':{bgcolor:'rgba(244, 67, 54, 0.08)'}},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:delete-outline\",width:16,height:16})})})]})]}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',bgcolor:statusConfig.color+'20',px:1,py:0.5,borderRadius:'4px'},children:/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",sx:{fontWeight:600,color:statusConfig.color,fontFamily:'\"Recursive Variable\", sans-serif'},children:[taskProgress,\"%\"]})})]})]}),hasDueDates&&/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',backgroundColor:`${mainYellowColor}08`,borderRadius:'4px',py:0.5,px:0.75,width:'fit-content'},children:[/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:calendar-month\",width:14,height:14,color:mainYellowColor}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.75rem',color:'#555',fontWeight:500,lineHeight:1,ml:0.5},children:[formatDate(localTask.start_date),\" ~ \",formatDate(localTask.end_date)]})]})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{pl:4,pr:1,pt:0.5,pb:0.5,borderLeft:`1px dashed ${statusConfig.color}`,ml:1.5,mt:0.5,display:(hasSubtasks||isAddingSubtask)&&showSubtasks?'block':'none'},children:[task.subtasks&&task.subtasks.map((subtask,index)=>/*#__PURE__*/_jsx(SubtaskItem,{subtask:subtask,index:index,totalSubtasks:task.subtasks.length,taskSlug:task.slug,calculateSubtaskProgress:calculateSubtaskProgress,getSubtaskStatus:getSubtaskStatus,onUpdateSubtask:onUpdateSubtask,onDeleteSubtask:onDeleteSubtask,onAssignMembers:onAssignMembers,invitedUsers:invitedUsers,planOwner:planOwner},index)),isAddingSubtask&&/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',py:0.5,borderBottom:'1px dotted #f0f0f0',backgroundColor:'#f9f9f9',borderRadius:'4px',px:1},children:[/*#__PURE__*/_jsx(Checkbox,{disabled:true,size:\"small\",sx:{p:0.5,mr:0.5,color:'#CCCCCC'},icon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:check-box-outline-blank\",width:18,height:18})}),/*#__PURE__*/_jsx(TextField,{inputRef:newSubtaskInputRef,value:newSubtaskName,onChange:e=>setNewSubtaskName(e.target.value),onKeyDown:handleNewSubtaskKeyPress,placeholder:\"Enter new subtask name...\",variant:\"standard\",fullWidth:true,autoFocus:true,sx:{fontFamily:'\"Recursive Variable\", sans-serif','& .MuiInputBase-input':{fontSize:'0.85rem',fontFamily:'\"Recursive Variable\", sans-serif',padding:'4px 0'},'& .MuiInput-underline:before':{borderBottomColor:'rgba(0, 0, 0, 0.1)'}}}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',ml:1},children:[/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleSaveNewSubtask,sx:{p:0.5},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:check\",width:18,height:18,color:\"#4CAF50\"})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>setIsAddingSubtask(false),sx:{p:0.5,ml:0.5},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:close\",width:18,height:18,color:\"#F44336\"})})]})]})]})]}),/*#__PURE__*/_jsx(CommentDialog,{open:commentDialogOpen,onClose:()=>setCommentDialogOpen(false),comments:comments,onAddComment:handleAddComment,onUpdateComment:handleUpdateComment,onDeleteComment:handleDeleteComment,loading:loadingComments,targetName:task.name,targetType:\"task\"}),/*#__PURE__*/_jsx(DueDateDialog,{open:dueDateDialogOpen,onClose:()=>setDueDateDialogOpen(false),task:localTask,onUpdateDueDate:handleUpdateDueDate,isUpdating:updatingDueDate}),/*#__PURE__*/_jsx(AssignMemberDialog,{open:assignMemberDialogOpen,onClose:()=>setAssignMemberDialogOpen(false),task:localTask,invitedUsers:invitedUsers,planOwner:planOwner,onAssignMembers:handleAssignMembers})]});};// Component for subtask\nconst SubtaskItem=_ref3=>{let{subtask,index,totalSubtasks,taskSlug,calculateSubtaskProgress,getSubtaskStatus,onUpdateSubtask,onDeleteSubtask,onAssignMembers,invitedUsers,planOwner}=_ref3;const[isEditing,setIsEditing]=useState(false);const[subtaskName,setSubtaskName]=useState(subtask.name);const[isChecked,setIsChecked]=useState(subtask.status===STATUS.COMPLETED||subtask.progress===100);const subtaskInputRef=useRef(null);// Update isChecked when subtask changes from outside\nuseEffect(()=>{setIsChecked(subtask.status===STATUS.COMPLETED||subtask.progress===100);},[subtask.status,subtask.progress]);// Handle edit mode for subtask\nconst handleSubtaskEditClick=()=>{setIsEditing(true);};// Handle save changes for subtask\nconst handleSubtaskSave=()=>{// Trim the name to remove leading/trailing whitespace\nconst trimmedName=subtaskName.trim();// Only update if the name has actually changed (after trimming)\nif(trimmedName!==''&&trimmedName!==subtask.name){if(onUpdateSubtask){onUpdateSubtask({...subtask,name:trimmedName,task:taskSlug});}}else{// Reset to original if empty or unchanged\nsetSubtaskName(subtask.name);}setIsEditing(false);};// Handle key press events for subtask\nconst handleSubtaskKeyPress=e=>{if(e.key==='Enter'){handleSubtaskSave();}else if(e.key==='Escape'){setSubtaskName(subtask.name);setIsEditing(false);}};// Handle status toggle\nconst handleStatusToggle=()=>{// Update UI immediately to provide user feedback\nconst newCheckedState=!isChecked;setIsChecked(newCheckedState);const newStatus=newCheckedState?STATUS.COMPLETED:STATUS.NOT_STARTED;const newProgress=newCheckedState?100:0;if(onUpdateSubtask){// Ensure to send both status and progress, even when progress = 0\nconst updatedData={...subtask,status:newStatus,progress:newProgress,task:taskSlug};console.log('Sending subtask update:',updatedData);onUpdateSubtask(updatedData);}};// Handle delete subtask\nconst handleDeleteSubtaskClick=()=>{if(window.confirm(`Are you sure you want to delete this subtask?`)){onDeleteSubtask(subtask,taskSlug);}};// Focus input when editing starts\nuseEffect(()=>{if(isEditing&&subtaskInputRef.current){subtaskInputRef.current.focus();}},[isEditing]);return/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',py:0.5,borderBottom:index<totalSubtasks-1?'1px dotted #f0f0f0':'none'},children:[/*#__PURE__*/_jsx(Checkbox,{checked:isChecked,onChange:handleStatusToggle,size:\"small\",sx:{p:0.5,mr:0.5,color:'#CCCCCC','&.Mui-checked':{color:'#4CAF50'}},icon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:check-box-outline-blank\",width:18,height:18}),checkedIcon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:check-box\",width:18,height:18})}),isEditing?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',flexGrow:1},children:[/*#__PURE__*/_jsx(TextField,{inputRef:subtaskInputRef,value:subtaskName,onChange:e=>setSubtaskName(e.target.value),onKeyDown:handleSubtaskKeyPress,onBlur:handleSubtaskSave,variant:\"standard\",fullWidth:true,autoFocus:true,sx:{fontFamily:'\"Recursive Variable\", sans-serif','& .MuiInputBase-input':{fontSize:'0.85rem',fontFamily:'\"Recursive Variable\", sans-serif'}}}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleSubtaskSave,sx:{ml:1},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:check\",width:14,height:14,color:\"#4CAF50\"})})]}):/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{flexGrow:1,fontSize:'0.85rem',fontFamily:'\"Recursive Variable\", sans-serif',color:isChecked?'#4CAF50':'#555',fontWeight:isChecked?500:400,cursor:'pointer',display:'flex',alignItems:'center','&:hover':{'& .subtask-edit-icon':{opacity:1}}},onClick:handleSubtaskEditClick,children:[subtaskName,/*#__PURE__*/_jsx(Tooltip,{title:\"Edit subtask name\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",className:\"subtask-edit-icon\",sx:{ml:0.5,opacity:0,transition:'opacity 0.2s',padding:'1px'},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:edit-outline\",width:12,height:12,color:\"#666\"})})})]}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center'},children:/*#__PURE__*/_jsx(Tooltip,{title:\"Delete subtask\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleDeleteSubtaskClick,sx:{p:0.5,color:'#F44336','&:hover':{backgroundColor:'#FFEBEE'}},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:delete-outline\",width:14,height:14})})})})]});};// Format date for display\nfunction formatDate(dateString){if(!dateString)return'';try{const date=new Date(dateString);if(isNaN(date.getTime()))return'';// Only display day and month, not year\nconst options={month:'short',day:'numeric'};return date.toLocaleDateString('en-US',options);}catch(error){return'';}}export default MilestoneCard;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "Typography", "Paper", "Chip", "LinearProgress", "<PERSON><PERSON>", "Divider", "TextField", "IconButton", "<PERSON><PERSON><PERSON>", "Checkbox", "CircularProgress", "Avatar", "AvatarGroup", "Collapse", "Iconify", "mainYellowColor", "STATUS", "STATUS_CONFIG", "styles", "CommentDialog", "DueDateDialog", "AssignMemberDialog", "getComments", "addComment", "updateComment", "deleteComment", "updateTask", "assignMembersToTask", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "MilestoneCard", "_ref", "_milestone$tasks", "_milestone$tasks2", "_milestone$tasks3", "milestone", "compact", "showSubtasks", "calculateMilestoneProgress", "getMilestoneStatus", "calculateTaskProgress", "getTaskStatus", "calculateSubtaskProgress", "getSubtaskStatus", "onUpdateMilestone", "onUpdateTask", "onUpdateSubtask", "onAddTask", "onAddSubtask", "onDeleteTask", "onDeleteSubtask", "onAssignMembers", "invitedUsers", "<PERSON><PERSON><PERSON><PERSON>", "isEditing", "setIsEditing", "milestoneName", "setMilestoneName", "name", "isAddingTask", "setIsAddingTask", "newTaskName", "setNewTaskName", "isExpanded", "setIsExpanded", "savedState", "localStorage", "getItem", "id", "JSON", "parse", "inputRef", "newTaskInputRef", "handleToggleExpanded", "newExpandedState", "setItem", "stringify", "progress", "milestoneStatus", "NOT_STARTED", "statusConfig", "hasDescription", "description", "trim", "length", "handleEditClick", "handleSave", "trimmedName", "handleKeyPress", "e", "key", "handleAddTaskClick", "handleSaveNewTask", "newTask", "status", "handleNewTaskKeyPress", "current", "focus", "elevation", "className", "milestoneCard", "sx", "padding", "paddingBottom", "children", "<PERSON><PERSON><PERSON><PERSON>", "display", "alignItems", "flex", "size", "onClick", "mr", "color", "backgroundColor", "icon", "width", "height", "value", "onChange", "target", "onKeyDown", "onBlur", "variant", "fullWidth", "autoFocus", "fontFamily", "fontWeight", "fontSize", "ml", "milestoneTitle", "cursor", "opacity", "title", "transition", "label", "borderRadius", "in", "timeout", "unmountOnExit", "mb", "mt", "p", "border", "whiteSpace", "lineHeight", "my", "justifyContent", "estimated_duration", "gap", "taskList", "tasks", "slice", "map", "task", "index", "TaskItem", "textTransform", "taskItem", "position", "flexShrink", "placeholder", "borderBottomColor", "_ref2", "_localTask$assignees", "taskName", "setTaskName", "taskInputRef", "isAddingSubtask", "setIsAddingSubtask", "newSubtaskName", "setNewSubtaskName", "newSubtaskInputRef", "commentDialogOpen", "setCommentDialogOpen", "comments", "setComments", "loadingComments", "setLoadingComments", "dueDateDialogOpen", "setDueDateDialogOpen", "updatingDueDate", "setUpdatingDueDate", "assignMemberDialogOpen", "setAssignMemberDialogOpen", "localTask", "setLocalTask", "handleOpenComments", "stopPropagation", "then", "response", "_response$data", "_response$data2", "console", "log", "data", "commentsData", "Array", "isArray", "catch", "error", "finally", "handleAddComment", "content", "_response$data3", "newComment", "currentUser", "Date", "now", "user", "first_name", "last_name", "avatar", "created_at", "toLocaleString", "hour", "minute", "day", "month", "replace", "prevComments", "refreshComments", "handleUpdateComment", "commentId", "updatedCommentTemp", "find", "c", "updatedComments", "success", "handleDeleteComment", "filter", "_response$data4", "_response$data5", "taskProgress", "taskStatus", "hasSubtasks", "subtasks", "isCompleted", "COMPLETED", "handleTaskEditClick", "handleTaskSave", "handleTaskKeyPress", "handleAddSubtaskClick", "handleDeleteTaskClick", "handleSaveNewSubtask", "newSubtask", "slug", "handleNewSubtaskKeyPress", "hasDueDates", "start_date", "end_date", "handleOpenDueDateDialog", "handleUpdateDueDate", "updatedTask", "taskToUpdate", "handleAssignMembers", "assignedUserIds", "numericIds", "Number", "assignees", "email", "taskItemContainer", "flexGrow", "flexDirection", "max", "assignee", "arrow", "src", "alt", "bgcolor", "char<PERSON>t", "toUpperCase", "visibility", "transform", "right", "top", "background", "boxShadow", "zIndex", "disabled", "px", "py", "formatDate", "pl", "pr", "pt", "pb", "borderLeft", "subtask", "SubtaskItem", "totalSubtasks", "taskSlug", "borderBottom", "open", "onClose", "onAddComment", "onUpdateComment", "onDeleteComment", "loading", "targetName", "targetType", "onUpdateDueDate", "isUpdating", "_ref3", "subtaskName", "setSubtaskName", "isChecked", "setIsChecked", "subtaskInputRef", "handleSubtaskEditClick", "handleSubtaskSave", "handleSubtaskKeyPress", "handleStatusToggle", "newCheckedState", "newStatus", "newProgress", "updatedData", "handleDeleteSubtaskClick", "window", "confirm", "checked", "checkedIcon", "dateString", "date", "isNaN", "getTime", "options", "toLocaleDateString"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/MilestoneCard.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  Chip,\r\n  LinearProgress,\r\n  Button,\r\n  Divider,\r\n  TextField,\r\n  IconButton,\r\n  Tooltip,\r\n  Checkbox,\r\n  CircularProgress,\r\n  Avatar,\r\n  AvatarGroup,\r\n  Collapse\r\n} from '@mui/material';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor } from \"helpers/constants\";\r\nimport { STATUS, STATUS_CONFIG } from '../hooks/usePlanData';\r\nimport styles from '../styles.module.scss';\r\nimport CommentDialog from '../dialogs/CommentDialog';\r\nimport DueDateDialog from '../dialogs/DueDateDialog';\r\nimport AssignMemberDialog from '../dialogs/AssignMemberDialog';\r\nimport { getComments, addComment, updateComment, deleteComment, updateTask, assignMembersToTask } from '../../services';\r\nimport { toast } from 'react-toastify';\r\n\r\nconst MilestoneCard = ({\r\n  milestone,\r\n  compact = false,\r\n  showSubtasks = false,\r\n  calculateMilestoneProgress,\r\n  getMilestoneStatus,\r\n  calculateTaskProgress,\r\n  getTaskStatus,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus,\r\n  onUpdateMilestone,\r\n  onUpdateTask,\r\n  onUpdateSubtask,\r\n  onAddTask,\r\n  onAddSubtask,\r\n  onDeleteTask,\r\n  onDeleteSubtask,\r\n  onAssignMembers,\r\n  invitedUsers,\r\n  planOwner\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [milestoneName, setMilestoneName] = useState(milestone.name);\r\n  const [isAddingTask, setIsAddingTask] = useState(false);\r\n  const [newTaskName, setNewTaskName] = useState('');\r\n  // Initialize collapse state from localStorage\r\n  const [isExpanded, setIsExpanded] = useState(() => {\r\n    const savedState = localStorage.getItem(`milestone_${milestone.id || milestone.name}_expanded`);\r\n    return savedState !== null ? JSON.parse(savedState) : true; // Default to expanded\r\n  });\r\n  const inputRef = useRef(null);\r\n  const newTaskInputRef = useRef(null);\r\n\r\n  // Handle expand/collapse toggle with state persistence\r\n  const handleToggleExpanded = () => {\r\n    const newExpandedState = !isExpanded;\r\n    setIsExpanded(newExpandedState);\r\n    // Save state to localStorage\r\n    localStorage.setItem(`milestone_${milestone.id || milestone.name}_expanded`, JSON.stringify(newExpandedState));\r\n  };\r\n\r\n  // Calculate milestone progress\r\n  const progress = calculateMilestoneProgress ? calculateMilestoneProgress(milestone) : 0;\r\n\r\n  // Determine status based on progress\r\n  const milestoneStatus = getMilestoneStatus ? getMilestoneStatus(milestone) : STATUS.NOT_STARTED;\r\n  const statusConfig = STATUS_CONFIG[milestoneStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\r\n\r\n  // Check if description exists and is not empty\r\n  const hasDescription = milestone.description && milestone.description.trim().length > 0;\r\n\r\n  // Handle edit mode\r\n  const handleEditClick = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle save changes\r\n  const handleSave = () => {\r\n    // Trim the name to remove leading/trailing whitespace\r\n    const trimmedName = milestoneName.trim();\r\n\r\n    // Only update if the name has actually changed (after trimming)\r\n    if (trimmedName !== '' && trimmedName !== milestone.name) {\r\n      if (onUpdateMilestone) {\r\n        onUpdateMilestone({ ...milestone, name: trimmedName });\r\n      }\r\n    } else {\r\n      // Reset to original if empty or unchanged\r\n      setMilestoneName(milestone.name);\r\n    }\r\n\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle key press events\r\n  const handleKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSave();\r\n    } else if (e.key === 'Escape') {\r\n      setMilestoneName(milestone.name);\r\n      setIsEditing(false);\r\n    }\r\n  };\r\n\r\n  // Handle add task\r\n  const handleAddTaskClick = () => {\r\n    setIsAddingTask(true);\r\n  };\r\n\r\n  // Handle save new task\r\n  const handleSaveNewTask = () => {\r\n    const trimmedName = newTaskName.trim();\r\n    if (trimmedName && onAddTask) {\r\n      const newTask = {\r\n        name: trimmedName,\r\n        milestone: milestone.id,\r\n        status: STATUS.NOT_STARTED,\r\n        progress: 0\r\n      };\r\n      onAddTask(newTask);\r\n      setNewTaskName('');\r\n    }\r\n    setIsAddingTask(false);\r\n  };\r\n\r\n  // Handle key press events for new task\r\n  const handleNewTaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSaveNewTask();\r\n    } else if (e.key === 'Escape') {\r\n      setNewTaskName('');\r\n      setIsAddingTask(false);\r\n    }\r\n  };\r\n\r\n  // Focus input when editing starts\r\n  useEffect(() => {\r\n    if (isEditing && inputRef.current) {\r\n      inputRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  // Focus input when adding task\r\n  useEffect(() => {\r\n    if (isAddingTask && newTaskInputRef.current) {\r\n      newTaskInputRef.current.focus();\r\n    }\r\n  }, [isAddingTask]);\r\n\r\n  return (\r\n    <Paper\r\n      elevation={0}\r\n      className={styles.milestoneCard}\r\n      sx={{ padding: '16px', paddingBottom: '5px' }}\r\n    >\r\n      <Box className={styles.milestoneHeader}>\r\n        <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>\r\n          {/* Expand/Collapse Button */}\r\n          <IconButton\r\n            size=\"small\"\r\n            onClick={handleToggleExpanded}\r\n            sx={{\r\n              mr: 1,\r\n              color: '#666',\r\n              '&:hover': {\r\n                backgroundColor: 'rgba(0, 0, 0, 0.04)'\r\n              }\r\n            }}\r\n          >\r\n            <Iconify\r\n              icon={isExpanded ? \"material-symbols:expand-less\" : \"material-symbols:expand-more\"}\r\n              width={20}\r\n              height={20}\r\n            />\r\n          </IconButton>\r\n\r\n          {isEditing ? (\r\n            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\r\n              <Iconify icon=\"material-symbols:flag\" width={20} height={20} color={mainYellowColor} sx={{ mr: 1 }} />\r\n              <TextField\r\n                inputRef={inputRef}\r\n                value={milestoneName}\r\n                onChange={(e) => setMilestoneName(e.target.value)}\r\n                onKeyDown={handleKeyPress}\r\n                onBlur={handleSave}\r\n                variant=\"standard\"\r\n                fullWidth\r\n                autoFocus\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  fontWeight: 600,\r\n                  fontSize: '1.1rem',\r\n                  '& .MuiInputBase-input': {\r\n                    fontWeight: 600,\r\n                    fontSize: '1.1rem',\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  }\r\n                }}\r\n              />\r\n              <IconButton size=\"small\" onClick={handleSave} sx={{ ml: 1 }}>\r\n                <Iconify icon=\"material-symbols:check\" width={20} height={20} color=\"#4CAF50\" />\r\n              </IconButton>\r\n            </Box>\r\n          ) : (\r\n            <Typography\r\n              variant=\"h6\"\r\n              className={styles.milestoneTitle}\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                cursor: 'pointer',\r\n                flex: 1,\r\n                '&:hover': {\r\n                  '& .edit-icon': {\r\n                    opacity: 1,\r\n                  }\r\n                }\r\n              }}\r\n              onClick={handleEditClick}\r\n            >\r\n              <Iconify icon=\"material-symbols:flag\" width={20} height={20} color={mainYellowColor} />\r\n              {milestoneName}\r\n              <Tooltip title=\"Edit milestone name\">\r\n                <IconButton\r\n                  size=\"small\"\r\n                  className=\"edit-icon\"\r\n                  sx={{\r\n                    ml: 1,\r\n                    opacity: 0,\r\n                    transition: 'opacity 0.2s',\r\n                    padding: '2px'\r\n                  }}\r\n                >\r\n                  <Iconify icon=\"material-symbols:edit-outline\" width={16} height={16} color=\"#666\" />\r\n                </IconButton>\r\n              </Tooltip>\r\n            </Typography>\r\n          )}\r\n        </Box>\r\n\r\n        <Chip\r\n          icon={<Iconify icon={statusConfig.icon} width={16} height={16} />}\r\n          label={statusConfig.label}\r\n          size=\"small\"\r\n          sx={{\r\n            backgroundColor: `${statusConfig.color}20`,\r\n            color: statusConfig.color,\r\n            fontWeight: 600,\r\n            borderRadius: '4px',\r\n            fontFamily: '\"Recursive Variable\", sans-serif'\r\n          }}\r\n        />\r\n      </Box>\r\n\r\n      <Collapse in={isExpanded} timeout=\"auto\" unmountOnExit>\r\n        {/* Description Section */}\r\n        {!compact && hasDescription && (\r\n          <Box sx={{ mb: 2, mt: 1.5 }}>\r\n            <Box\r\n              sx={{\r\n                backgroundColor: '#f9f9f9',\r\n                p: 1.5,\r\n                borderRadius: '8px',\r\n                border: '1px solid #f0f0f0'\r\n              }}\r\n            >\r\n              <Typography\r\n                variant=\"body2\"\r\n                sx={{\r\n                  color: '#333',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  whiteSpace: 'pre-line',\r\n                  lineHeight: 1.6,\r\n                  fontWeight: 500\r\n                }}\r\n              >\r\n                {milestone.description}\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n        )}\r\n\r\n        <Divider sx={{ my: 2, opacity: 0.6 }} />\r\n\r\n      <Box sx={{ mb: 2 }}>\r\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>\r\n          <Typography variant=\"body2\" sx={{ fontWeight: 600, fontFamily: '\"Recursive Variable\", sans-serif', color: '#333' }}>\r\n            Progress\r\n          </Typography>\r\n          <Typography variant=\"body2\" sx={{ fontWeight: 700, fontFamily: '\"Recursive Variable\", sans-serif', color: '#333' }}>\r\n            {progress}%\r\n          </Typography>\r\n        </Box>\r\n        <LinearProgress\r\n          variant=\"determinate\"\r\n          value={progress}\r\n          sx={{\r\n            height: 6,\r\n            borderRadius: 3,\r\n            backgroundColor: '#f0f0f0',\r\n            '& .MuiLinearProgress-bar': {\r\n              backgroundColor: statusConfig.color\r\n            }\r\n          }}\r\n        />\r\n      </Box>\r\n\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>\r\n        {milestone.estimated_duration && (\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            <Iconify icon=\"material-symbols:timer\" width={16} height={16} color=\"#333\" />\r\n            <Typography variant=\"body2\" sx={{ fontFamily: '\"Recursive Variable\", sans-serif', color: '#333', fontWeight: 500 }}>\r\n              {milestone.estimated_duration}\r\n            </Typography>\r\n          </Box>\r\n        )}\r\n      </Box>\r\n\r\n      {/* Tasks Section */}\r\n      <Box sx={{ mb: 1 }}>\r\n        <Box\r\n          sx={{\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'space-between',\r\n            mb: 1\r\n          }}\r\n        >\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            <Iconify\r\n              icon=\"material-symbols:checklist\"\r\n              width={16}\r\n              height={16}\r\n              sx={{ color: '#333' }}\r\n            />\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                fontWeight: 600,\r\n                color: '#333',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontSize: '0.9rem'\r\n              }}\r\n            >\r\n              Tasks\r\n            </Typography>\r\n          </Box>\r\n\r\n          {/* Add task button */}\r\n          <Tooltip title=\"Add new task\">\r\n            <IconButton\r\n              size=\"small\"\r\n              onClick={handleAddTaskClick}\r\n              sx={{\r\n                color: mainYellowColor,\r\n                '&:hover': {\r\n                  backgroundColor: `${mainYellowColor}10`\r\n                }\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:add-task\" width={18} height={18} />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Box>\r\n\r\n        {compact ? (\r\n          <Box className={styles.taskList}>\r\n            {milestone.tasks?.slice(0, 2).map((task, index) => (\r\n              <TaskItem\r\n                key={index}\r\n                task={task}\r\n                showSubtasks={showSubtasks}\r\n                compact={true}\r\n                calculateTaskProgress={calculateTaskProgress}\r\n                getTaskStatus={getTaskStatus}\r\n                calculateSubtaskProgress={calculateSubtaskProgress}\r\n                getSubtaskStatus={getSubtaskStatus}\r\n                onUpdateTask={onUpdateTask}\r\n                onUpdateSubtask={onUpdateSubtask}\r\n                onAddSubtask={onAddSubtask}\r\n                onDeleteTask={onDeleteTask}\r\n                onDeleteSubtask={onDeleteSubtask}\r\n                onAssignMembers={onAssignMembers}\r\n                invitedUsers={invitedUsers}\r\n                planOwner={planOwner}\r\n              />\r\n            ))}\r\n\r\n            {milestone.tasks?.length > 2 && (\r\n              <Button\r\n                variant=\"text\"\r\n                size=\"small\"\r\n                sx={{\r\n                  color: mainYellowColor,\r\n                  fontWeight: 600,\r\n                  textTransform: 'none',\r\n                  p: 0,\r\n                  mt: 1,\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                + {milestone.tasks.length - 2} more tasks\r\n              </Button>\r\n            )}\r\n          </Box>\r\n        ) : (\r\n          <Box className={styles.taskList}>\r\n            {milestone.tasks?.map((task, index) => (\r\n              <TaskItem\r\n                key={index}\r\n                task={task}\r\n                showSubtasks={showSubtasks}\r\n                compact={false}\r\n                calculateTaskProgress={calculateTaskProgress}\r\n                getTaskStatus={getTaskStatus}\r\n                calculateSubtaskProgress={calculateSubtaskProgress}\r\n                getSubtaskStatus={getSubtaskStatus}\r\n                onUpdateTask={onUpdateTask}\r\n                onUpdateSubtask={onUpdateSubtask}\r\n                onAddSubtask={onAddSubtask}\r\n                onDeleteTask={onDeleteTask}\r\n                onDeleteSubtask={onDeleteSubtask}\r\n                onAssignMembers={onAssignMembers}\r\n                invitedUsers={invitedUsers}\r\n                planOwner={planOwner}\r\n              />\r\n            ))}\r\n\r\n            {/* New task input field */}\r\n            {isAddingTask && (\r\n              <Box\r\n                className={styles.taskItem}\r\n                sx={{\r\n                  position: 'relative',\r\n                  mt: 1,\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  backgroundColor: '#f9f9f9',\r\n                  borderRadius: '6px',\r\n                  padding: '8px 12px',\r\n                  border: '1px solid #f0f0f0'\r\n                }}\r\n              >\r\n                <Box\r\n                  sx={{\r\n                    width: 10,\r\n                    height: 10,\r\n                    borderRadius: '50%',\r\n                    backgroundColor: '#CCCCCC',\r\n                    mr: 1.5,\r\n                    flexShrink: 0\r\n                  }}\r\n                />\r\n\r\n                <TextField\r\n                  inputRef={newTaskInputRef}\r\n                  value={newTaskName}\r\n                  onChange={(e) => setNewTaskName(e.target.value)}\r\n                  onKeyDown={handleNewTaskKeyPress}\r\n                  placeholder=\"Enter new task name...\"\r\n                  variant=\"standard\"\r\n                  fullWidth\r\n                  autoFocus\r\n                  sx={{\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                    '& .MuiInputBase-input': {\r\n                      fontSize: '0.9rem',\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      padding: '4px 0'\r\n                    },\r\n                    '& .MuiInput-underline:before': {\r\n                      borderBottomColor: 'rgba(0, 0, 0, 0.1)'\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <Box sx={{ display: 'flex', alignItems: 'center', ml: 'auto' }}>\r\n                  <IconButton size=\"small\" onClick={handleSaveNewTask} sx={{ ml: 1 }}>\r\n                    <Iconify icon=\"material-symbols:check\" width={18} height={18} color=\"#4CAF50\" />\r\n                  </IconButton>\r\n\r\n                  <IconButton size=\"small\" onClick={() => setIsAddingTask(false)} sx={{ ml: 0.5 }}>\r\n                    <Iconify icon=\"material-symbols:close\" width={18} height={18} color=\"#F44336\" />\r\n                  </IconButton>\r\n                </Box>\r\n              </Box>\r\n            )}\r\n          </Box>\r\n        )}\r\n      </Box>\r\n      </Collapse>\r\n    </Paper>\r\n  );\r\n};\r\n// Component to display task and subtask\r\nconst TaskItem = ({\r\n  task,\r\n  showSubtasks = false,\r\n  compact = false,\r\n  calculateTaskProgress,\r\n  getTaskStatus,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus,\r\n  onUpdateTask,\r\n  onUpdateSubtask,\r\n  onAddSubtask,\r\n  onDeleteTask,\r\n  onDeleteSubtask,\r\n  onAssignMembers,\r\n  invitedUsers,\r\n  planOwner\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [taskName, setTaskName] = useState(task.name);\r\n  const taskInputRef = useRef(null);\r\n  const [isAddingSubtask, setIsAddingSubtask] = useState(false);\r\n  const [newSubtaskName, setNewSubtaskName] = useState('');\r\n  const newSubtaskInputRef = useRef(null);\r\n\r\n  // Comment functionality\r\n  const [commentDialogOpen, setCommentDialogOpen] = useState(false);\r\n  const [comments, setComments] = useState([]);\r\n  const [loadingComments, setLoadingComments] = useState(false);\r\n\r\n  // Enable due date functionality\r\n  const [dueDateDialogOpen, setDueDateDialogOpen] = useState(false);\r\n  const [updatingDueDate, setUpdatingDueDate] = useState(false);\r\n\r\n  // Add state for assign member dialog\r\n  const [assignMemberDialogOpen, setAssignMemberDialogOpen] = useState(false);\r\n\r\n  // Add state to store current task information\r\n  const [localTask, setLocalTask] = useState(task);\r\n\r\n  // Update localTask when task changes from props\r\n  useEffect(() => {\r\n    setLocalTask(task);\r\n  }, [task]);\r\n\r\n  const handleOpenComments = (e) => {\r\n    e.stopPropagation();\r\n    setCommentDialogOpen(true);\r\n    setLoadingComments(true);\r\n    getComments(task.id)\r\n      .then(response => {\r\n        console.log('Comments data:', response.data);\r\n        // Check data structure and get correct comments array\r\n        let commentsData = [];\r\n        if (Array.isArray(response.data)) {\r\n          commentsData = response.data;\r\n        } else if (response.data?.data && Array.isArray(response.data.data)) {\r\n          commentsData = response.data.data;\r\n        } else if (response.data?.comments && Array.isArray(response.data.comments)) {\r\n          commentsData = response.data.comments;\r\n        }\r\n        setComments(commentsData);\r\n      })\r\n      .catch(error => {\r\n        console.error('Error fetching comments:', error);\r\n        toast.error('Failed to load comments');\r\n      })\r\n      .finally(() => {\r\n        setLoadingComments(false);\r\n      });\r\n  };\r\n\r\n  const handleAddComment = async (content) => {\r\n    try {\r\n      const response = await addComment(task.id, content);\r\n      console.log('Add comment response:', response);\r\n\r\n      // Create new comment from response or create temporary object\r\n      let newComment;\r\n      if (response.data?.data) {\r\n        newComment = response.data.data;\r\n      } else {\r\n        // Create temporary object if API does not return new comment\r\n        const currentUser = JSON.parse(localStorage.getItem('user')) || {};\r\n        newComment = {\r\n          id: Date.now(), // Temporary ID\r\n          content: content,\r\n          user: {\r\n            id: currentUser.id,\r\n            first_name: currentUser.first_name,\r\n            last_name: currentUser.last_name,\r\n            avatar: currentUser.avatar\r\n          },\r\n          created_at: new Date().toLocaleString('en-US', {\r\n            hour: '2-digit',\r\n            minute: '2-digit',\r\n            day: '2-digit',\r\n            month: '2-digit'\r\n          }).replace(',', '')\r\n        };\r\n      }\r\n\r\n      // Update state\r\n      setComments(prevComments => [...prevComments, newComment]);\r\n\r\n      // Refresh comments to ensure latest data\r\n      refreshComments();\r\n    } catch (error) {\r\n      console.error('Error adding comment:', error);\r\n      toast.error('Failed to add comment');\r\n    }\r\n  };\r\n\r\n  const handleUpdateComment = async (commentId, content) => {\r\n    try {\r\n      // Update UI first\r\n      const updatedCommentTemp = comments.find(c => c.id === commentId);\r\n      if (updatedCommentTemp) {\r\n        const updatedComments = comments.map(c =>\r\n          c.id === commentId ? { ...c, content: content } : c\r\n        );\r\n        setComments(updatedComments);\r\n      }\r\n\r\n      // Call API\r\n      const response = await updateComment(commentId, content);\r\n      console.log('Update comment response:', response);\r\n\r\n      toast.success('Comment updated successfully');\r\n\r\n      // Refresh comments to ensure latest data\r\n      refreshComments();\r\n    } catch (error) {\r\n      console.error('Error updating comment:', error);\r\n      toast.error('Failed to update comment');\r\n      // Refresh comments to restore original state if there is an error\r\n      refreshComments();\r\n    }\r\n  };\r\n\r\n  const handleDeleteComment = async (commentId) => {\r\n    try {\r\n      // Update UI first\r\n      setComments(prevComments => prevComments.filter(c => c.id !== commentId));\r\n\r\n      // Call API\r\n      await deleteComment(commentId);\r\n      console.log('Comment deleted successfully');\r\n\r\n      toast.success('Comment deleted successfully');\r\n\r\n      // Refresh comments to ensure latest data\r\n      refreshComments();\r\n    } catch (error) {\r\n      console.error('Error deleting comment:', error);\r\n      toast.error('Failed to delete comment');\r\n      // Refresh comments to restore original state if there is an error\r\n      refreshComments();\r\n    }\r\n  };\r\n\r\n  // Refresh comments function\r\n  const refreshComments = async () => {\r\n    try {\r\n      const response = await getComments(task.id);\r\n      console.log('Refreshed comments:', response.data);\r\n\r\n      // Handle returned data\r\n      let commentsData = [];\r\n      if (Array.isArray(response.data)) {\r\n        commentsData = response.data;\r\n      } else if (response.data?.data && Array.isArray(response.data.data)) {\r\n        commentsData = response.data.data;\r\n      } else if (response.data?.comments && Array.isArray(response.data.comments)) {\r\n        commentsData = response.data.comments;\r\n      }\r\n\r\n      setComments(commentsData);\r\n    } catch (error) {\r\n      console.error('Error refreshing comments:', error);\r\n    }\r\n  };\r\n\r\n  // Calculate task progress\r\n  const taskProgress = calculateTaskProgress ? calculateTaskProgress(task) : 0;\r\n\r\n  // Determine task status\r\n  const taskStatus = getTaskStatus ? getTaskStatus(task) : (task.status || STATUS.NOT_STARTED);\r\n  const statusConfig = STATUS_CONFIG[taskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\r\n\r\n  // Check if task has subtasks\r\n  const hasSubtasks = task.subtasks && task.subtasks.length > 0;\r\n\r\n  // Check if task is completed\r\n  const isCompleted = taskStatus === STATUS.COMPLETED;\r\n\r\n  // Handle edit mode for task\r\n  const handleTaskEditClick = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle save changes for task\r\n  const handleTaskSave = () => {\r\n    // Trim the name to remove leading/trailing whitespace\r\n    const trimmedName = taskName.trim();\r\n\r\n    // Only update if the name has actually changed (after trimming)\r\n    if (trimmedName !== '' && trimmedName !== task.name) {\r\n      if (onUpdateTask) {\r\n        onUpdateTask({ ...task, name: trimmedName });\r\n      }\r\n    } else {\r\n      // Reset to original if empty or unchanged\r\n      setTaskName(task.name);\r\n    }\r\n\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle key press events for task\r\n  const handleTaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleTaskSave();\r\n    } else if (e.key === 'Escape') {\r\n      setTaskName(task.name);\r\n      setIsEditing(false);\r\n    }\r\n  };\r\n\r\n  // Handle add subtask\r\n  const handleAddSubtaskClick = () => {\r\n    setIsAddingSubtask(true);\r\n  };\r\n\r\n  // Handle delete task\r\n  const handleDeleteTaskClick = () => {\r\n    if (onDeleteTask) {\r\n      onDeleteTask(task);\r\n    }\r\n  };\r\n\r\n  // Handle save new subtask\r\n  const handleSaveNewSubtask = () => {\r\n    const trimmedName = newSubtaskName.trim();\r\n    if (trimmedName && onAddSubtask) {\r\n      const newSubtask = {\r\n        name: trimmedName,\r\n        task: task.slug,\r\n        status: STATUS.NOT_STARTED,\r\n        progress: 0\r\n      };\r\n      onAddSubtask(newSubtask);\r\n      setNewSubtaskName('');\r\n    }\r\n    setIsAddingSubtask(false);\r\n  };\r\n\r\n  // Handle key press events for new subtask\r\n  const handleNewSubtaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSaveNewSubtask();\r\n    } else if (e.key === 'Escape') {\r\n      setNewSubtaskName('');\r\n      setIsAddingSubtask(false);\r\n    }\r\n  };\r\n\r\n  // Focus input when editing starts\r\n  useEffect(() => {\r\n    if (isEditing && taskInputRef.current) {\r\n      taskInputRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  // Focus input when adding subtask\r\n  useEffect(() => {\r\n    if (isAddingSubtask && newSubtaskInputRef.current) {\r\n      newSubtaskInputRef.current.focus();\r\n    }\r\n  }, [isAddingSubtask]);\r\n\r\n  // Check if task has due dates - use localTask instead of task\r\n  const hasDueDates = localTask.start_date || localTask.end_date;\r\n\r\n  const handleOpenDueDateDialog = (e) => {\r\n    e.stopPropagation();\r\n    setDueDateDialogOpen(true);\r\n  };\r\n\r\n  const handleUpdateDueDate = async (updatedTask) => {\r\n    setUpdatingDueDate(true);\r\n    try {\r\n      const taskToUpdate = {\r\n        ...localTask,\r\n        start_date: updatedTask.start_date,\r\n        end_date: updatedTask.end_date,\r\n        progress: localTask.progress || 0,\r\n        status: localTask.status || 1\r\n      };\r\n\r\n      // Update local state first\r\n      setLocalTask(taskToUpdate);\r\n\r\n      // Call API\r\n      const response = await updateTask(taskToUpdate);\r\n\r\n      // If API returns data, update local state\r\n      if (response && response.data) {\r\n        setLocalTask(response.data);\r\n      }\r\n\r\n      // Update state in parent component if necessary\r\n      if (onUpdateTask) {\r\n        onUpdateTask(taskToUpdate);\r\n      }\r\n\r\n      toast.success('Due date updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating due date:', error);\r\n      toast.error('Failed to update due date');\r\n\r\n      // If there is an error, restore original state\r\n      setLocalTask(task);\r\n    } finally {\r\n      setUpdatingDueDate(false);\r\n      setDueDateDialogOpen(false); // Close dialog after completion\r\n    }\r\n  };\r\n\r\n  // Add handler for member assignment updates\r\n  const handleAssignMembers = async (assignedUserIds) => {\r\n    try {\r\n      // Convert IDs to numbers\r\n      const numericIds = assignedUserIds.map(id => Number(id));\r\n      // Call API to assign members\r\n      const response = await assignMembersToTask(localTask.slug, numericIds);\r\n      // Create updated task with new assignees\r\n      const updatedTask = {\r\n        ...localTask,\r\n        assignees: response.assignees || numericIds.map(id => ({\r\n          id: id,\r\n          first_name: '',\r\n          last_name: '',\r\n          email: ''\r\n        }))\r\n      };\r\n\r\n      // Update both local state and parent state\r\n      setLocalTask(updatedTask);\r\n      if (onUpdateTask) {\r\n        onUpdateTask(updatedTask);\r\n      }\r\n\r\n      setAssignMemberDialogOpen(false);\r\n      toast.success('Members assigned successfully');\r\n    } catch (error) {\r\n      console.error('Error assigning members:', error);\r\n      toast.error('Failed to assign members');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Box className={styles.taskItemContainer}>\r\n        <Box\r\n          className={styles.taskItem}\r\n          sx={{ position: 'relative' }}>\r\n          <Box\r\n            sx={{\r\n              width: 10,\r\n              height: 10,\r\n              borderRadius: '50%',\r\n              backgroundColor: statusConfig.color,\r\n              mr: 1.5,\r\n              flexShrink: 0\r\n            }}\r\n          />\r\n\r\n          {isEditing ? (\r\n            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\r\n              <TextField\r\n                inputRef={taskInputRef}\r\n                value={taskName}\r\n                onChange={(e) => setTaskName(e.target.value)}\r\n                onKeyDown={handleTaskKeyPress}\r\n                onBlur={handleTaskSave}\r\n                variant=\"standard\"\r\n                fullWidth\r\n                autoFocus\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  '& .MuiInputBase-input': {\r\n                    fontSize: '0.9rem',\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  }\r\n                }}\r\n              />\r\n              <IconButton size=\"small\" onClick={handleTaskSave} sx={{ ml: 1 }}>\r\n                <Iconify icon=\"material-symbols:check\" width={16} height={16} color=\"#4CAF50\" />\r\n              </IconButton>\r\n            </Box>\r\n          ) : (\r\n            <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', gap: 0.5 }}>\r\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexGrow: 1 }}>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    className={styles.taskName}\r\n                    sx={{\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      cursor: 'pointer',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      color: isCompleted ? '#4CAF50' : 'inherit',\r\n                      fontWeight: isCompleted ? 500 : 400,\r\n                      flexGrow: 1,\r\n                      '&:hover': {\r\n                        '& .task-edit-icon': {\r\n                          opacity: 1,\r\n                        }\r\n                      }\r\n                    }}\r\n                    onClick={handleTaskEditClick}>\r\n                    {taskName}\r\n                    <Tooltip title=\"Edit task name\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        className=\"task-edit-icon\"\r\n                        sx={{\r\n                          ml: 0.5,\r\n                          opacity: 0,\r\n                          transition: 'opacity 0.2s',\r\n                          padding: '2px'\r\n                        }}\r\n                      >\r\n                        <Iconify icon=\"material-symbols:edit-outline\" width={14} height={14} color=\"#666\" />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n                  </Typography>\r\n                </Box>\r\n\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                  {/* Display assigned members avatars */}\r\n                  {localTask.assignees && localTask.assignees.length > 0 && (\r\n                    <AvatarGroup\r\n                      max={3}\r\n                      sx={{\r\n                        '& .MuiAvatar-root': {\r\n                          width: 24,\r\n                          height: 24,\r\n                          fontSize: '0.75rem',\r\n                          border: '1.5px solid #fff'\r\n                        }\r\n                      }}\r\n                    >\r\n                      {localTask.assignees.map((assignee, index) => (\r\n                        <Tooltip\r\n                          key={assignee.id}\r\n                          title={assignee.first_name && assignee.last_name\r\n                            ? `${assignee.first_name} ${assignee.last_name}`\r\n                            : assignee.email}\r\n                          arrow\r\n                        >\r\n                          <Avatar\r\n                            src={assignee.avatar}\r\n                            alt={assignee.first_name || assignee.email}\r\n                            sx={{\r\n                              bgcolor: mainYellowColor,\r\n                            }}\r\n                          >\r\n                            {assignee.first_name\r\n                              ? assignee.first_name.charAt(0).toUpperCase()\r\n                              : assignee.email\r\n                                ? assignee.email.charAt(0).toUpperCase()\r\n                                : ''}\r\n                          </Avatar>\r\n                        </Tooltip>\r\n                      ))}\r\n                    </AvatarGroup>\r\n                  )}\r\n\r\n                  {/* Hover-expandable toolbar */}\r\n                  <Box\r\n                    className=\"task-toolbar\"\r\n                    sx={{\r\n                      position: 'relative',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      '&:hover .toolbar-expanded': {\r\n                        opacity: 1,\r\n                        visibility: 'visible',\r\n                        transform: 'translateX(0)',\r\n                      },\r\n                      '&:hover .toolbar-dots': {\r\n                        opacity: 0,\r\n                        visibility: 'hidden',\r\n                      }\r\n                    }}\r\n                  >\r\n                    {/* Three dots button - visible by default */}\r\n                    <Box\r\n                      className=\"toolbar-dots\"\r\n                      sx={{\r\n                        opacity: 1,\r\n                        visibility: 'visible',\r\n                        transition: 'all 0.2s ease',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        cursor: 'pointer',\r\n                        padding: '4px 8px',\r\n                        borderRadius: '6px',\r\n                        '&:hover': {\r\n                          backgroundColor: 'rgba(0, 0, 0, 0.04)'\r\n                        }\r\n                      }}\r\n                    >\r\n                      <Iconify icon=\"material-symbols:more-horiz\" width={20} height={20} color=\"#666\" />\r\n                    </Box>\r\n\r\n                    {/* Expanded toolbar - hidden by default */}\r\n                    <Box\r\n                      className=\"toolbar-expanded\"\r\n                      sx={{\r\n                        position: 'absolute',\r\n                        right: 0,\r\n                        top: 0,\r\n                        opacity: 0,\r\n                        visibility: 'hidden',\r\n                        transform: 'translateX(10px)',\r\n                        transition: 'all 0.2s ease',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        border: '1px solid #eee',\r\n                        borderRadius: '8px',\r\n                        padding: '2px 4px',\r\n                        background: '#fff',\r\n                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\r\n                        zIndex: 10\r\n                      }}\r\n                    >\r\n                      {/* Due Date Button */}\r\n                      <Tooltip title={hasDueDates ? \"Edit due date\" : \"Set due date\"}>\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={handleOpenDueDateDialog}\r\n                          sx={{\r\n                            color: hasDueDates ? mainYellowColor : '#888',\r\n                            '&:hover': {\r\n                              color: mainYellowColor,\r\n                              bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Iconify\r\n                            icon={hasDueDates ? \"material-symbols:calendar-month\" : \"material-symbols:calendar-add-on\"}\r\n                            width={16}\r\n                            height={16}\r\n                          />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n\r\n                      {/* Comment Button */}\r\n                      <Tooltip title=\"Comments\">\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={handleOpenComments}\r\n                          disabled={loadingComments}\r\n                          sx={{\r\n                            color: mainYellowColor,\r\n                            '&:hover': {\r\n                              bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          {loadingComments ? (\r\n                            <CircularProgress size={16} sx={{ color: mainYellowColor }} />\r\n                          ) : (\r\n                            <Iconify icon=\"material-symbols:comment-outline\" width={16} height={16} />\r\n                          )}\r\n                        </IconButton>\r\n                      </Tooltip>\r\n\r\n                      {/* Assign Member Button */}\r\n                      <Tooltip title=\"Assign members\">\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={() => setAssignMemberDialogOpen(true)}\r\n                          sx={{\r\n                            color: localTask.assignees?.length > 0 ? mainYellowColor : '#888',\r\n                            '&:hover': {\r\n                              color: mainYellowColor,\r\n                              bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Iconify\r\n                            icon=\"mdi:account-multiple-plus\"\r\n                            width={16}\r\n                            height={16}\r\n                          />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n\r\n                      {/* Add subtask button */}\r\n                      <Tooltip title=\"Add subtask\">\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={handleAddSubtaskClick}\r\n                          sx={{\r\n                            color: mainYellowColor,\r\n                            '&:hover': {\r\n                              bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Iconify icon=\"material-symbols:add-task\" width={16} height={16} />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n\r\n                      {/* Edit Task Button */}\r\n                      <Tooltip title=\"Edit task\">\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={handleTaskEditClick}\r\n                          sx={{\r\n                            color: '#666',\r\n                            '&:hover': {\r\n                              bgcolor: 'rgba(0, 0, 0, 0.04)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Iconify icon=\"eva:edit-fill\" width={16} height={16} />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n\r\n                      {/* Delete task button */}\r\n                      <Tooltip title=\"Delete task\">\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={handleDeleteTaskClick}\r\n                          sx={{\r\n                            color: '#F44336',\r\n                            '&:hover': {\r\n                              bgcolor: 'rgba(244, 67, 54, 0.08)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Iconify icon=\"material-symbols:delete-outline\" width={16} height={16} />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n                    </Box>\r\n                  </Box>\r\n\r\n                  {/* Display task progress */}\r\n                  <Box\r\n                    sx={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      bgcolor: statusConfig.color + '20',\r\n                      px: 1,\r\n                      py: 0.5,\r\n                      borderRadius: '4px',\r\n                    }}\r\n                  >\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        fontWeight: 600,\r\n                        color: statusConfig.color,\r\n                        fontFamily: '\"Recursive Variable\", sans-serif'\r\n                      }}\r\n                    >\r\n                      {taskProgress}%\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n\r\n              {/* Due date display below task name */}\r\n              {hasDueDates && (\r\n                <Box\r\n                  sx={{\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    backgroundColor: `${mainYellowColor}08`,\r\n                    borderRadius: '4px',\r\n                    py: 0.5,\r\n                    px: 0.75,\r\n                    width: 'fit-content'\r\n                  }}\r\n                >\r\n                  <Iconify\r\n                    icon=\"material-symbols:calendar-month\"\r\n                    width={14}\r\n                    height={14}\r\n                    color={mainYellowColor} />\r\n                  <Typography\r\n                    variant=\"caption\"\r\n                    sx={{\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      fontSize: '0.75rem',\r\n                      color: '#555',\r\n                      fontWeight: 500,\r\n                      lineHeight: 1,\r\n                      ml: 0.5\r\n                    }}>\r\n                    {formatDate(localTask.start_date)} ~ {formatDate(localTask.end_date)}\r\n                  </Typography>\r\n                </Box>\r\n              )}\r\n            </Box>\r\n          )}\r\n        </Box>\r\n\r\n        <Box\r\n          sx={{\r\n            pl: 4,\r\n            pr: 1,\r\n            pt: 0.5,\r\n            pb: 0.5,\r\n            borderLeft: `1px dashed ${statusConfig.color}`,\r\n            ml: 1.5,\r\n            mt: 0.5,\r\n            display: (hasSubtasks || isAddingSubtask) && showSubtasks ? 'block' : 'none'\r\n          }}\r\n        >\r\n          {task.subtasks && task.subtasks.map((subtask, index) => (\r\n            <SubtaskItem\r\n              key={index}\r\n              subtask={subtask}\r\n              index={index}\r\n              totalSubtasks={task.subtasks.length}\r\n              taskSlug={task.slug}\r\n              calculateSubtaskProgress={calculateSubtaskProgress}\r\n              getSubtaskStatus={getSubtaskStatus}\r\n              onUpdateSubtask={onUpdateSubtask}\r\n              onDeleteSubtask={onDeleteSubtask}\r\n              onAssignMembers={onAssignMembers}\r\n              invitedUsers={invitedUsers}\r\n              planOwner={planOwner}\r\n            />\r\n          ))}\r\n\r\n          {/* New subtask input field */}\r\n          {isAddingSubtask && (\r\n            <Box\r\n              sx={{\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                py: 0.5,\r\n                borderBottom: '1px dotted #f0f0f0',\r\n                backgroundColor: '#f9f9f9',\r\n                borderRadius: '4px',\r\n                px: 1\r\n              }}\r\n            >\r\n              <Checkbox\r\n                disabled\r\n                size=\"small\"\r\n                sx={{\r\n                  p: 0.5,\r\n                  mr: 0.5,\r\n                  color: '#CCCCCC'\r\n                }}\r\n                icon={<Iconify icon=\"material-symbols:check-box-outline-blank\" width={18} height={18} />}\r\n              />\r\n\r\n              <TextField\r\n                inputRef={newSubtaskInputRef}\r\n                value={newSubtaskName}\r\n                onChange={(e) => setNewSubtaskName(e.target.value)}\r\n                onKeyDown={handleNewSubtaskKeyPress}\r\n                placeholder=\"Enter new subtask name...\"\r\n                variant=\"standard\"\r\n                fullWidth\r\n                autoFocus\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  '& .MuiInputBase-input': {\r\n                    fontSize: '0.85rem',\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                    padding: '4px 0'\r\n                  },\r\n                  '& .MuiInput-underline:before': {\r\n                    borderBottomColor: 'rgba(0, 0, 0, 0.1)'\r\n                  }\r\n                }}\r\n              />\r\n\r\n              <Box sx={{ display: 'flex', ml: 1 }}>\r\n                <IconButton size=\"small\" onClick={handleSaveNewSubtask} sx={{ p: 0.5 }}>\r\n                  <Iconify icon=\"material-symbols:check\" width={18} height={18} color=\"#4CAF50\" />\r\n                </IconButton>\r\n\r\n                <IconButton size=\"small\" onClick={() => setIsAddingSubtask(false)} sx={{ p: 0.5, ml: 0.5 }}>\r\n                  <Iconify icon=\"material-symbols:close\" width={18} height={18} color=\"#F44336\" />\r\n                </IconButton>\r\n              </Box>\r\n            </Box>\r\n          )}\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Comment Dialog */}\r\n      <CommentDialog\r\n        open={commentDialogOpen}\r\n        onClose={() => setCommentDialogOpen(false)}\r\n        comments={comments}\r\n        onAddComment={handleAddComment}\r\n        onUpdateComment={handleUpdateComment}\r\n        onDeleteComment={handleDeleteComment}\r\n        loading={loadingComments}\r\n        targetName={task.name}\r\n        targetType=\"task\"\r\n      />\r\n\r\n      {/* Due Date Dialog */}\r\n      <DueDateDialog\r\n        open={dueDateDialogOpen}\r\n        onClose={() => setDueDateDialogOpen(false)}\r\n        task={localTask}\r\n        onUpdateDueDate={handleUpdateDueDate}\r\n        isUpdating={updatingDueDate}\r\n      />\r\n\r\n      {/* Assign Member Dialog */}\r\n      <AssignMemberDialog\r\n        open={assignMemberDialogOpen}\r\n        onClose={() => setAssignMemberDialogOpen(false)}\r\n        task={localTask}\r\n        invitedUsers={invitedUsers}\r\n        planOwner={planOwner}\r\n        onAssignMembers={handleAssignMembers}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\n// Component for subtask\r\nconst SubtaskItem = ({\r\n  subtask,\r\n  index,\r\n  totalSubtasks,\r\n  taskSlug,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus,\r\n  onUpdateSubtask,\r\n  onDeleteSubtask,\r\n  onAssignMembers,\r\n  invitedUsers,\r\n  planOwner\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [subtaskName, setSubtaskName] = useState(subtask.name);\r\n  const [isChecked, setIsChecked] = useState(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\r\n  const subtaskInputRef = useRef(null);\r\n\r\n  // Update isChecked when subtask changes from outside\r\n  useEffect(() => {\r\n    setIsChecked(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\r\n  }, [subtask.status, subtask.progress]);\r\n\r\n  // Handle edit mode for subtask\r\n  const handleSubtaskEditClick = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle save changes for subtask\r\n  const handleSubtaskSave = () => {\r\n    // Trim the name to remove leading/trailing whitespace\r\n    const trimmedName = subtaskName.trim();\r\n\r\n    // Only update if the name has actually changed (after trimming)\r\n    if (trimmedName !== '' && trimmedName !== subtask.name) {\r\n      if (onUpdateSubtask) {\r\n        onUpdateSubtask({ ...subtask, name: trimmedName, task: taskSlug });\r\n      }\r\n    } else {\r\n      // Reset to original if empty or unchanged\r\n      setSubtaskName(subtask.name);\r\n    }\r\n\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle key press events for subtask\r\n  const handleSubtaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSubtaskSave();\r\n    } else if (e.key === 'Escape') {\r\n      setSubtaskName(subtask.name);\r\n      setIsEditing(false);\r\n    }\r\n  };\r\n\r\n  // Handle status toggle\r\n  const handleStatusToggle = () => {\r\n    // Update UI immediately to provide user feedback\r\n    const newCheckedState = !isChecked;\r\n    setIsChecked(newCheckedState);\r\n\r\n    const newStatus = newCheckedState ? STATUS.COMPLETED : STATUS.NOT_STARTED;\r\n    const newProgress = newCheckedState ? 100 : 0;\r\n\r\n    if (onUpdateSubtask) {\r\n      // Ensure to send both status and progress, even when progress = 0\r\n      const updatedData = {\r\n        ...subtask,\r\n        status: newStatus,\r\n        progress: newProgress,\r\n        task: taskSlug\r\n      };\r\n\r\n      console.log('Sending subtask update:', updatedData);\r\n      onUpdateSubtask(updatedData);\r\n    }\r\n  };\r\n\r\n  // Handle delete subtask\r\n  const handleDeleteSubtaskClick = () => {\r\n    if (window.confirm(`Are you sure you want to delete this subtask?`)) {\r\n      onDeleteSubtask(subtask, taskSlug);\r\n    }\r\n  };\r\n\r\n  // Focus input when editing starts\r\n  useEffect(() => {\r\n    if (isEditing && subtaskInputRef.current) {\r\n      subtaskInputRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        py: 0.5,\r\n        borderBottom: index < totalSubtasks - 1 ? '1px dotted #f0f0f0' : 'none'\r\n      }}\r\n    >\r\n      <Checkbox\r\n        checked={isChecked}\r\n        onChange={handleStatusToggle}\r\n        size=\"small\"\r\n        sx={{\r\n          p: 0.5,\r\n          mr: 0.5,\r\n          color: '#CCCCCC',\r\n          '&.Mui-checked': {\r\n            color: '#4CAF50',\r\n          }\r\n        }}\r\n        icon={<Iconify icon=\"material-symbols:check-box-outline-blank\" width={18} height={18} />}\r\n        checkedIcon={<Iconify icon=\"material-symbols:check-box\" width={18} height={18} />}\r\n      />\r\n\r\n      {isEditing ? (\r\n        <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\r\n          <TextField\r\n            inputRef={subtaskInputRef}\r\n            value={subtaskName}\r\n            onChange={(e) => setSubtaskName(e.target.value)}\r\n            onKeyDown={handleSubtaskKeyPress}\r\n            onBlur={handleSubtaskSave}\r\n            variant=\"standard\"\r\n            fullWidth\r\n            autoFocus\r\n            sx={{\r\n              fontFamily: '\"Recursive Variable\", sans-serif',\r\n              '& .MuiInputBase-input': {\r\n                fontSize: '0.85rem',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n              }\r\n            }}\r\n          />\r\n          <IconButton size=\"small\" onClick={handleSubtaskSave} sx={{ ml: 1 }}>\r\n            <Iconify icon=\"material-symbols:check\" width={14} height={14} color=\"#4CAF50\" />\r\n          </IconButton>\r\n        </Box>\r\n      ) : (\r\n        <Typography\r\n          variant=\"body2\"\r\n          sx={{\r\n            flexGrow: 1,\r\n            fontSize: '0.85rem',\r\n            fontFamily: '\"Recursive Variable\", sans-serif',\r\n            color: isChecked ? '#4CAF50' : '#555',\r\n            fontWeight: isChecked ? 500 : 400,\r\n            cursor: 'pointer',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            '&:hover': {\r\n              '& .subtask-edit-icon': {\r\n                opacity: 1,\r\n              }\r\n            }\r\n          }}\r\n          onClick={handleSubtaskEditClick}\r\n        >\r\n          {subtaskName}\r\n          <Tooltip title=\"Edit subtask name\">\r\n            <IconButton\r\n              size=\"small\"\r\n              className=\"subtask-edit-icon\"\r\n              sx={{\r\n                ml: 0.5,\r\n                opacity: 0,\r\n                transition: 'opacity 0.2s',\r\n                padding: '1px'\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:edit-outline\" width={12} height={12} color=\"#666\" />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Typography>\r\n      )}\r\n\r\n      <Box sx={{ display: 'flex', alignItems: 'center' }}>\r\n        {/* Delete subtask button */}\r\n        <Tooltip title=\"Delete subtask\">\r\n          <IconButton\r\n            size=\"small\"\r\n            onClick={handleDeleteSubtaskClick}\r\n            sx={{\r\n              p: 0.5,\r\n              color: '#F44336',\r\n              '&:hover': {\r\n                backgroundColor: '#FFEBEE'\r\n              }\r\n            }}\r\n          >\r\n            <Iconify icon=\"material-symbols:delete-outline\" width={14} height={14} />\r\n          </IconButton>\r\n        </Tooltip>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\n// Format date for display\r\nfunction formatDate(dateString) {\r\n  if (!dateString) return '';\r\n\r\n  try {\r\n    const date = new Date(dateString);\r\n    if (isNaN(date.getTime())) return '';\r\n\r\n    // Only display day and month, not year\r\n    const options = { month: 'short', day: 'numeric' };\r\n    return date.toLocaleDateString('en-US', options);\r\n  } catch (error) {\r\n    return '';\r\n  }\r\n}\r\n\r\nexport default MilestoneCard;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,IAAI,CACJC,cAAc,CACdC,MAAM,CACNC,OAAO,CACPC,SAAS,CACTC,UAAU,CACVC,OAAO,CACPC,QAAQ,CACRC,gBAAgB,CAChBC,MAAM,CACNC,WAAW,CACXC,QAAQ,KACH,eAAe,CACtB,MAAO,CAAAC,OAAO,KAAM,0BAA0B,CAC9C,OAASC,eAAe,KAAQ,mBAAmB,CACnD,OAASC,MAAM,CAAEC,aAAa,KAAQ,sBAAsB,CAC5D,MAAO,CAAAC,MAAM,KAAM,uBAAuB,CAC1C,MAAO,CAAAC,aAAa,KAAM,0BAA0B,CACpD,MAAO,CAAAC,aAAa,KAAM,0BAA0B,CACpD,MAAO,CAAAC,kBAAkB,KAAM,+BAA+B,CAC9D,OAASC,WAAW,CAAEC,UAAU,CAAEC,aAAa,CAAEC,aAAa,CAAEC,UAAU,CAAEC,mBAAmB,KAAQ,gBAAgB,CACvH,OAASC,KAAK,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEvC,KAAM,CAAAC,aAAa,CAAGC,IAAA,EAoBhB,KAAAC,gBAAA,CAAAC,iBAAA,CAAAC,iBAAA,IApBiB,CACrBC,SAAS,CACTC,OAAO,CAAG,KAAK,CACfC,YAAY,CAAG,KAAK,CACpBC,0BAA0B,CAC1BC,kBAAkB,CAClBC,qBAAqB,CACrBC,aAAa,CACbC,wBAAwB,CACxBC,gBAAgB,CAChBC,iBAAiB,CACjBC,YAAY,CACZC,eAAe,CACfC,SAAS,CACTC,YAAY,CACZC,YAAY,CACZC,eAAe,CACfC,eAAe,CACfC,YAAY,CACZC,SACF,CAAC,CAAAtB,IAAA,CACC,KAAM,CAACuB,SAAS,CAAEC,YAAY,CAAC,CAAGhE,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACiE,aAAa,CAAEC,gBAAgB,CAAC,CAAGlE,QAAQ,CAAC4C,SAAS,CAACuB,IAAI,CAAC,CAClE,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGrE,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACsE,WAAW,CAAEC,cAAc,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CAClD;AACA,KAAM,CAACwE,UAAU,CAAEC,aAAa,CAAC,CAAGzE,QAAQ,CAAC,IAAM,CACjD,KAAM,CAAA0E,UAAU,CAAGC,YAAY,CAACC,OAAO,CAAC,aAAahC,SAAS,CAACiC,EAAE,EAAIjC,SAAS,CAACuB,IAAI,WAAW,CAAC,CAC/F,MAAO,CAAAO,UAAU,GAAK,IAAI,CAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,CAAG,IAAI,CAAE;AAC9D,CAAC,CAAC,CACF,KAAM,CAAAM,QAAQ,CAAG/E,MAAM,CAAC,IAAI,CAAC,CAC7B,KAAM,CAAAgF,eAAe,CAAGhF,MAAM,CAAC,IAAI,CAAC,CAEpC;AACA,KAAM,CAAAiF,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,gBAAgB,CAAG,CAACX,UAAU,CACpCC,aAAa,CAACU,gBAAgB,CAAC,CAC/B;AACAR,YAAY,CAACS,OAAO,CAAC,aAAaxC,SAAS,CAACiC,EAAE,EAAIjC,SAAS,CAACuB,IAAI,WAAW,CAAEW,IAAI,CAACO,SAAS,CAACF,gBAAgB,CAAC,CAAC,CAChH,CAAC,CAED;AACA,KAAM,CAAAG,QAAQ,CAAGvC,0BAA0B,CAAGA,0BAA0B,CAACH,SAAS,CAAC,CAAG,CAAC,CAEvF;AACA,KAAM,CAAA2C,eAAe,CAAGvC,kBAAkB,CAAGA,kBAAkB,CAACJ,SAAS,CAAC,CAAGxB,MAAM,CAACoE,WAAW,CAC/F,KAAM,CAAAC,YAAY,CAAGpE,aAAa,CAACkE,eAAe,CAAC,EAAIlE,aAAa,CAACD,MAAM,CAACoE,WAAW,CAAC,CAExF;AACA,KAAM,CAAAE,cAAc,CAAG9C,SAAS,CAAC+C,WAAW,EAAI/C,SAAS,CAAC+C,WAAW,CAACC,IAAI,CAAC,CAAC,CAACC,MAAM,CAAG,CAAC,CAEvF;AACA,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B9B,YAAY,CAAC,IAAI,CAAC,CACpB,CAAC,CAED;AACA,KAAM,CAAA+B,UAAU,CAAGA,CAAA,GAAM,CACvB;AACA,KAAM,CAAAC,WAAW,CAAG/B,aAAa,CAAC2B,IAAI,CAAC,CAAC,CAExC;AACA,GAAII,WAAW,GAAK,EAAE,EAAIA,WAAW,GAAKpD,SAAS,CAACuB,IAAI,CAAE,CACxD,GAAId,iBAAiB,CAAE,CACrBA,iBAAiB,CAAC,CAAE,GAAGT,SAAS,CAAEuB,IAAI,CAAE6B,WAAY,CAAC,CAAC,CACxD,CACF,CAAC,IAAM,CACL;AACA9B,gBAAgB,CAACtB,SAAS,CAACuB,IAAI,CAAC,CAClC,CAEAH,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAAiC,cAAc,CAAIC,CAAC,EAAK,CAC5B,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,CAAE,CACrBJ,UAAU,CAAC,CAAC,CACd,CAAC,IAAM,IAAIG,CAAC,CAACC,GAAG,GAAK,QAAQ,CAAE,CAC7BjC,gBAAgB,CAACtB,SAAS,CAACuB,IAAI,CAAC,CAChCH,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAAoC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B/B,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAgC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAL,WAAW,CAAG1B,WAAW,CAACsB,IAAI,CAAC,CAAC,CACtC,GAAII,WAAW,EAAIxC,SAAS,CAAE,CAC5B,KAAM,CAAA8C,OAAO,CAAG,CACdnC,IAAI,CAAE6B,WAAW,CACjBpD,SAAS,CAAEA,SAAS,CAACiC,EAAE,CACvB0B,MAAM,CAAEnF,MAAM,CAACoE,WAAW,CAC1BF,QAAQ,CAAE,CACZ,CAAC,CACD9B,SAAS,CAAC8C,OAAO,CAAC,CAClB/B,cAAc,CAAC,EAAE,CAAC,CACpB,CACAF,eAAe,CAAC,KAAK,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAmC,qBAAqB,CAAIN,CAAC,EAAK,CACnC,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,CAAE,CACrBE,iBAAiB,CAAC,CAAC,CACrB,CAAC,IAAM,IAAIH,CAAC,CAACC,GAAG,GAAK,QAAQ,CAAE,CAC7B5B,cAAc,CAAC,EAAE,CAAC,CAClBF,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACAnE,SAAS,CAAC,IAAM,CACd,GAAI6D,SAAS,EAAIiB,QAAQ,CAACyB,OAAO,CAAE,CACjCzB,QAAQ,CAACyB,OAAO,CAACC,KAAK,CAAC,CAAC,CAC1B,CACF,CAAC,CAAE,CAAC3C,SAAS,CAAC,CAAC,CAEf;AACA7D,SAAS,CAAC,IAAM,CACd,GAAIkE,YAAY,EAAIa,eAAe,CAACwB,OAAO,CAAE,CAC3CxB,eAAe,CAACwB,OAAO,CAACC,KAAK,CAAC,CAAC,CACjC,CACF,CAAC,CAAE,CAACtC,YAAY,CAAC,CAAC,CAElB,mBACEhC,KAAA,CAAC/B,KAAK,EACJsG,SAAS,CAAE,CAAE,CACbC,SAAS,CAAEtF,MAAM,CAACuF,aAAc,CAChCC,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,KAAM,CAAE,CAAAC,QAAA,eAE9C7E,KAAA,CAACjC,GAAG,EAACyG,SAAS,CAAEtF,MAAM,CAAC4F,eAAgB,CAAAD,QAAA,eACrC7E,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,IAAI,CAAE,CAAE,CAAE,CAAAJ,QAAA,eAE1D/E,IAAA,CAACvB,UAAU,EACT2G,IAAI,CAAC,OAAO,CACZC,OAAO,CAAErC,oBAAqB,CAC9B4B,EAAE,CAAE,CACFU,EAAE,CAAE,CAAC,CACLC,KAAK,CAAE,MAAM,CACb,SAAS,CAAE,CACTC,eAAe,CAAE,qBACnB,CACF,CAAE,CAAAT,QAAA,cAEF/E,IAAA,CAAChB,OAAO,EACNyG,IAAI,CAAEnD,UAAU,CAAG,8BAA8B,CAAG,8BAA+B,CACnFoD,KAAK,CAAE,EAAG,CACVC,MAAM,CAAE,EAAG,CACZ,CAAC,CACQ,CAAC,CAEZ9D,SAAS,cACR3B,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEQ,KAAK,CAAE,MAAO,CAAE,CAAAX,QAAA,eAChE/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,uBAAuB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAEtG,eAAgB,CAAC2F,EAAE,CAAE,CAAEU,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cACtGtF,IAAA,CAACxB,SAAS,EACRsE,QAAQ,CAAEA,QAAS,CACnB8C,KAAK,CAAE7D,aAAc,CACrB8D,QAAQ,CAAG7B,CAAC,EAAKhC,gBAAgB,CAACgC,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE,CAClDG,SAAS,CAAEhC,cAAe,CAC1BiC,MAAM,CAAEnC,UAAW,CACnBoC,OAAO,CAAC,UAAU,CAClBC,SAAS,MACTC,SAAS,MACTvB,EAAE,CAAE,CACFwB,UAAU,CAAE,kCAAkC,CAC9CC,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,QAAQ,CAClB,uBAAuB,CAAE,CACvBD,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,QAAQ,CAClBF,UAAU,CAAE,kCACd,CACF,CAAE,CACH,CAAC,cACFpG,IAAA,CAACvB,UAAU,EAAC2G,IAAI,CAAC,OAAO,CAACC,OAAO,CAAExB,UAAW,CAACe,EAAE,CAAE,CAAE2B,EAAE,CAAE,CAAE,CAAE,CAAAxB,QAAA,cAC1D/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,wBAAwB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAC,SAAS,CAAE,CAAC,CACtE,CAAC,EACV,CAAC,cAENrF,KAAA,CAAChC,UAAU,EACT+H,OAAO,CAAC,IAAI,CACZvB,SAAS,CAAEtF,MAAM,CAACoH,cAAe,CACjC5B,EAAE,CAAE,CACFwB,UAAU,CAAE,kCAAkC,CAC9CnB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBuB,MAAM,CAAE,SAAS,CACjBtB,IAAI,CAAE,CAAC,CACP,SAAS,CAAE,CACT,cAAc,CAAE,CACduB,OAAO,CAAE,CACX,CACF,CACF,CAAE,CACFrB,OAAO,CAAEzB,eAAgB,CAAAmB,QAAA,eAEzB/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,uBAAuB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAEtG,eAAgB,CAAE,CAAC,CACtF8C,aAAa,cACd/B,IAAA,CAACtB,OAAO,EAACiI,KAAK,CAAC,qBAAqB,CAAA5B,QAAA,cAClC/E,IAAA,CAACvB,UAAU,EACT2G,IAAI,CAAC,OAAO,CACZV,SAAS,CAAC,WAAW,CACrBE,EAAE,CAAE,CACF2B,EAAE,CAAE,CAAC,CACLG,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,cAAc,CAC1B/B,OAAO,CAAE,KACX,CAAE,CAAAE,QAAA,cAEF/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,+BAA+B,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAC,MAAM,CAAE,CAAC,CAC1E,CAAC,CACN,CAAC,EACA,CACb,EACE,CAAC,cAENvF,IAAA,CAAC5B,IAAI,EACHqH,IAAI,cAAEzF,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAElC,YAAY,CAACkC,IAAK,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAE,CAClEkB,KAAK,CAAEtD,YAAY,CAACsD,KAAM,CAC1BzB,IAAI,CAAC,OAAO,CACZR,EAAE,CAAE,CACFY,eAAe,CAAE,GAAGjC,YAAY,CAACgC,KAAK,IAAI,CAC1CA,KAAK,CAAEhC,YAAY,CAACgC,KAAK,CACzBc,UAAU,CAAE,GAAG,CACfS,YAAY,CAAE,KAAK,CACnBV,UAAU,CAAE,kCACd,CAAE,CACH,CAAC,EACC,CAAC,cAENlG,KAAA,CAACnB,QAAQ,EAACgI,EAAE,CAAEzE,UAAW,CAAC0E,OAAO,CAAC,MAAM,CAACC,aAAa,MAAAlC,QAAA,EAEnD,CAACpE,OAAO,EAAI6C,cAAc,eACzBxD,IAAA,CAAC/B,GAAG,EAAC2G,EAAE,CAAE,CAAEsC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAApC,QAAA,cAC1B/E,IAAA,CAAC/B,GAAG,EACF2G,EAAE,CAAE,CACFY,eAAe,CAAE,SAAS,CAC1B4B,CAAC,CAAE,GAAG,CACNN,YAAY,CAAE,KAAK,CACnBO,MAAM,CAAE,mBACV,CAAE,CAAAtC,QAAA,cAEF/E,IAAA,CAAC9B,UAAU,EACT+H,OAAO,CAAC,OAAO,CACfrB,EAAE,CAAE,CACFW,KAAK,CAAE,MAAM,CACba,UAAU,CAAE,kCAAkC,CAC9CkB,UAAU,CAAE,UAAU,CACtBC,UAAU,CAAE,GAAG,CACflB,UAAU,CAAE,GACd,CAAE,CAAAtB,QAAA,CAEDrE,SAAS,CAAC+C,WAAW,CACZ,CAAC,CACV,CAAC,CACH,CACN,cAEDzD,IAAA,CAACzB,OAAO,EAACqG,EAAE,CAAE,CAAE4C,EAAE,CAAE,CAAC,CAAEd,OAAO,CAAE,GAAI,CAAE,CAAE,CAAC,cAE1CxG,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEsC,EAAE,CAAE,CAAE,CAAE,CAAAnC,QAAA,eACjB7E,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEwC,cAAc,CAAE,eAAe,CAAEvC,UAAU,CAAE,QAAQ,CAAEgC,EAAE,CAAE,GAAI,CAAE,CAAAnC,QAAA,eAC3F/E,IAAA,CAAC9B,UAAU,EAAC+H,OAAO,CAAC,OAAO,CAACrB,EAAE,CAAE,CAAEyB,UAAU,CAAE,GAAG,CAAED,UAAU,CAAE,kCAAkC,CAAEb,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,CAAC,UAEpH,CAAY,CAAC,cACb7E,KAAA,CAAChC,UAAU,EAAC+H,OAAO,CAAC,OAAO,CAACrB,EAAE,CAAE,CAAEyB,UAAU,CAAE,GAAG,CAAED,UAAU,CAAE,kCAAkC,CAAEb,KAAK,CAAE,MAAO,CAAE,CAAAR,QAAA,EAChH3B,QAAQ,CAAC,GACZ,EAAY,CAAC,EACV,CAAC,cACNpD,IAAA,CAAC3B,cAAc,EACb4H,OAAO,CAAC,aAAa,CACrBL,KAAK,CAAExC,QAAS,CAChBwB,EAAE,CAAE,CACFe,MAAM,CAAE,CAAC,CACTmB,YAAY,CAAE,CAAC,CACftB,eAAe,CAAE,SAAS,CAC1B,0BAA0B,CAAE,CAC1BA,eAAe,CAAEjC,YAAY,CAACgC,KAChC,CACF,CAAE,CACH,CAAC,EACC,CAAC,cAENvF,IAAA,CAAC/B,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEwC,cAAc,CAAE,eAAe,CAAEvC,UAAU,CAAE,QAAQ,CAAEgC,EAAE,CAAE,CAAE,CAAE,CAAAnC,QAAA,CACxFrE,SAAS,CAACgH,kBAAkB,eAC3BxH,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEyC,GAAG,CAAE,CAAE,CAAE,CAAA5C,QAAA,eACzD/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,wBAAwB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAC,MAAM,CAAE,CAAC,cAC7EvF,IAAA,CAAC9B,UAAU,EAAC+H,OAAO,CAAC,OAAO,CAACrB,EAAE,CAAE,CAAEwB,UAAU,CAAE,kCAAkC,CAAEb,KAAK,CAAE,MAAM,CAAEc,UAAU,CAAE,GAAI,CAAE,CAAAtB,QAAA,CAChHrE,SAAS,CAACgH,kBAAkB,CACnB,CAAC,EACV,CACN,CACE,CAAC,cAGNxH,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEsC,EAAE,CAAE,CAAE,CAAE,CAAAnC,QAAA,eACjB7E,KAAA,CAACjC,GAAG,EACF2G,EAAE,CAAE,CACFK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBuC,cAAc,CAAE,eAAe,CAC/BP,EAAE,CAAE,CACN,CAAE,CAAAnC,QAAA,eAEF7E,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEyC,GAAG,CAAE,CAAE,CAAE,CAAA5C,QAAA,eACzD/E,IAAA,CAAChB,OAAO,EACNyG,IAAI,CAAC,4BAA4B,CACjCC,KAAK,CAAE,EAAG,CACVC,MAAM,CAAE,EAAG,CACXf,EAAE,CAAE,CAAEW,KAAK,CAAE,MAAO,CAAE,CACvB,CAAC,cACFvF,IAAA,CAAC9B,UAAU,EACT+H,OAAO,CAAC,OAAO,CACfrB,EAAE,CAAE,CACFyB,UAAU,CAAE,GAAG,CACfd,KAAK,CAAE,MAAM,CACba,UAAU,CAAE,kCAAkC,CAC9CE,QAAQ,CAAE,QACZ,CAAE,CAAAvB,QAAA,CACH,OAED,CAAY,CAAC,EACV,CAAC,cAGN/E,IAAA,CAACtB,OAAO,EAACiI,KAAK,CAAC,cAAc,CAAA5B,QAAA,cAC3B/E,IAAA,CAACvB,UAAU,EACT2G,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEnB,kBAAmB,CAC5BU,EAAE,CAAE,CACFW,KAAK,CAAEtG,eAAe,CACtB,SAAS,CAAE,CACTuG,eAAe,CAAE,GAAGvG,eAAe,IACrC,CACF,CAAE,CAAA8F,QAAA,cAEF/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,2BAA2B,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAC,CACzD,CAAC,CACN,CAAC,EACP,CAAC,CAELhF,OAAO,cACNT,KAAA,CAACjC,GAAG,EAACyG,SAAS,CAAEtF,MAAM,CAACwI,QAAS,CAAA7C,QAAA,GAAAxE,gBAAA,CAC7BG,SAAS,CAACmH,KAAK,UAAAtH,gBAAA,iBAAfA,gBAAA,CAAiBuH,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAC5CjI,IAAA,CAACkI,QAAQ,EAEPF,IAAI,CAAEA,IAAK,CACXpH,YAAY,CAAEA,YAAa,CAC3BD,OAAO,CAAE,IAAK,CACdI,qBAAqB,CAAEA,qBAAsB,CAC7CC,aAAa,CAAEA,aAAc,CAC7BC,wBAAwB,CAAEA,wBAAyB,CACnDC,gBAAgB,CAAEA,gBAAiB,CACnCE,YAAY,CAAEA,YAAa,CAC3BC,eAAe,CAAEA,eAAgB,CACjCE,YAAY,CAAEA,YAAa,CAC3BC,YAAY,CAAEA,YAAa,CAC3BC,eAAe,CAAEA,eAAgB,CACjCC,eAAe,CAAEA,eAAgB,CACjCC,YAAY,CAAEA,YAAa,CAC3BC,SAAS,CAAEA,SAAU,EAfhBqG,KAgBN,CACF,CAAC,CAED,EAAAzH,iBAAA,CAAAE,SAAS,CAACmH,KAAK,UAAArH,iBAAA,iBAAfA,iBAAA,CAAiBmD,MAAM,EAAG,CAAC,eAC1BzD,KAAA,CAAC5B,MAAM,EACL2H,OAAO,CAAC,MAAM,CACdb,IAAI,CAAC,OAAO,CACZR,EAAE,CAAE,CACFW,KAAK,CAAEtG,eAAe,CACtBoH,UAAU,CAAE,GAAG,CACf8B,aAAa,CAAE,MAAM,CACrBf,CAAC,CAAE,CAAC,CACJD,EAAE,CAAE,CAAC,CACLf,UAAU,CAAE,kCACd,CAAE,CAAArB,QAAA,EACH,IACG,CAACrE,SAAS,CAACmH,KAAK,CAAClE,MAAM,CAAG,CAAC,CAAC,aAChC,EAAQ,CACT,EACE,CAAC,cAENzD,KAAA,CAACjC,GAAG,EAACyG,SAAS,CAAEtF,MAAM,CAACwI,QAAS,CAAA7C,QAAA,GAAAtE,iBAAA,CAC7BC,SAAS,CAACmH,KAAK,UAAApH,iBAAA,iBAAfA,iBAAA,CAAiBsH,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBAChCjI,IAAA,CAACkI,QAAQ,EAEPF,IAAI,CAAEA,IAAK,CACXpH,YAAY,CAAEA,YAAa,CAC3BD,OAAO,CAAE,KAAM,CACfI,qBAAqB,CAAEA,qBAAsB,CAC7CC,aAAa,CAAEA,aAAc,CAC7BC,wBAAwB,CAAEA,wBAAyB,CACnDC,gBAAgB,CAAEA,gBAAiB,CACnCE,YAAY,CAAEA,YAAa,CAC3BC,eAAe,CAAEA,eAAgB,CACjCE,YAAY,CAAEA,YAAa,CAC3BC,YAAY,CAAEA,YAAa,CAC3BC,eAAe,CAAEA,eAAgB,CACjCC,eAAe,CAAEA,eAAgB,CACjCC,YAAY,CAAEA,YAAa,CAC3BC,SAAS,CAAEA,SAAU,EAfhBqG,KAgBN,CACF,CAAC,CAGD/F,YAAY,eACXhC,KAAA,CAACjC,GAAG,EACFyG,SAAS,CAAEtF,MAAM,CAACgJ,QAAS,CAC3BxD,EAAE,CAAE,CACFyD,QAAQ,CAAE,UAAU,CACpBlB,EAAE,CAAE,CAAC,CACLlC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBM,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,KAAK,CACnBjC,OAAO,CAAE,UAAU,CACnBwC,MAAM,CAAE,mBACV,CAAE,CAAAtC,QAAA,eAEF/E,IAAA,CAAC/B,GAAG,EACF2G,EAAE,CAAE,CACFc,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVmB,YAAY,CAAE,KAAK,CACnBtB,eAAe,CAAE,SAAS,CAC1BF,EAAE,CAAE,GAAG,CACPgD,UAAU,CAAE,CACd,CAAE,CACH,CAAC,cAEFtI,IAAA,CAACxB,SAAS,EACRsE,QAAQ,CAAEC,eAAgB,CAC1B6C,KAAK,CAAExD,WAAY,CACnByD,QAAQ,CAAG7B,CAAC,EAAK3B,cAAc,CAAC2B,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE,CAChDG,SAAS,CAAEzB,qBAAsB,CACjCiE,WAAW,CAAC,wBAAwB,CACpCtC,OAAO,CAAC,UAAU,CAClBC,SAAS,MACTC,SAAS,MACTvB,EAAE,CAAE,CACFwB,UAAU,CAAE,kCAAkC,CAC9C,uBAAuB,CAAE,CACvBE,QAAQ,CAAE,QAAQ,CAClBF,UAAU,CAAE,kCAAkC,CAC9CvB,OAAO,CAAE,OACX,CAAC,CACD,8BAA8B,CAAE,CAC9B2D,iBAAiB,CAAE,oBACrB,CACF,CAAE,CACH,CAAC,cAEFtI,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEqB,EAAE,CAAE,MAAO,CAAE,CAAAxB,QAAA,eAC7D/E,IAAA,CAACvB,UAAU,EAAC2G,IAAI,CAAC,OAAO,CAACC,OAAO,CAAElB,iBAAkB,CAACS,EAAE,CAAE,CAAE2B,EAAE,CAAE,CAAE,CAAE,CAAAxB,QAAA,cACjE/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,wBAAwB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAC,SAAS,CAAE,CAAC,CACtE,CAAC,cAEbvF,IAAA,CAACvB,UAAU,EAAC2G,IAAI,CAAC,OAAO,CAACC,OAAO,CAAEA,CAAA,GAAMlD,eAAe,CAAC,KAAK,CAAE,CAACyC,EAAE,CAAE,CAAE2B,EAAE,CAAE,GAAI,CAAE,CAAAxB,QAAA,cAC9E/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,wBAAwB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAC,SAAS,CAAE,CAAC,CACtE,CAAC,EACV,CAAC,EACH,CACN,EACE,CACN,EACE,CAAC,EACI,CAAC,EACN,CAAC,CAEZ,CAAC,CACD;AACA,KAAM,CAAA2C,QAAQ,CAAGO,KAAA,EAgBX,KAAAC,oBAAA,IAhBY,CAChBV,IAAI,CACJpH,YAAY,CAAG,KAAK,CACpBD,OAAO,CAAG,KAAK,CACfI,qBAAqB,CACrBC,aAAa,CACbC,wBAAwB,CACxBC,gBAAgB,CAChBE,YAAY,CACZC,eAAe,CACfE,YAAY,CACZC,YAAY,CACZC,eAAe,CACfC,eAAe,CACfC,YAAY,CACZC,SACF,CAAC,CAAA6G,KAAA,CACC,KAAM,CAAC5G,SAAS,CAAEC,YAAY,CAAC,CAAGhE,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC6K,QAAQ,CAAEC,WAAW,CAAC,CAAG9K,QAAQ,CAACkK,IAAI,CAAC/F,IAAI,CAAC,CACnD,KAAM,CAAA4G,YAAY,CAAG9K,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAC+K,eAAe,CAAEC,kBAAkB,CAAC,CAAGjL,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACkL,cAAc,CAAEC,iBAAiB,CAAC,CAAGnL,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAAoL,kBAAkB,CAAGnL,MAAM,CAAC,IAAI,CAAC,CAEvC;AACA,KAAM,CAACoL,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGtL,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACuL,QAAQ,CAAEC,WAAW,CAAC,CAAGxL,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACyL,eAAe,CAAEC,kBAAkB,CAAC,CAAG1L,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACA,KAAM,CAAC2L,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5L,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC6L,eAAe,CAAEC,kBAAkB,CAAC,CAAG9L,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACA,KAAM,CAAC+L,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGhM,QAAQ,CAAC,KAAK,CAAC,CAE3E;AACA,KAAM,CAACiM,SAAS,CAAEC,YAAY,CAAC,CAAGlM,QAAQ,CAACkK,IAAI,CAAC,CAEhD;AACAhK,SAAS,CAAC,IAAM,CACdgM,YAAY,CAAChC,IAAI,CAAC,CACpB,CAAC,CAAE,CAACA,IAAI,CAAC,CAAC,CAEV,KAAM,CAAAiC,kBAAkB,CAAIjG,CAAC,EAAK,CAChCA,CAAC,CAACkG,eAAe,CAAC,CAAC,CACnBd,oBAAoB,CAAC,IAAI,CAAC,CAC1BI,kBAAkB,CAAC,IAAI,CAAC,CACxBhK,WAAW,CAACwI,IAAI,CAACrF,EAAE,CAAC,CACjBwH,IAAI,CAACC,QAAQ,EAAI,KAAAC,cAAA,CAAAC,eAAA,CAChBC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEJ,QAAQ,CAACK,IAAI,CAAC,CAC5C;AACA,GAAI,CAAAC,YAAY,CAAG,EAAE,CACrB,GAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAAC,CAAE,CAChCC,YAAY,CAAGN,QAAQ,CAACK,IAAI,CAC9B,CAAC,IAAM,IAAI,CAAAJ,cAAA,CAAAD,QAAQ,CAACK,IAAI,UAAAJ,cAAA,WAAbA,cAAA,CAAeI,IAAI,EAAIE,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACA,IAAI,CAAC,CAAE,CACnEC,YAAY,CAAGN,QAAQ,CAACK,IAAI,CAACA,IAAI,CACnC,CAAC,IAAM,IAAI,CAAAH,eAAA,CAAAF,QAAQ,CAACK,IAAI,UAAAH,eAAA,WAAbA,eAAA,CAAejB,QAAQ,EAAIsB,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACpB,QAAQ,CAAC,CAAE,CAC3EqB,YAAY,CAAGN,QAAQ,CAACK,IAAI,CAACpB,QAAQ,CACvC,CACAC,WAAW,CAACoB,YAAY,CAAC,CAC3B,CAAC,CAAC,CACDG,KAAK,CAACC,KAAK,EAAI,CACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDhL,KAAK,CAACgL,KAAK,CAAC,yBAAyB,CAAC,CACxC,CAAC,CAAC,CACDC,OAAO,CAAC,IAAM,CACbvB,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CACN,CAAC,CAED,KAAM,CAAAwB,gBAAgB,CAAG,KAAO,CAAAC,OAAO,EAAK,CAC1C,GAAI,KAAAC,eAAA,CACF,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAA3K,UAAU,CAACuI,IAAI,CAACrF,EAAE,CAAEsI,OAAO,CAAC,CACnDV,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEJ,QAAQ,CAAC,CAE9C;AACA,GAAI,CAAAe,UAAU,CACd,IAAAD,eAAA,CAAId,QAAQ,CAACK,IAAI,UAAAS,eAAA,WAAbA,eAAA,CAAeT,IAAI,CAAE,CACvBU,UAAU,CAAGf,QAAQ,CAACK,IAAI,CAACA,IAAI,CACjC,CAAC,IAAM,CACL;AACA,KAAM,CAAAW,WAAW,CAAGxI,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAI,CAAC,CAAC,CAClEyI,UAAU,CAAG,CACXxI,EAAE,CAAE0I,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE;AAChBL,OAAO,CAAEA,OAAO,CAChBM,IAAI,CAAE,CACJ5I,EAAE,CAAEyI,WAAW,CAACzI,EAAE,CAClB6I,UAAU,CAAEJ,WAAW,CAACI,UAAU,CAClCC,SAAS,CAAEL,WAAW,CAACK,SAAS,CAChCC,MAAM,CAAEN,WAAW,CAACM,MACtB,CAAC,CACDC,UAAU,CAAE,GAAI,CAAAN,IAAI,CAAC,CAAC,CAACO,cAAc,CAAC,OAAO,CAAE,CAC7CC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,SACT,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,CAAE,EAAE,CACpB,CAAC,CACH,CAEA;AACA3C,WAAW,CAAC4C,YAAY,EAAI,CAAC,GAAGA,YAAY,CAAEf,UAAU,CAAC,CAAC,CAE1D;AACAgB,eAAe,CAAC,CAAC,CACnB,CAAE,MAAOrB,KAAK,CAAE,CACdP,OAAO,CAACO,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7ChL,KAAK,CAACgL,KAAK,CAAC,uBAAuB,CAAC,CACtC,CACF,CAAC,CAED,KAAM,CAAAsB,mBAAmB,CAAG,KAAAA,CAAOC,SAAS,CAAEpB,OAAO,GAAK,CACxD,GAAI,CACF;AACA,KAAM,CAAAqB,kBAAkB,CAAGjD,QAAQ,CAACkD,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAC7J,EAAE,GAAK0J,SAAS,CAAC,CACjE,GAAIC,kBAAkB,CAAE,CACtB,KAAM,CAAAG,eAAe,CAAGpD,QAAQ,CAACtB,GAAG,CAACyE,CAAC,EACpCA,CAAC,CAAC7J,EAAE,GAAK0J,SAAS,CAAG,CAAE,GAAGG,CAAC,CAAEvB,OAAO,CAAEA,OAAQ,CAAC,CAAGuB,CACpD,CAAC,CACDlD,WAAW,CAACmD,eAAe,CAAC,CAC9B,CAEA;AACA,KAAM,CAAArC,QAAQ,CAAG,KAAM,CAAA1K,aAAa,CAAC2M,SAAS,CAAEpB,OAAO,CAAC,CACxDV,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEJ,QAAQ,CAAC,CAEjDtK,KAAK,CAAC4M,OAAO,CAAC,8BAA8B,CAAC,CAE7C;AACAP,eAAe,CAAC,CAAC,CACnB,CAAE,MAAOrB,KAAK,CAAE,CACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/ChL,KAAK,CAACgL,KAAK,CAAC,0BAA0B,CAAC,CACvC;AACAqB,eAAe,CAAC,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAQ,mBAAmB,CAAG,KAAO,CAAAN,SAAS,EAAK,CAC/C,GAAI,CACF;AACA/C,WAAW,CAAC4C,YAAY,EAAIA,YAAY,CAACU,MAAM,CAACJ,CAAC,EAAIA,CAAC,CAAC7J,EAAE,GAAK0J,SAAS,CAAC,CAAC,CAEzE;AACA,KAAM,CAAA1M,aAAa,CAAC0M,SAAS,CAAC,CAC9B9B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC,CAE3C1K,KAAK,CAAC4M,OAAO,CAAC,8BAA8B,CAAC,CAE7C;AACAP,eAAe,CAAC,CAAC,CACnB,CAAE,MAAOrB,KAAK,CAAE,CACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/ChL,KAAK,CAACgL,KAAK,CAAC,0BAA0B,CAAC,CACvC;AACAqB,eAAe,CAAC,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAA,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,KAAAU,eAAA,CAAAC,eAAA,CACF,KAAM,CAAA1C,QAAQ,CAAG,KAAM,CAAA5K,WAAW,CAACwI,IAAI,CAACrF,EAAE,CAAC,CAC3C4H,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEJ,QAAQ,CAACK,IAAI,CAAC,CAEjD;AACA,GAAI,CAAAC,YAAY,CAAG,EAAE,CACrB,GAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAAC,CAAE,CAChCC,YAAY,CAAGN,QAAQ,CAACK,IAAI,CAC9B,CAAC,IAAM,IAAI,CAAAoC,eAAA,CAAAzC,QAAQ,CAACK,IAAI,UAAAoC,eAAA,WAAbA,eAAA,CAAepC,IAAI,EAAIE,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACA,IAAI,CAAC,CAAE,CACnEC,YAAY,CAAGN,QAAQ,CAACK,IAAI,CAACA,IAAI,CACnC,CAAC,IAAM,IAAI,CAAAqC,eAAA,CAAA1C,QAAQ,CAACK,IAAI,UAAAqC,eAAA,WAAbA,eAAA,CAAezD,QAAQ,EAAIsB,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACpB,QAAQ,CAAC,CAAE,CAC3EqB,YAAY,CAAGN,QAAQ,CAACK,IAAI,CAACpB,QAAQ,CACvC,CAEAC,WAAW,CAACoB,YAAY,CAAC,CAC3B,CAAE,MAAOI,KAAK,CAAE,CACdP,OAAO,CAACO,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CACpD,CACF,CAAC,CAED;AACA,KAAM,CAAAiC,YAAY,CAAGhM,qBAAqB,CAAGA,qBAAqB,CAACiH,IAAI,CAAC,CAAG,CAAC,CAE5E;AACA,KAAM,CAAAgF,UAAU,CAAGhM,aAAa,CAAGA,aAAa,CAACgH,IAAI,CAAC,CAAIA,IAAI,CAAC3D,MAAM,EAAInF,MAAM,CAACoE,WAAY,CAC5F,KAAM,CAAAC,YAAY,CAAGpE,aAAa,CAAC6N,UAAU,CAAC,EAAI7N,aAAa,CAACD,MAAM,CAACoE,WAAW,CAAC,CAEnF;AACA,KAAM,CAAA2J,WAAW,CAAGjF,IAAI,CAACkF,QAAQ,EAAIlF,IAAI,CAACkF,QAAQ,CAACvJ,MAAM,CAAG,CAAC,CAE7D;AACA,KAAM,CAAAwJ,WAAW,CAAGH,UAAU,GAAK9N,MAAM,CAACkO,SAAS,CAEnD;AACA,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CAChCvL,YAAY,CAAC,IAAI,CAAC,CACpB,CAAC,CAED;AACA,KAAM,CAAAwL,cAAc,CAAGA,CAAA,GAAM,CAC3B;AACA,KAAM,CAAAxJ,WAAW,CAAG6E,QAAQ,CAACjF,IAAI,CAAC,CAAC,CAEnC;AACA,GAAII,WAAW,GAAK,EAAE,EAAIA,WAAW,GAAKkE,IAAI,CAAC/F,IAAI,CAAE,CACnD,GAAIb,YAAY,CAAE,CAChBA,YAAY,CAAC,CAAE,GAAG4G,IAAI,CAAE/F,IAAI,CAAE6B,WAAY,CAAC,CAAC,CAC9C,CACF,CAAC,IAAM,CACL;AACA8E,WAAW,CAACZ,IAAI,CAAC/F,IAAI,CAAC,CACxB,CAEAH,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAAyL,kBAAkB,CAAIvJ,CAAC,EAAK,CAChC,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,CAAE,CACrBqJ,cAAc,CAAC,CAAC,CAClB,CAAC,IAAM,IAAItJ,CAAC,CAACC,GAAG,GAAK,QAAQ,CAAE,CAC7B2E,WAAW,CAACZ,IAAI,CAAC/F,IAAI,CAAC,CACtBH,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAA0L,qBAAqB,CAAGA,CAAA,GAAM,CAClCzE,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED;AACA,KAAM,CAAA0E,qBAAqB,CAAGA,CAAA,GAAM,CAClC,GAAIjM,YAAY,CAAE,CAChBA,YAAY,CAACwG,IAAI,CAAC,CACpB,CACF,CAAC,CAED;AACA,KAAM,CAAA0F,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAA5J,WAAW,CAAGkF,cAAc,CAACtF,IAAI,CAAC,CAAC,CACzC,GAAII,WAAW,EAAIvC,YAAY,CAAE,CAC/B,KAAM,CAAAoM,UAAU,CAAG,CACjB1L,IAAI,CAAE6B,WAAW,CACjBkE,IAAI,CAAEA,IAAI,CAAC4F,IAAI,CACfvJ,MAAM,CAAEnF,MAAM,CAACoE,WAAW,CAC1BF,QAAQ,CAAE,CACZ,CAAC,CACD7B,YAAY,CAACoM,UAAU,CAAC,CACxB1E,iBAAiB,CAAC,EAAE,CAAC,CACvB,CACAF,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAA8E,wBAAwB,CAAI7J,CAAC,EAAK,CACtC,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,CAAE,CACrByJ,oBAAoB,CAAC,CAAC,CACxB,CAAC,IAAM,IAAI1J,CAAC,CAACC,GAAG,GAAK,QAAQ,CAAE,CAC7BgF,iBAAiB,CAAC,EAAE,CAAC,CACrBF,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CACF,CAAC,CAED;AACA/K,SAAS,CAAC,IAAM,CACd,GAAI6D,SAAS,EAAIgH,YAAY,CAACtE,OAAO,CAAE,CACrCsE,YAAY,CAACtE,OAAO,CAACC,KAAK,CAAC,CAAC,CAC9B,CACF,CAAC,CAAE,CAAC3C,SAAS,CAAC,CAAC,CAEf;AACA7D,SAAS,CAAC,IAAM,CACd,GAAI8K,eAAe,EAAII,kBAAkB,CAAC3E,OAAO,CAAE,CACjD2E,kBAAkB,CAAC3E,OAAO,CAACC,KAAK,CAAC,CAAC,CACpC,CACF,CAAC,CAAE,CAACsE,eAAe,CAAC,CAAC,CAErB;AACA,KAAM,CAAAgF,WAAW,CAAG/D,SAAS,CAACgE,UAAU,EAAIhE,SAAS,CAACiE,QAAQ,CAE9D,KAAM,CAAAC,uBAAuB,CAAIjK,CAAC,EAAK,CACrCA,CAAC,CAACkG,eAAe,CAAC,CAAC,CACnBR,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAwE,mBAAmB,CAAG,KAAO,CAAAC,WAAW,EAAK,CACjDvE,kBAAkB,CAAC,IAAI,CAAC,CACxB,GAAI,CACF,KAAM,CAAAwE,YAAY,CAAG,CACnB,GAAGrE,SAAS,CACZgE,UAAU,CAAEI,WAAW,CAACJ,UAAU,CAClCC,QAAQ,CAAEG,WAAW,CAACH,QAAQ,CAC9B5K,QAAQ,CAAE2G,SAAS,CAAC3G,QAAQ,EAAI,CAAC,CACjCiB,MAAM,CAAE0F,SAAS,CAAC1F,MAAM,EAAI,CAC9B,CAAC,CAED;AACA2F,YAAY,CAACoE,YAAY,CAAC,CAE1B;AACA,KAAM,CAAAhE,QAAQ,CAAG,KAAM,CAAAxK,UAAU,CAACwO,YAAY,CAAC,CAE/C;AACA,GAAIhE,QAAQ,EAAIA,QAAQ,CAACK,IAAI,CAAE,CAC7BT,YAAY,CAACI,QAAQ,CAACK,IAAI,CAAC,CAC7B,CAEA;AACA,GAAIrJ,YAAY,CAAE,CAChBA,YAAY,CAACgN,YAAY,CAAC,CAC5B,CAEAtO,KAAK,CAAC4M,OAAO,CAAC,+BAA+B,CAAC,CAChD,CAAE,MAAO5B,KAAK,CAAE,CACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDhL,KAAK,CAACgL,KAAK,CAAC,2BAA2B,CAAC,CAExC;AACAd,YAAY,CAAChC,IAAI,CAAC,CACpB,CAAC,OAAS,CACR4B,kBAAkB,CAAC,KAAK,CAAC,CACzBF,oBAAoB,CAAC,KAAK,CAAC,CAAE;AAC/B,CACF,CAAC,CAED;AACA,KAAM,CAAA2E,mBAAmB,CAAG,KAAO,CAAAC,eAAe,EAAK,CACrD,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAGD,eAAe,CAACvG,GAAG,CAACpF,EAAE,EAAI6L,MAAM,CAAC7L,EAAE,CAAC,CAAC,CACxD;AACA,KAAM,CAAAyH,QAAQ,CAAG,KAAM,CAAAvK,mBAAmB,CAACkK,SAAS,CAAC6D,IAAI,CAAEW,UAAU,CAAC,CACtE;AACA,KAAM,CAAAJ,WAAW,CAAG,CAClB,GAAGpE,SAAS,CACZ0E,SAAS,CAAErE,QAAQ,CAACqE,SAAS,EAAIF,UAAU,CAACxG,GAAG,CAACpF,EAAE,GAAK,CACrDA,EAAE,CAAEA,EAAE,CACN6I,UAAU,CAAE,EAAE,CACdC,SAAS,CAAE,EAAE,CACbiD,KAAK,CAAE,EACT,CAAC,CAAC,CACJ,CAAC,CAED;AACA1E,YAAY,CAACmE,WAAW,CAAC,CACzB,GAAI/M,YAAY,CAAE,CAChBA,YAAY,CAAC+M,WAAW,CAAC,CAC3B,CAEArE,yBAAyB,CAAC,KAAK,CAAC,CAChChK,KAAK,CAAC4M,OAAO,CAAC,+BAA+B,CAAC,CAChD,CAAE,MAAO5B,KAAK,CAAE,CACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDhL,KAAK,CAACgL,KAAK,CAAC,0BAA0B,CAAC,CACzC,CACF,CAAC,CAED,mBACE5K,KAAA,CAAAE,SAAA,EAAA2E,QAAA,eACE7E,KAAA,CAACjC,GAAG,EAACyG,SAAS,CAAEtF,MAAM,CAACuP,iBAAkB,CAAA5J,QAAA,eACvC7E,KAAA,CAACjC,GAAG,EACFyG,SAAS,CAAEtF,MAAM,CAACgJ,QAAS,CAC3BxD,EAAE,CAAE,CAAEyD,QAAQ,CAAE,UAAW,CAAE,CAAAtD,QAAA,eAC7B/E,IAAA,CAAC/B,GAAG,EACF2G,EAAE,CAAE,CACFc,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVmB,YAAY,CAAE,KAAK,CACnBtB,eAAe,CAAEjC,YAAY,CAACgC,KAAK,CACnCD,EAAE,CAAE,GAAG,CACPgD,UAAU,CAAE,CACd,CAAE,CACH,CAAC,CAEDzG,SAAS,cACR3B,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE0J,QAAQ,CAAE,CAAE,CAAE,CAAA7J,QAAA,eAC9D/E,IAAA,CAACxB,SAAS,EACRsE,QAAQ,CAAE+F,YAAa,CACvBjD,KAAK,CAAE+C,QAAS,CAChB9C,QAAQ,CAAG7B,CAAC,EAAK4E,WAAW,CAAC5E,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE,CAC7CG,SAAS,CAAEwH,kBAAmB,CAC9BvH,MAAM,CAAEsH,cAAe,CACvBrH,OAAO,CAAC,UAAU,CAClBC,SAAS,MACTC,SAAS,MACTvB,EAAE,CAAE,CACFwB,UAAU,CAAE,kCAAkC,CAC9C,uBAAuB,CAAE,CACvBE,QAAQ,CAAE,QAAQ,CAClBF,UAAU,CAAE,kCACd,CACF,CAAE,CACH,CAAC,cACFpG,IAAA,CAACvB,UAAU,EAAC2G,IAAI,CAAC,OAAO,CAACC,OAAO,CAAEiI,cAAe,CAAC1I,EAAE,CAAE,CAAE2B,EAAE,CAAE,CAAE,CAAE,CAAAxB,QAAA,cAC9D/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,wBAAwB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAC,SAAS,CAAE,CAAC,CACtE,CAAC,EACV,CAAC,cAENrF,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEgK,QAAQ,CAAE,CAAC,CAAE3J,OAAO,CAAE,MAAM,CAAE4J,aAAa,CAAE,QAAQ,CAAElH,GAAG,CAAE,GAAI,CAAE,CAAA5C,QAAA,eAC3E7E,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEuC,cAAc,CAAE,eAAe,CAAE/B,KAAK,CAAE,MAAO,CAAE,CAAAX,QAAA,eACjG/E,IAAA,CAAC/B,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEyC,GAAG,CAAE,CAAC,CAAEiH,QAAQ,CAAE,CAAE,CAAE,CAAA7J,QAAA,cACtE7E,KAAA,CAAChC,UAAU,EACT+H,OAAO,CAAC,OAAO,CACfvB,SAAS,CAAEtF,MAAM,CAACuJ,QAAS,CAC3B/D,EAAE,CAAE,CACFwB,UAAU,CAAE,kCAAkC,CAC9CK,MAAM,CAAE,SAAS,CACjBxB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBK,KAAK,CAAE4H,WAAW,CAAG,SAAS,CAAG,SAAS,CAC1C9G,UAAU,CAAE8G,WAAW,CAAG,GAAG,CAAG,GAAG,CACnCyB,QAAQ,CAAE,CAAC,CACX,SAAS,CAAE,CACT,mBAAmB,CAAE,CACnBlI,OAAO,CAAE,CACX,CACF,CACF,CAAE,CACFrB,OAAO,CAAEgI,mBAAoB,CAAAtI,QAAA,EAC5B4D,QAAQ,cACT3I,IAAA,CAACtB,OAAO,EAACiI,KAAK,CAAC,gBAAgB,CAAA5B,QAAA,cAC7B/E,IAAA,CAACvB,UAAU,EACT2G,IAAI,CAAC,OAAO,CACZV,SAAS,CAAC,gBAAgB,CAC1BE,EAAE,CAAE,CACF2B,EAAE,CAAE,GAAG,CACPG,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,cAAc,CAC1B/B,OAAO,CAAE,KACX,CAAE,CAAAE,QAAA,cAEF/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,+BAA+B,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAC,MAAM,CAAE,CAAC,CAC1E,CAAC,CACN,CAAC,EACA,CAAC,CACV,CAAC,cAENrF,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEyC,GAAG,CAAE,CAAE,CAAE,CAAA5C,QAAA,EAExDgF,SAAS,CAAC0E,SAAS,EAAI1E,SAAS,CAAC0E,SAAS,CAAC9K,MAAM,CAAG,CAAC,eACpD3D,IAAA,CAAClB,WAAW,EACVgQ,GAAG,CAAE,CAAE,CACPlK,EAAE,CAAE,CACF,mBAAmB,CAAE,CACnBc,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVW,QAAQ,CAAE,SAAS,CACnBe,MAAM,CAAE,kBACV,CACF,CAAE,CAAAtC,QAAA,CAEDgF,SAAS,CAAC0E,SAAS,CAAC1G,GAAG,CAAC,CAACgH,QAAQ,CAAE9G,KAAK,gBACvCjI,IAAA,CAACtB,OAAO,EAENiI,KAAK,CAAEoI,QAAQ,CAACvD,UAAU,EAAIuD,QAAQ,CAACtD,SAAS,CAC5C,GAAGsD,QAAQ,CAACvD,UAAU,IAAIuD,QAAQ,CAACtD,SAAS,EAAE,CAC9CsD,QAAQ,CAACL,KAAM,CACnBM,KAAK,MAAAjK,QAAA,cAEL/E,IAAA,CAACnB,MAAM,EACLoQ,GAAG,CAAEF,QAAQ,CAACrD,MAAO,CACrBwD,GAAG,CAAEH,QAAQ,CAACvD,UAAU,EAAIuD,QAAQ,CAACL,KAAM,CAC3C9J,EAAE,CAAE,CACFuK,OAAO,CAAElQ,eACX,CAAE,CAAA8F,QAAA,CAEDgK,QAAQ,CAACvD,UAAU,CAChBuD,QAAQ,CAACvD,UAAU,CAAC4D,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC3CN,QAAQ,CAACL,KAAK,CACZK,QAAQ,CAACL,KAAK,CAACU,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACtC,EAAE,CACF,CAAC,EAlBJN,QAAQ,CAACpM,EAmBP,CACV,CAAC,CACS,CACd,cAGDzC,KAAA,CAACjC,GAAG,EACFyG,SAAS,CAAC,cAAc,CACxBE,EAAE,CAAE,CACFyD,QAAQ,CAAE,UAAU,CACpBpD,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB,2BAA2B,CAAE,CAC3BwB,OAAO,CAAE,CAAC,CACV4I,UAAU,CAAE,SAAS,CACrBC,SAAS,CAAE,eACb,CAAC,CACD,uBAAuB,CAAE,CACvB7I,OAAO,CAAE,CAAC,CACV4I,UAAU,CAAE,QACd,CACF,CAAE,CAAAvK,QAAA,eAGF/E,IAAA,CAAC/B,GAAG,EACFyG,SAAS,CAAC,cAAc,CACxBE,EAAE,CAAE,CACF8B,OAAO,CAAE,CAAC,CACV4I,UAAU,CAAE,SAAS,CACrB1I,UAAU,CAAE,eAAe,CAC3B3B,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBuB,MAAM,CAAE,SAAS,CACjB5B,OAAO,CAAE,SAAS,CAClBiC,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,CACTtB,eAAe,CAAE,qBACnB,CACF,CAAE,CAAAT,QAAA,cAEF/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,6BAA6B,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAC,MAAM,CAAE,CAAC,CAC/E,CAAC,cAGNrF,KAAA,CAACjC,GAAG,EACFyG,SAAS,CAAC,kBAAkB,CAC5BE,EAAE,CAAE,CACFyD,QAAQ,CAAE,UAAU,CACpBmH,KAAK,CAAE,CAAC,CACRC,GAAG,CAAE,CAAC,CACN/I,OAAO,CAAE,CAAC,CACV4I,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,kBAAkB,CAC7B3I,UAAU,CAAE,eAAe,CAC3B3B,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBmC,MAAM,CAAE,gBAAgB,CACxBP,YAAY,CAAE,KAAK,CACnBjC,OAAO,CAAE,SAAS,CAClB6K,UAAU,CAAE,MAAM,CAClBC,SAAS,CAAE,2BAA2B,CACtCC,MAAM,CAAE,EACV,CAAE,CAAA7K,QAAA,eAGF/E,IAAA,CAACtB,OAAO,EAACiI,KAAK,CAAEmH,WAAW,CAAG,eAAe,CAAG,cAAe,CAAA/I,QAAA,cAC7D/E,IAAA,CAACvB,UAAU,EACT2G,IAAI,CAAC,OAAO,CACZC,OAAO,CAAE4I,uBAAwB,CACjCrJ,EAAE,CAAE,CACFW,KAAK,CAAEuI,WAAW,CAAG7O,eAAe,CAAG,MAAM,CAC7C,SAAS,CAAE,CACTsG,KAAK,CAAEtG,eAAe,CACtBkQ,OAAO,CAAE,yBACX,CACF,CAAE,CAAApK,QAAA,cAEF/E,IAAA,CAAChB,OAAO,EACNyG,IAAI,CAAEqI,WAAW,CAAG,iCAAiC,CAAG,kCAAmC,CAC3FpI,KAAK,CAAE,EAAG,CACVC,MAAM,CAAE,EAAG,CACZ,CAAC,CACQ,CAAC,CACN,CAAC,cAGV3F,IAAA,CAACtB,OAAO,EAACiI,KAAK,CAAC,UAAU,CAAA5B,QAAA,cACvB/E,IAAA,CAACvB,UAAU,EACT2G,IAAI,CAAC,OAAO,CACZC,OAAO,CAAE4E,kBAAmB,CAC5B4F,QAAQ,CAAEtG,eAAgB,CAC1B3E,EAAE,CAAE,CACFW,KAAK,CAAEtG,eAAe,CACtB,SAAS,CAAE,CACTkQ,OAAO,CAAE,yBACX,CACF,CAAE,CAAApK,QAAA,CAEDwE,eAAe,cACdvJ,IAAA,CAACpB,gBAAgB,EAACwG,IAAI,CAAE,EAAG,CAACR,EAAE,CAAE,CAAEW,KAAK,CAAEtG,eAAgB,CAAE,CAAE,CAAC,cAE9De,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,kCAAkC,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAC1E,CACS,CAAC,CACN,CAAC,cAGV3F,IAAA,CAACtB,OAAO,EAACiI,KAAK,CAAC,gBAAgB,CAAA5B,QAAA,cAC7B/E,IAAA,CAACvB,UAAU,EACT2G,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAMyE,yBAAyB,CAAC,IAAI,CAAE,CAC/ClF,EAAE,CAAE,CACFW,KAAK,CAAE,EAAAmD,oBAAA,CAAAqB,SAAS,CAAC0E,SAAS,UAAA/F,oBAAA,iBAAnBA,oBAAA,CAAqB/E,MAAM,EAAG,CAAC,CAAG1E,eAAe,CAAG,MAAM,CACjE,SAAS,CAAE,CACTsG,KAAK,CAAEtG,eAAe,CACtBkQ,OAAO,CAAE,yBACX,CACF,CAAE,CAAApK,QAAA,cAEF/E,IAAA,CAAChB,OAAO,EACNyG,IAAI,CAAC,2BAA2B,CAChCC,KAAK,CAAE,EAAG,CACVC,MAAM,CAAE,EAAG,CACZ,CAAC,CACQ,CAAC,CACN,CAAC,cAGV3F,IAAA,CAACtB,OAAO,EAACiI,KAAK,CAAC,aAAa,CAAA5B,QAAA,cAC1B/E,IAAA,CAACvB,UAAU,EACT2G,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEmI,qBAAsB,CAC/B5I,EAAE,CAAE,CACFW,KAAK,CAAEtG,eAAe,CACtB,SAAS,CAAE,CACTkQ,OAAO,CAAE,yBACX,CACF,CAAE,CAAApK,QAAA,cAEF/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,2BAA2B,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAC,CACzD,CAAC,CACN,CAAC,cAGV3F,IAAA,CAACtB,OAAO,EAACiI,KAAK,CAAC,WAAW,CAAA5B,QAAA,cACxB/E,IAAA,CAACvB,UAAU,EACT2G,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEgI,mBAAoB,CAC7BzI,EAAE,CAAE,CACFW,KAAK,CAAE,MAAM,CACb,SAAS,CAAE,CACT4J,OAAO,CAAE,qBACX,CACF,CAAE,CAAApK,QAAA,cAEF/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,eAAe,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAC,CAC7C,CAAC,CACN,CAAC,cAGV3F,IAAA,CAACtB,OAAO,EAACiI,KAAK,CAAC,aAAa,CAAA5B,QAAA,cAC1B/E,IAAA,CAACvB,UAAU,EACT2G,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEoI,qBAAsB,CAC/B7I,EAAE,CAAE,CACFW,KAAK,CAAE,SAAS,CAChB,SAAS,CAAE,CACT4J,OAAO,CAAE,yBACX,CACF,CAAE,CAAApK,QAAA,cAEF/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,iCAAiC,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAC,CAC/D,CAAC,CACN,CAAC,EACP,CAAC,EACH,CAAC,cAGN3F,IAAA,CAAC/B,GAAG,EACF2G,EAAE,CAAE,CACFK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBiK,OAAO,CAAE5L,YAAY,CAACgC,KAAK,CAAG,IAAI,CAClCuK,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPjJ,YAAY,CAAE,KAChB,CAAE,CAAA/B,QAAA,cAEF7E,KAAA,CAAChC,UAAU,EACT+H,OAAO,CAAC,SAAS,CACjBrB,EAAE,CAAE,CACFyB,UAAU,CAAE,GAAG,CACfd,KAAK,CAAEhC,YAAY,CAACgC,KAAK,CACzBa,UAAU,CAAE,kCACd,CAAE,CAAArB,QAAA,EAEDgI,YAAY,CAAC,GAChB,EAAY,CAAC,CACV,CAAC,EACH,CAAC,EACH,CAAC,CAGLe,WAAW,eACV5N,KAAA,CAACjC,GAAG,EACF2G,EAAE,CAAE,CACFK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBM,eAAe,CAAE,GAAGvG,eAAe,IAAI,CACvC6H,YAAY,CAAE,KAAK,CACnBiJ,EAAE,CAAE,GAAG,CACPD,EAAE,CAAE,IAAI,CACRpK,KAAK,CAAE,aACT,CAAE,CAAAX,QAAA,eAEF/E,IAAA,CAAChB,OAAO,EACNyG,IAAI,CAAC,iCAAiC,CACtCC,KAAK,CAAE,EAAG,CACVC,MAAM,CAAE,EAAG,CACXJ,KAAK,CAAEtG,eAAgB,CAAE,CAAC,cAC5BiB,KAAA,CAAChC,UAAU,EACT+H,OAAO,CAAC,SAAS,CACjBrB,EAAE,CAAE,CACFwB,UAAU,CAAE,kCAAkC,CAC9CE,QAAQ,CAAE,SAAS,CACnBf,KAAK,CAAE,MAAM,CACbc,UAAU,CAAE,GAAG,CACfkB,UAAU,CAAE,CAAC,CACbhB,EAAE,CAAE,GACN,CAAE,CAAAxB,QAAA,EACDiL,UAAU,CAACjG,SAAS,CAACgE,UAAU,CAAC,CAAC,KAAG,CAACiC,UAAU,CAACjG,SAAS,CAACiE,QAAQ,CAAC,EAC1D,CAAC,EACV,CACN,EACE,CACN,EACE,CAAC,cAEN9N,KAAA,CAACjC,GAAG,EACF2G,EAAE,CAAE,CACFqL,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,GAAG,CACPC,UAAU,CAAE,cAAc9M,YAAY,CAACgC,KAAK,EAAE,CAC9CgB,EAAE,CAAE,GAAG,CACPY,EAAE,CAAE,GAAG,CACPlC,OAAO,CAAE,CAACgI,WAAW,EAAInE,eAAe,GAAKlI,YAAY,CAAG,OAAO,CAAG,MACxE,CAAE,CAAAmE,QAAA,EAEDiD,IAAI,CAACkF,QAAQ,EAAIlF,IAAI,CAACkF,QAAQ,CAACnF,GAAG,CAAC,CAACuI,OAAO,CAAErI,KAAK,gBACjDjI,IAAA,CAACuQ,WAAW,EAEVD,OAAO,CAAEA,OAAQ,CACjBrI,KAAK,CAAEA,KAAM,CACbuI,aAAa,CAAExI,IAAI,CAACkF,QAAQ,CAACvJ,MAAO,CACpC8M,QAAQ,CAAEzI,IAAI,CAAC4F,IAAK,CACpB3M,wBAAwB,CAAEA,wBAAyB,CACnDC,gBAAgB,CAAEA,gBAAiB,CACnCG,eAAe,CAAEA,eAAgB,CACjCI,eAAe,CAAEA,eAAgB,CACjCC,eAAe,CAAEA,eAAgB,CACjCC,YAAY,CAAEA,YAAa,CAC3BC,SAAS,CAAEA,SAAU,EAXhBqG,KAYN,CACF,CAAC,CAGDa,eAAe,eACd5I,KAAA,CAACjC,GAAG,EACF2G,EAAE,CAAE,CACFK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB6K,EAAE,CAAE,GAAG,CACPW,YAAY,CAAE,oBAAoB,CAClClL,eAAe,CAAE,SAAS,CAC1BsB,YAAY,CAAE,KAAK,CACnBgJ,EAAE,CAAE,CACN,CAAE,CAAA/K,QAAA,eAEF/E,IAAA,CAACrB,QAAQ,EACPkR,QAAQ,MACRzK,IAAI,CAAC,OAAO,CACZR,EAAE,CAAE,CACFwC,CAAC,CAAE,GAAG,CACN9B,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,SACT,CAAE,CACFE,IAAI,cAAEzF,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,0CAA0C,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAE,CAC1F,CAAC,cAEF3F,IAAA,CAACxB,SAAS,EACRsE,QAAQ,CAAEoG,kBAAmB,CAC7BtD,KAAK,CAAEoD,cAAe,CACtBnD,QAAQ,CAAG7B,CAAC,EAAKiF,iBAAiB,CAACjF,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE,CACnDG,SAAS,CAAE8H,wBAAyB,CACpCtF,WAAW,CAAC,2BAA2B,CACvCtC,OAAO,CAAC,UAAU,CAClBC,SAAS,MACTC,SAAS,MACTvB,EAAE,CAAE,CACFwB,UAAU,CAAE,kCAAkC,CAC9C,uBAAuB,CAAE,CACvBE,QAAQ,CAAE,SAAS,CACnBF,UAAU,CAAE,kCAAkC,CAC9CvB,OAAO,CAAE,OACX,CAAC,CACD,8BAA8B,CAAE,CAC9B2D,iBAAiB,CAAE,oBACrB,CACF,CAAE,CACH,CAAC,cAEFtI,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAxB,QAAA,eAClC/E,IAAA,CAACvB,UAAU,EAAC2G,IAAI,CAAC,OAAO,CAACC,OAAO,CAAEqI,oBAAqB,CAAC9I,EAAE,CAAE,CAAEwC,CAAC,CAAE,GAAI,CAAE,CAAArC,QAAA,cACrE/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,wBAAwB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAC,SAAS,CAAE,CAAC,CACtE,CAAC,cAEbvF,IAAA,CAACvB,UAAU,EAAC2G,IAAI,CAAC,OAAO,CAACC,OAAO,CAAEA,CAAA,GAAM0D,kBAAkB,CAAC,KAAK,CAAE,CAACnE,EAAE,CAAE,CAAEwC,CAAC,CAAE,GAAG,CAAEb,EAAE,CAAE,GAAI,CAAE,CAAAxB,QAAA,cACzF/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,wBAAwB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAC,SAAS,CAAE,CAAC,CACtE,CAAC,EACV,CAAC,EACH,CACN,EACE,CAAC,EACH,CAAC,cAGNvF,IAAA,CAACX,aAAa,EACZsR,IAAI,CAAExH,iBAAkB,CACxByH,OAAO,CAAEA,CAAA,GAAMxH,oBAAoB,CAAC,KAAK,CAAE,CAC3CC,QAAQ,CAAEA,QAAS,CACnBwH,YAAY,CAAE7F,gBAAiB,CAC/B8F,eAAe,CAAE1E,mBAAoB,CACrC2E,eAAe,CAAEpE,mBAAoB,CACrCqE,OAAO,CAAEzH,eAAgB,CACzB0H,UAAU,CAAEjJ,IAAI,CAAC/F,IAAK,CACtBiP,UAAU,CAAC,MAAM,CAClB,CAAC,cAGFlR,IAAA,CAACV,aAAa,EACZqR,IAAI,CAAElH,iBAAkB,CACxBmH,OAAO,CAAEA,CAAA,GAAMlH,oBAAoB,CAAC,KAAK,CAAE,CAC3C1B,IAAI,CAAE+B,SAAU,CAChBoH,eAAe,CAAEjD,mBAAoB,CACrCkD,UAAU,CAAEzH,eAAgB,CAC7B,CAAC,cAGF3J,IAAA,CAACT,kBAAkB,EACjBoR,IAAI,CAAE9G,sBAAuB,CAC7B+G,OAAO,CAAEA,CAAA,GAAM9G,yBAAyB,CAAC,KAAK,CAAE,CAChD9B,IAAI,CAAE+B,SAAU,CAChBpI,YAAY,CAAEA,YAAa,CAC3BC,SAAS,CAAEA,SAAU,CACrBF,eAAe,CAAE2M,mBAAoB,CACtC,CAAC,EACF,CAAC,CAEP,CAAC,CAED;AACA,KAAM,CAAAkC,WAAW,CAAGc,KAAA,EAYd,IAZe,CACnBf,OAAO,CACPrI,KAAK,CACLuI,aAAa,CACbC,QAAQ,CACRxP,wBAAwB,CACxBC,gBAAgB,CAChBG,eAAe,CACfI,eAAe,CACfC,eAAe,CACfC,YAAY,CACZC,SACF,CAAC,CAAAyP,KAAA,CACC,KAAM,CAACxP,SAAS,CAAEC,YAAY,CAAC,CAAGhE,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACwT,WAAW,CAAEC,cAAc,CAAC,CAAGzT,QAAQ,CAACwS,OAAO,CAACrO,IAAI,CAAC,CAC5D,KAAM,CAACuP,SAAS,CAAEC,YAAY,CAAC,CAAG3T,QAAQ,CAACwS,OAAO,CAACjM,MAAM,GAAKnF,MAAM,CAACkO,SAAS,EAAIkD,OAAO,CAAClN,QAAQ,GAAK,GAAG,CAAC,CAC3G,KAAM,CAAAsO,eAAe,CAAG3T,MAAM,CAAC,IAAI,CAAC,CAEpC;AACAC,SAAS,CAAC,IAAM,CACdyT,YAAY,CAACnB,OAAO,CAACjM,MAAM,GAAKnF,MAAM,CAACkO,SAAS,EAAIkD,OAAO,CAAClN,QAAQ,GAAK,GAAG,CAAC,CAC/E,CAAC,CAAE,CAACkN,OAAO,CAACjM,MAAM,CAAEiM,OAAO,CAAClN,QAAQ,CAAC,CAAC,CAEtC;AACA,KAAM,CAAAuO,sBAAsB,CAAGA,CAAA,GAAM,CACnC7P,YAAY,CAAC,IAAI,CAAC,CACpB,CAAC,CAED;AACA,KAAM,CAAA8P,iBAAiB,CAAGA,CAAA,GAAM,CAC9B;AACA,KAAM,CAAA9N,WAAW,CAAGwN,WAAW,CAAC5N,IAAI,CAAC,CAAC,CAEtC;AACA,GAAII,WAAW,GAAK,EAAE,EAAIA,WAAW,GAAKwM,OAAO,CAACrO,IAAI,CAAE,CACtD,GAAIZ,eAAe,CAAE,CACnBA,eAAe,CAAC,CAAE,GAAGiP,OAAO,CAAErO,IAAI,CAAE6B,WAAW,CAAEkE,IAAI,CAAEyI,QAAS,CAAC,CAAC,CACpE,CACF,CAAC,IAAM,CACL;AACAc,cAAc,CAACjB,OAAO,CAACrO,IAAI,CAAC,CAC9B,CAEAH,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAA+P,qBAAqB,CAAI7N,CAAC,EAAK,CACnC,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,CAAE,CACrB2N,iBAAiB,CAAC,CAAC,CACrB,CAAC,IAAM,IAAI5N,CAAC,CAACC,GAAG,GAAK,QAAQ,CAAE,CAC7BsN,cAAc,CAACjB,OAAO,CAACrO,IAAI,CAAC,CAC5BH,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAAgQ,kBAAkB,CAAGA,CAAA,GAAM,CAC/B;AACA,KAAM,CAAAC,eAAe,CAAG,CAACP,SAAS,CAClCC,YAAY,CAACM,eAAe,CAAC,CAE7B,KAAM,CAAAC,SAAS,CAAGD,eAAe,CAAG7S,MAAM,CAACkO,SAAS,CAAGlO,MAAM,CAACoE,WAAW,CACzE,KAAM,CAAA2O,WAAW,CAAGF,eAAe,CAAG,GAAG,CAAG,CAAC,CAE7C,GAAI1Q,eAAe,CAAE,CACnB;AACA,KAAM,CAAA6Q,WAAW,CAAG,CAClB,GAAG5B,OAAO,CACVjM,MAAM,CAAE2N,SAAS,CACjB5O,QAAQ,CAAE6O,WAAW,CACrBjK,IAAI,CAAEyI,QACR,CAAC,CAEDlG,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAE0H,WAAW,CAAC,CACnD7Q,eAAe,CAAC6Q,WAAW,CAAC,CAC9B,CACF,CAAC,CAED;AACA,KAAM,CAAAC,wBAAwB,CAAGA,CAAA,GAAM,CACrC,GAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,CAAE,CACnE5Q,eAAe,CAAC6O,OAAO,CAAEG,QAAQ,CAAC,CACpC,CACF,CAAC,CAED;AACAzS,SAAS,CAAC,IAAM,CACd,GAAI6D,SAAS,EAAI6P,eAAe,CAACnN,OAAO,CAAE,CACxCmN,eAAe,CAACnN,OAAO,CAACC,KAAK,CAAC,CAAC,CACjC,CACF,CAAC,CAAE,CAAC3C,SAAS,CAAC,CAAC,CAEf,mBACE3B,KAAA,CAACjC,GAAG,EACF2G,EAAE,CAAE,CACFK,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB6K,EAAE,CAAE,GAAG,CACPW,YAAY,CAAEzI,KAAK,CAAGuI,aAAa,CAAG,CAAC,CAAG,oBAAoB,CAAG,MACnE,CAAE,CAAAzL,QAAA,eAEF/E,IAAA,CAACrB,QAAQ,EACP2T,OAAO,CAAEd,SAAU,CACnB3L,QAAQ,CAAEiM,kBAAmB,CAC7B1M,IAAI,CAAC,OAAO,CACZR,EAAE,CAAE,CACFwC,CAAC,CAAE,GAAG,CACN9B,EAAE,CAAE,GAAG,CACPC,KAAK,CAAE,SAAS,CAChB,eAAe,CAAE,CACfA,KAAK,CAAE,SACT,CACF,CAAE,CACFE,IAAI,cAAEzF,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,0CAA0C,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAE,CACzF4M,WAAW,cAAEvS,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,4BAA4B,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAE,CACnF,CAAC,CAED9D,SAAS,cACR3B,KAAA,CAACjC,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE0J,QAAQ,CAAE,CAAE,CAAE,CAAA7J,QAAA,eAC9D/E,IAAA,CAACxB,SAAS,EACRsE,QAAQ,CAAE4O,eAAgB,CAC1B9L,KAAK,CAAE0L,WAAY,CACnBzL,QAAQ,CAAG7B,CAAC,EAAKuN,cAAc,CAACvN,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE,CAChDG,SAAS,CAAE8L,qBAAsB,CACjC7L,MAAM,CAAE4L,iBAAkB,CAC1B3L,OAAO,CAAC,UAAU,CAClBC,SAAS,MACTC,SAAS,MACTvB,EAAE,CAAE,CACFwB,UAAU,CAAE,kCAAkC,CAC9C,uBAAuB,CAAE,CACvBE,QAAQ,CAAE,SAAS,CACnBF,UAAU,CAAE,kCACd,CACF,CAAE,CACH,CAAC,cACFpG,IAAA,CAACvB,UAAU,EAAC2G,IAAI,CAAC,OAAO,CAACC,OAAO,CAAEuM,iBAAkB,CAAChN,EAAE,CAAE,CAAE2B,EAAE,CAAE,CAAE,CAAE,CAAAxB,QAAA,cACjE/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,wBAAwB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAC,SAAS,CAAE,CAAC,CACtE,CAAC,EACV,CAAC,cAENrF,KAAA,CAAChC,UAAU,EACT+H,OAAO,CAAC,OAAO,CACfrB,EAAE,CAAE,CACFgK,QAAQ,CAAE,CAAC,CACXtI,QAAQ,CAAE,SAAS,CACnBF,UAAU,CAAE,kCAAkC,CAC9Cb,KAAK,CAAEiM,SAAS,CAAG,SAAS,CAAG,MAAM,CACrCnL,UAAU,CAAEmL,SAAS,CAAG,GAAG,CAAG,GAAG,CACjC/K,MAAM,CAAE,SAAS,CACjBxB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpB,SAAS,CAAE,CACT,sBAAsB,CAAE,CACtBwB,OAAO,CAAE,CACX,CACF,CACF,CAAE,CACFrB,OAAO,CAAEsM,sBAAuB,CAAA5M,QAAA,EAE/BuM,WAAW,cACZtR,IAAA,CAACtB,OAAO,EAACiI,KAAK,CAAC,mBAAmB,CAAA5B,QAAA,cAChC/E,IAAA,CAACvB,UAAU,EACT2G,IAAI,CAAC,OAAO,CACZV,SAAS,CAAC,mBAAmB,CAC7BE,EAAE,CAAE,CACF2B,EAAE,CAAE,GAAG,CACPG,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,cAAc,CAC1B/B,OAAO,CAAE,KACX,CAAE,CAAAE,QAAA,cAEF/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,+BAA+B,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACJ,KAAK,CAAC,MAAM,CAAE,CAAC,CAC1E,CAAC,CACN,CAAC,EACA,CACb,cAEDvF,IAAA,CAAC/B,GAAG,EAAC2G,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,cAEjD/E,IAAA,CAACtB,OAAO,EAACiI,KAAK,CAAC,gBAAgB,CAAA5B,QAAA,cAC7B/E,IAAA,CAACvB,UAAU,EACT2G,IAAI,CAAC,OAAO,CACZC,OAAO,CAAE8M,wBAAyB,CAClCvN,EAAE,CAAE,CACFwC,CAAC,CAAE,GAAG,CACN7B,KAAK,CAAE,SAAS,CAChB,SAAS,CAAE,CACTC,eAAe,CAAE,SACnB,CACF,CAAE,CAAAT,QAAA,cAEF/E,IAAA,CAAChB,OAAO,EAACyG,IAAI,CAAC,iCAAiC,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAC,CAC/D,CAAC,CACN,CAAC,CACP,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,QAAS,CAAAqK,UAAUA,CAACwC,UAAU,CAAE,CAC9B,GAAI,CAACA,UAAU,CAAE,MAAO,EAAE,CAE1B,GAAI,CACF,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAApH,IAAI,CAACmH,UAAU,CAAC,CACjC,GAAIE,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAAE,MAAO,EAAE,CAEpC;AACA,KAAM,CAAAC,OAAO,CAAG,CAAE5G,KAAK,CAAE,OAAO,CAAED,GAAG,CAAE,SAAU,CAAC,CAClD,MAAO,CAAA0G,IAAI,CAACI,kBAAkB,CAAC,OAAO,CAAED,OAAO,CAAC,CAClD,CAAE,MAAO9H,KAAK,CAAE,CACd,MAAO,EAAE,CACX,CACF,CAEA,cAAe,CAAAzK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
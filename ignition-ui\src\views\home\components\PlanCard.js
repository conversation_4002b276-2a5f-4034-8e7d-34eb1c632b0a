import React from 'react';
import { Box, Typography, Card, Avatar, LinearProgress } from '@mui/material';
import { Link } from "react-router-dom";
import dayjs from 'dayjs';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import styles from "../styles.module.scss";

// Status constants
const STATUS = {
  NOT_STARTED: 1,
  IN_PROGRESS: 2,
  COMPLETED: 3
};

const PlanCard = ({ plan, currentUser, index }) => {
  // Calculate subtask progress
  const calculateSubtaskProgress = (subtask) => {
    // Kiểm tra subtask tồn tại
    if (!subtask) {
      return 0;
    }
    
    // If progress is explicitly set, use it
    if (subtask.progress !== undefined && subtask.progress !== null) {
      return subtask.progress;
    }
    
    // Otherwise, determine based on status
    switch (subtask.status) {
      case STATUS.COMPLETED:
        return 100;
      case STATUS.IN_PROGRESS:
        return 50;
      default:
        return 0;
    }
  };

  // Calculate task progress based on subtasks
  const calculateTaskProgress = (task) => {
    // Ki<PERSON>m tra task tồn tại
    if (!task) {
      return 0;
    }
    
    // Đ<PERSON>m bảo subtasks là một mảng
    const subtasks = task.subtasks || [];
    
    // If no subtasks, base on task status
    if (subtasks.length === 0) {
      switch (task.status) {
        case STATUS.COMPLETED:
          return 100;
        case STATUS.IN_PROGRESS:
          return 50;
        default:
          return 0;
      }
    }
    
    // Calculate progress based on subtasks
    let totalProgress = 0;
    subtasks.forEach(subtask => {
      totalProgress += calculateSubtaskProgress(subtask);
    });
    
    return Math.floor(totalProgress / subtasks.length);
  };

  // Calculate milestone progress based on tasks
  const calculateMilestoneProgress = (milestone) => {
    // Kiểm tra milestone tồn tại
    if (!milestone) {
      return 0;
    }
    
    // Đảm bảo tasks là một mảng
    const tasks = milestone.tasks || [];
    
    // If no tasks, return 0
    if (tasks.length === 0) {
      return 0;
    }
    
    // Calculate progress based on tasks
    let totalProgress = 0;
    tasks.forEach(task => {
      // Đảm bảo task tồn tại và có thuộc tính cần thiết
      if (task) {
        totalProgress += calculateTaskProgress(task);
      }
    });
    
    return Math.floor(totalProgress / tasks.length);
  };

  // Calculate overall plan progress
  const calculatePlanProgress = () => {
    if (!plan || !plan.milestones || plan.milestones.length === 0) {
      return 0;
    }
    
    let totalProgress = 0;
    plan.milestones.forEach(milestone => {
      totalProgress += calculateMilestoneProgress(milestone);
    });
    
    return Math.floor(totalProgress / plan.milestones.length);
  };

  // Calculate overview information from plan data
  const totalMilestones = plan.milestones?.length || 0;

  // Calculate total tasks from all milestones
  const totalTasks = plan.milestones?.reduce((sum, milestone) =>
    sum + (milestone.tasks?.length || 0), 0) || 0;

  // Calculate total subtasks from all tasks
  const totalSubtasks = plan.milestones?.reduce((sum, milestone) =>
    sum + milestone.tasks?.reduce((taskSum, task) =>
      taskSum + (task.subtasks?.length || 0), 0), 0) || 0;

  // Calculate progress and task status counts
  const progress = calculatePlanProgress();
  
  // Calculate completed, in progress, and not started tasks
  let completedTasks = 0;
  let inProgressTasks = 0;
  let notStartedTasks = 0;

  plan.milestones?.forEach(milestone => {
    milestone.tasks?.forEach(task => {
      const taskProgress = calculateTaskProgress(task);
      if (taskProgress === 100) {
        completedTasks++;
      } else if (taskProgress > 0) {
        inProgressTasks++;
      } else {
        notStartedTasks++;
      }
    });
  });

  // Determine plan status
  let status = { color: '#4CAF50', label: 'Active' };
  if (progress === 100) {
    status = { color: '#2196F3', label: 'Completed' };
  } else if (progress === 0) {
    status = { color: '#FFC107', label: 'Not Started' };
  } else if (progress > 0) {
    status = { color: '#FF9800', label: 'In Progress' };
  }

  // Create tags from plan information
  const tags = [];
  if (plan.start_date && plan.end_date) {
    const duration = dayjs(plan.end_date).diff(dayjs(plan.start_date), 'day');
    tags.push(`${duration} days`);
  }

  if (totalMilestones > 0) {
    tags.push(`${totalMilestones} milestones`);
  }

  // Check if there are assignees
  const hasAssignees = plan.milestones?.some(milestone =>
    milestone.tasks?.some(task => task.assignees?.length > 0));

  if (hasAssignees) {
    tags.push('Has assignees');
  }

  return (
    <Card className={styles.planCard} key={index} sx={{ position: 'relative' }}>
      {/* Always show status indicator at top right */}
      <Box 
        className={styles.statusIndicator} 
        sx={{ 
          backgroundColor: status.color
        }}
      >
        <Typography 
          variant="caption"
          sx={{
            fontFamily: '"Recursive Variable", sans-serif'
          }}
        >
          {status.label}
        </Typography>
      </Box>
      
      <Link to={`/d/plan/${plan.slug}`} className={styles.planCardLink}>
        <Box className={styles.cardHeader}>
          <Box className={styles.cardIcon}>
            <Iconify icon="codicon:project" width={24} height={24} color={mainYellowColor} />
          </Box>
          <Typography
            variant="h6"
            className={styles.planName}
            noWrap
            sx={{
              fontFamily: '"Recursive Variable", sans-serif',
              color: '#333',
              fontSize: '1.2rem',
              fontWeight: 700,
              margin: 0,
              flex: 1,
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}
          >
            {plan.name}
          </Typography>
        </Box>

        {/* Access Level Badge */}
        <Box className={styles.accessLevelSection}>
          <Box className={styles.accessBadge}>
            <Iconify icon="material-symbols:security" width={16} height={16} color={mainYellowColor} />
            <Typography
              variant="caption"
              sx={{
                fontFamily: '"Recursive Variable", sans-serif',
                fontSize: '0.75rem',
                fontWeight: 600,
                color: '#333'
              }}
            >
              {plan.user_access_level?.is_head_owner
                ? 'Head Owner'
                : plan.user_access_level?.access_level
                  ? plan.user_access_level.access_level.charAt(0).toUpperCase() + plan.user_access_level.access_level.slice(1)
                  : (plan.user.id === currentUser.id ? 'Owner' : 'Shared')
              }
            </Typography>
          </Box>
        </Box>

        <Box className={styles.cardMeta}>
          {(plan.start_date || plan.end_date) && (
            <Box className={styles.metaItem}>
              <Iconify icon="carbon:calendar" width={16} height={16} color={mainYellowColor} />
              <Typography variant="caption">
                <span className={styles.metaLabel}>Timeline: </span>
                {plan.start_date ? dayjs(plan.start_date).format('MM/DD/YYYY') : 'Not started'}
                {plan.start_date && plan.end_date && " ~ "}
                {plan.end_date ? dayjs(plan.end_date).format('MM/DD/YYYY') : 'No end date'}
              </Typography>
            </Box>
          )}
        </Box>

        {/* Display plan structure overview */}
        <Box className={styles.planStructure}>
          <Box className={styles.structureItem}>
            <Box className={styles.structureIcon} sx={{ backgroundColor: '#E3F2FD' }}>
              <Iconify icon="mdi:flag-variant" width={16} height={16} color="#2196F3" />
            </Box>
            <Box className={styles.structureInfo}>
              <Typography
                variant="caption"
                className={styles.structureLabel}
                sx={{
                  fontSize: '0.7rem',
                  color: '#666',
                  fontFamily: '"Recursive Variable", sans-serif'
                }}
              >
                Milestones
              </Typography>
              <Typography
                variant="h6"
                className={styles.structureValue}
                sx={{
                  fontWeight: 700,
                  fontSize: '1.2rem',
                  color: '#333',
                  fontFamily: '"Recursive Variable", sans-serif',
                  margin: 0,
                  lineHeight: 1.2
                }}
              >
                {totalMilestones}
              </Typography>
            </Box>
          </Box>

          <Box className={styles.structureItem}>
            <Box className={styles.structureIcon} sx={{ backgroundColor: '#FFF8E1' }}>
              <Iconify icon="mdi:checkbox-marked-outline" width={16} height={16} color="#FFA000" />
            </Box>
            <Box className={styles.structureInfo}>
              <Typography
                variant="caption"
                className={styles.structureLabel}
                sx={{
                  fontSize: '0.7rem',
                  color: '#666',
                  fontFamily: '"Recursive Variable", sans-serif'
                }}
              >
                Tasks
              </Typography>
              <Typography
                variant="h6"
                className={styles.structureValue}
                sx={{
                  fontWeight: 700,
                  fontSize: '1.2rem',
                  color: '#333',
                  fontFamily: '"Recursive Variable", sans-serif',
                  margin: 0,
                  lineHeight: 1.2
                }}
              >
                {totalTasks}
              </Typography>
            </Box>
          </Box>

          <Box className={styles.structureItem}>
            <Box className={styles.structureIcon} sx={{ backgroundColor: '#E8F5E9' }}>
              <Iconify icon="mdi:format-list-checks" width={16} height={16} color="#4CAF50" />
            </Box>
            <Box className={styles.structureInfo}>
              <Typography
                variant="caption"
                className={styles.structureLabel}
                sx={{
                  fontSize: '0.7rem',
                  color: '#666',
                  fontFamily: '"Recursive Variable", sans-serif'
                }}
              >
                Subtasks
              </Typography>
              <Typography
                variant="h6"
                className={styles.structureValue}
                sx={{
                  fontWeight: 700,
                  fontSize: '1.2rem',
                  color: '#333',
                  fontFamily: '"Recursive Variable", sans-serif',
                  margin: 0,
                  lineHeight: 1.2
                }}
              >
                {totalSubtasks}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Display progress */}
        <Box className={styles.progressSection}>
          <Box className={styles.progressHeader}>
            <Typography
              variant="caption"
              color="textSecondary"
              sx={{
                fontFamily: '"Recursive Variable", sans-serif',
                fontSize: '0.7rem',
                color: '#666'
              }}
            >
              Progress
            </Typography>
            <Typography
              variant="h6"
              className={styles.progressValue}
              sx={{
                fontWeight: 700,
                fontSize: '1.2rem',
                color: '#333',
                fontFamily: '"Recursive Variable", sans-serif',
                margin: 0,
                lineHeight: 1.2
              }}
            >
              {progress}%
            </Typography>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={progress} 
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: 'rgba(0, 0, 0, 0.05)',
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
                backgroundColor: progress === 100 ? '#4CAF50' : mainYellowColor,
              }
            }}
          />

          {/* Display task status distribution */}
          <Box className={styles.taskStatusLegend}>
            <Box className={styles.legendItem}>
              <Box className={styles.legendColor} sx={{ backgroundColor: '#4CAF50' }} />
              <Typography
                variant="caption"
                className={styles.legendText}
                sx={{
                  fontFamily: '"Recursive Variable", sans-serif',
                  fontSize: '0.75rem',
                  color: '#666'
                }}
              >
                {completedTasks} completed
              </Typography>
            </Box>
            <Box className={styles.legendItem}>
              <Box className={styles.legendColor} sx={{ backgroundColor: '#FF9800' }} />
              <Typography
                variant="caption"
                className={styles.legendText}
                sx={{
                  fontFamily: '"Recursive Variable", sans-serif',
                  fontSize: '0.75rem',
                  color: '#666'
                }}
              >
                {inProgressTasks} in progress
              </Typography>
            </Box>
            <Box className={styles.legendItem}>
              <Box className={styles.legendColor} sx={{ backgroundColor: '#E0E0E0' }} />
              <Typography
                variant="caption"
                className={styles.legendText}
                sx={{
                  fontFamily: '"Recursive Variable", sans-serif',
                  fontSize: '0.75rem',
                  color: '#666'
                }}
              >
                {notStartedTasks} not started
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Display plan creator and creation time */}
        <Box 
          className={styles.planCreator} 
          sx={{ 
            borderTop: 'none',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            marginTop: '8px',
            paddingTop: '8px',
            justifyContent: 'space-between',
            fontFamily: '"Recursive Variable", sans-serif',
            marginBottom: '8px'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar
              src={plan.user.avatar || ''}
              alt={`${plan.user.first_name} ${plan.user.last_name}`}
              className={styles.creatorAvatar}
              sx={{ width: '28px', height: '28px' }}
            />
            <Box>
              <Typography
                variant="caption"
                className={styles.creatorLabel}
                sx={{
                  color: '#888',
                  fontSize: '0.7rem',
                  display: 'block',
                  fontFamily: '"Recursive Variable", sans-serif'
                }}
              >
                Created by
              </Typography>
              <Typography
                variant="caption"
                className={styles.creatorName}
                sx={{
                  fontWeight: 600,
                  fontSize: '0.8rem',
                  color: '#333',
                  fontFamily: '"Recursive Variable", sans-serif'
                }}
              >
                {plan.user.first_name} {plan.user.last_name}
              </Typography>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {plan.user.id !== currentUser.id && (
              <Box 
                sx={{ 
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                  backgroundColor: 'rgba(255, 99, 71, 0.9)',
                  color: 'white',
                  padding: '4px 10px',
                  borderRadius: '16px',
                  fontSize: '0.75rem',
                  fontWeight: 500,
                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                  marginRight: '8px'
                }}
              >
                <Iconify icon="ph:share-network" width={14} height={14} />
                <Typography 
                  variant="caption"
                  sx={{
                    fontFamily: '"Recursive Variable", sans-serif',
                    color: 'white'
                  }}
                >
                  Shared
                </Typography>
              </Box>
            )}
            <Typography
              variant="caption"
              className={styles.lastUpdated}
              sx={{
                color: '#888',
                fontSize: '0.7rem',
                display: 'flex',
                alignItems: 'center',
                gap: '4px',
                fontFamily: '"Recursive Variable", sans-serif'
              }}
            >
              <Iconify icon="mdi:clock-outline" width={14} height={14} />
              {dayjs(plan.updated_at).fromNow()}
            </Typography>
          </Box>
        </Box>
      </Link>
    </Card>
  );
};

export default PlanCard;

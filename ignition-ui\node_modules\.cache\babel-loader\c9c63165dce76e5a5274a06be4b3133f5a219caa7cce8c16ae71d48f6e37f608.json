{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\components\\\\Loading\\\\HexagonBallLoading.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\nimport Matter from 'matter-js';\nimport styles from './styles.module.scss';\n\n/**\r\n * Component displays loading screen with a bouncing ball inside a rotating hexagon\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page\r\n * @returns {JSX.Element} - HexagonBallLoading component\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HexagonBallLoading = _ref => {\n  _s();\n  let {\n    fromCreatePlan\n  } = _ref;\n  const canvasRef = useRef(null);\n  const engineRef = useRef(null);\n  const requestRef = useRef(null);\n  const ballRef = useRef(null);\n  const hexagonEdgesRef = useRef([]);\n  useEffect(() => {\n    // Optimized physics parameters for 60fps performance\n    const rotationSpeed = 0.01; // Optimized rotation speed\n    const gravity = 0.0003; // Increased gravity for more realistic physics\n    const ballRestitution = 0.995; // Realistic bounce with slight energy loss\n    const wallRestitution = 0.005; // Walls absorb some energy\n\n    // Initialize Matter.js modules\n    const Engine = Matter.Engine;\n    const Render = Matter.Render;\n    const World = Matter.World;\n    const Bodies = Matter.Bodies;\n    const Body = Matter.Body;\n\n    // Create engine with optimized physics settings\n    engineRef.current = Engine.create({\n      gravity: {\n        x: 0,\n        y: gravity,\n        scale: 1\n      }\n    });\n\n    // Optimize timing for consistent 60fps performance\n    engineRef.current.timing.timeScale = 1;\n    engineRef.current.positionIterations = 6; // Reduced for performance\n    engineRef.current.velocityIterations = 4; // Reduced for performance\n    engineRef.current.constraintIterations = 2; // Reduced for performance\n\n    // Create renderer\n    const render = Render.create({\n      canvas: canvasRef.current,\n      engine: engineRef.current,\n      options: {\n        width: 352,\n        height: 352,\n        wireframes: false,\n        background: '#ffffff',\n        showAngleIndicator: false,\n        showCollisions: false,\n        showVelocity: false\n      }\n    });\n\n    // Create hexagon\n    const hexagonRadius = 132;\n    const hexagonSides = 6;\n    const centerX = render.options.width / 2;\n    const centerY = render.options.height / 2;\n\n    // Create hexagon vertices\n    const hexagonVertices = [];\n    for (let i = 0; i < hexagonSides; i++) {\n      const angle = Math.PI * 2 * i / hexagonSides;\n      const x = hexagonRadius * Math.cos(angle);\n      const y = hexagonRadius * Math.sin(angle);\n      hexagonVertices.push({\n        x,\n        y\n      });\n    }\n\n    // Create hexagon edges with realistic physics\n    hexagonEdgesRef.current = [];\n    for (let i = 0; i < hexagonSides; i++) {\n      const j = (i + 1) % hexagonSides;\n      const edgeOptions = {\n        restitution: wallRestitution,\n        // Use realistic wall bounce\n        friction: 0.05,\n        // Reduced friction for smoother movement\n        frictionStatic: 0.1,\n        isStatic: true,\n        render: {\n          fillStyle: 'transparent',\n          strokeStyle: '#0f0f0f',\n          lineWidth: 2 // Slightly thicker for better visibility\n        }\n      };\n\n      // Calculate edge position and angle\n      const vertex1 = hexagonVertices[i];\n      const vertex2 = hexagonVertices[j];\n      const edgeLength = Math.sqrt(Math.pow(vertex2.x - vertex1.x, 2) + Math.pow(vertex2.y - vertex1.y, 2));\n      const edgeAngle = Math.atan2(vertex2.y - vertex1.y, vertex2.x - vertex1.x);\n      const edgeCenterX = centerX + (vertex1.x + vertex2.x) / 2;\n      const edgeCenterY = centerY + (vertex1.y + vertex2.y) / 2;\n\n      // Create edge\n      const edge = Bodies.rectangle(edgeCenterX, edgeCenterY, edgeLength, 1, edgeOptions);\n\n      // Rotate edge to angle\n      Body.rotate(edge, edgeAngle);\n      hexagonEdgesRef.current.push(edge);\n    }\n\n    // Create ball with corrected collision detection\n    const ballRadius = 12; // Fixed radius that matches visual size\n\n    // Start ball at a random position within the hexagon for variety\n    const startAngle = Math.random() * Math.PI * 2;\n    const startDistance = hexagonRadius * 0.25; // Start closer to center\n    const startX = centerX + Math.cos(startAngle) * startDistance;\n    const startY = centerY + Math.sin(startAngle) * startDistance;\n    ballRef.current = Bodies.circle(startX, startY, ballRadius, {\n      restitution: ballRestitution,\n      // Realistic bounce with energy loss\n      friction: 0.01,\n      // Very low friction for smooth rolling\n      frictionAir: 0.001,\n      // Minimal air resistance for realistic movement\n      density: 0.003,\n      // Optimized density for better physics\n      inertia: Infinity,\n      // Prevent rotation for cleaner movement\n      render: {\n        fillStyle: '#F0A500',\n        strokeStyle: '#E69500',\n        lineWidth: 2\n      }\n    });\n\n    // Add realistic initial velocity\n    const initialSpeed = 0.8;\n    const initialAngle = Math.random() * Math.PI * 2;\n    Body.setVelocity(ballRef.current, {\n      x: Math.cos(initialAngle) * initialSpeed,\n      y: Math.sin(initialAngle) * initialSpeed\n    });\n\n    // Add bodies to world\n    World.add(engineRef.current.world, [...hexagonEdgesRef.current, ballRef.current]);\n\n    // Run renderer\n    Render.run(render);\n\n    // Optimized animation loop for 60fps performance\n    let lastTime = performance.now();\n    const targetFPS = 60;\n    const frameTime = 1000 / targetFPS;\n    const animate = currentTime => {\n      const deltaTime = currentTime - lastTime;\n\n      // Only update if enough time has passed (throttle to 60fps)\n      if (deltaTime >= frameTime) {\n        // Update engine with consistent timing\n        Engine.update(engineRef.current, 16.667);\n\n        // Optimized hexagon rotation with reduced calculations\n        if (hexagonEdgesRef.current.length > 0) {\n          const cosRotation = Math.cos(rotationSpeed);\n          const sinRotation = Math.sin(rotationSpeed);\n          hexagonEdgesRef.current.forEach(edge => {\n            // Calculate new position after rotation (optimized)\n            const currentX = edge.position.x - centerX;\n            const currentY = edge.position.y - centerY;\n            const newX = currentX * cosRotation - currentY * sinRotation;\n            const newY = currentX * sinRotation + currentY * cosRotation;\n            Body.setPosition(edge, {\n              x: centerX + newX,\n              y: centerY + newY\n            });\n            Body.rotate(edge, rotationSpeed);\n          });\n        }\n        lastTime = currentTime;\n      }\n\n      // Optimized ball physics management with correct collision detection\n      if (ballRef.current) {\n        const ballPos = ballRef.current.position;\n        const velocity = ballRef.current.velocity;\n        const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);\n\n        // Calculate distance from ball center to hexagon center\n        const distanceFromCenter = Math.sqrt(Math.pow(ballPos.x - centerX, 2) + Math.pow(ballPos.y - centerY, 2));\n\n        // Corrected boundary checking - account for actual ball radius\n        const maxDistance = hexagonRadius - ballRadius - 8; // Proper collision boundary\n\n        if (distanceFromCenter > maxDistance) {\n          // Calculate normalized direction vector (optimized)\n          const directionX = ballPos.x - centerX;\n          const directionY = ballPos.y - centerY;\n          const magnitude = Math.sqrt(directionX * directionX + directionY * directionY);\n          if (magnitude > 0) {\n            // Prevent division by zero\n            const normalizedX = directionX / magnitude;\n            const normalizedY = directionY / magnitude;\n\n            // Position ball at correct boundary\n            const newPosX = centerX + normalizedX * maxDistance;\n            const newPosY = centerY + normalizedY * maxDistance;\n            Body.setPosition(ballRef.current, {\n              x: newPosX,\n              y: newPosY\n            });\n\n            // Improved bounce physics with proper reflection\n            const currentVelocity = ballRef.current.velocity;\n            const dotProduct = currentVelocity.x * normalizedX + currentVelocity.y * normalizedY;\n            if (dotProduct > 0) {\n              // Reflect velocity with realistic energy loss\n              const reflectedVelX = currentVelocity.x - 2 * dotProduct * normalizedX;\n              const reflectedVelY = currentVelocity.y - 2 * dotProduct * normalizedY;\n              Body.setVelocity(ballRef.current, {\n                x: reflectedVelX * ballRestitution,\n                y: reflectedVelY * ballRestitution\n              });\n            }\n          }\n        }\n\n        // Optimized energy maintenance\n        if (speed < 0.5) {\n          // Add subtle random impulse to keep ball moving\n          const impulseAngle = Math.random() * Math.PI * 2;\n          const impulseMagnitude = 0.2;\n          Body.applyForce(ballRef.current, ballRef.current.position, {\n            x: Math.cos(impulseAngle) * impulseMagnitude * 0.0008,\n            y: Math.sin(impulseAngle) * impulseMagnitude * 0.0008\n          });\n        }\n\n        // Limit maximum speed for smooth movement\n        if (speed > 5) {\n          const limitedVelX = velocity.x / speed * 5;\n          const limitedVelY = velocity.y / speed * 5;\n          Body.setVelocity(ballRef.current, {\n            x: limitedVelX,\n            y: limitedVelY\n          });\n        }\n      }\n\n      // Continue animation loop\n      requestRef.current = requestAnimationFrame(animate);\n    };\n\n    // Start optimized animation loop\n    requestRef.current = requestAnimationFrame(animate);\n\n    // Cleanup when component unmounts\n    return () => {\n      // Cancel animation loop\n      if (requestRef.current) {\n        cancelAnimationFrame(requestRef.current);\n      }\n\n      // Cleanup renderer and engine\n      Render.stop(render);\n      World.clear(engineRef.current.world);\n      Engine.clear(engineRef.current);\n      render.canvas = null;\n      render.context = null;\n      render.textures = {};\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: styles.loadingContainer,\n    sx: {\n      ...(!fromCreatePlan && {\n        minHeight: '90vh'\n      })\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.gameWrapper,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.hexagonLoadingContainer,\n        children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: canvasRef,\n          className: styles.hexagonCanvas\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 280,\n    columnNumber: 5\n  }, this);\n};\n_s(HexagonBallLoading, \"uDBlP5n4TAfWzOEW2LE7YQXblas=\");\n_c = HexagonBallLoading;\nexport default HexagonBallLoading;\nvar _c;\n$RefreshReg$(_c, \"HexagonBallLoading\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "Matter", "styles", "jsxDEV", "_jsxDEV", "HexagonBallLoading", "_ref", "_s", "fromCreatePlan", "canvasRef", "engineRef", "requestRef", "ballRef", "hexagonEdgesRef", "rotationSpeed", "gravity", "ballRestitution", "wallRestitution", "Engine", "Render", "World", "Bodies", "Body", "current", "create", "x", "y", "scale", "timing", "timeScale", "positionIterations", "velocityIterations", "constraintIterations", "render", "canvas", "engine", "options", "width", "height", "wireframes", "background", "showAngleIndicator", "showCollisions", "showVelocity", "hexagonRadius", "hexagonSides", "centerX", "centerY", "hexagonVertices", "i", "angle", "Math", "PI", "cos", "sin", "push", "j", "edgeOptions", "restitution", "friction", "frictionStatic", "isStatic", "fillStyle", "strokeStyle", "lineWidth", "vertex1", "vertex2", "edge<PERSON><PERSON><PERSON>", "sqrt", "pow", "edgeAngle", "atan2", "edgeCenterX", "edgeCenterY", "edge", "rectangle", "rotate", "ballRadius", "startAngle", "random", "startDistance", "startX", "startY", "circle", "frictionAir", "density", "inertia", "Infinity", "initialSpeed", "initialAngle", "setVelocity", "add", "world", "run", "lastTime", "performance", "now", "targetFPS", "frameTime", "animate", "currentTime", "deltaTime", "update", "length", "cosRotation", "sinRotation", "for<PERSON>ach", "currentX", "position", "currentY", "newX", "newY", "setPosition", "ballPos", "velocity", "speed", "distanceFromCenter", "maxDistance", "directionX", "directionY", "magnitude", "normalizedX", "normalizedY", "newPosX", "newPosY", "currentVelocity", "dotProduct", "reflectedVelX", "reflectedVelY", "impulseAngle", "impulseMagnitude", "applyForce", "limitedVelX", "limitedVelY", "requestAnimationFrame", "cancelAnimationFrame", "stop", "clear", "context", "textures", "className", "loadingContainer", "sx", "minHeight", "children", "gameWrapper", "hexagonLoadingContainer", "ref", "hexagonCanvas", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/components/Loading/HexagonBallLoading.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\nimport { Box } from '@mui/material';\r\nimport Matter from 'matter-js';\r\nimport styles from './styles.module.scss';\r\n\r\n/**\r\n * Component displays loading screen with a bouncing ball inside a rotating hexagon\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page\r\n * @returns {JSX.Element} - HexagonBallLoading component\r\n */\r\nconst HexagonBallLoading = ({ fromCreatePlan }) => {\r\n  const canvasRef = useRef(null);\r\n  const engineRef = useRef(null);\r\n  const requestRef = useRef(null);\r\n  const ballRef = useRef(null);\r\n  const hexagonEdgesRef = useRef([]);\r\n\r\n  useEffect(() => {\r\n    // Optimized physics parameters for 60fps performance\r\n    const rotationSpeed = 0.01; // Optimized rotation speed\r\n    const gravity = 0.0003; // Increased gravity for more realistic physics\r\n    const ballRestitution = 0.995; // Realistic bounce with slight energy loss\r\n    const wallRestitution = 0.005; // Walls absorb some energy\r\n\r\n    // Initialize Matter.js modules\r\n    const Engine = Matter.Engine;\r\n    const Render = Matter.Render;\r\n    const World = Matter.World;\r\n    const Bodies = Matter.Bodies;\r\n    const Body = Matter.Body;\r\n\r\n    // Create engine with optimized physics settings\r\n    engineRef.current = Engine.create({\r\n      gravity: { x: 0, y: gravity, scale: 1 },\r\n    });\r\n\r\n    // Optimize timing for consistent 60fps performance\r\n    engineRef.current.timing.timeScale = 1;\r\n    engineRef.current.positionIterations = 6; // Reduced for performance\r\n    engineRef.current.velocityIterations = 4; // Reduced for performance\r\n    engineRef.current.constraintIterations = 2; // Reduced for performance\r\n\r\n    // Create renderer\r\n    const render = Render.create({\r\n      canvas: canvasRef.current,\r\n      engine: engineRef.current,\r\n      options: {\r\n        width: 352,\r\n        height: 352,\r\n        wireframes: false,\r\n        background: '#ffffff',\r\n        showAngleIndicator: false,\r\n        showCollisions: false,\r\n        showVelocity: false,\r\n      },\r\n    });\r\n\r\n    // Create hexagon\r\n    const hexagonRadius = 132;\r\n    const hexagonSides = 6;\r\n    const centerX = render.options.width / 2;\r\n    const centerY = render.options.height / 2;\r\n\r\n    // Create hexagon vertices\r\n    const hexagonVertices = [];\r\n    for (let i = 0; i < hexagonSides; i++) {\r\n      const angle = (Math.PI * 2 * i) / hexagonSides;\r\n      const x = hexagonRadius * Math.cos(angle);\r\n      const y = hexagonRadius * Math.sin(angle);\r\n      hexagonVertices.push({ x, y });\r\n    }\r\n\r\n    // Create hexagon edges with realistic physics\r\n    hexagonEdgesRef.current = [];\r\n    for (let i = 0; i < hexagonSides; i++) {\r\n      const j = (i + 1) % hexagonSides;\r\n      const edgeOptions = {\r\n        restitution: wallRestitution, // Use realistic wall bounce\r\n        friction: 0.05, // Reduced friction for smoother movement\r\n        frictionStatic: 0.1,\r\n        isStatic: true,\r\n        render: {\r\n          fillStyle: 'transparent',\r\n          strokeStyle: '#0f0f0f',\r\n          lineWidth: 2, // Slightly thicker for better visibility\r\n        },\r\n      };\r\n\r\n      // Calculate edge position and angle\r\n      const vertex1 = hexagonVertices[i];\r\n      const vertex2 = hexagonVertices[j];\r\n      const edgeLength = Math.sqrt(\r\n        Math.pow(vertex2.x - vertex1.x, 2) + Math.pow(vertex2.y - vertex1.y, 2)\r\n      );\r\n      const edgeAngle = Math.atan2(vertex2.y - vertex1.y, vertex2.x - vertex1.x);\r\n      const edgeCenterX = centerX + (vertex1.x + vertex2.x) / 2;\r\n      const edgeCenterY = centerY + (vertex1.y + vertex2.y) / 2;\r\n\r\n      // Create edge\r\n      const edge = Bodies.rectangle(\r\n        edgeCenterX,\r\n        edgeCenterY,\r\n        edgeLength,\r\n        1,\r\n        edgeOptions\r\n      );\r\n\r\n      // Rotate edge to angle\r\n      Body.rotate(edge, edgeAngle);\r\n\r\n      hexagonEdgesRef.current.push(edge);\r\n    }\r\n\r\n    // Create ball with corrected collision detection\r\n    const ballRadius = 12; // Fixed radius that matches visual size\r\n\r\n    // Start ball at a random position within the hexagon for variety\r\n    const startAngle = Math.random() * Math.PI * 2;\r\n    const startDistance = hexagonRadius * 0.25; // Start closer to center\r\n    const startX = centerX + Math.cos(startAngle) * startDistance;\r\n    const startY = centerY + Math.sin(startAngle) * startDistance;\r\n\r\n    ballRef.current = Bodies.circle(startX, startY, ballRadius, {\r\n      restitution: ballRestitution, // Realistic bounce with energy loss\r\n      friction: 0.01, // Very low friction for smooth rolling\r\n      frictionAir: 0.001, // Minimal air resistance for realistic movement\r\n      density: 0.003, // Optimized density for better physics\r\n      inertia: Infinity, // Prevent rotation for cleaner movement\r\n      render: {\r\n        fillStyle: '#F0A500',\r\n        strokeStyle: '#E69500',\r\n        lineWidth: 2,\r\n      },\r\n    });\r\n\r\n    // Add realistic initial velocity\r\n    const initialSpeed = 0.8;\r\n    const initialAngle = Math.random() * Math.PI * 2;\r\n    Body.setVelocity(ballRef.current, {\r\n      x: Math.cos(initialAngle) * initialSpeed,\r\n      y: Math.sin(initialAngle) * initialSpeed\r\n    });\r\n\r\n    // Add bodies to world\r\n    World.add(engineRef.current.world, [...hexagonEdgesRef.current, ballRef.current]);\r\n\r\n    // Run renderer\r\n    Render.run(render);\r\n\r\n    // Optimized animation loop for 60fps performance\r\n    let lastTime = performance.now();\r\n    const targetFPS = 60;\r\n    const frameTime = 1000 / targetFPS;\r\n\r\n    const animate = (currentTime) => {\r\n      const deltaTime = currentTime - lastTime;\r\n\r\n      // Only update if enough time has passed (throttle to 60fps)\r\n      if (deltaTime >= frameTime) {\r\n        // Update engine with consistent timing\r\n        Engine.update(engineRef.current, 16.667);\r\n\r\n        // Optimized hexagon rotation with reduced calculations\r\n        if (hexagonEdgesRef.current.length > 0) {\r\n          const cosRotation = Math.cos(rotationSpeed);\r\n          const sinRotation = Math.sin(rotationSpeed);\r\n\r\n          hexagonEdgesRef.current.forEach(edge => {\r\n            // Calculate new position after rotation (optimized)\r\n            const currentX = edge.position.x - centerX;\r\n            const currentY = edge.position.y - centerY;\r\n\r\n            const newX = currentX * cosRotation - currentY * sinRotation;\r\n            const newY = currentX * sinRotation + currentY * cosRotation;\r\n\r\n            Body.setPosition(edge, {\r\n              x: centerX + newX,\r\n              y: centerY + newY\r\n            });\r\n            Body.rotate(edge, rotationSpeed);\r\n          });\r\n        }\r\n\r\n        lastTime = currentTime;\r\n      }\r\n\r\n        // Optimized ball physics management with correct collision detection\r\n        if (ballRef.current) {\r\n          const ballPos = ballRef.current.position;\r\n          const velocity = ballRef.current.velocity;\r\n          const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);\r\n\r\n          // Calculate distance from ball center to hexagon center\r\n          const distanceFromCenter = Math.sqrt(\r\n            Math.pow(ballPos.x - centerX, 2) + Math.pow(ballPos.y - centerY, 2)\r\n          );\r\n\r\n          // Corrected boundary checking - account for actual ball radius\r\n          const maxDistance = hexagonRadius - ballRadius - 8; // Proper collision boundary\r\n\r\n          if (distanceFromCenter > maxDistance) {\r\n            // Calculate normalized direction vector (optimized)\r\n            const directionX = ballPos.x - centerX;\r\n            const directionY = ballPos.y - centerY;\r\n            const magnitude = Math.sqrt(directionX * directionX + directionY * directionY);\r\n\r\n            if (magnitude > 0) { // Prevent division by zero\r\n              const normalizedX = directionX / magnitude;\r\n              const normalizedY = directionY / magnitude;\r\n\r\n              // Position ball at correct boundary\r\n              const newPosX = centerX + normalizedX * maxDistance;\r\n              const newPosY = centerY + normalizedY * maxDistance;\r\n\r\n              Body.setPosition(ballRef.current, { x: newPosX, y: newPosY });\r\n\r\n              // Improved bounce physics with proper reflection\r\n              const currentVelocity = ballRef.current.velocity;\r\n              const dotProduct = currentVelocity.x * normalizedX + currentVelocity.y * normalizedY;\r\n\r\n              if (dotProduct > 0) {\r\n                // Reflect velocity with realistic energy loss\r\n                const reflectedVelX = currentVelocity.x - 2 * dotProduct * normalizedX;\r\n                const reflectedVelY = currentVelocity.y - 2 * dotProduct * normalizedY;\r\n\r\n                Body.setVelocity(ballRef.current, {\r\n                  x: reflectedVelX * ballRestitution,\r\n                  y: reflectedVelY * ballRestitution\r\n                });\r\n              }\r\n            }\r\n          }\r\n\r\n          // Optimized energy maintenance\r\n          if (speed < 0.5) {\r\n            // Add subtle random impulse to keep ball moving\r\n            const impulseAngle = Math.random() * Math.PI * 2;\r\n            const impulseMagnitude = 0.2;\r\n\r\n            Body.applyForce(ballRef.current, ballRef.current.position, {\r\n              x: Math.cos(impulseAngle) * impulseMagnitude * 0.0008,\r\n              y: Math.sin(impulseAngle) * impulseMagnitude * 0.0008\r\n            });\r\n          }\r\n\r\n          // Limit maximum speed for smooth movement\r\n          if (speed > 5) {\r\n            const limitedVelX = (velocity.x / speed) * 5;\r\n            const limitedVelY = (velocity.y / speed) * 5;\r\n            Body.setVelocity(ballRef.current, { x: limitedVelX, y: limitedVelY });\r\n          }\r\n        }\r\n\r\n        // Continue animation loop\r\n        requestRef.current = requestAnimationFrame(animate);\r\n      };\r\n\r\n    // Start optimized animation loop\r\n    requestRef.current = requestAnimationFrame(animate);\r\n\r\n    // Cleanup when component unmounts\r\n    return () => {\r\n      // Cancel animation loop\r\n      if (requestRef.current) {\r\n        cancelAnimationFrame(requestRef.current);\r\n      }\r\n\r\n      // Cleanup renderer and engine\r\n      Render.stop(render);\r\n      World.clear(engineRef.current.world);\r\n      Engine.clear(engineRef.current);\r\n      render.canvas = null;\r\n      render.context = null;\r\n      render.textures = {};\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <Box className={styles.loadingContainer}\r\n      sx={{ ...(!fromCreatePlan && { minHeight: '90vh' }) }}>\r\n      <Box className={styles.gameWrapper}>\r\n        <Box className={styles.hexagonLoadingContainer}>\r\n          <canvas ref={canvasRef} className={styles.hexagonCanvas} />\r\n        </Box>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default HexagonBallLoading;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,GAAG,QAAQ,eAAe;AACnC,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,sBAAsB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AALA,SAAAC,MAAA,IAAAC,OAAA;AAMA,MAAMC,kBAAkB,GAAGC,IAAA,IAAwB;EAAAC,EAAA;EAAA,IAAvB;IAAEC;EAAe,CAAC,GAAAF,IAAA;EAC5C,MAAMG,SAAS,GAAGV,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMW,SAAS,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMY,UAAU,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMa,OAAO,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMc,eAAe,GAAGd,MAAM,CAAC,EAAE,CAAC;EAElCD,SAAS,CAAC,MAAM;IACd;IACA,MAAMgB,aAAa,GAAG,IAAI,CAAC,CAAC;IAC5B,MAAMC,OAAO,GAAG,MAAM,CAAC,CAAC;IACxB,MAAMC,eAAe,GAAG,KAAK,CAAC,CAAC;IAC/B,MAAMC,eAAe,GAAG,KAAK,CAAC,CAAC;;IAE/B;IACA,MAAMC,MAAM,GAAGjB,MAAM,CAACiB,MAAM;IAC5B,MAAMC,MAAM,GAAGlB,MAAM,CAACkB,MAAM;IAC5B,MAAMC,KAAK,GAAGnB,MAAM,CAACmB,KAAK;IAC1B,MAAMC,MAAM,GAAGpB,MAAM,CAACoB,MAAM;IAC5B,MAAMC,IAAI,GAAGrB,MAAM,CAACqB,IAAI;;IAExB;IACAZ,SAAS,CAACa,OAAO,GAAGL,MAAM,CAACM,MAAM,CAAC;MAChCT,OAAO,EAAE;QAAEU,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAEX,OAAO;QAAEY,KAAK,EAAE;MAAE;IACxC,CAAC,CAAC;;IAEF;IACAjB,SAAS,CAACa,OAAO,CAACK,MAAM,CAACC,SAAS,GAAG,CAAC;IACtCnB,SAAS,CAACa,OAAO,CAACO,kBAAkB,GAAG,CAAC,CAAC,CAAC;IAC1CpB,SAAS,CAACa,OAAO,CAACQ,kBAAkB,GAAG,CAAC,CAAC,CAAC;IAC1CrB,SAAS,CAACa,OAAO,CAACS,oBAAoB,GAAG,CAAC,CAAC,CAAC;;IAE5C;IACA,MAAMC,MAAM,GAAGd,MAAM,CAACK,MAAM,CAAC;MAC3BU,MAAM,EAAEzB,SAAS,CAACc,OAAO;MACzBY,MAAM,EAAEzB,SAAS,CAACa,OAAO;MACzBa,OAAO,EAAE;QACPC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,SAAS;QACrBC,kBAAkB,EAAE,KAAK;QACzBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;;IAEF;IACA,MAAMC,aAAa,GAAG,GAAG;IACzB,MAAMC,YAAY,GAAG,CAAC;IACtB,MAAMC,OAAO,GAAGb,MAAM,CAACG,OAAO,CAACC,KAAK,GAAG,CAAC;IACxC,MAAMU,OAAO,GAAGd,MAAM,CAACG,OAAO,CAACE,MAAM,GAAG,CAAC;;IAEzC;IACA,MAAMU,eAAe,GAAG,EAAE;IAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAE;MACrC,MAAMC,KAAK,GAAIC,IAAI,CAACC,EAAE,GAAG,CAAC,GAAGH,CAAC,GAAIJ,YAAY;MAC9C,MAAMpB,CAAC,GAAGmB,aAAa,GAAGO,IAAI,CAACE,GAAG,CAACH,KAAK,CAAC;MACzC,MAAMxB,CAAC,GAAGkB,aAAa,GAAGO,IAAI,CAACG,GAAG,CAACJ,KAAK,CAAC;MACzCF,eAAe,CAACO,IAAI,CAAC;QAAE9B,CAAC;QAAEC;MAAE,CAAC,CAAC;IAChC;;IAEA;IACAb,eAAe,CAACU,OAAO,GAAG,EAAE;IAC5B,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAE;MACrC,MAAMO,CAAC,GAAG,CAACP,CAAC,GAAG,CAAC,IAAIJ,YAAY;MAChC,MAAMY,WAAW,GAAG;QAClBC,WAAW,EAAEzC,eAAe;QAAE;QAC9B0C,QAAQ,EAAE,IAAI;QAAE;QAChBC,cAAc,EAAE,GAAG;QACnBC,QAAQ,EAAE,IAAI;QACd5B,MAAM,EAAE;UACN6B,SAAS,EAAE,aAAa;UACxBC,WAAW,EAAE,SAAS;UACtBC,SAAS,EAAE,CAAC,CAAE;QAChB;MACF,CAAC;;MAED;MACA,MAAMC,OAAO,GAAGjB,eAAe,CAACC,CAAC,CAAC;MAClC,MAAMiB,OAAO,GAAGlB,eAAe,CAACQ,CAAC,CAAC;MAClC,MAAMW,UAAU,GAAGhB,IAAI,CAACiB,IAAI,CAC1BjB,IAAI,CAACkB,GAAG,CAACH,OAAO,CAACzC,CAAC,GAAGwC,OAAO,CAACxC,CAAC,EAAE,CAAC,CAAC,GAAG0B,IAAI,CAACkB,GAAG,CAACH,OAAO,CAACxC,CAAC,GAAGuC,OAAO,CAACvC,CAAC,EAAE,CAAC,CACxE,CAAC;MACD,MAAM4C,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACL,OAAO,CAACxC,CAAC,GAAGuC,OAAO,CAACvC,CAAC,EAAEwC,OAAO,CAACzC,CAAC,GAAGwC,OAAO,CAACxC,CAAC,CAAC;MAC1E,MAAM+C,WAAW,GAAG1B,OAAO,GAAG,CAACmB,OAAO,CAACxC,CAAC,GAAGyC,OAAO,CAACzC,CAAC,IAAI,CAAC;MACzD,MAAMgD,WAAW,GAAG1B,OAAO,GAAG,CAACkB,OAAO,CAACvC,CAAC,GAAGwC,OAAO,CAACxC,CAAC,IAAI,CAAC;;MAEzD;MACA,MAAMgD,IAAI,GAAGrD,MAAM,CAACsD,SAAS,CAC3BH,WAAW,EACXC,WAAW,EACXN,UAAU,EACV,CAAC,EACDV,WACF,CAAC;;MAED;MACAnC,IAAI,CAACsD,MAAM,CAACF,IAAI,EAAEJ,SAAS,CAAC;MAE5BzD,eAAe,CAACU,OAAO,CAACgC,IAAI,CAACmB,IAAI,CAAC;IACpC;;IAEA;IACA,MAAMG,UAAU,GAAG,EAAE,CAAC,CAAC;;IAEvB;IACA,MAAMC,UAAU,GAAG3B,IAAI,CAAC4B,MAAM,CAAC,CAAC,GAAG5B,IAAI,CAACC,EAAE,GAAG,CAAC;IAC9C,MAAM4B,aAAa,GAAGpC,aAAa,GAAG,IAAI,CAAC,CAAC;IAC5C,MAAMqC,MAAM,GAAGnC,OAAO,GAAGK,IAAI,CAACE,GAAG,CAACyB,UAAU,CAAC,GAAGE,aAAa;IAC7D,MAAME,MAAM,GAAGnC,OAAO,GAAGI,IAAI,CAACG,GAAG,CAACwB,UAAU,CAAC,GAAGE,aAAa;IAE7DpE,OAAO,CAACW,OAAO,GAAGF,MAAM,CAAC8D,MAAM,CAACF,MAAM,EAAEC,MAAM,EAAEL,UAAU,EAAE;MAC1DnB,WAAW,EAAE1C,eAAe;MAAE;MAC9B2C,QAAQ,EAAE,IAAI;MAAE;MAChByB,WAAW,EAAE,KAAK;MAAE;MACpBC,OAAO,EAAE,KAAK;MAAE;MAChBC,OAAO,EAAEC,QAAQ;MAAE;MACnBtD,MAAM,EAAE;QACN6B,SAAS,EAAE,SAAS;QACpBC,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE;MACb;IACF,CAAC,CAAC;;IAEF;IACA,MAAMwB,YAAY,GAAG,GAAG;IACxB,MAAMC,YAAY,GAAGtC,IAAI,CAAC4B,MAAM,CAAC,CAAC,GAAG5B,IAAI,CAACC,EAAE,GAAG,CAAC;IAChD9B,IAAI,CAACoE,WAAW,CAAC9E,OAAO,CAACW,OAAO,EAAE;MAChCE,CAAC,EAAE0B,IAAI,CAACE,GAAG,CAACoC,YAAY,CAAC,GAAGD,YAAY;MACxC9D,CAAC,EAAEyB,IAAI,CAACG,GAAG,CAACmC,YAAY,CAAC,GAAGD;IAC9B,CAAC,CAAC;;IAEF;IACApE,KAAK,CAACuE,GAAG,CAACjF,SAAS,CAACa,OAAO,CAACqE,KAAK,EAAE,CAAC,GAAG/E,eAAe,CAACU,OAAO,EAAEX,OAAO,CAACW,OAAO,CAAC,CAAC;;IAEjF;IACAJ,MAAM,CAAC0E,GAAG,CAAC5D,MAAM,CAAC;;IAElB;IACA,IAAI6D,QAAQ,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;IAChC,MAAMC,SAAS,GAAG,EAAE;IACpB,MAAMC,SAAS,GAAG,IAAI,GAAGD,SAAS;IAElC,MAAME,OAAO,GAAIC,WAAW,IAAK;MAC/B,MAAMC,SAAS,GAAGD,WAAW,GAAGN,QAAQ;;MAExC;MACA,IAAIO,SAAS,IAAIH,SAAS,EAAE;QAC1B;QACAhF,MAAM,CAACoF,MAAM,CAAC5F,SAAS,CAACa,OAAO,EAAE,MAAM,CAAC;;QAExC;QACA,IAAIV,eAAe,CAACU,OAAO,CAACgF,MAAM,GAAG,CAAC,EAAE;UACtC,MAAMC,WAAW,GAAGrD,IAAI,CAACE,GAAG,CAACvC,aAAa,CAAC;UAC3C,MAAM2F,WAAW,GAAGtD,IAAI,CAACG,GAAG,CAACxC,aAAa,CAAC;UAE3CD,eAAe,CAACU,OAAO,CAACmF,OAAO,CAAChC,IAAI,IAAI;YACtC;YACA,MAAMiC,QAAQ,GAAGjC,IAAI,CAACkC,QAAQ,CAACnF,CAAC,GAAGqB,OAAO;YAC1C,MAAM+D,QAAQ,GAAGnC,IAAI,CAACkC,QAAQ,CAAClF,CAAC,GAAGqB,OAAO;YAE1C,MAAM+D,IAAI,GAAGH,QAAQ,GAAGH,WAAW,GAAGK,QAAQ,GAAGJ,WAAW;YAC5D,MAAMM,IAAI,GAAGJ,QAAQ,GAAGF,WAAW,GAAGI,QAAQ,GAAGL,WAAW;YAE5DlF,IAAI,CAAC0F,WAAW,CAACtC,IAAI,EAAE;cACrBjD,CAAC,EAAEqB,OAAO,GAAGgE,IAAI;cACjBpF,CAAC,EAAEqB,OAAO,GAAGgE;YACf,CAAC,CAAC;YACFzF,IAAI,CAACsD,MAAM,CAACF,IAAI,EAAE5D,aAAa,CAAC;UAClC,CAAC,CAAC;QACJ;QAEAgF,QAAQ,GAAGM,WAAW;MACxB;;MAEE;MACA,IAAIxF,OAAO,CAACW,OAAO,EAAE;QACnB,MAAM0F,OAAO,GAAGrG,OAAO,CAACW,OAAO,CAACqF,QAAQ;QACxC,MAAMM,QAAQ,GAAGtG,OAAO,CAACW,OAAO,CAAC2F,QAAQ;QACzC,MAAMC,KAAK,GAAGhE,IAAI,CAACiB,IAAI,CAAC8C,QAAQ,CAACzF,CAAC,GAAGyF,QAAQ,CAACzF,CAAC,GAAGyF,QAAQ,CAACxF,CAAC,GAAGwF,QAAQ,CAACxF,CAAC,CAAC;;QAE1E;QACA,MAAM0F,kBAAkB,GAAGjE,IAAI,CAACiB,IAAI,CAClCjB,IAAI,CAACkB,GAAG,CAAC4C,OAAO,CAACxF,CAAC,GAAGqB,OAAO,EAAE,CAAC,CAAC,GAAGK,IAAI,CAACkB,GAAG,CAAC4C,OAAO,CAACvF,CAAC,GAAGqB,OAAO,EAAE,CAAC,CACpE,CAAC;;QAED;QACA,MAAMsE,WAAW,GAAGzE,aAAa,GAAGiC,UAAU,GAAG,CAAC,CAAC,CAAC;;QAEpD,IAAIuC,kBAAkB,GAAGC,WAAW,EAAE;UACpC;UACA,MAAMC,UAAU,GAAGL,OAAO,CAACxF,CAAC,GAAGqB,OAAO;UACtC,MAAMyE,UAAU,GAAGN,OAAO,CAACvF,CAAC,GAAGqB,OAAO;UACtC,MAAMyE,SAAS,GAAGrE,IAAI,CAACiB,IAAI,CAACkD,UAAU,GAAGA,UAAU,GAAGC,UAAU,GAAGA,UAAU,CAAC;UAE9E,IAAIC,SAAS,GAAG,CAAC,EAAE;YAAE;YACnB,MAAMC,WAAW,GAAGH,UAAU,GAAGE,SAAS;YAC1C,MAAME,WAAW,GAAGH,UAAU,GAAGC,SAAS;;YAE1C;YACA,MAAMG,OAAO,GAAG7E,OAAO,GAAG2E,WAAW,GAAGJ,WAAW;YACnD,MAAMO,OAAO,GAAG7E,OAAO,GAAG2E,WAAW,GAAGL,WAAW;YAEnD/F,IAAI,CAAC0F,WAAW,CAACpG,OAAO,CAACW,OAAO,EAAE;cAAEE,CAAC,EAAEkG,OAAO;cAAEjG,CAAC,EAAEkG;YAAQ,CAAC,CAAC;;YAE7D;YACA,MAAMC,eAAe,GAAGjH,OAAO,CAACW,OAAO,CAAC2F,QAAQ;YAChD,MAAMY,UAAU,GAAGD,eAAe,CAACpG,CAAC,GAAGgG,WAAW,GAAGI,eAAe,CAACnG,CAAC,GAAGgG,WAAW;YAEpF,IAAII,UAAU,GAAG,CAAC,EAAE;cAClB;cACA,MAAMC,aAAa,GAAGF,eAAe,CAACpG,CAAC,GAAG,CAAC,GAAGqG,UAAU,GAAGL,WAAW;cACtE,MAAMO,aAAa,GAAGH,eAAe,CAACnG,CAAC,GAAG,CAAC,GAAGoG,UAAU,GAAGJ,WAAW;cAEtEpG,IAAI,CAACoE,WAAW,CAAC9E,OAAO,CAACW,OAAO,EAAE;gBAChCE,CAAC,EAAEsG,aAAa,GAAG/G,eAAe;gBAClCU,CAAC,EAAEsG,aAAa,GAAGhH;cACrB,CAAC,CAAC;YACJ;UACF;QACF;;QAEA;QACA,IAAImG,KAAK,GAAG,GAAG,EAAE;UACf;UACA,MAAMc,YAAY,GAAG9E,IAAI,CAAC4B,MAAM,CAAC,CAAC,GAAG5B,IAAI,CAACC,EAAE,GAAG,CAAC;UAChD,MAAM8E,gBAAgB,GAAG,GAAG;UAE5B5G,IAAI,CAAC6G,UAAU,CAACvH,OAAO,CAACW,OAAO,EAAEX,OAAO,CAACW,OAAO,CAACqF,QAAQ,EAAE;YACzDnF,CAAC,EAAE0B,IAAI,CAACE,GAAG,CAAC4E,YAAY,CAAC,GAAGC,gBAAgB,GAAG,MAAM;YACrDxG,CAAC,EAAEyB,IAAI,CAACG,GAAG,CAAC2E,YAAY,CAAC,GAAGC,gBAAgB,GAAG;UACjD,CAAC,CAAC;QACJ;;QAEA;QACA,IAAIf,KAAK,GAAG,CAAC,EAAE;UACb,MAAMiB,WAAW,GAAIlB,QAAQ,CAACzF,CAAC,GAAG0F,KAAK,GAAI,CAAC;UAC5C,MAAMkB,WAAW,GAAInB,QAAQ,CAACxF,CAAC,GAAGyF,KAAK,GAAI,CAAC;UAC5C7F,IAAI,CAACoE,WAAW,CAAC9E,OAAO,CAACW,OAAO,EAAE;YAAEE,CAAC,EAAE2G,WAAW;YAAE1G,CAAC,EAAE2G;UAAY,CAAC,CAAC;QACvE;MACF;;MAEA;MACA1H,UAAU,CAACY,OAAO,GAAG+G,qBAAqB,CAACnC,OAAO,CAAC;IACrD,CAAC;;IAEH;IACAxF,UAAU,CAACY,OAAO,GAAG+G,qBAAqB,CAACnC,OAAO,CAAC;;IAEnD;IACA,OAAO,MAAM;MACX;MACA,IAAIxF,UAAU,CAACY,OAAO,EAAE;QACtBgH,oBAAoB,CAAC5H,UAAU,CAACY,OAAO,CAAC;MAC1C;;MAEA;MACAJ,MAAM,CAACqH,IAAI,CAACvG,MAAM,CAAC;MACnBb,KAAK,CAACqH,KAAK,CAAC/H,SAAS,CAACa,OAAO,CAACqE,KAAK,CAAC;MACpC1E,MAAM,CAACuH,KAAK,CAAC/H,SAAS,CAACa,OAAO,CAAC;MAC/BU,MAAM,CAACC,MAAM,GAAG,IAAI;MACpBD,MAAM,CAACyG,OAAO,GAAG,IAAI;MACrBzG,MAAM,CAAC0G,QAAQ,GAAG,CAAC,CAAC;IACtB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEvI,OAAA,CAACJ,GAAG;IAAC4I,SAAS,EAAE1I,MAAM,CAAC2I,gBAAiB;IACtCC,EAAE,EAAE;MAAE,IAAI,CAACtI,cAAc,IAAI;QAAEuI,SAAS,EAAE;MAAO,CAAC;IAAE,CAAE;IAAAC,QAAA,eACtD5I,OAAA,CAACJ,GAAG;MAAC4I,SAAS,EAAE1I,MAAM,CAAC+I,WAAY;MAAAD,QAAA,eACjC5I,OAAA,CAACJ,GAAG;QAAC4I,SAAS,EAAE1I,MAAM,CAACgJ,uBAAwB;QAAAF,QAAA,eAC7C5I,OAAA;UAAQ+I,GAAG,EAAE1I,SAAU;UAACmI,SAAS,EAAE1I,MAAM,CAACkJ;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjJ,EAAA,CArRIF,kBAAkB;AAAAoJ,EAAA,GAAlBpJ,kBAAkB;AAuRxB,eAAeA,kBAAkB;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
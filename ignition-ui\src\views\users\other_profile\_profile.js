import React from 'react';
import { Box, Grid, Typography, Divider, InputAdornment } from '@mui/material';
import Iconify from 'components/Iconify/index';

//--------------------------------------------------------------------------------------------------

const SectionTitle = ({ children, ...props }) => (
  <Typography 
    sx={{
      fontFamily: 'Recursive Variable',
      fontSize: '1.5rem',
      fontWeight: 'bold',
      color: '#0F52BA',
      marginBottom: 2,
      ...props.sx
    }}
    {...props}
  >
    {children}
  </Typography>
);

const InputWithLabel = ({ label, InputComponent, ...props }) => {
  return (
    <Box sx={{ mb: 3 }}>
      <Typography
        sx={{
          fontFamily: 'Recursive Variable',
          fontSize: '1rem',
          fontWeight: 'bold',
          mb: 0.5,
          color: '#333'
        }}>
        {label}
      </Typography>
      <InputComponent 
        {...props} 
        disabled 
        fullWidth 
        className="darkDisabledText"
        sx={{ 
          '& .MuiInputBase-input': { 
            fontWeight: '700 !important',
            color: '#000 !important',
            WebkitTextFillColor: '#000 !important',
            opacity: '1 !important',
          },
          '&.Mui-disabled': {
            background: 'rgba(0, 0, 0, 0.02) !important',
            '& .MuiInputBase-input': {
              color: '#000 !important',
              '-webkit-text-fill-color': '#000 !important',
              opacity: '1 !important',
              fontWeight: '700 !important',
              cursor: 'default !important',
            },
            '& .MuiInputAdornment-root': {
              color: '#555 !important',
            },
          },
          ...props.sx
        }}
      />
    </Box>
  );
};

export const ProfileComponent = ({ profile, loading, StyledInputBase }) => {
  return (
    <Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <SectionTitle>Basic Information</SectionTitle>
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="First Name"
            value={profile?.firstName || ''}
            placeholder="First Name"
            InputComponent={StyledInputBase}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:account-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Last Name"
            value={profile?.lastName || ''}
            placeholder="Last Name"
            InputComponent={StyledInputBase}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:account-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <SectionTitle>Professional Information</SectionTitle>
        </Grid>

        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Occupation"
            value={profile?.occupation || ''}
            placeholder="Occupation"
            InputComponent={StyledInputBase}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:briefcase-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Company/Organization"
            value={profile?.company || ''}
            placeholder="Company or Organization"
            InputComponent={StyledInputBase}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:domain" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Address"
            value={profile?.address || ''}
            placeholder="Address"
            InputComponent={StyledInputBase}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:map-marker-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Position/Title"
            value={profile?.position || ''}
            placeholder="Position or Title"
            InputComponent={StyledInputBase}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:badge-account-horizontal-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <SectionTitle>About Me</SectionTitle>
        </Grid>

        <Grid item xs={12}>
          <InputWithLabel
            label="About Me"
            value={profile?.desc || ''}
            multiline={true}
            minRows={4}
            InputComponent={StyledInputBase}
            placeholder="Tell us about yourself"
          />
        </Grid>
      </Grid>
    </Box>
  );
};

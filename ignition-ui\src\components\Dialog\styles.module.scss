.mainDialogContent {
  margin-top: -100px;

  .dialogTitle {
    display: flex;
    justify-content: center;
    font-family: 'Recursive Variable';
    color: #F0A500;
    font-weight: bold;
    font-size: 1.5rem;
  }

  .dialogDescription {
    font-size: 1.25rem;
    font-family: 'Recursive Variable';
    color: #333;
  }

  .dialogIcon {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #333;
    padding: 25px 0;
  }

  .actionDialog {
    display: flex;
    justify-content: flex-end;
    padding: 0 24px 24px;
  }

  .dialogButtonCancel {
    font-family: 'Recursive Variable';
    font-size: 1.125rem;
    width: 100px;
    height: 40px;
    background-color: lightgray;
    color: #333;
    border-radius: 10px;

    &:hover {
      background-color: lightgray;
      color: #333;
    }
  }

  .dialogButtonConfirm {
    font-family: 'Recursive Variable';
    font-size: 1.125rem;
    width: 110px;
    height: 40px;
    background-color: #2b2b2b;
    color: white;
    border-radius: 10px;
    margin-left: 5px;

    &:hover {
      background-color: #2b2b2b;
      color: white;
    }
  }
}

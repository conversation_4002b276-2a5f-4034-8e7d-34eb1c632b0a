/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Link } from "react-router-dom";
import {
  Box, Container, Grid, Typography, Button, Avatar,
  Chip, IconButton, Skeleton, Fade, Grow, Zoom, Card, CardContent
} from '@mui/material';
import { mainRedColor, btnPrimaryColor } from "helpers/constants";
import { fetchUserFriendDataService } from '../services';
import Iconify from 'components/Iconify/index';
import FilterPopper from './popper';
import styles from '../styles.module.scss';

// Components
const FriendCard = ({ user, index }) => (
  <Grid item xs={12} sm={6} md={6} lg={6} key={user.id}>
    <Zoom in={true} style={{ transitionDelay: `${index * 100}ms` }}>
      <Card
        elevation={3}
        sx={{
          borderRadius: '10px',
          overflow: 'hidden',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          transition: 'transform 0.3s, box-shadow 0.3s',
          '&:hover': {
            transform: 'translateY(-8px)',
            boxShadow: '0 8px 16px rgba(240, 165, 0, 0.3)'
          }
        }}
      >
        <Box
          sx={{
            height: '100px',
            background: '#f8f4eb',
            position: 'relative'
          }}
        />

        <CardContent
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            p: 3,
            pt: 0,
            mt: '-72px',
            fontFamily: '"Recursive Variable", sans-serif',
            flexGrow: 1
          }}
        >
          <Avatar
            alt={user.first_name}
            src={user.avatar}
            sx={{
              width: 173,
              height: 173,
              border: '5px solid white',
              boxShadow: '0 4px 10px rgba(240, 165, 0, 0.2)'
            }}
          />

          <Typography
            variant="h5"
            sx={{
              mt: 2,
              fontWeight: 'bold',
              textAlign: 'center',
              color: '#F0A500',
              fontFamily: '"Recursive Variable", sans-serif'
            }}
          >
            {user.first_name} {user.last_name}
          </Typography>

          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              mt: 1.5
            }}
          >
            <Iconify icon="clarity:email-solid" width={16} height={16} sx={{ color: mainRedColor, flexShrink: 0, mr: 1 }} />
            <Typography
              variant="body2"
              sx={{
                color: '#333',
                fontSize: '0.9rem',
                fontFamily: '"Recursive Variable", sans-serif',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                ml: 1
              }}
            >
              {user.email}
            </Typography>
          </Box>

          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              mt: 1,
              mb: 2
            }}
          >
            <Iconify icon="hugeicons:permanent-job" width={16} height={16} sx={{ color: mainRedColor, flexShrink: 0, mr: 1 }} />
            <Typography
              variant="body2"
              sx={{
                color: '#333',
                fontSize: '0.9rem',
                fontFamily: '"Recursive Variable", sans-serif',
                ml: 1
              }}
            >
              {user.occupation || 'No Occupation available'}
            </Typography>
          </Box>

          <Box
            sx={{
              width: '100%',
              mb: 'auto',
              flexGrow: 1
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                mb: 1
              }}
            >
              <Iconify icon="carbon:skill-level" width={16} height={16} sx={{ color: '#F0A500', flexShrink: 0, mr: 1 }} />
              <Typography
                variant="body2"
                fontWeight="bold"
                sx={{
                  color: '#333',
                  fontSize: '0.9rem',
                  fontFamily: '"Recursive Variable", sans-serif',
                  ml: 1
                }}
              >
                Skills
              </Typography>
            </Box>

            <Box
              sx={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 1,
                minHeight: '24px'
              }}
            >
              {user.skills && user.skills.length > 0 ? (
                user.skills.map((skill, idx) => (
                  <Chip
                    key={idx}
                    label={skill.name}
                    size="small"
                    sx={{
                      borderRadius: '4px',
                      backgroundColor: '#F0A500',
                      color: 'white',
                      fontFamily: '"Recursive Variable", sans-serif',
                      fontWeight: 'bold',
                      fontSize: '0.7rem',
                      height: '24px'
                    }}
                  />
                ))
              ) : (
                <Typography
                  variant="body2"
                  sx={{
                    color: '#666',
                    fontFamily: '"Recursive Variable", sans-serif',
                    fontSize: '0.9rem'
                  }}
                >
                  No skills available
                </Typography>
              )}
            </Box>
          </Box>

          <Button
            component={Link}
            variant="contained"
            to={`/d/other/profile/${user.email}`}
            sx={{
              width: '100%',
              borderRadius: '4px',
              py: 1,
              mt: 3,
              fontWeight: 'bold',
              fontFamily: '"Recursive Variable", sans-serif',
              backgroundColor: '#F0A500',
              textTransform: 'uppercase',
              transition: 'all 0.15s ease-in-out',
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: '-100%',
                width: '100%',
                height: '100%',
                background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                transition: 'all 0.5s ease',
              },
              '&:hover': {
                backgroundColor: '#F0A500',
                boxShadow: '0 4px 8px rgba(240, 165, 0, 0.3)',
                '&::before': {
                  left: '100%'
                }
              }
            }}
          >
            VIEW PROFILE
          </Button>
        </CardContent>
      </Card>
    </Zoom>
  </Grid>
);

const LoadingFriendCards = () => (
  [...Array(6)].map((_, index) => (
    <Grid item xs={12} sm={6} md={6} lg={6} key={index}>
      <Fade in={true} style={{ transitionDelay: `${index * 50}ms` }}>
        <Card
          elevation={3}
          sx={{
            borderRadius: '10px',
            overflow: 'hidden',
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          <Skeleton
            variant="rectangular"
            width="100%"
            height={100}
            animation="wave"
          />

          <CardContent sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            p: 3,
            pt: 0,
            mt: '-72px'
          }}>
            <Skeleton
              variant="circular"
              width={173}
              height={173}
              animation="wave"
              sx={{ mb: 2 }}
            />

            <Skeleton variant="text" width="70%" height={30} animation="wave" />
            <Skeleton variant="text" width="90%" height={20} animation="wave" />
            <Skeleton variant="text" width="60%" height={20} animation="wave" sx={{ mb: 2 }} />

            <Box sx={{ width: '100%', mb: 2, flexGrow: 1 }}>
              <Skeleton variant="text" width="30%" height={20} animation="wave" sx={{ mb: 1 }} />
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, minHeight: '24px' }}>
                <Skeleton variant="rectangular" width={60} height={24} animation="wave" sx={{ borderRadius: '4px' }} />
                <Skeleton variant="rectangular" width={80} height={24} animation="wave" sx={{ borderRadius: '4px' }} />
                <Skeleton variant="rectangular" width={70} height={24} animation="wave" sx={{ borderRadius: '4px' }} />
              </Box>
            </Box>

            <Skeleton
              variant="rectangular"
              width="100%"
              height={36}
              animation="wave"
              sx={{ borderRadius: '4px', mt: 'auto' }}
            />
          </CardContent>
        </Card>
      </Fade>
    </Grid>
  ))
);

const EmptyState = () => (
  <Grow in={true}>
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        padding: '60px 20px',
        width: '100%',
        minHeight: '50vh',
        fontFamily: '"Recursive Variable", sans-serif'
      }}
    >
      <Box
        sx={{
          width: 120,
          height: 120,
          borderRadius: '50%',
          backgroundColor: 'rgba(240, 165, 0, 0.1)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mb: 3
        }}
      >
        <Iconify icon="mdi:folder-open" width={60} height={60} color="#F0A500" />
      </Box>

      <Typography
        variant="h4"
        sx={{
          fontWeight: 'bold',
          mb: 2,
          color: '#333',
          fontFamily: '"Recursive Variable", sans-serif',
          fontSize: '1.4rem'
        }}
      >
        No users found
      </Typography>

      <Typography
        variant="body1"
        sx={{
          color: '#333',
          maxWidth: '500px',
          mb: 4,
          fontFamily: '"Recursive Variable", sans-serif',
          fontSize: '1.125rem'
        }}
      >
        It seems like there are no users matching your filters. Try adjusting your filters to find more relevant results.
      </Typography>

      <Button
        variant="contained"
        startIcon={<Iconify icon="mdi:filter-remove" />}
        onClick={() => window.location.reload()}
        sx={{
          borderRadius: '5px',
          py: 1,
          px: 3,
          backgroundColor: '#F0A500',
          color: 'white',
          fontFamily: '"Recursive Variable", sans-serif',
          fontWeight: 'bold',
          '&:hover': {
            backgroundColor: '#ca8a00',
            boxShadow: '0 4px 8px rgba(240, 165, 0, 0.3)'
          }
        }}
      >
        Clear all filters
      </Button>
    </Box>
  </Grow>
);

//--------------------------------------------------------------------------------------------------

const ConnectFriend = () => {
  const INIT_FILTER_DATA = { keyword: '', skills: '' };
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filterAnchor, setFilterAnchor] = useState(null);
  const [filters, setFilters] = useState(INIT_FILTER_DATA);
  const [tempFilters, setTempFilters] = useState(INIT_FILTER_DATA);
  const [loadingMore, setLoadingMore] = useState(false);
  const observerRef = useRef(null);
  const observer = useRef(null);

  const handleObserver = (entries) => {
    const target = entries[0];
    if (target.isIntersecting && page < totalPages && !loadingMore) {
      setPage((prevPage) => prevPage + 1);
    }
  };

  const fetchUserFriendData = useCallback(async (append = false) => {
    try {
      if (append) {
        setLoadingMore(true);
      }

      const response = await fetchUserFriendDataService({
        keyword: filters.keyword,
        skills: filters.skills
      }, page);

      if (append) {
        setData((prevData) => [...prevData, ...response.results]);
      } else {
        setData(response.results);
      }

      setTotalPages(Math.ceil(response.count / 12));
    } catch (error) {
      console.log('Error loading: ', error);
    } finally {
      setLoadingMore(false);
      setLoading(false);
    }
  }, [filters, page]);

  const handleTempFilterChange = (e) => {
    setTempFilters({ ...tempFilters, [e.target.name]: e.target.value });
  };

  const handleFilterClick = (event) => {
    setFilterAnchor(filterAnchor ? null : event.currentTarget);
  };

  const handleApplyFilters = () => {
    setLoading(true);
    setFilters(tempFilters);
    setPage(1);
    setFilterAnchor(null);
  };

  const handleClearAllFilters = () => {
    setLoading(true);
    setFilters(INIT_FILTER_DATA);
    setTempFilters(INIT_FILTER_DATA);
    setPage(1);
    fetchUserFriendData();
  };

  const handleRemoveFilter = (filterName) => {
    const updatedFilters = { ...filters, [filterName]: '' };
    setFilters(updatedFilters);
    setTempFilters(updatedFilters);
    setPage(1);
    fetchUserFriendData();
  };

  const handleClosePopper = () => {
    setFilterAnchor(null);
  };

  useEffect(() => {
    if (observer.current) observer.current.disconnect();
    observer.current = new IntersectionObserver(handleObserver, { threshold: 1.0 });

    if (observerRef.current) {
      observer.current.observe(observerRef.current);
    }
  }, [loadingMore, totalPages]);

  useEffect(() => {
    setPage(1);
    fetchUserFriendData();
  }, [filters]);

  useEffect(() => {
    if (page > 1) {
      fetchUserFriendData(true);
    }
  }, [page]);

  const open = Boolean(filterAnchor);
  const id = open ? 'filter-popper' : undefined;

  return (
    <Container maxWidth="lg" className={styles.mainContactContainer}>
      <Box
        sx={{
          pt: 3,
          pb: 6
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', md: 'center' },
            mb: 3,
            pb: 2,
            borderBottom: '1px solid rgba(0, 0, 0, 0.08)'
          }}
        >
          <Box>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 'bold',
                color: '#F0A500',
                fontFamily: '"Recursive Variable", sans-serif',
                mb: 1
              }}
            >
              Connect with Friends
            </Typography>

            <Typography
              variant="body1"
              sx={{
                color: '#555',
                fontFamily: '"Recursive Variable", sans-serif',
                maxWidth: '500px',
                mb: { xs: 2, md: 0 }
              }}
            >
              Discover and connect with other users on our platform
            </Typography>
          </Box>

          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2
            }}
          >
            <Button
              variant="outlined"
              startIcon={<Iconify icon="material-symbols:group-add-outline" />}
              sx={{
                borderRadius: '5px',
                borderColor: '#F0A500',
                color: '#F0A500',
                fontFamily: '"Recursive Variable", sans-serif',
                fontWeight: 'bold',
                px: 2,
                py: 1,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: '-100%',
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(90deg, transparent, rgba(240, 165, 0, 0.2), transparent)',
                  transition: 'all 0.5s ease',
                },
                '&:hover': {
                  borderColor: '#F0A500',
                  backgroundColor: 'rgba(240, 165, 0, 0.05)',
                  '&::before': {
                    left: '100%'
                  }
                }
              }}
            >
              INVITE
            </Button>

            <Button
              variant="contained"
              startIcon={<Iconify icon="mdi:account-group" />}
              sx={{
                borderRadius: '5px',
                backgroundColor: '#F0A500',
                color: 'white',
                fontFamily: '"Recursive Variable", sans-serif',
                fontWeight: 'bold',
                px: 2,
                py: 1,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: '-100%',
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                  transition: 'all 0.5s ease',
                },
                '&:hover': {
                  backgroundColor: '#F0A500',
                  boxShadow: '0 4px 8px rgba(240, 165, 0, 0.3)',
                  '&::before': {
                    left: '100%'
                  }
                }
              }}
            >
              GROUPS
            </Button>
          </Box>
        </Box>

        <Button
          startIcon={<Iconify icon="mage:filter" width={20} height={20} />}
          onClick={handleFilterClick}
          variant="contained"
          sx={{
            mb: 4,
            backgroundColor: '#F0A500',
            color: 'white',
            borderRadius: '5px',
            boxShadow: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 'bold',
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: '-100%',
              width: '100%',
              height: '100%',
              background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
              transition: 'all 0.5s ease',
            },
            '&:hover': {
              backgroundColor: '#F0A500',
              boxShadow: '0 4px 8px rgba(240, 165, 0, 0.3)',
              '&::before': {
                left: '100%'
              }
            }
          }}
        >
          FILTER USERS
        </Button>

        {(filters.keyword || filters.skills) && (
          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 1,
              mb: 4,
              alignItems: 'center'
            }}
          >
            {filters.keyword && (
              <Box className={styles.filterBoxParamUnit}>
                <Iconify icon="eva:search-outline" width={20} height={20} color={btnPrimaryColor} />
                <Chip
                  label={`Keyword: ${filters.keyword}`}
                  onDelete={() => handleRemoveFilter('keyword')}
                  className={styles.chipParamVal}
                />
              </Box>
            )}

            {filters.skills && (
              <Box className={styles.filterBoxParamUnit}>
                <Iconify icon="mdi:account" width={20} height={20} color={btnPrimaryColor} />
                <Chip
                  label={`Skills: ${filters.skills}`}
                  onDelete={() => handleRemoveFilter('skills')}
                  className={styles.chipParamVal}
                />
              </Box>
            )}

            <IconButton
              onClick={handleClearAllFilters}
              sx={{
                color: 'white',
                backgroundColor: '#F0A500',
                padding: '8px',
                borderRadius: '8px',
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: '-100%',
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                  transition: 'all 0.5s ease',
                },
                '&:hover': {
                  backgroundColor: '#F0A500',
                  '&::before': {
                    left: '100%'
                  }
                }
              }}
            >
              <Iconify icon="fluent-mdl2:clear-filter" width={20} height={20} color="white" />
            </IconButton>
          </Box>
        )}

        <FilterPopper
          id={id}
          open={open}
          anchorEl={filterAnchor}
          filters={tempFilters}
          onFilterChange={handleTempFilterChange}
          onApplyFilters={handleApplyFilters}
          onClose={handleClosePopper}
        />

        {loading ? (
          <Grid container spacing={3}>
            <LoadingFriendCards />
          </Grid>
        ) : (data || []).length > 0 ? (
          <Grid container spacing={3}>
            {data.map((user, index) => (
              <FriendCard user={user} index={index} key={user.id} />
            ))}
          </Grid>
        ) : (
          <EmptyState />
        )}

        {loadingMore && (
          <Box
            className={styles.loadMoreContract}
            sx={{ py: 3 }}
          >
            <Iconify icon="eos-icons:bubble-loading" width={40} height={40} color="#F0A500" />
          </Box>
        )}

        <Box ref={observerRef} sx={{ height: '10px' }}></Box>
      </Box>
    </Container>
  );
};

export default ConnectFriend;

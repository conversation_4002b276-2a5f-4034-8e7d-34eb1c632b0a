import random
from django.core.management.base import BaseCommand
from django.contrib.auth.hashers import make_password
from users.models import User
from faker import Faker


class Command(BaseCommand):
    help = 'Seed data for the User model'

    def add_arguments(self, parser):
        parser.add_argument('--users', type=int, default=10,
                            help='Number of users to create')
        parser.add_argument('--clear', action='store_true',
                            help='Clear all existing users')

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing all existing users...')
            User.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('All users cleared!'))

        number_of_users = options['users']
        self.stdout.write(f'Creating {number_of_users} sample users...')

        fake = Faker()
        occupations = ['Software Engineer', 'Teacher', 'Doctor', 'Designer',
                       'Manager', 'Architect', 'Student', 'Artist',
                       'Writer', 'Scientist', 'Engineer', 'Lawyer', 'Nurse', 'Chef']

        for i in range(number_of_users):
            first_name = fake.first_name()
            last_name = fake.last_name()
            email = fake.email()
            password = make_password('password@123')

            # Ensure phone number does not exceed 20 characters
            phone = fake.phone_number()
            if len(phone) > 20:
                phone = phone[:20]

            user = User(
                first_name=first_name,
                last_name=last_name,
                email=email,
                password=password,
                description=fake.text(max_nb_chars=200),
                address=fake.address(),
                phone_number=phone,
                occupation=random.choice(occupations)
            )

            try:
                user.save()
                self.stdout.write(f'Created user: {email}')
            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f'Error creating user {email}: {str(e)}'))

        self.stdout.write(self.style.SUCCESS(
            f'Successfully created {number_of_users} users!'))

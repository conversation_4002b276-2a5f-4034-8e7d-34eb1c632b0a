import React, { useState } from "react";
import {
  InputAdornment,
  IconButton,
  Button,
  Typography,
  Box,
  Container,
  Card,
  CardContent,
  FormControl,
  CircularProgress
} from "@mui/material";
import { iconPrimaryColor, APIURL, mainYellowColor } from "helpers/constants";
import { errorSnackbar } from 'components/Snackbar/index';
import Iconify from 'components/Iconify/index';
import InputBase from 'components/Input/InputBase';
import InputPasswordBase from 'components/Input/InputPasswordBase';
import GoogleSignInButton from 'components/Button/GoogleSignInButton';
import axios from 'axios';
import styles from './styles.module.scss';


const Register = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
  });
  const [isRegistered, setIsRegistered] = useState(false);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [showPass, setShowPass] = useState(false);

  const handleChange = (field, value) => {
    setFormData((prevState) => ({
      ...prevState,
      [field]: value,
    }));
  };

  const handleRegister = async () => {
    const { email, password, firstName, lastName } = formData;
    const registrationData = new FormData();
    registrationData.append('email', email);
    registrationData.append('password', password);
    registrationData.append('first_name', firstName);
    registrationData.append('last_name', lastName);

    setLoading(true);
    try {
      const response = await axios.post(`${APIURL}/api/user/create`, registrationData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      if (response.data.status === 201) {
        setIsRegistered(true);
      }
    } catch (error) {
      console.error("error:: ", error);
      errorSnackbar(error?.response?.data?.message);
      setErrors(error.response.data || {});
    } finally {
      setLoading(false);
    }
  };

  const handleShowPass = () => {
    setShowPass(!showPass);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleRegister();
    }
  };

  return (
    <Container maxWidth="md" className={styles.loginContainer}>
      <Box sx={{ width: '100%', maxWidth: { md: '66.66%' }, mx: 'auto' }}>
        {!isRegistered ? (
          <Card className={styles.loginCard}>
            <Box className={styles.cardHeader}>
              <Box className={styles.headerContent}>
                <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
                <Box className={styles.headerTextContainer}>
                  <Typography variant="h4" className={styles.headerTitle}>
                    Ignition
                  </Typography>
                  <Typography variant="body1" className={styles.headerSubtitle}>
                    Create a new account
                  </Typography>
                </Box>
              </Box>
            </Box>

            <CardContent sx={{ px: { xs: 3, lg: 5 }, pb: { xs: 3, lg: 5 } }}>
              <Box className={styles.googleButton}>
                <GoogleSignInButton action="Sign up" />
              </Box>

              <div className={styles.divider}>
                <span>Or sign up with credentials</span>
              </div>

              <Box component="form" onKeyPress={handleKeyPress} noValidate>
                <FormControl className={styles.formGroup} fullWidth sx={{ mb: 3 }}>
                  <div className={styles.inputField}>
                    <InputBase
                      type="text"
                      handleChange={handleChange}
                      errorText={errors.firstName}
                      placeholder="First Name"
                      value={formData.firstName}
                      keyword="firstName"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="material-symbols:person" width={24} color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                  {errors.firstName && <div className={styles.errorMessage}>{errors.firstName[0]}</div>}
                </FormControl>

                <FormControl className={styles.formGroup} fullWidth sx={{ mb: 3 }}>
                  <div className={styles.inputField}>
                    <InputBase
                      type="text"
                      handleChange={handleChange}
                      errorText={errors.lastName}
                      placeholder="Last Name"
                      value={formData.lastName}
                      keyword="lastName"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="material-symbols:person" width={24} color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                  {errors.lastName && <div className={styles.errorMessage}>{errors.lastName[0]}</div>}
                </FormControl>

                <FormControl className={styles.formGroup} fullWidth sx={{ mb: 3 }}>
                  <div className={styles.inputField}>
                    <InputBase
                      type="email"
                      handleChange={handleChange}
                      errorText={errors.email}
                      placeholder="Email"
                      value={formData.email}
                      keyword="email"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="ic:baseline-email" width={24} color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                  {errors.email && <div className={styles.errorMessage}>{errors.email[0]}</div>}
                </FormControl>

                <FormControl className={styles.formGroup} fullWidth>
                  <div className={styles.inputField}>
                    <InputPasswordBase
                      showPass={showPass}
                      handleChange={handleChange}
                      errorText={errors.password}
                      placeholder="Password"
                      value={formData.password}
                      name="password"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="material-symbols:lock" color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton onClick={handleShowPass} edge="end">
                              <Iconify icon={showPass ? 'mdi:eye-off' : 'mdi:eye'} color={iconPrimaryColor} />
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                  {errors.password && <div className={styles.errorMessage}>{errors.password[0]}</div>}
                </FormControl>

                <Box display="flex" justifyContent="center" mt={4}>
                  <Button
                    onClick={handleRegister}
                    variant="contained"
                    className={styles.submitBtn}
                    disabled={loading}
                  >
                    {loading ? <CircularProgress size={24} color="inherit" /> : 'Sign up'}
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>
        ) : (
          <Card className={styles.loginCard}>
            <Box className={styles.cardHeader}>
              <Box className={styles.headerContent}>
                <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
                <Box className={styles.headerTextContainer}>
                  <Typography variant="h4" className={styles.headerTitle}>
                    Ignition
                  </Typography>
                  <Typography variant="body1" className={styles.headerSubtitle}>
                    Registration Successful
                  </Typography>
                </Box>
              </Box>
            </Box>
            <CardContent sx={{
              pb: { xs: 3, lg: 5 },
              px: { xs: 5, lg: 7 },
              textAlign: 'center'
            }}>
              <Box sx={{ position: 'relative', zIndex: 1 }}>
                <Box sx={{ mb: 5, mt: 2 }}>
                  <Box
                    className={styles.successIconContainer}
                    sx={{
                      mb: 3,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100px',
                      height: '100px',
                      borderRadius: '50%',
                      backgroundColor: '#4caf50',
                      border: '2px solid #4caf50',
                      boxShadow: '0 0 15px rgba(76, 175, 80, 0.5)',
                      margin: '0 auto'
                    }}
                  >
                    <Iconify
                      icon="mdi:check"
                      width={60}
                      height={60}
                      className={styles.successIcon}
                      sx={{
                        color: '#ffffff',
                      }}
                    />
                  </Box>
                </Box>
                <Typography
                  variant="h5"
                  className={styles.successTitle}
                  sx={{
                    mb: 3,
                    fontWeight: 'bold',
                    color: mainYellowColor,
                    fontFamily: "'Recursive Variable', sans-serif"
                  }}
                >
                  Success message
                </Typography>
                <Typography
                  variant="body1"
                  className={styles.successMessage}
                  sx={{
                    fontSize: '1.25rem',
                    color: mainYellowColor,
                    maxWidth: '100%',
                    mx: 'auto',
                    lineHeight: 1.6,
                    fontFamily: "'Recursive Variable', sans-serif"
                  }}
                >
                  Please wait for activation before you proceed
                </Typography>
              </Box>
            </CardContent>
          </Card>
        )}

        <Box className={styles.linksContainer} sx={{ justifyContent: 'center' }}>
          <a href="/login" className={styles.customeALink}>
            <Iconify icon="mdi:login" width={20} height={20} className={styles.linkIcon} />
            Already have an account? Sign in
          </a>
        </Box>
      </Box>
    </Container>
  );
};

export default Register;

import React, { useEffect, useRef } from 'react';
import { Box } from '@mui/material';
import Matter from 'matter-js';
import styles from './styles.module.scss';

/**
 * Component displays loading screen with a bouncing ball inside a rotating hexagon
 * @param {Object} props - Component props
 * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page
 * @returns {JSX.Element} - HexagonBallLoading component
 */
const HexagonBallLoading = ({ fromCreatePlan }) => {
  const canvasRef = useRef(null);
  const engineRef = useRef(null);
  const requestRef = useRef(null);
  const ballRef = useRef(null);
  const hexagonEdgesRef = useRef([]);

  useEffect(() => {
    // Realistic physics parameters
    const rotationSpeed = 0.012; // Slower, more natural rotation
    const gravity = 0.0001; // More realistic gravity strength
    const ballRestitution = 1; // Realistic bounce with energy loss
    const wallRestitution = 0; // Walls absorb some energy

    // Initialize Matter.js modules
    const Engine = Matter.Engine;
    const Render = Matter.Render;
    const World = Matter.World;
    const Bodies = Matter.Bodies;
    const Body = Matter.Body;

    // Create engine with realistic physics
    engineRef.current = Engine.create({
      gravity: { x: 0, y: gravity, scale: 1 },
    });

    // Set realistic timing for physics simulation
    engineRef.current.timing.timeScale = 1;

    // Create renderer
    const render = Render.create({
      canvas: canvasRef.current,
      engine: engineRef.current,
      options: {
        width: 352,
        height: 352,
        wireframes: false,
        background: '#ffffff',
        showAngleIndicator: false,
        showCollisions: false,
        showVelocity: false,
      },
    });

    // Create hexagon
    const hexagonRadius = 132;
    const hexagonSides = 6;
    const centerX = render.options.width / 2;
    const centerY = render.options.height / 2;

    // Create hexagon vertices
    const hexagonVertices = [];
    for (let i = 0; i < hexagonSides; i++) {
      const angle = (Math.PI * 2 * i) / hexagonSides;
      const x = hexagonRadius * Math.cos(angle);
      const y = hexagonRadius * Math.sin(angle);
      hexagonVertices.push({ x, y });
    }

    // Create hexagon edges with realistic physics
    hexagonEdgesRef.current = [];
    for (let i = 0; i < hexagonSides; i++) {
      const j = (i + 1) % hexagonSides;
      const edgeOptions = {
        restitution: wallRestitution, // Use realistic wall bounce
        friction: 0.05, // Reduced friction for smoother movement
        frictionStatic: 0.1,
        isStatic: true,
        render: {
          fillStyle: 'transparent',
          strokeStyle: '#0f0f0f',
          lineWidth: 2, // Slightly thicker for better visibility
        },
      };

      // Calculate edge position and angle
      const vertex1 = hexagonVertices[i];
      const vertex2 = hexagonVertices[j];
      const edgeLength = Math.sqrt(
        Math.pow(vertex2.x - vertex1.x, 2) + Math.pow(vertex2.y - vertex1.y, 2)
      );
      const edgeAngle = Math.atan2(vertex2.y - vertex1.y, vertex2.x - vertex1.x);
      const edgeCenterX = centerX + (vertex1.x + vertex2.x) / 2;
      const edgeCenterY = centerY + (vertex1.y + vertex2.y) / 2;

      // Create edge
      const edge = Bodies.rectangle(
        edgeCenterX,
        edgeCenterY,
        edgeLength,
        1,
        edgeOptions
      );

      // Rotate edge to angle
      Body.rotate(edge, edgeAngle);

      hexagonEdgesRef.current.push(edge);
    }

    // Create ball with realistic physics
    const originalBallRadius = 15;
    const ballRadius = originalBallRadius * 0.65; // Slightly smaller for better movement

    // Start ball at a random position within the hexagon for variety
    const startAngle = Math.random() * Math.PI * 2;
    const startDistance = hexagonRadius * 0.3; // Start closer to center
    const startX = centerX + Math.cos(startAngle) * startDistance;
    const startY = centerY + Math.sin(startAngle) * startDistance;

    ballRef.current = Bodies.circle(startX, startY, ballRadius, {
      restitution: ballRestitution, // Realistic bounce with energy loss
      friction: 0.02, // Very low friction for smooth rolling
      frictionAir: 0.0005, // Minimal air resistance
      density: 0.002, // Slightly heavier for more realistic momentum
      inertia: Infinity, // Prevent rotation for cleaner movement
      render: {
        fillStyle: '#F0A500',
        strokeStyle: '#E69500',
        lineWidth: 1,
      },
    });

    // Add realistic initial velocity
    const initialSpeed = 0.8;
    const initialAngle = Math.random() * Math.PI * 2;
    Body.setVelocity(ballRef.current, {
      x: Math.cos(initialAngle) * initialSpeed,
      y: Math.sin(initialAngle) * initialSpeed
    });

    // Add bodies to world
    World.add(engineRef.current.world, [...hexagonEdgesRef.current, ballRef.current]);

    // Run renderer
    Render.run(render);

    // Animation loop function with realistic physics
    const animate = () => {
      // Update engine with consistent timing
      Engine.update(engineRef.current, 16.667);

      // Smoothly rotate hexagon edges
      if (hexagonEdgesRef.current.length > 0) {
        hexagonEdgesRef.current.forEach(edge => {
          // Calculate new position after rotation
          const currentX = edge.position.x - centerX;
          const currentY = edge.position.y - centerY;

          const newX = currentX * Math.cos(rotationSpeed) - currentY * Math.sin(rotationSpeed);
          const newY = currentX * Math.sin(rotationSpeed) + currentY * Math.cos(rotationSpeed);

          Body.setPosition(edge, {
            x: centerX + newX,
            y: centerY + newY
          });
          Body.rotate(edge, rotationSpeed);
        });
      }

      // Realistic ball physics management
      if (ballRef.current) {
        const ballPos = ballRef.current.position;
        const velocity = ballRef.current.velocity;
        const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);

        // Calculate distance from ball to hexagon center
        const distanceFromCenter = Math.sqrt(
          Math.pow(ballPos.x - centerX, 2) + Math.pow(ballPos.y - centerY, 2)
        );

        // Boundary checking with realistic physics
        const maxDistance = hexagonRadius * 0.88; // Allow ball to get close to edges

        if (distanceFromCenter > maxDistance) {
          // Calculate normalized direction vector
          const directionX = ballPos.x - centerX;
          const directionY = ballPos.y - centerY;
          const magnitude = Math.sqrt(directionX * directionX + directionY * directionY);

          const normalizedX = directionX / magnitude;
          const normalizedY = directionY / magnitude;

          // Position ball at boundary
          const newPosX = centerX + normalizedX * maxDistance;
          const newPosY = centerY + normalizedY * maxDistance;

          Body.setPosition(ballRef.current, { x: newPosX, y: newPosY });

          // Realistic bounce physics
          const currentVelocity = ballRef.current.velocity;
          const dotProduct = currentVelocity.x * normalizedX + currentVelocity.y * normalizedY;

          if (dotProduct > 0) {
            // Reflect velocity with energy loss
            const reflectedVelX = currentVelocity.x - 2 * dotProduct * normalizedX;
            const reflectedVelY = currentVelocity.y - 2 * dotProduct * normalizedY;

            Body.setVelocity(ballRef.current, {
              x: reflectedVelX * ballRestitution,
              y: reflectedVelY * ballRestitution
            });
          }
        }

        // Maintain minimum energy for continuous movement
        if (speed < 0.3) {
          // Add subtle random impulse to keep ball moving
          const impulseAngle = Math.random() * Math.PI * 2;
          const impulseMagnitude = 0.15;

          Body.applyForce(ballRef.current, ballRef.current.position, {
            x: Math.cos(impulseAngle) * impulseMagnitude * 0.001,
            y: Math.sin(impulseAngle) * impulseMagnitude * 0.001
          });
        }

        // Limit maximum speed for realistic movement
        if (speed > 4) {
          const limitedVelX = (velocity.x / speed) * 4;
          const limitedVelY = (velocity.y / speed) * 4;
          Body.setVelocity(ballRef.current, { x: limitedVelX, y: limitedVelY });
        }
      }

      // Continue animation loop
      requestRef.current = requestAnimationFrame(animate);
    };

    // Start animation loop
    requestRef.current = requestAnimationFrame(animate);

    // Cleanup when component unmounts
    return () => {
      // Cancel animation loop
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }

      // Cleanup renderer and engine
      Render.stop(render);
      World.clear(engineRef.current.world);
      Engine.clear(engineRef.current);
      render.canvas = null;
      render.context = null;
      render.textures = {};
    };
  }, []);

  return (
    <Box className={styles.loadingContainer}
      sx={{ ...(!fromCreatePlan && { minHeight: '90vh' }) }}>
      <Box className={styles.gameWrapper}>
        <Box className={styles.hexagonLoadingContainer}>
          <canvas ref={canvasRef} className={styles.hexagonCanvas} />
        </Box>
      </Box>
    </Box>
  );
};

export default HexagonBallLoading;

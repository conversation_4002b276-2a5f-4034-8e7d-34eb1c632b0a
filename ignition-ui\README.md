# Ignition UI - AI-Powered Student Planning Dashboard

[![React](https://img.shields.io/badge/React-18.2.0-61DAFB?style=for-the-badge&logo=react&logoColor=black)](https://reactjs.org/)
[![Material-UI](https://img.shields.io/badge/Material--UI-5.15.20-0081CB?style=for-the-badge&logo=material-ui&logoColor=white)](https://mui.com/)
[![Redux](https://img.shields.io/badge/Redux-5.0.1-764ABC?style=for-the-badge&logo=redux&logoColor=white)](https://redux.js.org/)
[![Bootstrap](https://img.shields.io/badge/Bootstrap-4.6.2-7952B3?style=for-the-badge&logo=bootstrap&logoColor=white)](https://getbootstrap.com/)

## 📋 Mô tả dự án

Ignition UI là frontend dashboard được xây dựng bằng React, cung cấp giao diện người dùng hiện đại và thân thiện cho hệ thống lập kế hoạch học tập AI-powered. Ứng dụng tích hợp nhiều thư viện UI mạnh mẽ như Material-UI, Bootstrap và các component tùy chỉnh để tạo ra trải nghiệm người dùng tối ưu.

### ✨ Tính năng chính

- 📊 **Interactive Dashboard**: Bảng điều khiển tương tác với biểu đồ và thống kê
- 📅 **Advanced Calendar**: Lịch thông minh với drag-drop và scheduling
- 🎯 **Task Management**: Quản lý công việc với Kanban board
- 👤 **User Profile**: Quản lý hồ sơ và cài đặt cá nhân
- 💬 **Real-time Chat**: Hệ thống chat và thông báo real-time
- 📱 **Responsive Design**: Tối ưu cho mọi thiết bị
- 🎨 **Modern UI/UX**: Giao diện hiện đại với Material Design
- 🔍 **Smart Search**: Tìm kiếm thông minh và lọc dữ liệu

## 🔧 Yêu cầu hệ thống

### Phần mềm cần thiết

#### macOS
- **Node.js**: 18.0 hoặc cao hơn (LTS khuyến nghị)
- **npm**: 9.0 hoặc cao hơn (hoặc yarn/pnpm)
- **Git**: Phiên bản mới nhất
- **VS Code**: Khuyến nghị cho development

#### Windows
- **Node.js**: 18.0 hoặc cao hơn (LTS khuyến nghị)
- **npm**: 9.0 hoặc cao hơn (hoặc yarn/pnpm)
- **Git**: Phiên bản mới nhất
- **VS Code**: Khuyến nghị cho development
- **Windows Terminal**: Khuyến nghị cho trải nghiệm terminal tốt hơn

### Phần cứng khuyến nghị
- **RAM**: Tối thiểu 8GB, khuyến nghị 16GB+
- **Storage**: Tối thiểu 5GB dung lượng trống
- **CPU**: Quad-core hoặc cao hơn
- **GPU**: Tích hợp hoặc rời đều được (cho hardware acceleration)

## 🚀 Hướng dẫn cài đặt

### 📱 Cài đặt trên macOS

#### Bước 1: Cài đặt Homebrew (nếu chưa có)
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

#### Bước 2: Cài đặt Node.js và npm
```bash
# Cài đặt Node.js LTS
brew install node

# Kiểm tra phiên bản
node --version
npm --version
```

#### Bước 3: Cài đặt Yarn (tùy chọn, khuyến nghị)
```bash
brew install yarn
```

#### Bước 4: Clone dự án
```bash
git clone https://github.com/your-repo/ignition.git
cd ignition/ignition-ui
```

#### Bước 5: Cài đặt dependencies
```bash
# Sử dụng npm
npm install

# Hoặc sử dụng yarn (khuyến nghị)
yarn install
```

### 🪟 Cài đặt trên Windows

#### Bước 1: Cài đặt Node.js
1. Tải Node.js LTS từ [nodejs.org](https://nodejs.org/en/download/)
2. Chạy installer và làm theo hướng dẫn
3. Khởi động lại Command Prompt/PowerShell
4. Kiểm tra cài đặt:
```cmd
node --version
npm --version
```

#### Bước 2: Cài đặt Yarn (tùy chọn, khuyến nghị)
```cmd
npm install -g yarn
```

#### Bước 3: Cài đặt Git
Tải và cài đặt Git từ [git-scm.com](https://git-scm.com/download/win)

#### Bước 4: Clone dự án
```cmd
git clone https://github.com/your-repo/ignition.git
cd ignition\ignition-ui
```

#### Bước 5: Cài đặt dependencies
```cmd
# Sử dụng npm
npm install

# Hoặc sử dụng yarn (khuyến nghị)
yarn install
```

## ⚙️ Cấu hình

### Environment Variables
Tạo file `.env` trong thư mục `ignition-ui`:

```env
# API Configuration
REACT_APP_API_BASE_URL=http://localhost:8000/api
REACT_APP_API_TIMEOUT=30000

# WebSocket Configuration
REACT_APP_WS_URL=ws://localhost:8000/ws

# Authentication
REACT_APP_JWT_SECRET=your-jwt-secret
REACT_APP_TOKEN_EXPIRE_TIME=3600

# Feature Flags
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_DARK_MODE=true

# External Services
REACT_APP_GOOGLE_ANALYTICS_ID=your-ga-id
REACT_APP_SENTRY_DSN=your-sentry-dsn

# Development
REACT_APP_DEBUG=true
GENERATE_SOURCEMAP=true
```

### Cấu hình Development
Tạo file `.env.development`:

```env
REACT_APP_API_BASE_URL=http://localhost:8000/api
REACT_APP_DEBUG=true
REACT_APP_LOG_LEVEL=debug
```

### Cấu hình Production
Tạo file `.env.production`:

```env
REACT_APP_API_BASE_URL=https://your-api-domain.com/api
REACT_APP_DEBUG=false
REACT_APP_LOG_LEVEL=error
GENERATE_SOURCEMAP=false
```

## 🏃‍♂️ Chạy ứng dụng

### Development Server

#### macOS/Linux
```bash
# Sử dụng npm
npm start

# Hoặc sử dụng yarn
yarn start
```

#### Windows
```cmd
# Sử dụng npm
npm start

# Hoặc sử dụng yarn
yarn start
```

Ứng dụng sẽ chạy tại: http://localhost:3000

### Build cho Production

#### macOS/Linux
```bash
# Sử dụng npm
npm run build

# Hoặc sử dụng yarn
yarn build
```

#### Windows
```cmd
# Sử dụng npm
npm run build

# Hoặc sử dụng yarn
yarn build
```

### Chạy Tests

```bash
# Chạy tests
npm test
# hoặc
yarn test

# Chạy tests với coverage
npm run test -- --coverage
# hoặc
yarn test --coverage
```

## 📦 Scripts có sẵn

```json
{
  "start": "Chạy development server",
  "build": "Build ứng dụng cho production",
  "test": "Chạy test suite",
  "eject": "Eject từ Create React App (không khuyến nghị)",
  "install:clean": "Xóa node_modules và cài đặt lại",
  "compile:scss": "Compile SCSS thành CSS",
  "minify:scss": "Minify CSS files",
  "build:scss": "Build tất cả SCSS files"
}
```

## 🎨 Cấu trúc thư mục

```
ignition-ui/
├── public/                 # Static files
│   ├── index.html         # HTML template
│   ├── favicon.ico        # App icon
│   └── manifest.json      # PWA manifest
├── src/                   # Source code
│   ├── components/        # Reusable components
│   ├── pages/            # Page components
│   ├── hooks/            # Custom React hooks
│   ├── services/         # API services
│   ├── store/            # Redux store
│   ├── utils/            # Utility functions
│   ├── assets/           # Images, fonts, styles
│   │   ├── css/          # Compiled CSS
│   │   ├── scss/         # SCSS source files
│   │   ├── img/          # Images
│   │   └── fonts/        # Font files
│   ├── App.js            # Main App component
│   └── index.js          # Entry point
├── build/                # Production build
├── node_modules/         # Dependencies
├── package.json          # Project configuration
├── .env                  # Environment variables
└── README.md            # This file
```

## 🔌 Tích hợp API

### Cấu hình Axios
```javascript
// src/services/api.js
import axios from 'axios';

const API = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL,
  timeout: process.env.REACT_APP_API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
API.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default API;
```

### Sử dụng API Services
```javascript
// src/services/planService.js
import API from './api';

export const planService = {
  getPlans: () => API.get('/plans/'),
  createPlan: (data) => API.post('/plans/', data),
  updatePlan: (id, data) => API.put(`/plans/${id}/`, data),
  deletePlan: (id) => API.delete(`/plans/${id}/`),
};
```

## 🎯 State Management với Redux

### Store Configuration
```javascript
// src/store/index.js
import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import planSlice from './slices/planSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    plans: planSlice,
  },
});
```

### Sử dụng Redux Hooks
```javascript
// src/components/Dashboard.js
import { useSelector, useDispatch } from 'react-redux';
import { fetchPlans } from '../store/slices/planSlice';

const Dashboard = () => {
  const dispatch = useDispatch();
  const { plans, loading } = useSelector(state => state.plans);

  useEffect(() => {
    dispatch(fetchPlans());
  }, [dispatch]);

  return (
    // Component JSX
  );
};
```

## 🎨 UI Components và Styling

### Material-UI Theme
```javascript
// src/theme/index.js
import { createTheme } from '@mui/material/styles';

export const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: 'Recursive Variable, Arial, sans-serif',
  },
});
```

### Custom Components
```javascript
// src/components/CustomCard.js
import { Card, CardContent, CardActions } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  boxShadow: theme.shadows[3],
  '&:hover': {
    boxShadow: theme.shadows[6],
  },
}));
```

## 📱 Responsive Design

### Breakpoints
```javascript
// src/utils/breakpoints.js
export const breakpoints = {
  xs: '0px',
  sm: '600px',
  md: '960px',
  lg: '1280px',
  xl: '1920px',
};
```

### Media Queries
```scss
// src/assets/scss/mixins/_responsive.scss
@mixin mobile {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1024px) {
    @content;
  }
}
```

## 🔧 Performance Optimization

### Code Splitting
```javascript
// src/pages/LazyPages.js
import { lazy } from 'react';

export const Dashboard = lazy(() => import('./Dashboard'));
export const Profile = lazy(() => import('./Profile'));
export const Calendar = lazy(() => import('./Calendar'));
```

### Memoization
```javascript
// src/components/ExpensiveComponent.js
import { memo, useMemo, useCallback } from 'react';

const ExpensiveComponent = memo(({ data, onUpdate }) => {
  const processedData = useMemo(() => {
    return data.map(item => ({
      ...item,
      processed: true,
    }));
  }, [data]);

  const handleUpdate = useCallback((id) => {
    onUpdate(id);
  }, [onUpdate]);

  return (
    // Component JSX
  );
});
```

## 🧪 Testing

### Unit Tests với Jest
```javascript
// src/components/__tests__/Dashboard.test.js
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '../../store';
import Dashboard from '../Dashboard';

test('renders dashboard component', () => {
  render(
    <Provider store={store}>
      <Dashboard />
    </Provider>
  );

  expect(screen.getByText('Dashboard')).toBeInTheDocument();
});
```

### Integration Tests
```javascript
// src/__tests__/integration/UserFlow.test.js
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from '../../App';

test('user can create a new plan', async () => {
  const user = userEvent.setup();
  render(<App />);

  // Test user flow
  await user.click(screen.getByText('Create Plan'));
  await user.type(screen.getByLabelText('Plan Name'), 'Test Plan');
  await user.click(screen.getByText('Save'));

  await waitFor(() => {
    expect(screen.getByText('Plan created successfully')).toBeInTheDocument();
  });
});
```

## 📦 Build và Deployment

### Build Optimization
```javascript
// webpack.config.js (nếu ejected)
const path = require('path');

module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@components': path.resolve(__dirname, 'src/components'),
      '@pages': path.resolve(__dirname, 'src/pages'),
      '@utils': path.resolve(__dirname, 'src/utils'),
    },
  },
};
```

### Docker Deployment
```dockerfile
# Dockerfile
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Nginx Configuration
```nginx
# nginx.conf
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔧 Troubleshooting

### Lỗi thường gặp

#### 1. Node.js version mismatch
```bash
# Kiểm tra phiên bản Node.js
node --version

# Cài đặt Node Version Manager (nvm)
# macOS/Linux
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Windows (sử dụng nvm-windows)
# Tải từ: https://github.com/coreybutler/nvm-windows

# Sử dụng Node.js 18
nvm install 18
nvm use 18
```

#### 2. npm install errors
```bash
# Xóa cache npm
npm cache clean --force

# Xóa node_modules và package-lock.json
rm -rf node_modules package-lock.json

# Cài đặt lại
npm install

# Hoặc sử dụng yarn
yarn install
```

#### 3. Build errors
```bash
# Tăng memory limit cho Node.js
export NODE_OPTIONS="--max-old-space-size=4096"

# Windows
set NODE_OPTIONS=--max-old-space-size=4096

# Build lại
npm run build
```

#### 4. CORS errors
```javascript
// src/setupProxy.js (cho development)
const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:8000',
      changeOrigin: true,
    })
  );
};
```

## 🚀 Production Checklist

### Pre-deployment
- [ ] Chạy tất cả tests: `npm test`
- [ ] Build thành công: `npm run build`
- [ ] Kiểm tra bundle size: `npm run analyze`
- [ ] Cập nhật environment variables
- [ ] Kiểm tra API endpoints
- [ ] Test responsive design
- [ ] Kiểm tra accessibility (a11y)

### Performance
- [ ] Optimize images và assets
- [ ] Enable gzip compression
- [ ] Configure CDN
- [ ] Set up caching headers
- [ ] Monitor Core Web Vitals

## 🤝 Contributing

### Development Workflow
1. Fork repository
2. Tạo feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Tạo Pull Request

### Code Standards
- Sử dụng ESLint và Prettier
- Viết tests cho components mới
- Tuân thủ naming conventions
- Thêm documentation cho complex logic

### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

Ví dụ:
```
feat(dashboard): add new chart component

Add interactive pie chart for displaying plan statistics
Includes hover effects and responsive design

Closes #123
```

## 📄 License

Dự án này được phân phối dưới MIT License. Xem file `LICENSE` để biết thêm chi tiết.

## 📞 Hỗ trợ

### Báo cáo lỗi
Nếu gặp lỗi, vui lòng tạo issue với thông tin:
- Mô tả lỗi chi tiết
- Các bước tái tạo lỗi
- Screenshots (nếu có)
- Thông tin môi trường (OS, browser, Node.js version)

### Liên hệ
- **Email**: <EMAIL>
- **GitHub Issues**: [https://github.com/your-repo/ignition/issues](https://github.com/your-repo/ignition/issues)
- **Documentation**: [https://docs.ignition.com](https://docs.ignition.com)

## 🎯 Roadmap

### Version 2.0
- [ ] PWA support
- [ ] Offline functionality
- [ ] Advanced analytics dashboard
- [ ] Mobile app (React Native)
- [ ] Real-time collaboration features

### Version 2.1
- [ ] AI-powered recommendations
- [ ] Advanced calendar integrations
- [ ] Custom themes và branding
- [ ] API rate limiting dashboard
- [ ] Advanced user permissions

# Generated by Django 4.2.13 on 2024-05-18 03:19

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('plans', '0008_subtask_end_date_subtask_progress_subtask_slug_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='task',
            name='end_date',
            field=models.DateField(null=True),
        ),
        migrations.AddField(
            model_name='task',
            name='progress',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='task',
            name='slug',
            field=models.SlugField(default=uuid.uuid4, max_length=255, unique=True),
        ),
        migrations.AddField(
            model_name='task',
            name='start_date',
            field=models.DateField(null=True),
        ),
        migrations.AddField(
            model_name='task',
            name='status',
            field=models.IntegerField(choices=[(1, 'Todo'), (2, 'In Progress'), (3, 'Done')], default=1),
        ),
    ]

# Django settings
SECRET_KEY='***********************************'

# Database settings
DB_HOST='localhost'
DB_USER='****'
DB_PORT='3306'
DB_USER='****'
DB_PASSWORD='*********'
DB_NAME="*******"

# Security settings
ALLOWED_HOSTS=locahost,127.0.0.1,127.0.0.1:3000,locahost:3000
CORS_ORIGIN_WHITELIST=http://localhost:3000,http://127.0.0.1:3000

# Mail settings
SENDGRID_API_KEY="SENDGRID_API_KEY"
SENDGRID_FROM_NAME="Ignition App"
FROM_EMAIL="FROM_EMAIL"

# SMS Service Configuration
# Options: "aws_sns", "vonage", "disabled"
SMS_PROVIDER="aws_sns"

# AWS SNS SMS settings (when SMS_PROVIDER=aws_sns)
AWS_ACCESS_KEY_ID="your_aws_access_key_id"
AWS_SECRET_ACCESS_KEY="your_aws_secret_access_key"
AWS_DEFAULT_REGION="us-east-1"

# Vonage SMS settings (when SMS_PROVIDER=vonage)
# Get €2 free trial at https://www.vonage.com/communications-apis/
VONAGE_API_KEY="your_vonage_api_key"
VONAGE_API_SECRET="your_vonage_api_secret"
VONAGE_FROM_NUMBER="Ignition"

# URLs
URL_FRONTEND="http://localhost:3000"
URL_BACKEND="http://127.0.0.1:8000"

# Google OAuth2 settings
GOOGLE_OAUTH2_CLIENT_ID=
GOOGLE_OAUTH2_CLIENT_SECRET=
FRONTEND_BASE_URL="http://localhost:3000"

# AI Provider Configuration
# Primary AI provider to use: "openai" or "openrouter"
AI_PROVIDER=openai

# OpenAI settings
OPENAI_API_KEY=
OPENAI_DEFAULT_MODEL=gpt-4o-mini
ASSISTANT_ID=

# OpenRouter settings (alternative to OpenAI)
# Get API key at https://openrouter.ai/
OPENROUTER_API_KEY=
OPENROUTER_DEFAULT_MODEL=anthropic/claude-3.5-sonnet
OPENROUTER_APP_NAME=Ignition
OPENROUTER_SITE_URL=https://ignition.app

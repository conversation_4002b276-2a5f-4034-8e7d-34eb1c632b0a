$cell_size_height: 16px;
$cell_size_width: 16px;
$box_height: 354px;
$box_width: 354px;
$main_yellow_color: #F0A500;
$main_font: 'Recursive Variable';

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .gameWrapper {
    width: 100%;
    max-width: calc($box_width + 30px);
    background-color: #333;
    color: white;
    border-radius: 8px;
    padding: 16px;
  }

  // Styles cho HexagonBallLoading
  .hexagonLoadingContainer {
    width: $box_width;
    height: $box_height;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 4px;
  }

  .hexagonCanvas {
    width: 100%;
    height: 100%;
    background-color: #000000;
  }
}

import os
import re
import time
import json
from openai import OpenAI
from starlette import status
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from assistant.serializers import PlanPromptSerializer
from rest_framework import status
from assistant.assistant_functions import OpenAIConfig
from assistant.utils import convert_json_text, generate_openai_prompt, save_plan_to_db
from assistant.prompts import get_assistant_system_prompt
from assistant.ai_providers import create_chat_completion, get_available_providers, get_provider_info
# from assistant.tasks import call_assistant_api
from urllib.error import HTTPError, URLError
from dotenv import load_dotenv
from django.db.models import Q
from django.shortcuts import get_object_or_404
from plans.models import Plan, Milestone, Task, Subtask, Risk
from plans.serializers import PlanViewSerializer, MilestoneSerializer, TaskUpdateSerializer, SubtaskSerializer
import threading
load_dotenv()


class CreateAssitantThreadView(APIView):
    def post(self, request):
        openai_config = OpenAIConfig()
        try:
            thread = openai_config.create_thread()
            return Response({
                "data": thread,
                "status": status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                "error": str(e),
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class AddMessageView(APIView):
    @swagger_auto_schema(
        operation_description="Add a message to an OpenAI thread",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['thread_id', 'message'],
            properties={
                'thread_id': openapi.Schema(type=openapi.TYPE_STRING, description='Thread ID'),
                'message': openapi.Schema(type=openapi.TYPE_STRING, description='Message content')
            },
        ),
        responses={200: openapi.Response('Response', openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'data': openapi.Schema(type=openapi.TYPE_STRING, description='Response data'),
                'status': openapi.Schema(type=openapi.TYPE_INTEGER, description='Response status')
            }
        ))}
    )
    def post(self, request):
        openai_config = OpenAIConfig()
        try:
            thread_id = request.data.get("thread_id")
            message = request.data.get("message")
            response = openai_config.add_message(thread_id, message)
            return Response({
                "data": response,
                "status": status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                "error": str(e),
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class CreateRunAssistantGetResultView(APIView):
    @swagger_auto_schema(
        operation_description="Add a message to an OpenAI thread",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['thread_id', 'content'],
            properties={
                'thread_id': openapi.Schema(type=openapi.TYPE_STRING, description='Thread ID'),
            },
        ),
        responses={200: openapi.Response('Response', openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'data': openapi.Schema(type=openapi.TYPE_STRING, description='Response data'),
                'status': openapi.Schema(type=openapi.TYPE_INTEGER, description='Response status')
            }
        ))}
    )
    def post(self, request):
        thread_id = request.data.get('thread_id')
        assistant_id = os.getenv("ASSISTANT_ID")

        if not thread_id or not assistant_id:
            return Response({
                "error": "Missing thread_id or assistant_id in the request."
            }, status=status.HTTP_400_BAD_REQUEST)

        openai_config = OpenAIConfig()
        try:
            response = openai_config.create_run_assistant(thread_id, assistant_id)
            run_id = response["id"]
            start_time = time.time()

            while True:
                if time.time() - start_time > 300:
                    return Response({
                        "error": "Timeout after 3 minutes",
                        "status": status.HTTP_408_REQUEST_TIMEOUT
                    }, status=status.HTTP_408_REQUEST_TIMEOUT)

                run_status = openai_config.retrieve_run_assistant(
                    thread_id, run_id)

                if run_status["status"] == 'completed':
                    result_message = openai_config.list_messages_assistant(
                        thread_id)
                    return Response({
                        "data": result_message,
                        "status": status.HTTP_200_OK
                    }, status=status.HTTP_200_OK)
                time.sleep(1)
        except Exception as e:
            return Response({
                "error": str(e),
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class CreateProjectPlannerByAssistantView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=PlanPromptSerializer, responses={200: 'Plan created successfully'})
    def post(self, request):
        serializer = PlanPromptSerializer(data=request.data)
        if serializer.is_valid():
            prompt = serializer.validated_data['prompt']
            language = serializer.validated_data.get('language', 'English')
            role = serializer.validated_data.get('role', 'Project Manager')
            user = request.user
            try:
                client = OpenAI(api_key=os.environ['OPENAI_API_KEY'])
                thread = client.beta.threads.create()
                content = generate_openai_prompt(
                    prompt=prompt,
                    role=role,
                    language=language
                )

                message = client.beta.threads.messages.create(
                    thread_id=thread.id,
                    role='user',
                    content=content
                )

                run = client.beta.threads.runs.create(
                    thread_id=thread.id,
                    assistant_id=os.environ['ASSISTANT_ID'],
                )

                while True:
                    run_status = client.beta.threads.runs.retrieve(
                        thread_id=thread.id,
                        run_id=run.id
                    )

                    if run_status.status == 'completed':
                        result_message = client.beta.threads.messages.list(
                            thread_id=thread.id
                        )
                        for message in result_message.data:
                            created_plan_data = message.content[0].text.value
                            converted_data = convert_json_text(created_plan_data)
                            plan_data_dict = json.loads(converted_data, strict=False)
                            saved_plan_data = save_plan_to_db(plan_data_dict, request.user.id)
                            return Response(saved_plan_data, status=status.HTTP_200_OK)

            except HTTPError as e:
                print(f"HTTP Error occurred using Assistant API: {e}")
                return Response({
                    "message": "HTTP Error occurred using Assistant API",
                    "status": str(e)
                }, status=status.HTTP_404_NOT_FOUND)
            except URLError as e:
                print(f"URL Error occurred using Assistant API: {e}")
                return Response({
                    "message": "URL Error occurred using Assistant API",
                    "status": str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                print(f"An Error occurred: {e}")
                return Response({
                    "message": f"An error occurred: {e}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({
            'error_messages': serializer.errors,
            'error_code': 400
        }, status=status.HTTP_400_BAD_REQUEST)


class CreateProjectPlannerByChatView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=PlanPromptSerializer, responses={202: 'Plan creation in progress'})
    def post(self, request):
        serializer = PlanPromptSerializer(data=request.data)
        if serializer.is_valid():
            prompt = serializer.validated_data['prompt']
            language = serializer.validated_data.get('language', 'English')
            role = serializer.validated_data.get('role', 'Project Manager')
            duration = serializer.validated_data.get('duration', '3 tháng')
            user = request.user

            # Tạo bản ghi plan tạm thời
            plan = Plan.objects.create(
                name="Plan being generated...",
                description="Waiting for AI response...",
                user=user,
                status="pending"
            )

            # Bắt đầu thread để xử lý yêu cầu AI
            thread = threading.Thread(
                target=self.process_ai_request,
                args=(prompt, language, role, duration, user.id, plan.id)
            )
            thread.daemon = True  # Thread sẽ tự động kết thúc khi chương trình chính kết thúc
            thread.start()

            return Response({
                "message": "Plan creation in progress",
                "plan_id": plan.id,
                "plan_slug": plan.slug,
                "status": "pending"
            }, status=status.HTTP_202_ACCEPTED)

        return Response({
            'error_messages': serializer.errors,
            'error_code': 400
        }, status=status.HTTP_400_BAD_REQUEST)

    def process_ai_request(self, prompt, language, role, duration, user_id, plan_id):
        try:
            # Lấy plan từ database
            plan = Plan.objects.get(id=plan_id)
            plan.status = "processing"
            plan.save()

            # Tạo prompt
            ai_prompt = generate_openai_prompt(
                prompt=prompt,
                role=role,
                language=language,
                duration=duration
            )

            # Gọi AI API (có thể là OpenAI hoặc OpenRouter)
            messages = [
                {"role": "system", "content": get_assistant_system_prompt()},
                {"role": "user", "content": ai_prompt}
            ]

            # Sử dụng AI provider manager
            ai_response = create_chat_completion(messages)

            # Xử lý kết quả
            if ai_response and ai_response.get('content'):
                plan_data = ai_response['content']
                json_data_match = re.search(r"\{.*\}", plan_data, re.DOTALL)
                
                if json_data_match:
                    plan_data_cleaned = json_data_match.group(0)
                    
                    try:
                        plan_data_dict = json.loads(plan_data_cleaned, strict=False)
                        
                        # Cập nhật plan hiện có thay vì tạo mới
                        plan.name = plan_data_dict['name']
                        plan.description = plan_data_dict['description']
                        plan.status = "completed"
                        plan.save()
                        
                        # Lưu milestones, tasks, subtasks
                        for milestone_data in plan_data_dict['milestones']:
                            milestone = Milestone.objects.create(
                                name=milestone_data['name'],
                                description=milestone_data.get('description', ''),
                                plan=plan,
                                estimated_duration=milestone_data.get('estimated_duration', ''),
                                success_criteria=milestone_data.get('success_criteria', '')
                            )
                            
                            # Lưu risks nếu có
                            if 'risks' in milestone_data and milestone_data['risks']:
                                for risk_data in milestone_data['risks']:
                                    Risk.objects.create(
                                        risk=risk_data.get('risk', ''),
                                        mitigation=risk_data.get('mitigation', ''),
                                        milestone=milestone
                                    )
                            
                            # Lưu tasks
                            for task_data in milestone_data['tasks']:
                                task = Task.objects.create(
                                    name=task_data['name'],
                                    description=task_data.get('description', ''),
                                    milestone=milestone,
                                    estimated_duration=task_data.get('estimated_duration', '')
                                )
                                
                                # Lưu subtasks
                                if 'subtasks' in task_data and task_data['subtasks']:
                                    for subtask_data in task_data['subtasks']:
                                        Subtask.objects.create(
                                            name=subtask_data['name'],
                                            description=subtask_data.get('description', ''),
                                            task=task
                                        )
                    except Exception as e:
                        plan.status = "failed"
                        plan.description = f"Error processing plan data: {str(e)}"
                        plan.save()
                else:
                    plan.status = "failed"
                    plan.description = "No valid JSON found in the response"
                    plan.save()
            else:
                plan.status = "failed"
                plan.description = "Invalid response structure from AI API"
                plan.save()
                
        except Exception as e:
            # Cập nhật plan với thông báo lỗi
            try:
                plan = Plan.objects.get(id=plan_id)
                plan.status = "failed"
                plan.description = f"An error occurred: {str(e)}"
                plan.save()
            except:
                print(f"Could not update plan status for plan_id {plan_id}: {str(e)}")


class PlanStatusView(APIView):
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(responses={200: 'Plan status retrieved successfully'})
    def get(self, request, plan_id):
        try:
            plan = Plan.objects.get(id=plan_id, user=request.user)
            
            if plan.status == 'completed':
                # Lấy thông tin đầy đủ của plan
                serializer = PlanViewSerializer(plan)
                return Response(serializer.data)
            else:
                # Chỉ trả về thông tin cơ bản và trạng thái
                return Response({
                    'id': plan.id,
                    'slug': plan.slug,
                    'name': plan.name,
                    'description': plan.description,
                    'status': plan.status,
                    'created_at': plan.created_at
                })
                
        except Plan.DoesNotExist:
            return Response({
                'error': 'Plan not found'
            }, status=status.HTTP_404_NOT_FOUND)


class AIProvidersInfoView(APIView):
    """API endpoint to get information about available AI providers"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(responses={200: 'AI providers information retrieved successfully'})
    def get(self, request):
        """Get information about available AI providers and their configuration status"""
        try:
            provider_info = get_provider_info()
            available_providers = get_available_providers()

            return Response({
                'available_providers': available_providers,
                'provider_details': provider_info,
                'current_default': os.getenv('AI_PROVIDER', 'openai').lower()
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': f'Error retrieving provider information: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AIAgentChatView(APIView):
    """API endpoint for AI agent chat with plan context"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'message': openapi.Schema(type=openapi.TYPE_STRING, description='User message'),
                'plan_slug': openapi.Schema(type=openapi.TYPE_STRING, description='Plan slug'),
            },
            required=['message', 'plan_slug']
        ),
        responses={
            200: openapi.Response(description="AI response generated successfully"),
            400: openapi.Response(description="Invalid input"),
            404: openapi.Response(description="Plan not found")
        }
    )
    def post(self, request):
        try:
            message = request.data.get('message', '').strip()
            plan_slug = request.data.get('plan_slug')

            if not message or not plan_slug:
                return Response({
                    'error': 'Message and plan_slug are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the plan with full context
            plan = get_object_or_404(Plan, slug=plan_slug)

            # Check if user has permission to access this plan
            if plan.user != request.user:
                from plans.models import PlanAccess
                if not PlanAccess.objects.filter(user=request.user, plan=plan).exists():
                    return Response({
                        'error': 'You do not have permission to access this plan'
                    }, status=status.HTTP_403_FORBIDDEN)

            # Get complete plan context
            plan_context = self._get_plan_context(plan)

            # Generate AI response with plan context
            ai_response = self._generate_ai_response(message, plan_context)

            return Response({
                'message': ai_response.get('content', ''),
                'actions': ai_response.get('actions', []),
                'metadata': {
                    'model': ai_response.get('model'),
                    'provider': ai_response.get('provider'),
                    'plan_context_included': True
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_plan_context(self, plan):
        """Get complete plan context for AI"""
        try:
            milestones = []
            for milestone in plan.milestone_set.all().order_by('id'):
                milestone_data = {
                    'id': milestone.id,
                    'name': milestone.name or '',
                    'description': milestone.description or '',
                    'estimated_duration': milestone.estimated_duration or '',
                    'success_criteria': milestone.success_criteria or '',
                    'tasks': []
                }

                for task in milestone.task_set.all().order_by('id'):
                    task_data = {
                        'id': task.id,
                        'slug': task.slug,
                        'name': task.name or '',
                        'description': task.description or '',
                        'status': task.status,
                        'priority': task.priority,
                        'start_date': task.start_date.isoformat() if task.start_date else None,
                        'end_date': task.end_date.isoformat() if task.end_date else None,
                        'progress': task.progress or 0,
                        'estimated_duration': getattr(task, 'estimated_duration', '') or '',
                        'order': getattr(task, 'order', None),
                        'subtasks': []
                    }

                    for subtask in task.subtask_set.all().order_by('id'):
                        subtask_data = {
                            'id': subtask.id,
                            'slug': subtask.slug,
                            'name': subtask.name or '',
                            'description': subtask.description or '',
                            'status': subtask.status,
                            'progress': subtask.progress or 0,
                            'start_date': subtask.start_date.isoformat() if subtask.start_date else None,
                            'end_date': subtask.end_date.isoformat() if subtask.end_date else None,
                            'order': getattr(subtask, 'order', None)
                        }
                        task_data['subtasks'].append(subtask_data)

                    milestone_data['tasks'].append(task_data)

                milestones.append(milestone_data)
        except Exception as e:
            # Log the error but don't fail completely
            print(f"Error building plan context: {e}")
            milestones = []

        return {
            'plan': {
                'id': plan.id,
                'slug': plan.slug,
                'name': plan.name,
                'description': plan.description,
                'priority': plan.priority,
                'status': plan.status,
                'created_at': plan.created_at.isoformat()
            },
            'milestones': milestones,
            'statistics': {
                'total_milestones': len(milestones),
                'total_tasks': sum(len(m['tasks']) for m in milestones),
                'total_subtasks': sum(len(t['subtasks']) for m in milestones for t in m['tasks']),
                'completed_tasks': sum(1 for m in milestones for t in m['tasks'] if t['status'] == 3),
                'in_progress_tasks': sum(1 for m in milestones for t in m['tasks'] if t['status'] == 2)
            }
        }

    def _generate_ai_response(self, user_message, plan_context):
        """Generate AI response using external API with plan context"""
        try:
            # Create system prompt with plan context
            system_prompt = self._create_system_prompt(plan_context)

            # Create user prompt with context
            user_prompt = f"""
User message: {user_message}

Please analyze this request in the context of the project and provide:
1. A helpful response to the user
2. Any specific actions that should be taken (if applicable)

If actions are needed, format them as JSON in your response like this:
```json
{{
    "actions": [
        {{
            "type": "add_milestone",
            "data": {{
                "name": "Milestone Name",
                "description": "Description"
            }}
        }}
    ]
}}
```

Available action types:
- add_milestone: Add a new milestone
- add_task: Add a task to a milestone (requires milestone_id)
- add_subtask: Add a subtask to a task (requires task_slug)
- update_task_status: Update task status (requires task_slug and status)
- complete_task: Mark task as completed (requires task_slug)
"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            # Use OpenRouter with deepseek model as per user preference
            ai_response = create_chat_completion(
                messages=messages,
                provider='openrouter',
                model='deepseek/deepseek-r1-0528:free'
            )

            # Parse actions from response
            actions = self._parse_actions_from_response(ai_response.get('content', ''))

            return {
                'content': ai_response.get('content', ''),
                'actions': actions,
                'model': ai_response.get('model'),
                'provider': ai_response.get('provider')
            }

        except Exception as e:
            # Fallback to basic response if AI fails
            return {
                'content': f"I understand you want to: {user_message}\n\nI'm having trouble connecting to my AI service right now, but I can still help you with basic project management tasks. Please try rephrasing your request or contact support if the issue persists.",
                'actions': [],
                'model': 'fallback',
                'provider': 'local',
                'error': str(e)
            }

    def _create_system_prompt(self, plan_context):
        """Create system prompt with plan context"""
        plan = plan_context['plan']
        stats = plan_context['statistics']

        milestones_summary = []
        for milestone in plan_context['milestones']:
            task_count = len(milestone['tasks'])
            completed_tasks = sum(1 for task in milestone['tasks'] if task['status'] == 3)
            milestones_summary.append(f"- {milestone['name']}: {completed_tasks}/{task_count} tasks completed")

        return f"""You are an AI Project Assistant helping manage the project "{plan['name']}".

PROJECT OVERVIEW:
- Project: {plan['name']}
- Description: {plan['description']}
- Status: {plan['status']}
- Total Milestones: {stats['total_milestones']}
- Total Tasks: {stats['total_tasks']}
- Total Subtasks: {stats['total_subtasks']}
- Completed Tasks: {stats['completed_tasks']}
- In Progress Tasks: {stats['in_progress_tasks']}

CURRENT MILESTONES:
{chr(10).join(milestones_summary)}

FULL PROJECT STRUCTURE:
{json.dumps(plan_context, indent=2)}

You are an expert project manager who can:
1. Analyze project status and provide insights
2. Suggest improvements and next steps
3. Help create, modify, and organize project elements
4. Answer questions about project progress
5. Provide actionable recommendations

When users request changes, provide both a conversational response AND specific actions in JSON format.
Be helpful, professional, and focus on practical project management advice.
Always consider the current project context when responding."""

    def _parse_actions_from_response(self, response_content):
        """Parse actions from AI response"""
        actions = []
        try:
            # Look for JSON blocks in the response
            import re
            json_pattern = r'```json\s*(\{.*?\})\s*```'
            matches = re.findall(json_pattern, response_content, re.DOTALL)

            for match in matches:
                try:
                    parsed_json = json.loads(match)
                    if 'actions' in parsed_json and isinstance(parsed_json['actions'], list):
                        actions.extend(parsed_json['actions'])
                except json.JSONDecodeError:
                    continue

            # Also try to find standalone JSON objects
            if not actions:
                json_pattern = r'\{[^{}]*"actions"[^{}]*\[[^\]]*\][^{}]*\}'
                matches = re.findall(json_pattern, response_content, re.DOTALL)
                for match in matches:
                    try:
                        parsed_json = json.loads(match)
                        if 'actions' in parsed_json and isinstance(parsed_json['actions'], list):
                            actions.extend(parsed_json['actions'])
                    except json.JSONDecodeError:
                        continue

        except Exception as e:
            print(f"Error parsing actions from AI response: {e}")

        return actions


class AIAgentActionView(APIView):
    """API endpoint for AI agent to perform actions on plans"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'action': openapi.Schema(type=openapi.TYPE_STRING, description='Action type: add_milestone, add_task, add_subtask, etc.'),
                'plan_slug': openapi.Schema(type=openapi.TYPE_STRING, description='Plan slug'),
                'data': openapi.Schema(type=openapi.TYPE_OBJECT, description='Action data'),
                'message': openapi.Schema(type=openapi.TYPE_STRING, description='User message for context')
            },
            required=['action', 'plan_slug', 'data']
        ),
        responses={
            200: openapi.Response(description="Action completed successfully"),
            400: openapi.Response(description="Invalid input"),
            404: openapi.Response(description="Plan not found")
        }
    )
    def post(self, request):
        try:
            action = request.data.get('action')
            plan_slug = request.data.get('plan_slug')
            data = request.data.get('data', {})
            message = request.data.get('message', '')

            # Get the plan
            plan = get_object_or_404(Plan, slug=plan_slug)

            # Check if user has permission to modify this plan
            # For now, we'll check if user is the owner or has access
            if plan.user != request.user:
                # Check if user has access through PlanAccess
                from plans.models import PlanAccess
                if not PlanAccess.objects.filter(user=request.user, plan=plan).exists():
                    return Response({
                        'error': 'You do not have permission to modify this plan'
                    }, status=status.HTTP_403_FORBIDDEN)

            result = None

            if action == 'add_milestone':
                result = self._add_milestone(plan, data)
            elif action == 'add_task':
                result = self._add_task(plan, data)
            elif action == 'add_subtask':
                result = self._add_subtask(plan, data)
            elif action == 'update_task_status':
                result = self._update_task_status(plan, data)
            elif action == 'complete_task':
                result = self._complete_task(plan, data)
            elif action == 'update_milestone':
                result = self._update_milestone(plan, data)
            elif action == 'update_task':
                result = self._update_task(plan, data)
            else:
                return Response({
                    'error': f'Unknown action: {action}'
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'message': 'Action completed successfully',
                'action': action,
                'result': result
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _add_milestone(self, plan, data):
        """Add a new milestone to the plan"""
        milestone_data = {
            'name': data.get('name', 'New Milestone'),
            'description': data.get('description', ''),
            'plan': plan.id,
            'estimated_duration': data.get('estimated_duration', ''),
            'success_criteria': data.get('success_criteria', '')
        }

        serializer = MilestoneSerializer(data=milestone_data)
        if serializer.is_valid():
            milestone = serializer.save()
            return {
                'milestone_id': milestone.id,
                'milestone_name': milestone.name,
                'milestone_data': serializer.data
            }
        else:
            raise ValueError(f'Invalid milestone data: {serializer.errors}')

    def _add_task(self, plan, data):
        """Add a new task to a milestone"""
        milestone_id = data.get('milestone_id')
        if not milestone_id:
            raise ValueError('milestone_id is required for adding tasks')

        milestone = get_object_or_404(Milestone, id=milestone_id, plan=plan)

        task_data = {
            'name': data.get('name', 'New Task'),
            'description': data.get('description', ''),
            'milestone': milestone.id
        }

        serializer = TaskUpdateSerializer(data=task_data)
        if serializer.is_valid():
            task = serializer.save()

            # Set order if not provided
            if 'order' not in task_data:
                task.order = Task.objects.filter(milestone=milestone).count()
                task.save()

            return {
                'task_id': task.id,
                'task_slug': task.slug,
                'task_name': task.name,
                'task_data': serializer.data
            }
        else:
            raise ValueError(f'Invalid task data: {serializer.errors}')

    def _add_subtask(self, plan, data):
        """Add a new subtask to a task"""
        task_slug = data.get('task_slug')
        if not task_slug:
            raise ValueError('task_slug is required for adding subtasks')

        task = get_object_or_404(Task, slug=task_slug, milestone__plan=plan)

        subtask_data = {
            'name': data.get('name', 'New Subtask'),
            'description': data.get('description', ''),
            'task': task.id
        }

        serializer = SubtaskSerializer(data=subtask_data)
        if serializer.is_valid():
            subtask = serializer.save()
            return {
                'subtask_id': subtask.id,
                'subtask_slug': subtask.slug,
                'subtask_name': subtask.name,
                'subtask_data': serializer.data
            }
        else:
            raise ValueError(f'Invalid subtask data: {serializer.errors}')

    def _update_task_status(self, plan, data):
        """Update task status"""
        task_slug = data.get('task_slug')
        new_status = data.get('status')

        if not task_slug or new_status is None:
            raise ValueError('task_slug and status are required for updating task status')

        task = get_object_or_404(Task, slug=task_slug, milestone__plan=plan)
        task.status = new_status
        task.save()

        return {
            'task_id': task.id,
            'task_slug': task.slug,
            'task_name': task.name,
            'new_status': new_status
        }

    def _complete_task(self, plan, data):
        """Mark task as completed"""
        task_slug = data.get('task_slug')

        if not task_slug:
            raise ValueError('task_slug is required for completing tasks')

        task = get_object_or_404(Task, slug=task_slug, milestone__plan=plan)
        task.status = 3  # Completed status
        task.progress = 100
        task.save()

        return {
            'task_id': task.id,
            'task_slug': task.slug,
            'task_name': task.name,
            'status': 'completed',
            'progress': 100
        }

    def _update_milestone(self, plan, data):
        """Update milestone details"""
        milestone_id = data.get('milestone_id')
        if not milestone_id:
            raise ValueError('milestone_id is required for updating milestones')

        milestone = get_object_or_404(Milestone, id=milestone_id, plan=plan)

        # Update fields if provided
        if 'name' in data:
            milestone.name = data['name']
        if 'description' in data:
            milestone.description = data['description']
        if 'estimated_duration' in data:
            milestone.estimated_duration = data['estimated_duration']
        if 'success_criteria' in data:
            milestone.success_criteria = data['success_criteria']

        milestone.save()

        return {
            'milestone_id': milestone.id,
            'milestone_name': milestone.name,
            'updated_fields': list(data.keys())
        }

    def _update_task(self, plan, data):
        """Update task details"""
        task_slug = data.get('task_slug')
        if not task_slug:
            raise ValueError('task_slug is required for updating tasks')

        task = get_object_or_404(Task, slug=task_slug, milestone__plan=plan)

        # Update fields if provided
        if 'name' in data:
            task.name = data['name']
        if 'description' in data:
            task.description = data['description']
        if 'priority' in data:
            task.priority = data['priority']
        if 'start_date' in data:
            from datetime import datetime
            task.start_date = datetime.fromisoformat(data['start_date']).date()
        if 'end_date' in data:
            from datetime import datetime
            task.end_date = datetime.fromisoformat(data['end_date']).date()
        if 'progress' in data:
            task.progress = data['progress']

        task.save()

        return {
            'task_id': task.id,
            'task_slug': task.slug,
            'task_name': task.name,
            'updated_fields': list(data.keys())
        }

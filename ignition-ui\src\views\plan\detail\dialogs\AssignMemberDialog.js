/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Avatar,
  FormControl,
  Select,
  MenuItem,
  Chip,
  ListItemText,
  Checkbox,
  ListItemAvatar,
  CircularProgress
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from 'helpers/constants';
import { toast } from 'react-toastify';
import { getInvitedUsers } from '../../services';
import { useSelector } from 'react-redux';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 6.5 + ITEM_PADDING_TOP,
      width: 400,
    },
  },
};

const AssignMemberDialog = ({ open, onClose, task, invitedUsers = [], planOwner, onAssignMembers }) => {
  const [selectedUserIds, setSelectedUserIds] = useState([]);
  const [availableUsers, setAvailableUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [, setFetchedUsers] = useState([]);

  // Get current user information from Redux store or localStorage
  const currentUserFromRedux = useSelector((state) => state.user);
  const currentUserFromLocalStorage = typeof window !== 'undefined' ? JSON.parse(localStorage.getItem('user') || '{}') : {};
  const currentUser = currentUserFromRedux?.id ? currentUserFromRedux : currentUserFromLocalStorage;

  // Function to process user data
  const processUsersData = useCallback((usersData) => {
    const users = [];

    // Add current user to the list if available
    if (currentUser && currentUser.id) {
      // Check if the current user is already in the list
      const isCurrentUserExists = users.some(user => user.id === currentUser.id);

      if (!isCurrentUserExists) {
        users.push({
          id: currentUser.id,
          first_name: currentUser.first_name || '',
          last_name: currentUser.last_name || '',
          email: currentUser.email || '',
          avatar: currentUser.avatar || '',
          isOwner: false,
          isCurrent: true
        });
      }
    }

    // Add the owner of the plan if available and different from the current user
    if (planOwner && planOwner.id && planOwner.id !== currentUser?.id) {
      users.push({
        id: planOwner.id,
        first_name: planOwner.first_name || '',
        last_name: planOwner.last_name || '',
        email: planOwner.email || '',
        avatar: planOwner.avatar || '',
        isOwner: true
      });
    }

    // Add invited members if available
    if (Array.isArray(usersData) && usersData.length > 0) {
      usersData.forEach((user, index) => {
        let userId;

        // Check if the user has invited_user_info property
        if (user && user.invited_user_info && user.invited_user_info.id) {
          userId = user.invited_user_info.id;

          // Add only if not the current user or plan owner
          if (userId !== currentUser?.id && userId !== planOwner?.id) {
            users.push({
              id: userId,
              first_name: user.invited_user_info.first_name || '',
              last_name: user.invited_user_info.last_name || '',
              email: user.email || '',
              avatar: user.invited_user_info.avatar || '',
              isOwner: false
            });
          }
        } else if (user && user.id) {
          // Add user if they already have complete information (not through invited_user_info)
          userId = user.id;

          // Add only if not the current user or plan owner
          if (userId !== currentUser?.id && userId !== planOwner?.id) {
            users.push({
              id: userId,
              first_name: user.first_name || '',
              last_name: user.last_name || '',
              email: user.email || '',
              avatar: user.avatar || '',
              isOwner: false
            });
          }
        }
      });
    }

    // If there are no users, add dummy data for testing purposes
    if (users.length === 0) {
      for (let i = 1; i <= 5; i++) {
        users.push({
          id: i,
          first_name: `Test ${i}`,
          last_name: 'User',
          email: `test${i}@example.com`,
          avatar: '',
          isOwner: i === 1
        });
      }
    }

    setAvailableUsers(users);
    setLoading(false);
    return users;
  }, [currentUser, planOwner]);

  // Fetch invited users directly if invitedUsers prop is empty
  useEffect(() => {
    if (open && task) {
      setLoading(true);

      // Initialize selectedUserIds from task.assignees if available
      if (task.assignees && Array.isArray(task.assignees)) {
        const assigneeIds = task.assignees.map(assignee =>
          typeof assignee.id === 'string' ? parseInt(assignee.id, 10) : assignee.id
        );
        setSelectedUserIds(assigneeIds);
      } else {
        setSelectedUserIds([]);
      }

      // If invitedUsers is empty, fetch data from the API
      if (!invitedUsers || invitedUsers.length === 0) {
        const fetchUsers = async () => {
          try {
            // Get planSlug from the task
            const planSlug = task.plan_slug || (typeof window !== 'undefined' && window.location.pathname.split('/').pop());

            if (!planSlug) {
              console.error('Could not determine plan slug for API request');
              setLoading(false);
              return;
            }

            const response = await getInvitedUsers(planSlug);

            let usersData = [];
            if (response && response.data) {
              usersData = response.data;
            } else if (Array.isArray(response)) {
              usersData = response;
            }

            setFetchedUsers(usersData);
            processUsersData(usersData);
          } catch (error) {
            console.error('Error fetching invited users:', error);
            setLoading(false);
          }
        };

        fetchUsers();
      } else {
        // If invitedUsers is not empty, process it directly
        processUsersData(invitedUsers);
      }
    }
  }, [open, task, invitedUsers, processUsersData]);

  // Process users data when component mounts or users change
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    if (invitedUsers) {
      const processedUsers = processUsersData(invitedUsers);
      setAvailableUsers(processedUsers);
    }
  }, [invitedUsers]);

  const handleRemoveUser = (userId) => {
    const numericId = typeof userId === 'string' ? parseInt(userId, 10) : userId;
    setSelectedUserIds(selectedUserIds.filter(id => id !== numericId));
  };

  const handleSave = async () => {
    try {
      console.log('Saving selected user IDs:', selectedUserIds);
      await onAssignMembers(selectedUserIds);
      onClose();
    } catch (error) {
      console.error('Error assigning members:', error);
      toast.error('Failed to assign members');
    }
  };

  const getUserById = (userId) => {
    return availableUsers.find(user => user.id === userId);
  };

  const handleChange = (event) => {
    const {
      target: { value },
    } = event;

    // Convert string IDs to numbers
    const numericIds = (typeof value === 'string' ? value.split(',') : value).map(id =>
      typeof id === 'string' ? parseInt(id, 10) : id
    );

    setSelectedUserIds(numericIds);
  };

  // Filter out unselected users to display in the dropdown
  const unselectedUsers = availableUsers.filter(user => !selectedUserIds.includes(user.id));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="sm"
      PaperProps={{
        sx: {
          borderRadius: '12px',
          boxShadow: '0px 8px 24px rgba(0, 0, 0, 0.15)'
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        alignItems: 'center',
        padding: '16px 24px',
        borderBottom: '1px solid #eee'
      }}>
        <Iconify
          icon="mdi:account-multiple-plus"
          width={24}
          height={24}
          color={mainYellowColor}
          sx={{ mr: 1 }}
        />
        <Typography
          variant="h6"
          sx={{
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            flexGrow: 1,
            ml: 1
          }}
        >
          Assign members to the task
        </Typography>
        <IconButton onClick={onClose} size="small">
          <Iconify icon="eva:close-fill" width={20} height={20} />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ padding: '24px' }}>
        <Box sx={{ mb: 2 }}>
          <Typography
            variant="subtitle1"
            sx={{
              fontFamily: '"Recursive Variable", sans-serif',
              fontWeight: 600,
              mb: 1
            }}
          >
            Task: {task?.name}
          </Typography>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography
            variant="subtitle2"
            sx={{
              fontFamily: '"Recursive Variable", sans-serif',
              fontWeight: 500,
              mb: 1
            }}
          >
            Selected members ({selectedUserIds.length})
          </Typography>

          {selectedUserIds.length > 0 ? (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {selectedUserIds.map((userId) => {
                const user = getUserById(userId);
                return user ? (
                  <Chip
                    key={userId}
                    avatar={<Avatar src={user.avatar} alt={user.first_name} />}
                    label={`${user.first_name} ${user.last_name}${user.isCurrent ? ' (You)' : ''}`}
                    onDelete={() => handleRemoveUser(userId)}
                    sx={{
                      bgcolor: user.isCurrent ? `${mainYellowColor}30` : '#f5f5f5',
                      '& .MuiChip-label': {
                        fontFamily: '"Recursive Variable", sans-serif',
                      }
                    }}
                  />
                ) : null;
              })}
            </Box>
          ) : (
            <Typography
              variant="body2"
              sx={{
                fontStyle: 'italic',
                color: '#666'
              }}
            >
              No member selected yet
            </Typography>
          )}
        </Box>

        <Box>
          <Typography
            variant="subtitle2"
            sx={{
              fontFamily: '"Recursive Variable", sans-serif',
              fontWeight: 500,
              mb: 1
            }}
          >
            Add member
          </Typography>

          <FormControl fullWidth>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress size={30} sx={{ color: mainYellowColor }} />
              </Box>
            ) : (
              <Select
                multiple
                displayEmpty
                value={selectedUserIds}
                onChange={handleChange}
                renderValue={(selected) => {
                  if (selected.length === 0) {
                    return <Typography sx={{ color: 'gray' }}>Select members</Typography>;
                  }

                  return (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => {
                        const user = getUserById(value);
                        return user && (
                          <Chip
                            key={value}
                            label={`${user.first_name} ${user.last_name}${user.isCurrent ? ' (You)' : ''}`}
                            sx={{
                              bgcolor: `${mainYellowColor}20`,
                              color: '#333',
                              fontFamily: '"Recursive Variable", sans-serif',
                            }}
                          />
                        );
                      })}
                    </Box>
                  );
                }}
                MenuProps={MenuProps}
                sx={{
                  fontFamily: '"Recursive Variable", sans-serif',
                  '& .MuiSelect-select': {
                    padding: '10px 14px',
                  }
                }}
              >
                {/* Only display unselected members */}
                {unselectedUsers.length > 0 ? (
                  unselectedUsers.map((user) => (
                    <MenuItem
                      key={user.id}
                      value={user.id}
                      sx={{
                        fontFamily: '"Recursive Variable", sans-serif',
                      }}
                    >
                      <Checkbox checked={selectedUserIds.indexOf(user.id) > -1} />
                      <ListItemAvatar>
                        <Avatar
                          src={user.avatar}
                          alt={user.first_name}
                          sx={{
                            width: 30,
                            height: 30,
                            bgcolor: user.isCurrent ? mainYellowColor : undefined
                          }}
                        />
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography>
                            {user.first_name} {user.last_name}
                            {user.isOwner && (
                              <Typography component="span" sx={{ ml: 1, fontSize: '0.75rem', color: mainYellowColor }}>
                                (Plan Owner)
                              </Typography>
                            )}
                            {user.isCurrent && (
                              <Typography component="span" sx={{ ml: 1, fontSize: '0.75rem', color: mainYellowColor, fontWeight: 'bold' }}>
                                (You)
                              </Typography>
                            )}
                          </Typography>
                        }
                        secondary={user.email}
                      />
                    </MenuItem>
                  ))
                ) : (
                  <MenuItem disabled>
                    <Typography sx={{ fontStyle: 'italic', color: '#666' }}>
                      No member available
                    </Typography>
                  </MenuItem>
                )}

                {/* Display a message if all members are selected */}
                {availableUsers.length > 0 && unselectedUsers.length === 0 && (
                  <MenuItem disabled>
                    <Typography sx={{ fontStyle: 'italic', color: '#666' }}>
                      All members selected
                    </Typography>
                  </MenuItem>
                )}
              </Select>
            )}
          </FormControl>
        </Box>
      </DialogContent>

      <DialogActions sx={{ padding: '16px 24px', borderTop: '1px solid #eee' }}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            borderColor: '#ddd',
            color: '#666',
            '&:hover': {
              borderColor: '#ccc',
              backgroundColor: '#f5f5f5'
            }
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          disabled={loading}
          sx={{
            bgcolor: mainYellowColor,
            color: '#333',
            fontWeight: 600,
            '&:hover': {
              bgcolor: '#e6a800',
            }
          }}
        >
          Assign
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AssignMemberDialog;

[{"C:\\ignition\\ignition-ui\\src\\index.js": "1", "C:\\ignition\\ignition-ui\\src\\redux\\store.js": "2", "C:\\ignition\\ignition-ui\\src\\views\\main\\Landing.js": "3", "C:\\ignition\\ignition-ui\\src\\layouts\\Auth.js": "4", "C:\\ignition\\ignition-ui\\src\\layouts\\Admin.js": "5", "C:\\ignition\\ignition-ui\\src\\layouts\\Public.js": "6", "C:\\ignition\\ignition-ui\\src\\redux\\userSlice.js": "7", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\About.js": "8", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Services.js": "9", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Header.js": "10", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Testimonials.js": "11", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Features.js": "12", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Hero.js": "13", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Contact.js": "14", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Footer.js": "15", "C:\\ignition\\ignition-ui\\src\\helpers\\auth.js": "16", "C:\\ignition\\ignition-ui\\src\\routes\\index.js": "17", "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\SidebarComponent.js": "18", "C:\\ignition\\ignition-ui\\src\\helpers\\constants.js": "19", "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\RightSidebar.js": "20", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Gallery.js": "21", "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\MobileHeader.js": "22", "C:\\ignition\\ignition-ui\\src\\components\\Footers\\AdminFooter.js": "23", "C:\\ignition\\ignition-ui\\src\\views\\plan\\create.js": "24", "C:\\ignition\\ignition-ui\\src\\views\\home\\index.js": "25", "C:\\ignition\\ignition-ui\\src\\views\\calendar\\index.js": "26", "C:\\ignition\\ignition-ui\\src\\views\\auth\\Login.js": "27", "C:\\ignition\\ignition-ui\\src\\views\\auth\\Activate.js": "28", "C:\\ignition\\ignition-ui\\src\\views\\auth\\Register.js": "29", "C:\\ignition\\ignition-ui\\src\\views\\auth\\ForgotPassword.js": "30", "C:\\ignition\\ignition-ui\\src\\views\\auth\\Reset.js": "31", "C:\\ignition\\ignition-ui\\src\\views\\auth\\GoogleAuthHandle.js": "32", "C:\\ignition\\ignition-ui\\src\\helpers\\functions.js": "33", "C:\\ignition\\ignition-ui\\src\\views\\plan\\invitation.js": "34", "C:\\ignition\\ignition-ui\\src\\views\\auth\\RegisterByInvitation.js": "35", "C:\\ignition\\ignition-ui\\src\\views\\todo\\index.js": "36", "C:\\ignition\\ignition-ui\\src\\views\\notifications\\index.js": "37", "C:\\ignition\\ignition-ui\\src\\RouteWrapper.js": "38", "C:\\ignition\\ignition-ui\\src\\views\\users\\profile\\index.js": "39", "C:\\ignition\\ignition-ui\\src\\components\\Iconify\\index.js": "40", "C:\\ignition\\ignition-ui\\src\\views\\public\\policies.js": "41", "C:\\ignition\\ignition-ui\\src\\views\\users\\connect_friend\\index.js": "42", "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\index.js": "43", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\index.js": "44", "C:\\ignition\\ignition-ui\\src\\views\\home\\popper.js": "45", "C:\\ignition\\ignition-ui\\src\\components\\Input\\TextAreaBase.js": "46", "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputSelectBase.js": "47", "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputBase.js": "48", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\EmptyState.js": "49", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\ViewControls.js": "50", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\PlanList.js": "51", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\FilterBar.js": "52", "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputPasswordBase.js": "53", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\LoadingPlans.js": "54", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\CreatePlanButton.js": "55", "C:\\ignition\\ignition-ui\\src\\views\\notifications\\mockData.js": "56", "C:\\ignition\\ignition-ui\\src\\views\\users\\services.js": "57", "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\useFilters.js": "58", "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\useGrouping.js": "59", "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\usePlanFetching.js": "60", "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_profile.js": "61", "C:\\ignition\\ignition-ui\\src\\views\\users\\connect_friend\\popper.js": "62", "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_contact.js": "63", "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_skill.js": "64", "C:\\ignition\\ignition-ui\\src\\components\\Snackbar\\index.js": "65", "C:\\ignition\\ignition-ui\\src\\components\\Button\\GoogleSignInButton.js": "66", "C:\\ignition\\ignition-ui\\src\\views\\plan\\services.js": "67", "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\dateSelectionDialog.js": "68", "C:\\ignition\\ignition-ui\\src\\components\\Dialog\\confirm.js": "69", "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\constants.js": "70", "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\addTaskDialog.js": "71", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Header.js": "72", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Progress.js": "73", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Description.js": "74", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Statistics.js": "75", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\TaskList.js": "76", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneList.js": "77", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneOverview.js": "78", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Members.js": "79", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\hooks\\usePlanData.js": "80", "C:\\ignition\\ignition-ui\\src\\hooks\\useProfile.js": "81", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\hooks\\useViewMode.js": "82", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\ConfirmDialog.js": "83", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\InviteDialog.js": "84", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\DeleteDialog.js": "85", "C:\\ignition\\ignition-ui\\src\\components\\Input\\CustomDatePicker.js": "86", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\PlanCard.js": "87", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\SkeletonCard.js": "88", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\TaskWithSubtasks.js": "89", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneCard.js": "90", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\CommentDialog.js": "91", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\AssignMemberDialog.js": "92", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\DueDateDialog.js": "93", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\OptOutDialog.js": "94", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\AccessManagement.js": "95", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\ChatbotBar.js": "96", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\AgentTab.js": "97"}, {"size": 1083, "mtime": 1749040749319, "results": "98", "hashOfConfig": "99"}, {"size": 182, "mtime": 1749040749320, "results": "100", "hashOfConfig": "99"}, {"size": 1569, "mtime": 1749040749333, "results": "101", "hashOfConfig": "99"}, {"size": 988, "mtime": 1749040749320, "results": "102", "hashOfConfig": "99"}, {"size": 5106, "mtime": 1749976316607, "results": "103", "hashOfConfig": "99"}, {"size": 708, "mtime": 1749040749320, "results": "104", "hashOfConfig": "99"}, {"size": 707, "mtime": 1749040749321, "results": "105", "hashOfConfig": "99"}, {"size": 1516, "mtime": 1749040749302, "results": "106", "hashOfConfig": "99"}, {"size": 1846, "mtime": 1749040749304, "results": "107", "hashOfConfig": "99"}, {"size": 2462, "mtime": 1749040749304, "results": "108", "hashOfConfig": "99"}, {"size": 2349, "mtime": 1749040749305, "results": "109", "hashOfConfig": "99"}, {"size": 2193, "mtime": 1749040749303, "results": "110", "hashOfConfig": "99"}, {"size": 429, "mtime": 1749040749304, "results": "111", "hashOfConfig": "99"}, {"size": 951, "mtime": 1749040749303, "results": "112", "hashOfConfig": "99"}, {"size": 324, "mtime": 1749040749303, "results": "113", "hashOfConfig": "99"}, {"size": 782, "mtime": 1749040749316, "results": "114", "hashOfConfig": "99"}, {"size": 3867, "mtime": 1749977349612, "results": "115", "hashOfConfig": "99"}, {"size": 7128, "mtime": 1749556803348, "results": "116", "hashOfConfig": "99"}, {"size": 1704, "mtime": 1749040749316, "results": "117", "hashOfConfig": "99"}, {"size": 8896, "mtime": 1749556803347, "results": "118", "hashOfConfig": "99"}, {"size": 1192, "mtime": 1749040749303, "results": "119", "hashOfConfig": "99"}, {"size": 5408, "mtime": 1749040749313, "results": "120", "hashOfConfig": "99"}, {"size": 1563, "mtime": 1749040749297, "results": "121", "hashOfConfig": "99"}, {"size": 18519, "mtime": 1749977179494, "results": "122", "hashOfConfig": "99"}, {"size": 3747, "mtime": 1749556803354, "results": "123", "hashOfConfig": "99"}, {"size": 36381, "mtime": 1749040749326, "results": "124", "hashOfConfig": "99"}, {"size": 7156, "mtime": 1749040749324, "results": "125", "hashOfConfig": "99"}, {"size": 4868, "mtime": 1749040749322, "results": "126", "hashOfConfig": "99"}, {"size": 11745, "mtime": 1749040749324, "results": "127", "hashOfConfig": "99"}, {"size": 7640, "mtime": 1749556803350, "results": "128", "hashOfConfig": "99"}, {"size": 11414, "mtime": 1749040749324, "results": "129", "hashOfConfig": "99"}, {"size": 5983, "mtime": 1749040749323, "results": "130", "hashOfConfig": "99"}, {"size": 989, "mtime": 1749040749316, "results": "131", "hashOfConfig": "99"}, {"size": 14801, "mtime": 1749040749347, "results": "132", "hashOfConfig": "99"}, {"size": 14611, "mtime": 1749040749324, "results": "133", "hashOfConfig": "99"}, {"size": 16276, "mtime": 1749040749351, "results": "134", "hashOfConfig": "99"}, {"size": 7161, "mtime": 1749040749335, "results": "135", "hashOfConfig": "99"}, {"size": 1011, "mtime": 1749040749156, "results": "136", "hashOfConfig": "99"}, {"size": 31280, "mtime": 1749040749358, "results": "137", "hashOfConfig": "99"}, {"size": 566, "mtime": 1749040749297, "results": "138", "hashOfConfig": "99"}, {"size": 7601, "mtime": 1749040749350, "results": "139", "hashOfConfig": "99"}, {"size": 23019, "mtime": 1749040749354, "results": "140", "hashOfConfig": "99"}, {"size": 11567, "mtime": 1749040749356, "results": "141", "hashOfConfig": "99"}, {"size": 25590, "mtime": 1749974427659, "results": "142", "hashOfConfig": "99"}, {"size": 2444, "mtime": 1749556803355, "results": "143", "hashOfConfig": "99"}, {"size": 2077, "mtime": 1749040749301, "results": "144", "hashOfConfig": "99"}, {"size": 4227, "mtime": 1749040749300, "results": "145", "hashOfConfig": "99"}, {"size": 2665, "mtime": 1749040749298, "results": "146", "hashOfConfig": "99"}, {"size": 1863, "mtime": 1749040749327, "results": "147", "hashOfConfig": "99"}, {"size": 2537, "mtime": 1749040749330, "results": "148", "hashOfConfig": "99"}, {"size": 2429, "mtime": 1749556803352, "results": "149", "hashOfConfig": "99"}, {"size": 2423, "mtime": 1749040749328, "results": "150", "hashOfConfig": "99"}, {"size": 2198, "mtime": 1749040749300, "results": "151", "hashOfConfig": "99"}, {"size": 613, "mtime": 1749040749328, "results": "152", "hashOfConfig": "99"}, {"size": 983, "mtime": 1749040749327, "results": "153", "hashOfConfig": "99"}, {"size": 10000, "mtime": 1749040749335, "results": "154", "hashOfConfig": "99"}, {"size": 2035, "mtime": 1749040749359, "results": "155", "hashOfConfig": "99"}, {"size": 1611, "mtime": 1749040749330, "results": "156", "hashOfConfig": "99"}, {"size": 2826, "mtime": 1749040749331, "results": "157", "hashOfConfig": "99"}, {"size": 2681, "mtime": 1749556803354, "results": "158", "hashOfConfig": "99"}, {"size": 5853, "mtime": 1749040749355, "results": "159", "hashOfConfig": "99"}, {"size": 2032, "mtime": 1749040749354, "results": "160", "hashOfConfig": "99"}, {"size": 7248, "mtime": 1749040749354, "results": "161", "hashOfConfig": "99"}, {"size": 3298, "mtime": 1749040749355, "results": "162", "hashOfConfig": "99"}, {"size": 3737, "mtime": 1749040749316, "results": "163", "hashOfConfig": "99"}, {"size": 1872, "mtime": 1749040749295, "results": "164", "hashOfConfig": "99"}, {"size": 12332, "mtime": 1749556803363, "results": "165", "hashOfConfig": "99"}, {"size": 2806, "mtime": 1749040749351, "results": "166", "hashOfConfig": "99"}, {"size": 1754, "mtime": 1749040749296, "results": "167", "hashOfConfig": "99"}, {"size": 2891, "mtime": 1749040749351, "results": "168", "hashOfConfig": "99"}, {"size": 2195, "mtime": 1749040749351, "results": "169", "hashOfConfig": "99"}, {"size": 6205, "mtime": 1749975452446, "results": "170", "hashOfConfig": "99"}, {"size": 4753, "mtime": 1749040749341, "results": "171", "hashOfConfig": "99"}, {"size": 1301, "mtime": 1749040749337, "results": "172", "hashOfConfig": "99"}, {"size": 2978, "mtime": 1749040749341, "results": "173", "hashOfConfig": "99"}, {"size": 7055, "mtime": 1749040749342, "results": "174", "hashOfConfig": "99"}, {"size": 2411, "mtime": 1749040749340, "results": "175", "hashOfConfig": "99"}, {"size": 17852, "mtime": 1749972691893, "results": "176", "hashOfConfig": "99"}, {"size": 9016, "mtime": 1749040749339, "results": "177", "hashOfConfig": "99"}, {"size": 12009, "mtime": 1749556803360, "results": "178", "hashOfConfig": "99"}, {"size": 2030, "mtime": 1749040749317, "results": "179", "hashOfConfig": "99"}, {"size": 366, "mtime": 1749040749346, "results": "180", "hashOfConfig": "99"}, {"size": 2418, "mtime": 1749040749344, "results": "181", "hashOfConfig": "99"}, {"size": 4635, "mtime": 1749040749345, "results": "182", "hashOfConfig": "99"}, {"size": 3241, "mtime": 1749040749344, "results": "183", "hashOfConfig": "99"}, {"size": 1832, "mtime": 1749556803346, "results": "184", "hashOfConfig": "99"}, {"size": 18099, "mtime": 1749556803352, "results": "185", "hashOfConfig": "99"}, {"size": 3093, "mtime": 1749040749329, "results": "186", "hashOfConfig": "99"}, {"size": 24502, "mtime": 1749040749342, "results": "187", "hashOfConfig": "99"}, {"size": 53286, "mtime": 1749974242811, "results": "188", "hashOfConfig": "99"}, {"size": 11968, "mtime": 1749040749344, "results": "189", "hashOfConfig": "99"}, {"size": 16428, "mtime": 1749040749343, "results": "190", "hashOfConfig": "99"}, {"size": 10533, "mtime": 1749040749345, "results": "191", "hashOfConfig": "99"}, {"size": 3414, "mtime": 1749556803359, "results": "192", "hashOfConfig": "99"}, {"size": 10339, "mtime": 1749556803357, "results": "193", "hashOfConfig": "99"}, {"size": 9680, "mtime": 1749972691890, "results": "194", "hashOfConfig": "99"}, {"size": 20163, "mtime": 1749978006251, "results": "195", "hashOfConfig": "99"}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1k7u7zg", {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\ignition\\ignition-ui\\src\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\redux\\store.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\main\\Landing.js", [], [], "C:\\ignition\\ignition-ui\\src\\layouts\\Auth.js", [], [], "C:\\ignition\\ignition-ui\\src\\layouts\\Admin.js", [], [], "C:\\ignition\\ignition-ui\\src\\layouts\\Public.js", [], [], "C:\\ignition\\ignition-ui\\src\\redux\\userSlice.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\About.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Services.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Header.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Testimonials.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Features.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Hero.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Contact.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Footer.js", [], [], "C:\\ignition\\ignition-ui\\src\\helpers\\auth.js", [], [], "C:\\ignition\\ignition-ui\\src\\routes\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\SidebarComponent.js", [], ["487"], "C:\\ignition\\ignition-ui\\src\\helpers\\constants.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\RightSidebar.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Gallery.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\MobileHeader.js", [], ["488", "489"], "C:\\ignition\\ignition-ui\\src\\components\\Footers\\AdminFooter.js", [], ["490"], "C:\\ignition\\ignition-ui\\src\\views\\plan\\create.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\calendar\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\Login.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\Activate.js", [], ["491"], "C:\\ignition\\ignition-ui\\src\\views\\auth\\Register.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\ForgotPassword.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\Reset.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\GoogleAuthHandle.js", [], ["492"], "C:\\ignition\\ignition-ui\\src\\helpers\\functions.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\invitation.js", [], ["493"], "C:\\ignition\\ignition-ui\\src\\views\\auth\\RegisterByInvitation.js", [], ["494"], "C:\\ignition\\ignition-ui\\src\\views\\todo\\index.js", [], ["495"], "C:\\ignition\\ignition-ui\\src\\views\\notifications\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\RouteWrapper.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\profile\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Iconify\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\public\\policies.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\connect_friend\\index.js", [], ["496", "497", "498"], "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\popper.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\TextAreaBase.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputSelectBase.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputBase.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\EmptyState.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\ViewControls.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\PlanList.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\FilterBar.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputPasswordBase.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\LoadingPlans.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\CreatePlanButton.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\notifications\\mockData.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\services.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\useFilters.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\useGrouping.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\usePlanFetching.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_profile.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\connect_friend\\popper.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_contact.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_skill.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Snackbar\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Button\\GoogleSignInButton.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\services.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\dateSelectionDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Dialog\\confirm.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\constants.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\addTaskDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Header.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Progress.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Description.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Statistics.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\TaskList.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneList.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneOverview.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Members.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\hooks\\usePlanData.js", [], [], "C:\\ignition\\ignition-ui\\src\\hooks\\useProfile.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\hooks\\useViewMode.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\ConfirmDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\InviteDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\DeleteDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\CustomDatePicker.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\PlanCard.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\SkeletonCard.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\TaskWithSubtasks.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneCard.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\CommentDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\AssignMemberDialog.js", [], ["499"], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\DueDateDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\OptOutDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\AccessManagement.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\ChatbotBar.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\AgentTab.js", [], ["500"], {"ruleId": "501", "severity": 1, "message": "502", "line": 24, "column": 6, "nodeType": "503", "endLine": 24, "endColumn": 8, "suggestions": "504", "suppressions": "505"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 28, "column": 6, "nodeType": "503", "endLine": 28, "endColumn": 8, "suggestions": "506", "suppressions": "507"}, {"ruleId": "508", "severity": 1, "message": "509", "line": 124, "column": 11, "nodeType": "510", "endLine": 124, "endColumn": 65, "suppressions": "511"}, {"ruleId": "512", "severity": 1, "message": "513", "line": 11, "column": 13, "nodeType": "510", "endLine": 12, "endColumn": 31, "suppressions": "514"}, {"ruleId": "501", "severity": 1, "message": "515", "line": 26, "column": 6, "nodeType": "503", "endLine": 26, "endColumn": 22, "suggestions": "516", "suppressions": "517"}, {"ruleId": "501", "severity": 1, "message": "518", "line": 27, "column": 6, "nodeType": "503", "endLine": 27, "endColumn": 8, "suggestions": "519", "suppressions": "520"}, {"ruleId": "501", "severity": 1, "message": "521", "line": 76, "column": 6, "nodeType": "503", "endLine": 76, "endColumn": 8, "suggestions": "522", "suppressions": "523"}, {"ruleId": "501", "severity": 1, "message": "524", "line": 110, "column": 6, "nodeType": "503", "endLine": 110, "endColumn": 8, "suggestions": "525", "suppressions": "526"}, {"ruleId": "501", "severity": 1, "message": "527", "line": 59, "column": 6, "nodeType": "503", "endLine": 59, "endColumn": 8, "suggestions": "528", "suppressions": "529"}, {"ruleId": "501", "severity": 1, "message": "530", "line": 471, "column": 6, "nodeType": "503", "endLine": 471, "endColumn": 31, "suggestions": "531", "suppressions": "532"}, {"ruleId": "501", "severity": 1, "message": "533", "line": 476, "column": 6, "nodeType": "503", "endLine": 476, "endColumn": 15, "suggestions": "534", "suppressions": "535"}, {"ruleId": "501", "severity": 1, "message": "533", "line": 482, "column": 6, "nodeType": "503", "endLine": 482, "endColumn": 12, "suggestions": "536", "suppressions": "537"}, {"ruleId": "501", "severity": 1, "message": "538", "line": 202, "column": 6, "nodeType": "503", "endLine": 202, "endColumn": 20, "suggestions": "539", "suppressions": "540"}, {"ruleId": "501", "severity": 1, "message": "541", "line": 55, "column": 6, "nodeType": "503", "endLine": 55, "endColumn": 36, "suggestions": "542", "suppressions": "543"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAccountInfo'. Either include it or remove the dependency array.", "ArrayExpression", ["544"], ["545"], ["546"], ["547"], "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", ["548"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", ["549"], "React Hook useEffect has a missing dependency: 'handleActivate'. Either include it or remove the dependency array.", ["550"], ["551"], "React Hook useEffect has missing dependencies: 'location.search' and 'onGooglelogin'. Either include them or remove the dependency array.", ["552"], ["553"], "React Hook useEffect has a missing dependency: 'handleCheckInvitation'. Either include it or remove the dependency array.", ["554"], ["555"], "React Hook useEffect has a missing dependency: 'checkSignedId'. Either include it or remove the dependency array.", ["556"], ["557"], "React Hook useEffect has a missing dependency: 'fetchTasks'. Either include it or remove the dependency array.", ["558"], ["559"], "React Hook useEffect has a missing dependency: 'handleObserver'. Either include it or remove the dependency array.", ["560"], ["561"], "React Hook useEffect has a missing dependency: 'fetchUserFriendData'. Either include it or remove the dependency array.", ["562"], ["563"], ["564"], ["565"], "React Hook useEffect has a missing dependency: 'processUsersData'. Either include it or remove the dependency array.", ["566"], ["567"], "React Hook useEffect has a missing dependency: 'handleSendMessage'. Either include it or remove the dependency array.", ["568"], ["569"], {"desc": "570", "fix": "571"}, {"kind": "572", "justification": "573"}, {"desc": "570", "fix": "574"}, {"kind": "572", "justification": "573"}, {"kind": "572", "justification": "573"}, {"kind": "572", "justification": "573"}, {"desc": "575", "fix": "576"}, {"kind": "572", "justification": "573"}, {"desc": "577", "fix": "578"}, {"kind": "572", "justification": "573"}, {"desc": "579", "fix": "580"}, {"kind": "572", "justification": "573"}, {"desc": "581", "fix": "582"}, {"kind": "572", "justification": "573"}, {"desc": "583", "fix": "584"}, {"kind": "572", "justification": "573"}, {"desc": "585", "fix": "586"}, {"kind": "572", "justification": "573"}, {"desc": "587", "fix": "588"}, {"kind": "572", "justification": "573"}, {"desc": "589", "fix": "590"}, {"kind": "572", "justification": "573"}, {"desc": "591", "fix": "592"}, {"kind": "572", "justification": "573"}, {"desc": "593", "fix": "594"}, {"kind": "572", "justification": "573"}, "Update the dependencies array to be: [fetchAccountInfo]", {"range": "595", "text": "596"}, "directive", "", {"range": "597", "text": "596"}, "Update the dependencies array to be: [handleActivate, param1, param2]", {"range": "598", "text": "599"}, "Update the dependencies array to be: [location.search, onGooglelogin]", {"range": "600", "text": "601"}, "Update the dependencies array to be: [handleCheckInvitation]", {"range": "602", "text": "603"}, "Update the dependencies array to be: [checkSignedId]", {"range": "604", "text": "605"}, "Update the dependencies array to be: [fetchTasks]", {"range": "606", "text": "607"}, "Update the dependencies array to be: [handleObserver, loadingMore, totalPages]", {"range": "608", "text": "609"}, "Update the dependencies array to be: [fetchUserFriendData, filters]", {"range": "610", "text": "611"}, "Update the dependencies array to be: [fetchUserFriendData, page]", {"range": "612", "text": "613"}, "Update the dependencies array to be: [invitedUsers, processUsersData]", {"range": "614", "text": "615"}, "Update the dependencies array to be: [planInfo?.id, location.state, handleSendMessage]", {"range": "616", "text": "617"}, [1021, 1023], "[fetchAccountInfo]", [799, 801], [800, 816], "[handleActivate, param1, param2]", [1073, 1075], "[location.search, onGooglelogin]", [2532, 2534], "[handleCheckInvitation]", [3427, 3429], "[checkSignedId]", [2266, 2268], "[fetchTasks]", [14298, 14323], "[handleObserver, loadingMore, totalPages]", [14400, 14409], "[fetchUserFriendData, filters]", [14503, 14509], "[fetchUserFriendData, page]", [6690, 6704], "[invitedUsers, processUsersData]", [1966, 1996], "[planInfo?.id, location.state, handleSendMessage]"]
{"ast": null, "code": "/* eslint-disable react-hooks/exhaustive-deps */import React,{useState,useEffect,useRef}from'react';import{Box,Typography,Paper,TextField,IconButton,Avatar,CircularProgress,Chip,Tooltip}from'@mui/material';import{useLocation}from'react-router-dom';import Iconify from'components/Iconify/index';import{mainYellowColor,APIURL}from\"helpers/constants\";import{getHeaders}from\"helpers/functions\";import axios from'axios';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AgentTab=_ref=>{let{planInfo,onPlanUpdate}=_ref;const[conversations,setConversations]=useState([]);const[currentMessage,setCurrentMessage]=useState('');const[isLoading,setIsLoading]=useState(false);const messagesEndRef=useRef(null);const inputRef=useRef(null);const[,setError]=useState(null);const location=useLocation();// Load conversations from localStorage\nuseEffect(()=>{var _location$state;const savedConversations=JSON.parse(localStorage.getItem('agent_conversations')||'[]');const planConversations=savedConversations.filter(conv=>conv.planId===(planInfo===null||planInfo===void 0?void 0:planInfo.id));setConversations(planConversations);// Check for pending message from ChatbotBar\nconst pendingMessage=localStorage.getItem('pending_agent_message');if(pendingMessage){try{var _messageData$planInfo;const messageData=JSON.parse(pendingMessage);if(((_messageData$planInfo=messageData.planInfo)===null||_messageData$planInfo===void 0?void 0:_messageData$planInfo.id)===(planInfo===null||planInfo===void 0?void 0:planInfo.id)){handleSendMessage(messageData.message);}// Clear the pending message\nlocalStorage.removeItem('pending_agent_message');}catch(error){console.error('Error processing pending message:',error);localStorage.removeItem('pending_agent_message');}}// If coming from chatbot bar via navigation state, add the initial message\nif((_location$state=location.state)!==null&&_location$state!==void 0&&_location$state.message){handleSendMessage(location.state.message);}},[planInfo===null||planInfo===void 0?void 0:planInfo.id,location.state]);// Auto scroll to bottom\nuseEffect(()=>{var _messagesEndRef$curre;(_messagesEndRef$curre=messagesEndRef.current)===null||_messagesEndRef$curre===void 0?void 0:_messagesEndRef$curre.scrollIntoView({behavior:'smooth'});},[conversations]);const handleSendMessage=async function(){let messageText=arguments.length>0&&arguments[0]!==undefined?arguments[0]:currentMessage;if(!messageText.trim()||isLoading)return;setIsLoading(true);const userMessage={id:Date.now(),type:'user',content:messageText.trim(),timestamp:new Date().toISOString()};const newConversations=[...conversations,userMessage];setConversations(newConversations);setCurrentMessage('');try{// Call the new AI agent chat endpoint with plan context\nconst response=await axios.post(`${APIURL}/api/assistant/agent-chat`,{message:messageText.trim(),plan_slug:planInfo.slug},{headers:getHeaders()});const aiResponseData=response.data;// Create AI response message\nconst aiResponse={id:Date.now()+1,type:'assistant',content:aiResponseData.message||'I received your message but had trouble generating a response.',timestamp:new Date().toISOString(),actions:aiResponseData.actions||[],metadata:aiResponseData.metadata||{}};const updatedConversations=[...newConversations,aiResponse];setConversations(updatedConversations);// Execute any actions returned by the AI\nif(aiResponseData.actions&&aiResponseData.actions.length>0){let actionResults=[];for(const action of aiResponseData.actions){try{const result=await executeAIAction(action);actionResults.push(result);}catch(error){console.error('Error executing AI action:',error);actionResults.push({success:false,error:error.message||'Unknown error'});}}// If any actions were successful, trigger plan refresh\nif(actionResults.some(result=>result.success)&&onPlanUpdate){onPlanUpdate();}// Add action results to the conversation if there were any issues\nconst failedActions=actionResults.filter(result=>!result.success);if(failedActions.length>0){const errorMessage={id:Date.now()+2,type:'assistant',content:`Note: I encountered some issues executing the requested actions:\\n${failedActions.map(result=>`• ${result.error}`).join('\\n')}`,timestamp:new Date().toISOString(),isError:true};setConversations(prev=>[...prev,errorMessage]);}}// Save to localStorage\nconst allConversations=JSON.parse(localStorage.getItem('agent_conversations')||'[]');const otherPlanConversations=allConversations.filter(conv=>conv.planId!==(planInfo===null||planInfo===void 0?void 0:planInfo.id));const planConversations=updatedConversations.map(conv=>({...conv,planId:planInfo===null||planInfo===void 0?void 0:planInfo.id,planName:planInfo===null||planInfo===void 0?void 0:planInfo.name}));localStorage.setItem('agent_conversations',JSON.stringify([...otherPlanConversations,...planConversations]));}catch(error){var _error$response,_error$response$data;console.error('Error processing message:',error);const errorResponse={id:Date.now()+1,type:'assistant',content:`Sorry, I encountered an error while processing your request: ${((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.error)||error.message||'Unknown error'}. Please try again.`,timestamp:new Date().toISOString(),isError:true};setConversations([...newConversations,errorResponse]);setError(error.message||'Unknown error occurred');}finally{setIsLoading(false);}};const executeAIAction=async action=>{try{const response=await axios.post(`${APIURL}/api/assistant/plan-action`,{action:action.type,plan_slug:planInfo.slug,data:action.data||{},message:`AI-generated action: ${action.type}`},{headers:getHeaders()});return{success:true,data:response.data,action:action};}catch(error){var _error$response2,_error$response2$data;console.error('Error executing AI action:',error);return{success:false,error:((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.error)||error.message||'Unknown error',action:action};}};const handleKeyPress=e=>{if(e.key==='Enter'&&!e.shiftKey){e.preventDefault();handleSendMessage();}};const formatTimestamp=timestamp=>{return new Date(timestamp).toLocaleTimeString('en-US',{hour:'2-digit',minute:'2-digit'});};return/*#__PURE__*/_jsxs(Box,{sx:{height:'70vh',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsx(Paper,{elevation:0,sx:{p:2,borderRadius:'12px 12px 0 0',border:'1px solid #f0f0f0',borderBottom:'none',backgroundColor:'#fafafa'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Avatar,{sx:{backgroundColor:mainYellowColor,width:40,height:40},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:robot\",width:24,height:24,color:\"#fff\"})}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',fontWeight:600,color:'#333'},children:\"AI Project Agent\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",sx:{color:'#666',fontFamily:'\"Recursive Variable\", sans-serif'},children:[\"Managing: \",planInfo===null||planInfo===void 0?void 0:planInfo.name]})]}),/*#__PURE__*/_jsx(Chip,{label:\"Beta\",size:\"small\",sx:{backgroundColor:`${mainYellowColor}20`,color:mainYellowColor,fontWeight:600,ml:'auto'}})]})}),/*#__PURE__*/_jsx(Paper,{elevation:0,sx:{flex:1,border:'1px solid #f0f0f0',borderTop:'none',borderBottom:'none',overflow:'auto',p:2,backgroundColor:'#fff'},children:conversations.length===0?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',height:'100%',textAlign:'center'},children:[/*#__PURE__*/_jsx(Avatar,{sx:{backgroundColor:`${mainYellowColor}20`,width:60,height:60,mb:2},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:robot\",width:32,height:32,color:mainYellowColor})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',fontWeight:600,color:'#333',mb:1},children:\"Welcome to AI Project Agent\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:'#666',fontFamily:'\"Recursive Variable\", sans-serif',maxWidth:400},children:\"I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\"})]}):/*#__PURE__*/_jsxs(Box,{children:[conversations.map(message=>/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:message.type==='user'?'flex-end':'flex-start',mb:2},children:/*#__PURE__*/_jsxs(Box,{sx:{maxWidth:'70%',display:'flex',flexDirection:message.type==='user'?'row-reverse':'row',alignItems:'flex-start',gap:1},children:[/*#__PURE__*/_jsx(Avatar,{sx:{width:32,height:32,backgroundColor:message.type==='user'?'#e0e0e0':mainYellowColor},children:/*#__PURE__*/_jsx(Iconify,{icon:message.type==='user'?\"material-symbols:person\":\"mdi:robot\",width:18,height:18,color:message.type==='user'?'#666':'#fff'})}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Paper,{elevation:0,sx:{p:1.5,borderRadius:'12px',backgroundColor:message.type==='user'?mainYellowColor:'#f5f5f5',color:message.type==='user'?'#fff':'#333',border:message.isError?'1px solid #f44336':'none'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',lineHeight:1.5,whiteSpace:'pre-line'},children:message.content}),message.type==='assistant'&&message.actions&&message.actions.length>0&&/*#__PURE__*/_jsx(Box,{sx:{mt:1.5,display:'flex',flexWrap:'wrap',gap:1},children:message.actions.slice(0,3).map((action,actionIndex)=>/*#__PURE__*/_jsx(Chip,{label:action.description,size:\"small\",onClick:()=>{// Handle quick action\nsetCurrentMessage(action.originalMessage||`Please ${action.description.toLowerCase()}`);if(inputRef.current){inputRef.current.focus();}},sx:{backgroundColor:'#fff',border:`1px solid ${mainYellowColor}`,color:mainYellowColor,fontSize:'0.7rem',height:'24px',cursor:'pointer','&:hover':{backgroundColor:`${mainYellowColor}10`}}},actionIndex))})]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{color:'#999',fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.7rem',mt:0.5,display:'block',textAlign:message.type==='user'?'right':'left'},children:formatTimestamp(message.timestamp)})]})]})},message.id)),isLoading&&/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'flex-start',mb:2},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'flex-start',gap:1},children:[/*#__PURE__*/_jsx(Avatar,{sx:{width:32,height:32,backgroundColor:mainYellowColor},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:robot\",width:18,height:18,color:\"#fff\"})}),/*#__PURE__*/_jsxs(Paper,{elevation:0,sx:{p:1.5,borderRadius:'12px',backgroundColor:'#f5f5f5',display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(CircularProgress,{size:16,sx:{color:mainYellowColor}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',color:'#666'},children:\"Thinking...\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{ref:messagesEndRef})]})}),/*#__PURE__*/_jsx(Paper,{elevation:0,sx:{p:2,borderRadius:'0 0 12px 12px',border:'1px solid #f0f0f0',borderTop:'none'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1,alignItems:'flex-end'},children:[/*#__PURE__*/_jsx(TextField,{inputRef:inputRef,value:currentMessage,onChange:e=>setCurrentMessage(e.target.value),onKeyPress:handleKeyPress,placeholder:\"Ask me anything about your project...\",multiline:true,maxRows:3,fullWidth:true,variant:\"outlined\",disabled:isLoading,sx:{'& .MuiOutlinedInput-root':{borderRadius:'8px',fontFamily:'\"Recursive Variable\", sans-serif'}}}),/*#__PURE__*/_jsx(Tooltip,{title:\"Send message\",children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleSendMessage(),disabled:!currentMessage.trim()||isLoading,sx:{backgroundColor:currentMessage.trim()&&!isLoading?mainYellowColor:'#f0f0f0',color:currentMessage.trim()&&!isLoading?'#fff':'#999','&:hover':{backgroundColor:currentMessage.trim()&&!isLoading?'#E69500':'#f0f0f0'}},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:send\",width:20,height:20})})})]})})]});};export default AgentTab;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Typography", "Paper", "TextField", "IconButton", "Avatar", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "useLocation", "Iconify", "mainYellowColor", "APIURL", "getHeaders", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "AgentTab", "_ref", "planInfo", "onPlanUpdate", "conversations", "setConversations", "currentMessage", "setCurrentMessage", "isLoading", "setIsLoading", "messagesEndRef", "inputRef", "setError", "location", "_location$state", "savedConversations", "JSON", "parse", "localStorage", "getItem", "planConversations", "filter", "conv", "planId", "id", "pendingMessage", "_messageData$planInfo", "messageData", "handleSendMessage", "message", "removeItem", "error", "console", "state", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "messageText", "arguments", "length", "undefined", "trim", "userMessage", "Date", "now", "type", "content", "timestamp", "toISOString", "newConversations", "response", "post", "plan_slug", "slug", "headers", "aiResponseData", "data", "aiResponse", "actions", "metadata", "updatedConversations", "actionResults", "action", "result", "executeAIAction", "push", "success", "some", "failedActions", "errorMessage", "map", "join", "isError", "prev", "allConversations", "otherPlanConversations", "planName", "name", "setItem", "stringify", "_error$response", "_error$response$data", "errorResponse", "_error$response2", "_error$response2$data", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "formatTimestamp", "toLocaleTimeString", "hour", "minute", "sx", "height", "display", "flexDirection", "children", "elevation", "p", "borderRadius", "border", "borderBottom", "backgroundColor", "alignItems", "gap", "width", "icon", "color", "variant", "fontFamily", "fontWeight", "label", "size", "ml", "flex", "borderTop", "overflow", "justifyContent", "textAlign", "mb", "max<PERSON><PERSON><PERSON>", "lineHeight", "whiteSpace", "mt", "flexWrap", "slice", "actionIndex", "description", "onClick", "originalMessage", "toLowerCase", "focus", "fontSize", "cursor", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "multiline", "maxRows", "fullWidth", "disabled", "title"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/AgentTab.js"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  TextField,\r\n  IconButton,\r\n  Avatar,\r\n  CircularProgress,\r\n  Chip,\r\n  Tooltip\r\n} from '@mui/material';\r\nimport { useLocation } from 'react-router-dom';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\r\nimport { getHeaders } from \"helpers/functions\";\r\nimport axios from 'axios';\r\n\r\nconst AgentTab = ({ planInfo, onPlanUpdate }) => {\r\n  const [conversations, setConversations] = useState([]);\r\n  const [currentMessage, setCurrentMessage] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const messagesEndRef = useRef(null);\r\n  const inputRef = useRef(null);\r\n  const [ , setError] = useState(null);\r\n  const location = useLocation();\r\n\r\n  // Load conversations from localStorage\r\n  useEffect(() => {\r\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n    const planConversations = savedConversations.filter(conv => conv.planId === planInfo?.id);\r\n    setConversations(planConversations);\r\n\r\n    // Check for pending message from ChatbotBar\r\n    const pendingMessage = localStorage.getItem('pending_agent_message');\r\n    if (pendingMessage) {\r\n      try {\r\n        const messageData = JSON.parse(pendingMessage);\r\n        if (messageData.planInfo?.id === planInfo?.id) {\r\n          handleSendMessage(messageData.message);\r\n        }\r\n        // Clear the pending message\r\n        localStorage.removeItem('pending_agent_message');\r\n      } catch (error) {\r\n        console.error('Error processing pending message:', error);\r\n        localStorage.removeItem('pending_agent_message');\r\n      }\r\n    }\r\n\r\n    // If coming from chatbot bar via navigation state, add the initial message\r\n    if (location.state?.message) {\r\n      handleSendMessage(location.state.message);\r\n    }\r\n  }, [planInfo?.id, location.state]);\r\n\r\n  // Auto scroll to bottom\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  }, [conversations]);\r\n\r\n\r\n\r\n  const handleSendMessage = async (messageText = currentMessage) => {\r\n    if (!messageText.trim() || isLoading) return;\r\n\r\n    setIsLoading(true);\r\n    const userMessage = {\r\n      id: Date.now(),\r\n      type: 'user',\r\n      content: messageText.trim(),\r\n      timestamp: new Date().toISOString()\r\n    };\r\n\r\n    const newConversations = [...conversations, userMessage];\r\n    setConversations(newConversations);\r\n    setCurrentMessage('');\r\n\r\n    try {\r\n      // Call the new AI agent chat endpoint with plan context\r\n      const response = await axios.post(\r\n        `${APIURL}/api/assistant/agent-chat`,\r\n        {\r\n          message: messageText.trim(),\r\n          plan_slug: planInfo.slug\r\n        },\r\n        { headers: getHeaders() }\r\n      );\r\n\r\n      const aiResponseData = response.data;\r\n\r\n      // Create AI response message\r\n      const aiResponse = {\r\n        id: Date.now() + 1,\r\n        type: 'assistant',\r\n        content: aiResponseData.message || 'I received your message but had trouble generating a response.',\r\n        timestamp: new Date().toISOString(),\r\n        actions: aiResponseData.actions || [],\r\n        metadata: aiResponseData.metadata || {}\r\n      };\r\n\r\n      const updatedConversations = [...newConversations, aiResponse];\r\n      setConversations(updatedConversations);\r\n\r\n      // Execute any actions returned by the AI\r\n      if (aiResponseData.actions && aiResponseData.actions.length > 0) {\r\n        let actionResults = [];\r\n        for (const action of aiResponseData.actions) {\r\n          try {\r\n            const result = await executeAIAction(action);\r\n            actionResults.push(result);\r\n          } catch (error) {\r\n            console.error('Error executing AI action:', error);\r\n            actionResults.push({\r\n              success: false,\r\n              error: error.message || 'Unknown error'\r\n            });\r\n          }\r\n        }\r\n\r\n        // If any actions were successful, trigger plan refresh\r\n        if (actionResults.some(result => result.success) && onPlanUpdate) {\r\n          onPlanUpdate();\r\n        }\r\n\r\n        // Add action results to the conversation if there were any issues\r\n        const failedActions = actionResults.filter(result => !result.success);\r\n        if (failedActions.length > 0) {\r\n          const errorMessage = {\r\n            id: Date.now() + 2,\r\n            type: 'assistant',\r\n            content: `Note: I encountered some issues executing the requested actions:\\n${failedActions.map(result => `• ${result.error}`).join('\\n')}`,\r\n            timestamp: new Date().toISOString(),\r\n            isError: true\r\n          };\r\n          setConversations(prev => [...prev, errorMessage]);\r\n        }\r\n      }\r\n\r\n      // Save to localStorage\r\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== planInfo?.id);\r\n      const planConversations = updatedConversations.map(conv => ({\r\n        ...conv,\r\n        planId: planInfo?.id,\r\n        planName: planInfo?.name\r\n      }));\r\n\r\n      localStorage.setItem('agent_conversations', JSON.stringify([\r\n        ...otherPlanConversations,\r\n        ...planConversations\r\n      ]));\r\n\r\n    } catch (error) {\r\n      console.error('Error processing message:', error);\r\n      const errorResponse = {\r\n        id: Date.now() + 1,\r\n        type: 'assistant',\r\n        content: `Sorry, I encountered an error while processing your request: ${error.response?.data?.error || error.message || 'Unknown error'}. Please try again.`,\r\n        timestamp: new Date().toISOString(),\r\n        isError: true\r\n      };\r\n      setConversations([...newConversations, errorResponse]);\r\n      setError(error.message || 'Unknown error occurred');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const executeAIAction = async (action) => {\r\n    try {\r\n      const response = await axios.post(\r\n        `${APIURL}/api/assistant/plan-action`,\r\n        {\r\n          action: action.type,\r\n          plan_slug: planInfo.slug,\r\n          data: action.data || {},\r\n          message: `AI-generated action: ${action.type}`\r\n        },\r\n        { headers: getHeaders() }\r\n      );\r\n\r\n      return {\r\n        success: true,\r\n        data: response.data,\r\n        action: action\r\n      };\r\n    } catch (error) {\r\n      console.error('Error executing AI action:', error);\r\n      return {\r\n        success: false,\r\n        error: error.response?.data?.error || error.message || 'Unknown error',\r\n        action: action\r\n      };\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleKeyPress = (e) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const formatTimestamp = (timestamp) => {\r\n    return new Date(timestamp).toLocaleTimeString('en-US', {\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>\r\n      {/* Header */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          p: 2,\r\n          borderRadius: '12px 12px 0 0',\r\n          border: '1px solid #f0f0f0',\r\n          borderBottom: 'none',\r\n          backgroundColor: '#fafafa'\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n          <Avatar\r\n            sx={{\r\n              backgroundColor: mainYellowColor,\r\n              width: 40,\r\n              height: 40\r\n            }}\r\n          >\r\n            <Iconify icon=\"mdi:robot\" width={24} height={24} color=\"#fff\" />\r\n          </Avatar>\r\n          <Box>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                color: '#333'\r\n              }}\r\n            >\r\n              AI Project Agent\r\n            </Typography>\r\n            <Typography\r\n              variant=\"caption\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }}\r\n            >\r\n              Managing: {planInfo?.name}\r\n            </Typography>\r\n          </Box>\r\n          <Chip\r\n            label=\"Beta\"\r\n            size=\"small\"\r\n            sx={{\r\n              backgroundColor: `${mainYellowColor}20`,\r\n              color: mainYellowColor,\r\n              fontWeight: 600,\r\n              ml: 'auto'\r\n            }}\r\n          />\r\n        </Box>\r\n      </Paper>\r\n\r\n      {/* Messages Area */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          flex: 1,\r\n          border: '1px solid #f0f0f0',\r\n          borderTop: 'none',\r\n          borderBottom: 'none',\r\n          overflow: 'auto',\r\n          p: 2,\r\n          backgroundColor: '#fff'\r\n        }}\r\n      >\r\n        {conversations.length === 0 ? (\r\n          <Box\r\n            sx={{\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              height: '100%',\r\n              textAlign: 'center'\r\n            }}\r\n          >\r\n            <Avatar\r\n              sx={{\r\n                backgroundColor: `${mainYellowColor}20`,\r\n                width: 60,\r\n                height: 60,\r\n                mb: 2\r\n              }}\r\n            >\r\n              <Iconify icon=\"mdi:robot\" width={32} height={32} color={mainYellowColor} />\r\n            </Avatar>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                color: '#333',\r\n                mb: 1\r\n              }}\r\n            >\r\n              Welcome to AI Project Agent\r\n            </Typography>\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                maxWidth: 400\r\n              }}\r\n            >\r\n              I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\r\n            </Typography>\r\n          </Box>\r\n        ) : (\r\n          <Box>\r\n            {conversations.map((message) => (\r\n              <Box\r\n                key={message.id}\r\n                sx={{\r\n                  display: 'flex',\r\n                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\r\n                  mb: 2\r\n                }}\r\n              >\r\n                <Box\r\n                  sx={{\r\n                    maxWidth: '70%',\r\n                    display: 'flex',\r\n                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\r\n                    alignItems: 'flex-start',\r\n                    gap: 1\r\n                  }}\r\n                >\r\n                  <Avatar\r\n                    sx={{\r\n                      width: 32,\r\n                      height: 32,\r\n                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\r\n                    }}\r\n                  >\r\n                    <Iconify\r\n                      icon={message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\"}\r\n                      width={18}\r\n                      height={18}\r\n                      color={message.type === 'user' ? '#666' : '#fff'}\r\n                    />\r\n                  </Avatar>\r\n                  <Box>\r\n                    <Paper\r\n                      elevation={0}\r\n                      sx={{\r\n                        p: 1.5,\r\n                        borderRadius: '12px',\r\n                        backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\r\n                        color: message.type === 'user' ? '#fff' : '#333',\r\n                        border: message.isError ? '1px solid #f44336' : 'none'\r\n                      }}\r\n                    >\r\n                      <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                          fontFamily: '\"Recursive Variable\", sans-serif',\r\n                          lineHeight: 1.5,\r\n                          whiteSpace: 'pre-line'\r\n                        }}\r\n                      >\r\n                        {message.content}\r\n                      </Typography>\r\n\r\n                      {/* Quick Action Buttons for AI responses */}\r\n                      {message.type === 'assistant' && message.actions && message.actions.length > 0 && (\r\n                        <Box sx={{ mt: 1.5, display: 'flex', flexWrap: 'wrap', gap: 1 }}>\r\n                          {message.actions.slice(0, 3).map((action, actionIndex) => (\r\n                            <Chip\r\n                              key={actionIndex}\r\n                              label={action.description}\r\n                              size=\"small\"\r\n                              onClick={() => {\r\n                                // Handle quick action\r\n                                setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);\r\n                                if (inputRef.current) {\r\n                                  inputRef.current.focus();\r\n                                }\r\n                              }}\r\n                              sx={{\r\n                                backgroundColor: '#fff',\r\n                                border: `1px solid ${mainYellowColor}`,\r\n                                color: mainYellowColor,\r\n                                fontSize: '0.7rem',\r\n                                height: '24px',\r\n                                cursor: 'pointer',\r\n                                '&:hover': {\r\n                                  backgroundColor: `${mainYellowColor}10`\r\n                                }\r\n                              }}\r\n                            />\r\n                          ))}\r\n                        </Box>\r\n                      )}\r\n                    </Paper>\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        color: '#999',\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        fontSize: '0.7rem',\r\n                        mt: 0.5,\r\n                        display: 'block',\r\n                        textAlign: message.type === 'user' ? 'right' : 'left'\r\n                      }}\r\n                    >\r\n                      {formatTimestamp(message.timestamp)}\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n            ))}\r\n            {isLoading && (\r\n              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>\r\n                  <Avatar\r\n                    sx={{\r\n                      width: 32,\r\n                      height: 32,\r\n                      backgroundColor: mainYellowColor\r\n                    }}\r\n                  >\r\n                    <Iconify icon=\"mdi:robot\" width={18} height={18} color=\"#fff\" />\r\n                  </Avatar>\r\n                  <Paper\r\n                    elevation={0}\r\n                    sx={{\r\n                      p: 1.5,\r\n                      borderRadius: '12px',\r\n                      backgroundColor: '#f5f5f5',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: 1\r\n                    }}\r\n                  >\r\n                    <CircularProgress size={16} sx={{ color: mainYellowColor }} />\r\n                    <Typography\r\n                      variant=\"body2\"\r\n                      sx={{\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        color: '#666'\r\n                      }}\r\n                    >\r\n                      Thinking...\r\n                    </Typography>\r\n                  </Paper>\r\n                </Box>\r\n              </Box>\r\n            )}\r\n            <div ref={messagesEndRef} />\r\n          </Box>\r\n        )}\r\n      </Paper>\r\n\r\n      {/* Input Area */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          p: 2,\r\n          borderRadius: '0 0 12px 12px',\r\n          border: '1px solid #f0f0f0',\r\n          borderTop: 'none'\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\r\n          <TextField\r\n            inputRef={inputRef}\r\n            value={currentMessage}\r\n            onChange={(e) => setCurrentMessage(e.target.value)}\r\n            onKeyPress={handleKeyPress}\r\n            placeholder=\"Ask me anything about your project...\"\r\n            multiline\r\n            maxRows={3}\r\n            fullWidth\r\n            variant=\"outlined\"\r\n            disabled={isLoading}\r\n            sx={{\r\n              '& .MuiOutlinedInput-root': {\r\n                borderRadius: '8px',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }\r\n            }}\r\n          />\r\n          <Tooltip title=\"Send message\">\r\n            <IconButton\r\n              onClick={() => handleSendMessage()}\r\n              disabled={!currentMessage.trim() || isLoading}\r\n              sx={{\r\n                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\r\n                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\r\n                '&:hover': {\r\n                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\r\n                }\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:send\" width={20} height={20} />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Box>\r\n      </Paper>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default AgentTab;\r\n"], "mappings": "AAAA,gDACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,SAAS,CACTC,UAAU,CACVC,MAAM,CACNC,gBAAgB,CAChBC,IAAI,CACJC,OAAO,KACF,eAAe,CACtB,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,OAAO,KAAM,0BAA0B,CAC9C,OAASC,eAAe,CAAEC,MAAM,KAAQ,mBAAmB,CAC3D,OAASC,UAAU,KAAQ,mBAAmB,CAC9C,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,QAAQ,CAAGC,IAAA,EAAgC,IAA/B,CAAEC,QAAQ,CAAEC,YAAa,CAAC,CAAAF,IAAA,CAC1C,KAAM,CAACG,aAAa,CAAEC,gBAAgB,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC4B,cAAc,CAAEC,iBAAiB,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC8B,SAAS,CAAEC,YAAY,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAAgC,cAAc,CAAG9B,MAAM,CAAC,IAAI,CAAC,CACnC,KAAM,CAAA+B,QAAQ,CAAG/B,MAAM,CAAC,IAAI,CAAC,CAC7B,KAAM,EAAIgC,QAAQ,CAAC,CAAGlC,QAAQ,CAAC,IAAI,CAAC,CACpC,KAAM,CAAAmC,QAAQ,CAAGvB,WAAW,CAAC,CAAC,CAE9B;AACAX,SAAS,CAAC,IAAM,KAAAmC,eAAA,CACd,KAAM,CAAAC,kBAAkB,CAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,EAAI,IAAI,CAAC,CAC1F,KAAM,CAAAC,iBAAiB,CAAGL,kBAAkB,CAACM,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACC,MAAM,IAAKrB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,EAAE,EAAC,CACzFnB,gBAAgB,CAACe,iBAAiB,CAAC,CAEnC;AACA,KAAM,CAAAK,cAAc,CAAGP,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,CACpE,GAAIM,cAAc,CAAE,CAClB,GAAI,KAAAC,qBAAA,CACF,KAAM,CAAAC,WAAW,CAAGX,IAAI,CAACC,KAAK,CAACQ,cAAc,CAAC,CAC9C,GAAI,EAAAC,qBAAA,CAAAC,WAAW,CAACzB,QAAQ,UAAAwB,qBAAA,iBAApBA,qBAAA,CAAsBF,EAAE,KAAKtB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,EAAE,EAAE,CAC7CI,iBAAiB,CAACD,WAAW,CAACE,OAAO,CAAC,CACxC,CACA;AACAX,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC,CAClD,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzDb,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC,CAClD,CACF,CAEA;AACA,IAAAhB,eAAA,CAAID,QAAQ,CAACoB,KAAK,UAAAnB,eAAA,WAAdA,eAAA,CAAgBe,OAAO,CAAE,CAC3BD,iBAAiB,CAACf,QAAQ,CAACoB,KAAK,CAACJ,OAAO,CAAC,CAC3C,CACF,CAAC,CAAE,CAAC3B,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,EAAE,CAAEX,QAAQ,CAACoB,KAAK,CAAC,CAAC,CAElC;AACAtD,SAAS,CAAC,IAAM,KAAAuD,qBAAA,CACd,CAAAA,qBAAA,CAAAxB,cAAc,CAACyB,OAAO,UAAAD,qBAAA,iBAAtBA,qBAAA,CAAwBE,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChE,CAAC,CAAE,CAACjC,aAAa,CAAC,CAAC,CAInB,KAAM,CAAAwB,iBAAiB,CAAG,cAAAA,CAAA,CAAwC,IAAjC,CAAAU,WAAW,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAGjC,cAAc,CAC3D,GAAI,CAACgC,WAAW,CAACI,IAAI,CAAC,CAAC,EAAIlC,SAAS,CAAE,OAEtCC,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAkC,WAAW,CAAG,CAClBnB,EAAE,CAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,CACdC,IAAI,CAAE,MAAM,CACZC,OAAO,CAAET,WAAW,CAACI,IAAI,CAAC,CAAC,CAC3BM,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CACpC,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAG,CAAC,GAAG9C,aAAa,CAAEuC,WAAW,CAAC,CACxDtC,gBAAgB,CAAC6C,gBAAgB,CAAC,CAClC3C,iBAAiB,CAAC,EAAE,CAAC,CAErB,GAAI,CACF;AACA,KAAM,CAAA4C,QAAQ,CAAG,KAAM,CAAAxD,KAAK,CAACyD,IAAI,CAC/B,GAAG3D,MAAM,2BAA2B,CACpC,CACEoC,OAAO,CAAES,WAAW,CAACI,IAAI,CAAC,CAAC,CAC3BW,SAAS,CAAEnD,QAAQ,CAACoD,IACtB,CAAC,CACD,CAAEC,OAAO,CAAE7D,UAAU,CAAC,CAAE,CAC1B,CAAC,CAED,KAAM,CAAA8D,cAAc,CAAGL,QAAQ,CAACM,IAAI,CAEpC;AACA,KAAM,CAAAC,UAAU,CAAG,CACjBlC,EAAE,CAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,CAAC,CAClBC,IAAI,CAAE,WAAW,CACjBC,OAAO,CAAES,cAAc,CAAC3B,OAAO,EAAI,gEAAgE,CACnGmB,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,CACnCU,OAAO,CAAEH,cAAc,CAACG,OAAO,EAAI,EAAE,CACrCC,QAAQ,CAAEJ,cAAc,CAACI,QAAQ,EAAI,CAAC,CACxC,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAG,CAAC,GAAGX,gBAAgB,CAAEQ,UAAU,CAAC,CAC9DrD,gBAAgB,CAACwD,oBAAoB,CAAC,CAEtC;AACA,GAAIL,cAAc,CAACG,OAAO,EAAIH,cAAc,CAACG,OAAO,CAACnB,MAAM,CAAG,CAAC,CAAE,CAC/D,GAAI,CAAAsB,aAAa,CAAG,EAAE,CACtB,IAAK,KAAM,CAAAC,MAAM,GAAI,CAAAP,cAAc,CAACG,OAAO,CAAE,CAC3C,GAAI,CACF,KAAM,CAAAK,MAAM,CAAG,KAAM,CAAAC,eAAe,CAACF,MAAM,CAAC,CAC5CD,aAAa,CAACI,IAAI,CAACF,MAAM,CAAC,CAC5B,CAAE,MAAOjC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD+B,aAAa,CAACI,IAAI,CAAC,CACjBC,OAAO,CAAE,KAAK,CACdpC,KAAK,CAAEA,KAAK,CAACF,OAAO,EAAI,eAC1B,CAAC,CAAC,CACJ,CACF,CAEA;AACA,GAAIiC,aAAa,CAACM,IAAI,CAACJ,MAAM,EAAIA,MAAM,CAACG,OAAO,CAAC,EAAIhE,YAAY,CAAE,CAChEA,YAAY,CAAC,CAAC,CAChB,CAEA;AACA,KAAM,CAAAkE,aAAa,CAAGP,aAAa,CAACzC,MAAM,CAAC2C,MAAM,EAAI,CAACA,MAAM,CAACG,OAAO,CAAC,CACrE,GAAIE,aAAa,CAAC7B,MAAM,CAAG,CAAC,CAAE,CAC5B,KAAM,CAAA8B,YAAY,CAAG,CACnB9C,EAAE,CAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,CAAC,CAClBC,IAAI,CAAE,WAAW,CACjBC,OAAO,CAAE,qEAAqEsB,aAAa,CAACE,GAAG,CAACP,MAAM,EAAI,KAAKA,MAAM,CAACjC,KAAK,EAAE,CAAC,CAACyC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC3IxB,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,CACnCwB,OAAO,CAAE,IACX,CAAC,CACDpE,gBAAgB,CAACqE,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEJ,YAAY,CAAC,CAAC,CACnD,CACF,CAEA;AACA,KAAM,CAAAK,gBAAgB,CAAG3D,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,EAAI,IAAI,CAAC,CACxF,KAAM,CAAAyD,sBAAsB,CAAGD,gBAAgB,CAACtD,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACC,MAAM,IAAKrB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,EAAE,EAAC,CAC5F,KAAM,CAAAJ,iBAAiB,CAAGyC,oBAAoB,CAACU,GAAG,CAACjD,IAAI,GAAK,CAC1D,GAAGA,IAAI,CACPC,MAAM,CAAErB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,EAAE,CACpBqD,QAAQ,CAAE3E,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE4E,IACtB,CAAC,CAAC,CAAC,CAEH5D,YAAY,CAAC6D,OAAO,CAAC,qBAAqB,CAAE/D,IAAI,CAACgE,SAAS,CAAC,CACzD,GAAGJ,sBAAsB,CACzB,GAAGxD,iBAAiB,CACrB,CAAC,CAAC,CAEL,CAAE,MAAOW,KAAK,CAAE,KAAAkD,eAAA,CAAAC,oBAAA,CACdlD,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,KAAM,CAAAoD,aAAa,CAAG,CACpB3D,EAAE,CAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,CAAC,CAClBC,IAAI,CAAE,WAAW,CACjBC,OAAO,CAAE,gEAAgE,EAAAkC,eAAA,CAAAlD,KAAK,CAACoB,QAAQ,UAAA8B,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBxB,IAAI,UAAAyB,oBAAA,iBAApBA,oBAAA,CAAsBnD,KAAK,GAAIA,KAAK,CAACF,OAAO,EAAI,eAAe,qBAAqB,CAC7JmB,SAAS,CAAE,GAAI,CAAAJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,CACnCwB,OAAO,CAAE,IACX,CAAC,CACDpE,gBAAgB,CAAC,CAAC,GAAG6C,gBAAgB,CAAEiC,aAAa,CAAC,CAAC,CACtDvE,QAAQ,CAACmB,KAAK,CAACF,OAAO,EAAI,wBAAwB,CAAC,CACrD,CAAC,OAAS,CACRpB,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAwD,eAAe,CAAG,KAAO,CAAAF,MAAM,EAAK,CACxC,GAAI,CACF,KAAM,CAAAZ,QAAQ,CAAG,KAAM,CAAAxD,KAAK,CAACyD,IAAI,CAC/B,GAAG3D,MAAM,4BAA4B,CACrC,CACEsE,MAAM,CAAEA,MAAM,CAACjB,IAAI,CACnBO,SAAS,CAAEnD,QAAQ,CAACoD,IAAI,CACxBG,IAAI,CAAEM,MAAM,CAACN,IAAI,EAAI,CAAC,CAAC,CACvB5B,OAAO,CAAE,wBAAwBkC,MAAM,CAACjB,IAAI,EAC9C,CAAC,CACD,CAAES,OAAO,CAAE7D,UAAU,CAAC,CAAE,CAC1B,CAAC,CAED,MAAO,CACLyE,OAAO,CAAE,IAAI,CACbV,IAAI,CAAEN,QAAQ,CAACM,IAAI,CACnBM,MAAM,CAAEA,MACV,CAAC,CACH,CAAE,MAAOhC,KAAK,CAAE,KAAAqD,gBAAA,CAAAC,qBAAA,CACdrD,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,MAAO,CACLoC,OAAO,CAAE,KAAK,CACdpC,KAAK,CAAE,EAAAqD,gBAAA,CAAArD,KAAK,CAACoB,QAAQ,UAAAiC,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB3B,IAAI,UAAA4B,qBAAA,iBAApBA,qBAAA,CAAsBtD,KAAK,GAAIA,KAAK,CAACF,OAAO,EAAI,eAAe,CACtEkC,MAAM,CAAEA,MACV,CAAC,CACH,CACF,CAAC,CAID,KAAM,CAAAuB,cAAc,CAAIC,CAAC,EAAK,CAC5B,GAAIA,CAAC,CAACC,GAAG,GAAK,OAAO,EAAI,CAACD,CAAC,CAACE,QAAQ,CAAE,CACpCF,CAAC,CAACG,cAAc,CAAC,CAAC,CAClB9D,iBAAiB,CAAC,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAA+D,eAAe,CAAI3C,SAAS,EAAK,CACrC,MAAO,IAAI,CAAAJ,IAAI,CAACI,SAAS,CAAC,CAAC4C,kBAAkB,CAAC,OAAO,CAAE,CACrDC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CACJ,CAAC,CAED,mBACE/F,KAAA,CAAClB,GAAG,EAACkH,EAAE,CAAE,CAAEC,MAAM,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAS,CAAE,CAAAC,QAAA,eAEpEtG,IAAA,CAACd,KAAK,EACJqH,SAAS,CAAE,CAAE,CACbL,EAAE,CAAE,CACFM,CAAC,CAAE,CAAC,CACJC,YAAY,CAAE,eAAe,CAC7BC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,MAAM,CACpBC,eAAe,CAAE,SACnB,CAAE,CAAAN,QAAA,cAEFpG,KAAA,CAAClB,GAAG,EAACkH,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAES,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAR,QAAA,eACzDtG,IAAA,CAACX,MAAM,EACL6G,EAAE,CAAE,CACFU,eAAe,CAAEjH,eAAe,CAChCoH,KAAK,CAAE,EAAE,CACTZ,MAAM,CAAE,EACV,CAAE,CAAAG,QAAA,cAEFtG,IAAA,CAACN,OAAO,EAACsH,IAAI,CAAC,WAAW,CAACD,KAAK,CAAE,EAAG,CAACZ,MAAM,CAAE,EAAG,CAACc,KAAK,CAAC,MAAM,CAAE,CAAC,CAC1D,CAAC,cACT/G,KAAA,CAAClB,GAAG,EAAAsH,QAAA,eACFtG,IAAA,CAACf,UAAU,EACTiI,OAAO,CAAC,IAAI,CACZhB,EAAE,CAAE,CACFiB,UAAU,CAAE,kCAAkC,CAC9CC,UAAU,CAAE,GAAG,CACfH,KAAK,CAAE,MACT,CAAE,CAAAX,QAAA,CACH,kBAED,CAAY,CAAC,cACbpG,KAAA,CAACjB,UAAU,EACTiI,OAAO,CAAC,SAAS,CACjBhB,EAAE,CAAE,CACFe,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,kCACd,CAAE,CAAAb,QAAA,EACH,YACW,CAACjG,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE4E,IAAI,EACf,CAAC,EACV,CAAC,cACNjF,IAAA,CAACT,IAAI,EACH8H,KAAK,CAAC,MAAM,CACZC,IAAI,CAAC,OAAO,CACZpB,EAAE,CAAE,CACFU,eAAe,CAAE,GAAGjH,eAAe,IAAI,CACvCsH,KAAK,CAAEtH,eAAe,CACtByH,UAAU,CAAE,GAAG,CACfG,EAAE,CAAE,MACN,CAAE,CACH,CAAC,EACC,CAAC,CACD,CAAC,cAGRvH,IAAA,CAACd,KAAK,EACJqH,SAAS,CAAE,CAAE,CACbL,EAAE,CAAE,CACFsB,IAAI,CAAE,CAAC,CACPd,MAAM,CAAE,mBAAmB,CAC3Be,SAAS,CAAE,MAAM,CACjBd,YAAY,CAAE,MAAM,CACpBe,QAAQ,CAAE,MAAM,CAChBlB,CAAC,CAAE,CAAC,CACJI,eAAe,CAAE,MACnB,CAAE,CAAAN,QAAA,CAED/F,aAAa,CAACoC,MAAM,GAAK,CAAC,cACzBzC,KAAA,CAAClB,GAAG,EACFkH,EAAE,CAAE,CACFE,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBQ,UAAU,CAAE,QAAQ,CACpBc,cAAc,CAAE,QAAQ,CACxBxB,MAAM,CAAE,MAAM,CACdyB,SAAS,CAAE,QACb,CAAE,CAAAtB,QAAA,eAEFtG,IAAA,CAACX,MAAM,EACL6G,EAAE,CAAE,CACFU,eAAe,CAAE,GAAGjH,eAAe,IAAI,CACvCoH,KAAK,CAAE,EAAE,CACTZ,MAAM,CAAE,EAAE,CACV0B,EAAE,CAAE,CACN,CAAE,CAAAvB,QAAA,cAEFtG,IAAA,CAACN,OAAO,EAACsH,IAAI,CAAC,WAAW,CAACD,KAAK,CAAE,EAAG,CAACZ,MAAM,CAAE,EAAG,CAACc,KAAK,CAAEtH,eAAgB,CAAE,CAAC,CACrE,CAAC,cACTK,IAAA,CAACf,UAAU,EACTiI,OAAO,CAAC,IAAI,CACZhB,EAAE,CAAE,CACFiB,UAAU,CAAE,kCAAkC,CAC9CC,UAAU,CAAE,GAAG,CACfH,KAAK,CAAE,MAAM,CACbY,EAAE,CAAE,CACN,CAAE,CAAAvB,QAAA,CACH,6BAED,CAAY,CAAC,cACbtG,IAAA,CAACf,UAAU,EACTiI,OAAO,CAAC,OAAO,CACfhB,EAAE,CAAE,CACFe,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,kCAAkC,CAC9CW,QAAQ,CAAE,GACZ,CAAE,CAAAxB,QAAA,CACH,+GAED,CAAY,CAAC,EACV,CAAC,cAENpG,KAAA,CAAClB,GAAG,EAAAsH,QAAA,EACD/F,aAAa,CAACmE,GAAG,CAAE1C,OAAO,eACzBhC,IAAA,CAAChB,GAAG,EAEFkH,EAAE,CAAE,CACFE,OAAO,CAAE,MAAM,CACfuB,cAAc,CAAE3F,OAAO,CAACiB,IAAI,GAAK,MAAM,CAAG,UAAU,CAAG,YAAY,CACnE4E,EAAE,CAAE,CACN,CAAE,CAAAvB,QAAA,cAEFpG,KAAA,CAAClB,GAAG,EACFkH,EAAE,CAAE,CACF4B,QAAQ,CAAE,KAAK,CACf1B,OAAO,CAAE,MAAM,CACfC,aAAa,CAAErE,OAAO,CAACiB,IAAI,GAAK,MAAM,CAAG,aAAa,CAAG,KAAK,CAC9D4D,UAAU,CAAE,YAAY,CACxBC,GAAG,CAAE,CACP,CAAE,CAAAR,QAAA,eAEFtG,IAAA,CAACX,MAAM,EACL6G,EAAE,CAAE,CACFa,KAAK,CAAE,EAAE,CACTZ,MAAM,CAAE,EAAE,CACVS,eAAe,CAAE5E,OAAO,CAACiB,IAAI,GAAK,MAAM,CAAG,SAAS,CAAGtD,eACzD,CAAE,CAAA2G,QAAA,cAEFtG,IAAA,CAACN,OAAO,EACNsH,IAAI,CAAEhF,OAAO,CAACiB,IAAI,GAAK,MAAM,CAAG,yBAAyB,CAAG,WAAY,CACxE8D,KAAK,CAAE,EAAG,CACVZ,MAAM,CAAE,EAAG,CACXc,KAAK,CAAEjF,OAAO,CAACiB,IAAI,GAAK,MAAM,CAAG,MAAM,CAAG,MAAO,CAClD,CAAC,CACI,CAAC,cACT/C,KAAA,CAAClB,GAAG,EAAAsH,QAAA,eACFpG,KAAA,CAAChB,KAAK,EACJqH,SAAS,CAAE,CAAE,CACbL,EAAE,CAAE,CACFM,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,MAAM,CACpBG,eAAe,CAAE5E,OAAO,CAACiB,IAAI,GAAK,MAAM,CAAGtD,eAAe,CAAG,SAAS,CACtEsH,KAAK,CAAEjF,OAAO,CAACiB,IAAI,GAAK,MAAM,CAAG,MAAM,CAAG,MAAM,CAChDyD,MAAM,CAAE1E,OAAO,CAAC4C,OAAO,CAAG,mBAAmB,CAAG,MAClD,CAAE,CAAA0B,QAAA,eAEFtG,IAAA,CAACf,UAAU,EACTiI,OAAO,CAAC,OAAO,CACfhB,EAAE,CAAE,CACFiB,UAAU,CAAE,kCAAkC,CAC9CY,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE,UACd,CAAE,CAAA1B,QAAA,CAEDtE,OAAO,CAACkB,OAAO,CACN,CAAC,CAGZlB,OAAO,CAACiB,IAAI,GAAK,WAAW,EAAIjB,OAAO,CAAC8B,OAAO,EAAI9B,OAAO,CAAC8B,OAAO,CAACnB,MAAM,CAAG,CAAC,eAC5E3C,IAAA,CAAChB,GAAG,EAACkH,EAAE,CAAE,CAAE+B,EAAE,CAAE,GAAG,CAAE7B,OAAO,CAAE,MAAM,CAAE8B,QAAQ,CAAE,MAAM,CAAEpB,GAAG,CAAE,CAAE,CAAE,CAAAR,QAAA,CAC7DtE,OAAO,CAAC8B,OAAO,CAACqE,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACzD,GAAG,CAAC,CAACR,MAAM,CAAEkE,WAAW,gBACnDpI,IAAA,CAACT,IAAI,EAEH8H,KAAK,CAAEnD,MAAM,CAACmE,WAAY,CAC1Bf,IAAI,CAAC,OAAO,CACZgB,OAAO,CAAEA,CAAA,GAAM,CACb;AACA5H,iBAAiB,CAACwD,MAAM,CAACqE,eAAe,EAAI,UAAUrE,MAAM,CAACmE,WAAW,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,CACzF,GAAI1H,QAAQ,CAACwB,OAAO,CAAE,CACpBxB,QAAQ,CAACwB,OAAO,CAACmG,KAAK,CAAC,CAAC,CAC1B,CACF,CAAE,CACFvC,EAAE,CAAE,CACFU,eAAe,CAAE,MAAM,CACvBF,MAAM,CAAE,aAAa/G,eAAe,EAAE,CACtCsH,KAAK,CAAEtH,eAAe,CACtB+I,QAAQ,CAAE,QAAQ,CAClBvC,MAAM,CAAE,MAAM,CACdwC,MAAM,CAAE,SAAS,CACjB,SAAS,CAAE,CACT/B,eAAe,CAAE,GAAGjH,eAAe,IACrC,CACF,CAAE,EApBGyI,WAqBN,CACF,CAAC,CACC,CACN,EACI,CAAC,cACRpI,IAAA,CAACf,UAAU,EACTiI,OAAO,CAAC,SAAS,CACjBhB,EAAE,CAAE,CACFe,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,kCAAkC,CAC9CuB,QAAQ,CAAE,QAAQ,CAClBT,EAAE,CAAE,GAAG,CACP7B,OAAO,CAAE,OAAO,CAChBwB,SAAS,CAAE5F,OAAO,CAACiB,IAAI,GAAK,MAAM,CAAG,OAAO,CAAG,MACjD,CAAE,CAAAqD,QAAA,CAEDR,eAAe,CAAC9D,OAAO,CAACmB,SAAS,CAAC,CACzB,CAAC,EACV,CAAC,EACH,CAAC,EAjGDnB,OAAO,CAACL,EAkGV,CACN,CAAC,CACDhB,SAAS,eACRX,IAAA,CAAChB,GAAG,EAACkH,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEuB,cAAc,CAAE,YAAY,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAvB,QAAA,cAChEpG,KAAA,CAAClB,GAAG,EAACkH,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAES,UAAU,CAAE,YAAY,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAR,QAAA,eAC7DtG,IAAA,CAACX,MAAM,EACL6G,EAAE,CAAE,CACFa,KAAK,CAAE,EAAE,CACTZ,MAAM,CAAE,EAAE,CACVS,eAAe,CAAEjH,eACnB,CAAE,CAAA2G,QAAA,cAEFtG,IAAA,CAACN,OAAO,EAACsH,IAAI,CAAC,WAAW,CAACD,KAAK,CAAE,EAAG,CAACZ,MAAM,CAAE,EAAG,CAACc,KAAK,CAAC,MAAM,CAAE,CAAC,CAC1D,CAAC,cACT/G,KAAA,CAAChB,KAAK,EACJqH,SAAS,CAAE,CAAE,CACbL,EAAE,CAAE,CACFM,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,MAAM,CACpBG,eAAe,CAAE,SAAS,CAC1BR,OAAO,CAAE,MAAM,CACfS,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,CACP,CAAE,CAAAR,QAAA,eAEFtG,IAAA,CAACV,gBAAgB,EAACgI,IAAI,CAAE,EAAG,CAACpB,EAAE,CAAE,CAAEe,KAAK,CAAEtH,eAAgB,CAAE,CAAE,CAAC,cAC9DK,IAAA,CAACf,UAAU,EACTiI,OAAO,CAAC,OAAO,CACfhB,EAAE,CAAE,CACFiB,UAAU,CAAE,kCAAkC,CAC9CF,KAAK,CAAE,MACT,CAAE,CAAAX,QAAA,CACH,aAED,CAAY,CAAC,EACR,CAAC,EACL,CAAC,CACH,CACN,cACDtG,IAAA,QAAK4I,GAAG,CAAE/H,cAAe,CAAE,CAAC,EACzB,CACN,CACI,CAAC,cAGRb,IAAA,CAACd,KAAK,EACJqH,SAAS,CAAE,CAAE,CACbL,EAAE,CAAE,CACFM,CAAC,CAAE,CAAC,CACJC,YAAY,CAAE,eAAe,CAC7BC,MAAM,CAAE,mBAAmB,CAC3Be,SAAS,CAAE,MACb,CAAE,CAAAnB,QAAA,cAEFpG,KAAA,CAAClB,GAAG,EAACkH,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEU,GAAG,CAAE,CAAC,CAAED,UAAU,CAAE,UAAW,CAAE,CAAAP,QAAA,eAC3DtG,IAAA,CAACb,SAAS,EACR2B,QAAQ,CAAEA,QAAS,CACnB+H,KAAK,CAAEpI,cAAe,CACtBqI,QAAQ,CAAGpD,CAAC,EAAKhF,iBAAiB,CAACgF,CAAC,CAACqD,MAAM,CAACF,KAAK,CAAE,CACnDG,UAAU,CAAEvD,cAAe,CAC3BwD,WAAW,CAAC,uCAAuC,CACnDC,SAAS,MACTC,OAAO,CAAE,CAAE,CACXC,SAAS,MACTlC,OAAO,CAAC,UAAU,CAClBmC,QAAQ,CAAE1I,SAAU,CACpBuF,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BO,YAAY,CAAE,KAAK,CACnBU,UAAU,CAAE,kCACd,CACF,CAAE,CACH,CAAC,cACFnH,IAAA,CAACR,OAAO,EAAC8J,KAAK,CAAC,cAAc,CAAAhD,QAAA,cAC3BtG,IAAA,CAACZ,UAAU,EACTkJ,OAAO,CAAEA,CAAA,GAAMvG,iBAAiB,CAAC,CAAE,CACnCsH,QAAQ,CAAE,CAAC5I,cAAc,CAACoC,IAAI,CAAC,CAAC,EAAIlC,SAAU,CAC9CuF,EAAE,CAAE,CACFU,eAAe,CAAEnG,cAAc,CAACoC,IAAI,CAAC,CAAC,EAAI,CAAClC,SAAS,CAAGhB,eAAe,CAAG,SAAS,CAClFsH,KAAK,CAAExG,cAAc,CAACoC,IAAI,CAAC,CAAC,EAAI,CAAClC,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5D,SAAS,CAAE,CACTiG,eAAe,CAAEnG,cAAc,CAACoC,IAAI,CAAC,CAAC,EAAI,CAAClC,SAAS,CAAG,SAAS,CAAG,SACrE,CACF,CAAE,CAAA2F,QAAA,cAEFtG,IAAA,CAACN,OAAO,EAACsH,IAAI,CAAC,uBAAuB,CAACD,KAAK,CAAE,EAAG,CAACZ,MAAM,CAAE,EAAG,CAAE,CAAC,CACrD,CAAC,CACN,CAAC,EACP,CAAC,CACD,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
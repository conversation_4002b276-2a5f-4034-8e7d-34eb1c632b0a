# Generated by Django 4.2.13 on 2024-06-09 02:56

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('plans', '0010_milestone_created_at_plan_created_at_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='plan',
            name='priority',
            field=models.IntegerField(choices=[(1, 'Low'), (2, 'Medium'), (3, 'High'), (4, 'Critical'), (5, 'In Progress')], default=1),
        ),
        migrations.AddField(
            model_name='task',
            name='assignees',
            field=models.ManyToManyField(blank=True, related_name='assigned_task', to=settings.AUTH_USER_MODEL),
        ),
    ]

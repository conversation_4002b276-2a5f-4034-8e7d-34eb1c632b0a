{"ast": null, "code": "import React,{useEffect,useRef}from'react';import{Box}from'@mui/material';import Matter from'matter-js';import styles from'./styles.module.scss';/**\r\n * Component displays loading screen with a bouncing ball inside a rotating hexagon\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page\r\n * @returns {JSX.Element} - HexagonBallLoading component\r\n */import{jsx as _jsx}from\"react/jsx-runtime\";const HexagonBallLoading=_ref=>{let{fromCreatePlan}=_ref;const canvasRef=useRef(null);const engineRef=useRef(null);const requestRef=useRef(null);const ballRef=useRef(null);const hexagonEdgesRef=useRef([]);useEffect(()=>{// Optimized physics parameters for 60fps performance\nconst rotationSpeed=0.008;// Optimized rotation speed\nconst gravity=0.0008;// Increased gravity for more realistic physics\nconst ballRestitution=0.85;// Realistic bounce with slight energy loss\nconst wallRestitution=0.1;// Walls absorb some energy\n// Initialize Matter.js modules\nconst Engine=Matter.Engine;const Render=Matter.Render;const World=Matter.World;const Bodies=Matter.Bodies;const Body=Matter.Body;// Create engine with optimized physics settings\nengineRef.current=Engine.create({gravity:{x:0,y:gravity,scale:1}});// Optimize timing for consistent 60fps performance\nengineRef.current.timing.timeScale=1;engineRef.current.positionIterations=6;// Reduced for performance\nengineRef.current.velocityIterations=4;// Reduced for performance\nengineRef.current.constraintIterations=2;// Reduced for performance\n// Create renderer\nconst render=Render.create({canvas:canvasRef.current,engine:engineRef.current,options:{width:352,height:352,wireframes:false,background:'#ffffff',showAngleIndicator:false,showCollisions:false,showVelocity:false}});// Create hexagon\nconst hexagonRadius=132;const hexagonSides=6;const centerX=render.options.width/2;const centerY=render.options.height/2;// Create hexagon vertices\nconst hexagonVertices=[];for(let i=0;i<hexagonSides;i++){const angle=Math.PI*2*i/hexagonSides;const x=hexagonRadius*Math.cos(angle);const y=hexagonRadius*Math.sin(angle);hexagonVertices.push({x,y});}// Create hexagon edges with realistic physics\nhexagonEdgesRef.current=[];for(let i=0;i<hexagonSides;i++){const j=(i+1)%hexagonSides;const edgeOptions={restitution:wallRestitution,// Use realistic wall bounce\nfriction:0.05,// Reduced friction for smoother movement\nfrictionStatic:0.1,isStatic:true,render:{fillStyle:'transparent',strokeStyle:'#0f0f0f',lineWidth:2// Slightly thicker for better visibility\n}};// Calculate edge position and angle\nconst vertex1=hexagonVertices[i];const vertex2=hexagonVertices[j];const edgeLength=Math.sqrt(Math.pow(vertex2.x-vertex1.x,2)+Math.pow(vertex2.y-vertex1.y,2));const edgeAngle=Math.atan2(vertex2.y-vertex1.y,vertex2.x-vertex1.x);const edgeCenterX=centerX+(vertex1.x+vertex2.x)/2;const edgeCenterY=centerY+(vertex1.y+vertex2.y)/2;// Create edge\nconst edge=Bodies.rectangle(edgeCenterX,edgeCenterY,edgeLength,1,edgeOptions);// Rotate edge to angle\nBody.rotate(edge,edgeAngle);hexagonEdgesRef.current.push(edge);}// Create ball with corrected collision detection\nconst ballRadius=12;// Fixed radius that matches visual size\n// Start ball at a random position within the hexagon for variety\nconst startAngle=Math.random()*Math.PI*2;const startDistance=hexagonRadius*0.25;// Start closer to center\nconst startX=centerX+Math.cos(startAngle)*startDistance;const startY=centerY+Math.sin(startAngle)*startDistance;ballRef.current=Bodies.circle(startX,startY,ballRadius,{restitution:ballRestitution,// Realistic bounce with energy loss\nfriction:0.01,// Very low friction for smooth rolling\nfrictionAir:0.001,// Minimal air resistance for realistic movement\ndensity:0.003,// Optimized density for better physics\ninertia:Infinity,// Prevent rotation for cleaner movement\nrender:{fillStyle:'#F0A500',strokeStyle:'#E69500',lineWidth:2}});// Add realistic initial velocity\nconst initialSpeed=0.8;const initialAngle=Math.random()*Math.PI*2;Body.setVelocity(ballRef.current,{x:Math.cos(initialAngle)*initialSpeed,y:Math.sin(initialAngle)*initialSpeed});// Add bodies to world\nWorld.add(engineRef.current.world,[...hexagonEdgesRef.current,ballRef.current]);// Run renderer\nRender.run(render);// Optimized animation loop for 60fps performance\nlet lastTime=performance.now();const targetFPS=60;const frameTime=1000/targetFPS;const animate=currentTime=>{const deltaTime=currentTime-lastTime;// Only update if enough time has passed (throttle to 60fps)\nif(deltaTime>=frameTime){// Update engine with consistent timing\nEngine.update(engineRef.current,16.667);// Optimized hexagon rotation with reduced calculations\nif(hexagonEdgesRef.current.length>0){const cosRotation=Math.cos(rotationSpeed);const sinRotation=Math.sin(rotationSpeed);hexagonEdgesRef.current.forEach(edge=>{// Calculate new position after rotation (optimized)\nconst currentX=edge.position.x-centerX;const currentY=edge.position.y-centerY;const newX=currentX*cosRotation-currentY*sinRotation;const newY=currentX*sinRotation+currentY*cosRotation;Body.setPosition(edge,{x:centerX+newX,y:centerY+newY});Body.rotate(edge,rotationSpeed);});}lastTime=currentTime;}// Optimized ball physics management with correct collision detection\nif(ballRef.current){const ballPos=ballRef.current.position;const velocity=ballRef.current.velocity;const speed=Math.sqrt(velocity.x*velocity.x+velocity.y*velocity.y);// Calculate distance from ball center to hexagon center\nconst distanceFromCenter=Math.sqrt(Math.pow(ballPos.x-centerX,2)+Math.pow(ballPos.y-centerY,2));// Corrected boundary checking - account for actual ball radius\nconst maxDistance=hexagonRadius-ballRadius-8;// Proper collision boundary\nif(distanceFromCenter>maxDistance){// Calculate normalized direction vector (optimized)\nconst directionX=ballPos.x-centerX;const directionY=ballPos.y-centerY;const magnitude=Math.sqrt(directionX*directionX+directionY*directionY);if(magnitude>0){// Prevent division by zero\nconst normalizedX=directionX/magnitude;const normalizedY=directionY/magnitude;// Position ball at correct boundary\nconst newPosX=centerX+normalizedX*maxDistance;const newPosY=centerY+normalizedY*maxDistance;Body.setPosition(ballRef.current,{x:newPosX,y:newPosY});// Improved bounce physics with proper reflection\nconst currentVelocity=ballRef.current.velocity;const dotProduct=currentVelocity.x*normalizedX+currentVelocity.y*normalizedY;if(dotProduct>0){// Reflect velocity with realistic energy loss\nconst reflectedVelX=currentVelocity.x-2*dotProduct*normalizedX;const reflectedVelY=currentVelocity.y-2*dotProduct*normalizedY;Body.setVelocity(ballRef.current,{x:reflectedVelX*ballRestitution,y:reflectedVelY*ballRestitution});}}}// Optimized energy maintenance\nif(speed<0.5){// Add subtle random impulse to keep ball moving\nconst impulseAngle=Math.random()*Math.PI*2;const impulseMagnitude=0.2;Body.applyForce(ballRef.current,ballRef.current.position,{x:Math.cos(impulseAngle)*impulseMagnitude*0.0008,y:Math.sin(impulseAngle)*impulseMagnitude*0.0008});}// Limit maximum speed for smooth movement\nif(speed>5){const limitedVelX=velocity.x/speed*5;const limitedVelY=velocity.y/speed*5;Body.setVelocity(ballRef.current,{x:limitedVelX,y:limitedVelY});}}// Continue animation loop\nrequestRef.current=requestAnimationFrame(animate);};// Start optimized animation loop\nrequestRef.current=requestAnimationFrame(animate);// Cleanup when component unmounts\nreturn()=>{// Cancel animation loop\nif(requestRef.current){cancelAnimationFrame(requestRef.current);}// Cleanup renderer and engine\nRender.stop(render);World.clear(engineRef.current.world);Engine.clear(engineRef.current);render.canvas=null;render.context=null;render.textures={};};},[]);return/*#__PURE__*/_jsx(Box,{className:styles.loadingContainer,sx:{...(!fromCreatePlan&&{minHeight:'90vh'})},children:/*#__PURE__*/_jsx(Box,{className:styles.gameWrapper,children:/*#__PURE__*/_jsx(Box,{className:styles.hexagonLoadingContainer,children:/*#__PURE__*/_jsx(\"canvas\",{ref:canvasRef,className:styles.hexagonCanvas})})})});};export default HexagonBallLoading;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "Matter", "styles", "jsx", "_jsx", "HexagonBallLoading", "_ref", "fromCreatePlan", "canvasRef", "engineRef", "requestRef", "ballRef", "hexagonEdgesRef", "rotationSpeed", "gravity", "ballRestitution", "wallRestitution", "Engine", "Render", "World", "Bodies", "Body", "current", "create", "x", "y", "scale", "timing", "timeScale", "positionIterations", "velocityIterations", "constraintIterations", "render", "canvas", "engine", "options", "width", "height", "wireframes", "background", "showAngleIndicator", "showCollisions", "showVelocity", "hexagonRadius", "hexagonSides", "centerX", "centerY", "hexagonVertices", "i", "angle", "Math", "PI", "cos", "sin", "push", "j", "edgeOptions", "restitution", "friction", "frictionStatic", "isStatic", "fillStyle", "strokeStyle", "lineWidth", "vertex1", "vertex2", "edge<PERSON><PERSON><PERSON>", "sqrt", "pow", "edgeAngle", "atan2", "edgeCenterX", "edgeCenterY", "edge", "rectangle", "rotate", "ballRadius", "startAngle", "random", "startDistance", "startX", "startY", "circle", "frictionAir", "density", "inertia", "Infinity", "initialSpeed", "initialAngle", "setVelocity", "add", "world", "run", "lastTime", "performance", "now", "targetFPS", "frameTime", "animate", "currentTime", "deltaTime", "update", "length", "cosRotation", "sinRotation", "for<PERSON>ach", "currentX", "position", "currentY", "newX", "newY", "setPosition", "ballPos", "velocity", "speed", "distanceFromCenter", "maxDistance", "directionX", "directionY", "magnitude", "normalizedX", "normalizedY", "newPosX", "newPosY", "currentVelocity", "dotProduct", "reflectedVelX", "reflectedVelY", "impulseAngle", "impulseMagnitude", "applyForce", "limitedVelX", "limitedVelY", "requestAnimationFrame", "cancelAnimationFrame", "stop", "clear", "context", "textures", "className", "loadingContainer", "sx", "minHeight", "children", "gameWrapper", "hexagonLoadingContainer", "ref", "hexagonCanvas"], "sources": ["C:/ignition/ignition-ui/src/components/Loading/HexagonBallLoading.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\nimport { Box } from '@mui/material';\r\nimport Matter from 'matter-js';\r\nimport styles from './styles.module.scss';\r\n\r\n/**\r\n * Component displays loading screen with a bouncing ball inside a rotating hexagon\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page\r\n * @returns {JSX.Element} - HexagonBallLoading component\r\n */\r\nconst HexagonBallLoading = ({ fromCreatePlan }) => {\r\n  const canvasRef = useRef(null);\r\n  const engineRef = useRef(null);\r\n  const requestRef = useRef(null);\r\n  const ballRef = useRef(null);\r\n  const hexagonEdgesRef = useRef([]);\r\n\r\n  useEffect(() => {\r\n    // Optimized physics parameters for 60fps performance\r\n    const rotationSpeed = 0.008; // Optimized rotation speed\r\n    const gravity = 0.0008; // Increased gravity for more realistic physics\r\n    const ballRestitution = 0.85; // Realistic bounce with slight energy loss\r\n    const wallRestitution = 0.1; // Walls absorb some energy\r\n\r\n    // Initialize Matter.js modules\r\n    const Engine = Matter.Engine;\r\n    const Render = Matter.Render;\r\n    const World = Matter.World;\r\n    const Bodies = Matter.Bodies;\r\n    const Body = Matter.Body;\r\n\r\n    // Create engine with optimized physics settings\r\n    engineRef.current = Engine.create({\r\n      gravity: { x: 0, y: gravity, scale: 1 },\r\n    });\r\n\r\n    // Optimize timing for consistent 60fps performance\r\n    engineRef.current.timing.timeScale = 1;\r\n    engineRef.current.positionIterations = 6; // Reduced for performance\r\n    engineRef.current.velocityIterations = 4; // Reduced for performance\r\n    engineRef.current.constraintIterations = 2; // Reduced for performance\r\n\r\n    // Create renderer\r\n    const render = Render.create({\r\n      canvas: canvasRef.current,\r\n      engine: engineRef.current,\r\n      options: {\r\n        width: 352,\r\n        height: 352,\r\n        wireframes: false,\r\n        background: '#ffffff',\r\n        showAngleIndicator: false,\r\n        showCollisions: false,\r\n        showVelocity: false,\r\n      },\r\n    });\r\n\r\n    // Create hexagon\r\n    const hexagonRadius = 132;\r\n    const hexagonSides = 6;\r\n    const centerX = render.options.width / 2;\r\n    const centerY = render.options.height / 2;\r\n\r\n    // Create hexagon vertices\r\n    const hexagonVertices = [];\r\n    for (let i = 0; i < hexagonSides; i++) {\r\n      const angle = (Math.PI * 2 * i) / hexagonSides;\r\n      const x = hexagonRadius * Math.cos(angle);\r\n      const y = hexagonRadius * Math.sin(angle);\r\n      hexagonVertices.push({ x, y });\r\n    }\r\n\r\n    // Create hexagon edges with realistic physics\r\n    hexagonEdgesRef.current = [];\r\n    for (let i = 0; i < hexagonSides; i++) {\r\n      const j = (i + 1) % hexagonSides;\r\n      const edgeOptions = {\r\n        restitution: wallRestitution, // Use realistic wall bounce\r\n        friction: 0.05, // Reduced friction for smoother movement\r\n        frictionStatic: 0.1,\r\n        isStatic: true,\r\n        render: {\r\n          fillStyle: 'transparent',\r\n          strokeStyle: '#0f0f0f',\r\n          lineWidth: 2, // Slightly thicker for better visibility\r\n        },\r\n      };\r\n\r\n      // Calculate edge position and angle\r\n      const vertex1 = hexagonVertices[i];\r\n      const vertex2 = hexagonVertices[j];\r\n      const edgeLength = Math.sqrt(\r\n        Math.pow(vertex2.x - vertex1.x, 2) + Math.pow(vertex2.y - vertex1.y, 2)\r\n      );\r\n      const edgeAngle = Math.atan2(vertex2.y - vertex1.y, vertex2.x - vertex1.x);\r\n      const edgeCenterX = centerX + (vertex1.x + vertex2.x) / 2;\r\n      const edgeCenterY = centerY + (vertex1.y + vertex2.y) / 2;\r\n\r\n      // Create edge\r\n      const edge = Bodies.rectangle(\r\n        edgeCenterX,\r\n        edgeCenterY,\r\n        edgeLength,\r\n        1,\r\n        edgeOptions\r\n      );\r\n\r\n      // Rotate edge to angle\r\n      Body.rotate(edge, edgeAngle);\r\n\r\n      hexagonEdgesRef.current.push(edge);\r\n    }\r\n\r\n    // Create ball with corrected collision detection\r\n    const ballRadius = 12; // Fixed radius that matches visual size\r\n\r\n    // Start ball at a random position within the hexagon for variety\r\n    const startAngle = Math.random() * Math.PI * 2;\r\n    const startDistance = hexagonRadius * 0.25; // Start closer to center\r\n    const startX = centerX + Math.cos(startAngle) * startDistance;\r\n    const startY = centerY + Math.sin(startAngle) * startDistance;\r\n\r\n    ballRef.current = Bodies.circle(startX, startY, ballRadius, {\r\n      restitution: ballRestitution, // Realistic bounce with energy loss\r\n      friction: 0.01, // Very low friction for smooth rolling\r\n      frictionAir: 0.001, // Minimal air resistance for realistic movement\r\n      density: 0.003, // Optimized density for better physics\r\n      inertia: Infinity, // Prevent rotation for cleaner movement\r\n      render: {\r\n        fillStyle: '#F0A500',\r\n        strokeStyle: '#E69500',\r\n        lineWidth: 2,\r\n      },\r\n    });\r\n\r\n    // Add realistic initial velocity\r\n    const initialSpeed = 0.8;\r\n    const initialAngle = Math.random() * Math.PI * 2;\r\n    Body.setVelocity(ballRef.current, {\r\n      x: Math.cos(initialAngle) * initialSpeed,\r\n      y: Math.sin(initialAngle) * initialSpeed\r\n    });\r\n\r\n    // Add bodies to world\r\n    World.add(engineRef.current.world, [...hexagonEdgesRef.current, ballRef.current]);\r\n\r\n    // Run renderer\r\n    Render.run(render);\r\n\r\n    // Optimized animation loop for 60fps performance\r\n    let lastTime = performance.now();\r\n    const targetFPS = 60;\r\n    const frameTime = 1000 / targetFPS;\r\n\r\n    const animate = (currentTime) => {\r\n      const deltaTime = currentTime - lastTime;\r\n\r\n      // Only update if enough time has passed (throttle to 60fps)\r\n      if (deltaTime >= frameTime) {\r\n        // Update engine with consistent timing\r\n        Engine.update(engineRef.current, 16.667);\r\n\r\n        // Optimized hexagon rotation with reduced calculations\r\n        if (hexagonEdgesRef.current.length > 0) {\r\n          const cosRotation = Math.cos(rotationSpeed);\r\n          const sinRotation = Math.sin(rotationSpeed);\r\n\r\n          hexagonEdgesRef.current.forEach(edge => {\r\n            // Calculate new position after rotation (optimized)\r\n            const currentX = edge.position.x - centerX;\r\n            const currentY = edge.position.y - centerY;\r\n\r\n            const newX = currentX * cosRotation - currentY * sinRotation;\r\n            const newY = currentX * sinRotation + currentY * cosRotation;\r\n\r\n            Body.setPosition(edge, {\r\n              x: centerX + newX,\r\n              y: centerY + newY\r\n            });\r\n            Body.rotate(edge, rotationSpeed);\r\n          });\r\n        }\r\n\r\n        lastTime = currentTime;\r\n      }\r\n\r\n        // Optimized ball physics management with correct collision detection\r\n        if (ballRef.current) {\r\n          const ballPos = ballRef.current.position;\r\n          const velocity = ballRef.current.velocity;\r\n          const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);\r\n\r\n          // Calculate distance from ball center to hexagon center\r\n          const distanceFromCenter = Math.sqrt(\r\n            Math.pow(ballPos.x - centerX, 2) + Math.pow(ballPos.y - centerY, 2)\r\n          );\r\n\r\n          // Corrected boundary checking - account for actual ball radius\r\n          const maxDistance = hexagonRadius - ballRadius - 8; // Proper collision boundary\r\n\r\n          if (distanceFromCenter > maxDistance) {\r\n            // Calculate normalized direction vector (optimized)\r\n            const directionX = ballPos.x - centerX;\r\n            const directionY = ballPos.y - centerY;\r\n            const magnitude = Math.sqrt(directionX * directionX + directionY * directionY);\r\n\r\n            if (magnitude > 0) { // Prevent division by zero\r\n              const normalizedX = directionX / magnitude;\r\n              const normalizedY = directionY / magnitude;\r\n\r\n              // Position ball at correct boundary\r\n              const newPosX = centerX + normalizedX * maxDistance;\r\n              const newPosY = centerY + normalizedY * maxDistance;\r\n\r\n              Body.setPosition(ballRef.current, { x: newPosX, y: newPosY });\r\n\r\n              // Improved bounce physics with proper reflection\r\n              const currentVelocity = ballRef.current.velocity;\r\n              const dotProduct = currentVelocity.x * normalizedX + currentVelocity.y * normalizedY;\r\n\r\n              if (dotProduct > 0) {\r\n                // Reflect velocity with realistic energy loss\r\n                const reflectedVelX = currentVelocity.x - 2 * dotProduct * normalizedX;\r\n                const reflectedVelY = currentVelocity.y - 2 * dotProduct * normalizedY;\r\n\r\n                Body.setVelocity(ballRef.current, {\r\n                  x: reflectedVelX * ballRestitution,\r\n                  y: reflectedVelY * ballRestitution\r\n                });\r\n              }\r\n            }\r\n          }\r\n\r\n          // Optimized energy maintenance\r\n          if (speed < 0.5) {\r\n            // Add subtle random impulse to keep ball moving\r\n            const impulseAngle = Math.random() * Math.PI * 2;\r\n            const impulseMagnitude = 0.2;\r\n\r\n            Body.applyForce(ballRef.current, ballRef.current.position, {\r\n              x: Math.cos(impulseAngle) * impulseMagnitude * 0.0008,\r\n              y: Math.sin(impulseAngle) * impulseMagnitude * 0.0008\r\n            });\r\n          }\r\n\r\n          // Limit maximum speed for smooth movement\r\n          if (speed > 5) {\r\n            const limitedVelX = (velocity.x / speed) * 5;\r\n            const limitedVelY = (velocity.y / speed) * 5;\r\n            Body.setVelocity(ballRef.current, { x: limitedVelX, y: limitedVelY });\r\n          }\r\n        }\r\n\r\n        // Continue animation loop\r\n        requestRef.current = requestAnimationFrame(animate);\r\n      };\r\n\r\n    // Start optimized animation loop\r\n    requestRef.current = requestAnimationFrame(animate);\r\n\r\n    // Cleanup when component unmounts\r\n    return () => {\r\n      // Cancel animation loop\r\n      if (requestRef.current) {\r\n        cancelAnimationFrame(requestRef.current);\r\n      }\r\n\r\n      // Cleanup renderer and engine\r\n      Render.stop(render);\r\n      World.clear(engineRef.current.world);\r\n      Engine.clear(engineRef.current);\r\n      render.canvas = null;\r\n      render.context = null;\r\n      render.textures = {};\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <Box className={styles.loadingContainer}\r\n      sx={{ ...(!fromCreatePlan && { minHeight: '90vh' }) }}>\r\n      <Box className={styles.gameWrapper}>\r\n        <Box className={styles.hexagonLoadingContainer}>\r\n          <canvas ref={canvasRef} className={styles.hexagonCanvas} />\r\n        </Box>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default HexagonBallLoading;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAChD,OAASC,GAAG,KAAQ,eAAe,CACnC,MAAO,CAAAC,MAAM,KAAM,WAAW,CAC9B,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CAEzC;AACA;AACA;AACA;AACA;AACA,GALA,OAAAC,GAAA,IAAAC,IAAA,yBAMA,KAAM,CAAAC,kBAAkB,CAAGC,IAAA,EAAwB,IAAvB,CAAEC,cAAe,CAAC,CAAAD,IAAA,CAC5C,KAAM,CAAAE,SAAS,CAAGT,MAAM,CAAC,IAAI,CAAC,CAC9B,KAAM,CAAAU,SAAS,CAAGV,MAAM,CAAC,IAAI,CAAC,CAC9B,KAAM,CAAAW,UAAU,CAAGX,MAAM,CAAC,IAAI,CAAC,CAC/B,KAAM,CAAAY,OAAO,CAAGZ,MAAM,CAAC,IAAI,CAAC,CAC5B,KAAM,CAAAa,eAAe,CAAGb,MAAM,CAAC,EAAE,CAAC,CAElCD,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAe,aAAa,CAAG,KAAK,CAAE;AAC7B,KAAM,CAAAC,OAAO,CAAG,MAAM,CAAE;AACxB,KAAM,CAAAC,eAAe,CAAG,IAAI,CAAE;AAC9B,KAAM,CAAAC,eAAe,CAAG,GAAG,CAAE;AAE7B;AACA,KAAM,CAAAC,MAAM,CAAGhB,MAAM,CAACgB,MAAM,CAC5B,KAAM,CAAAC,MAAM,CAAGjB,MAAM,CAACiB,MAAM,CAC5B,KAAM,CAAAC,KAAK,CAAGlB,MAAM,CAACkB,KAAK,CAC1B,KAAM,CAAAC,MAAM,CAAGnB,MAAM,CAACmB,MAAM,CAC5B,KAAM,CAAAC,IAAI,CAAGpB,MAAM,CAACoB,IAAI,CAExB;AACAZ,SAAS,CAACa,OAAO,CAAGL,MAAM,CAACM,MAAM,CAAC,CAChCT,OAAO,CAAE,CAAEU,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAEX,OAAO,CAAEY,KAAK,CAAE,CAAE,CACxC,CAAC,CAAC,CAEF;AACAjB,SAAS,CAACa,OAAO,CAACK,MAAM,CAACC,SAAS,CAAG,CAAC,CACtCnB,SAAS,CAACa,OAAO,CAACO,kBAAkB,CAAG,CAAC,CAAE;AAC1CpB,SAAS,CAACa,OAAO,CAACQ,kBAAkB,CAAG,CAAC,CAAE;AAC1CrB,SAAS,CAACa,OAAO,CAACS,oBAAoB,CAAG,CAAC,CAAE;AAE5C;AACA,KAAM,CAAAC,MAAM,CAAGd,MAAM,CAACK,MAAM,CAAC,CAC3BU,MAAM,CAAEzB,SAAS,CAACc,OAAO,CACzBY,MAAM,CAAEzB,SAAS,CAACa,OAAO,CACzBa,OAAO,CAAE,CACPC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,UAAU,CAAE,KAAK,CACjBC,UAAU,CAAE,SAAS,CACrBC,kBAAkB,CAAE,KAAK,CACzBC,cAAc,CAAE,KAAK,CACrBC,YAAY,CAAE,KAChB,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,aAAa,CAAG,GAAG,CACzB,KAAM,CAAAC,YAAY,CAAG,CAAC,CACtB,KAAM,CAAAC,OAAO,CAAGb,MAAM,CAACG,OAAO,CAACC,KAAK,CAAG,CAAC,CACxC,KAAM,CAAAU,OAAO,CAAGd,MAAM,CAACG,OAAO,CAACE,MAAM,CAAG,CAAC,CAEzC;AACA,KAAM,CAAAU,eAAe,CAAG,EAAE,CAC1B,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGJ,YAAY,CAAEI,CAAC,EAAE,CAAE,CACrC,KAAM,CAAAC,KAAK,CAAIC,IAAI,CAACC,EAAE,CAAG,CAAC,CAAGH,CAAC,CAAIJ,YAAY,CAC9C,KAAM,CAAApB,CAAC,CAAGmB,aAAa,CAAGO,IAAI,CAACE,GAAG,CAACH,KAAK,CAAC,CACzC,KAAM,CAAAxB,CAAC,CAAGkB,aAAa,CAAGO,IAAI,CAACG,GAAG,CAACJ,KAAK,CAAC,CACzCF,eAAe,CAACO,IAAI,CAAC,CAAE9B,CAAC,CAAEC,CAAE,CAAC,CAAC,CAChC,CAEA;AACAb,eAAe,CAACU,OAAO,CAAG,EAAE,CAC5B,IAAK,GAAI,CAAA0B,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGJ,YAAY,CAAEI,CAAC,EAAE,CAAE,CACrC,KAAM,CAAAO,CAAC,CAAG,CAACP,CAAC,CAAG,CAAC,EAAIJ,YAAY,CAChC,KAAM,CAAAY,WAAW,CAAG,CAClBC,WAAW,CAAEzC,eAAe,CAAE;AAC9B0C,QAAQ,CAAE,IAAI,CAAE;AAChBC,cAAc,CAAE,GAAG,CACnBC,QAAQ,CAAE,IAAI,CACd5B,MAAM,CAAE,CACN6B,SAAS,CAAE,aAAa,CACxBC,WAAW,CAAE,SAAS,CACtBC,SAAS,CAAE,CAAG;AAChB,CACF,CAAC,CAED;AACA,KAAM,CAAAC,OAAO,CAAGjB,eAAe,CAACC,CAAC,CAAC,CAClC,KAAM,CAAAiB,OAAO,CAAGlB,eAAe,CAACQ,CAAC,CAAC,CAClC,KAAM,CAAAW,UAAU,CAAGhB,IAAI,CAACiB,IAAI,CAC1BjB,IAAI,CAACkB,GAAG,CAACH,OAAO,CAACzC,CAAC,CAAGwC,OAAO,CAACxC,CAAC,CAAE,CAAC,CAAC,CAAG0B,IAAI,CAACkB,GAAG,CAACH,OAAO,CAACxC,CAAC,CAAGuC,OAAO,CAACvC,CAAC,CAAE,CAAC,CACxE,CAAC,CACD,KAAM,CAAA4C,SAAS,CAAGnB,IAAI,CAACoB,KAAK,CAACL,OAAO,CAACxC,CAAC,CAAGuC,OAAO,CAACvC,CAAC,CAAEwC,OAAO,CAACzC,CAAC,CAAGwC,OAAO,CAACxC,CAAC,CAAC,CAC1E,KAAM,CAAA+C,WAAW,CAAG1B,OAAO,CAAG,CAACmB,OAAO,CAACxC,CAAC,CAAGyC,OAAO,CAACzC,CAAC,EAAI,CAAC,CACzD,KAAM,CAAAgD,WAAW,CAAG1B,OAAO,CAAG,CAACkB,OAAO,CAACvC,CAAC,CAAGwC,OAAO,CAACxC,CAAC,EAAI,CAAC,CAEzD;AACA,KAAM,CAAAgD,IAAI,CAAGrD,MAAM,CAACsD,SAAS,CAC3BH,WAAW,CACXC,WAAW,CACXN,UAAU,CACV,CAAC,CACDV,WACF,CAAC,CAED;AACAnC,IAAI,CAACsD,MAAM,CAACF,IAAI,CAAEJ,SAAS,CAAC,CAE5BzD,eAAe,CAACU,OAAO,CAACgC,IAAI,CAACmB,IAAI,CAAC,CACpC,CAEA;AACA,KAAM,CAAAG,UAAU,CAAG,EAAE,CAAE;AAEvB;AACA,KAAM,CAAAC,UAAU,CAAG3B,IAAI,CAAC4B,MAAM,CAAC,CAAC,CAAG5B,IAAI,CAACC,EAAE,CAAG,CAAC,CAC9C,KAAM,CAAA4B,aAAa,CAAGpC,aAAa,CAAG,IAAI,CAAE;AAC5C,KAAM,CAAAqC,MAAM,CAAGnC,OAAO,CAAGK,IAAI,CAACE,GAAG,CAACyB,UAAU,CAAC,CAAGE,aAAa,CAC7D,KAAM,CAAAE,MAAM,CAAGnC,OAAO,CAAGI,IAAI,CAACG,GAAG,CAACwB,UAAU,CAAC,CAAGE,aAAa,CAE7DpE,OAAO,CAACW,OAAO,CAAGF,MAAM,CAAC8D,MAAM,CAACF,MAAM,CAAEC,MAAM,CAAEL,UAAU,CAAE,CAC1DnB,WAAW,CAAE1C,eAAe,CAAE;AAC9B2C,QAAQ,CAAE,IAAI,CAAE;AAChByB,WAAW,CAAE,KAAK,CAAE;AACpBC,OAAO,CAAE,KAAK,CAAE;AAChBC,OAAO,CAAEC,QAAQ,CAAE;AACnBtD,MAAM,CAAE,CACN6B,SAAS,CAAE,SAAS,CACpBC,WAAW,CAAE,SAAS,CACtBC,SAAS,CAAE,CACb,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAwB,YAAY,CAAG,GAAG,CACxB,KAAM,CAAAC,YAAY,CAAGtC,IAAI,CAAC4B,MAAM,CAAC,CAAC,CAAG5B,IAAI,CAACC,EAAE,CAAG,CAAC,CAChD9B,IAAI,CAACoE,WAAW,CAAC9E,OAAO,CAACW,OAAO,CAAE,CAChCE,CAAC,CAAE0B,IAAI,CAACE,GAAG,CAACoC,YAAY,CAAC,CAAGD,YAAY,CACxC9D,CAAC,CAAEyB,IAAI,CAACG,GAAG,CAACmC,YAAY,CAAC,CAAGD,YAC9B,CAAC,CAAC,CAEF;AACApE,KAAK,CAACuE,GAAG,CAACjF,SAAS,CAACa,OAAO,CAACqE,KAAK,CAAE,CAAC,GAAG/E,eAAe,CAACU,OAAO,CAAEX,OAAO,CAACW,OAAO,CAAC,CAAC,CAEjF;AACAJ,MAAM,CAAC0E,GAAG,CAAC5D,MAAM,CAAC,CAElB;AACA,GAAI,CAAA6D,QAAQ,CAAGC,WAAW,CAACC,GAAG,CAAC,CAAC,CAChC,KAAM,CAAAC,SAAS,CAAG,EAAE,CACpB,KAAM,CAAAC,SAAS,CAAG,IAAI,CAAGD,SAAS,CAElC,KAAM,CAAAE,OAAO,CAAIC,WAAW,EAAK,CAC/B,KAAM,CAAAC,SAAS,CAAGD,WAAW,CAAGN,QAAQ,CAExC;AACA,GAAIO,SAAS,EAAIH,SAAS,CAAE,CAC1B;AACAhF,MAAM,CAACoF,MAAM,CAAC5F,SAAS,CAACa,OAAO,CAAE,MAAM,CAAC,CAExC;AACA,GAAIV,eAAe,CAACU,OAAO,CAACgF,MAAM,CAAG,CAAC,CAAE,CACtC,KAAM,CAAAC,WAAW,CAAGrD,IAAI,CAACE,GAAG,CAACvC,aAAa,CAAC,CAC3C,KAAM,CAAA2F,WAAW,CAAGtD,IAAI,CAACG,GAAG,CAACxC,aAAa,CAAC,CAE3CD,eAAe,CAACU,OAAO,CAACmF,OAAO,CAAChC,IAAI,EAAI,CACtC;AACA,KAAM,CAAAiC,QAAQ,CAAGjC,IAAI,CAACkC,QAAQ,CAACnF,CAAC,CAAGqB,OAAO,CAC1C,KAAM,CAAA+D,QAAQ,CAAGnC,IAAI,CAACkC,QAAQ,CAAClF,CAAC,CAAGqB,OAAO,CAE1C,KAAM,CAAA+D,IAAI,CAAGH,QAAQ,CAAGH,WAAW,CAAGK,QAAQ,CAAGJ,WAAW,CAC5D,KAAM,CAAAM,IAAI,CAAGJ,QAAQ,CAAGF,WAAW,CAAGI,QAAQ,CAAGL,WAAW,CAE5DlF,IAAI,CAAC0F,WAAW,CAACtC,IAAI,CAAE,CACrBjD,CAAC,CAAEqB,OAAO,CAAGgE,IAAI,CACjBpF,CAAC,CAAEqB,OAAO,CAAGgE,IACf,CAAC,CAAC,CACFzF,IAAI,CAACsD,MAAM,CAACF,IAAI,CAAE5D,aAAa,CAAC,CAClC,CAAC,CAAC,CACJ,CAEAgF,QAAQ,CAAGM,WAAW,CACxB,CAEE;AACA,GAAIxF,OAAO,CAACW,OAAO,CAAE,CACnB,KAAM,CAAA0F,OAAO,CAAGrG,OAAO,CAACW,OAAO,CAACqF,QAAQ,CACxC,KAAM,CAAAM,QAAQ,CAAGtG,OAAO,CAACW,OAAO,CAAC2F,QAAQ,CACzC,KAAM,CAAAC,KAAK,CAAGhE,IAAI,CAACiB,IAAI,CAAC8C,QAAQ,CAACzF,CAAC,CAAGyF,QAAQ,CAACzF,CAAC,CAAGyF,QAAQ,CAACxF,CAAC,CAAGwF,QAAQ,CAACxF,CAAC,CAAC,CAE1E;AACA,KAAM,CAAA0F,kBAAkB,CAAGjE,IAAI,CAACiB,IAAI,CAClCjB,IAAI,CAACkB,GAAG,CAAC4C,OAAO,CAACxF,CAAC,CAAGqB,OAAO,CAAE,CAAC,CAAC,CAAGK,IAAI,CAACkB,GAAG,CAAC4C,OAAO,CAACvF,CAAC,CAAGqB,OAAO,CAAE,CAAC,CACpE,CAAC,CAED;AACA,KAAM,CAAAsE,WAAW,CAAGzE,aAAa,CAAGiC,UAAU,CAAG,CAAC,CAAE;AAEpD,GAAIuC,kBAAkB,CAAGC,WAAW,CAAE,CACpC;AACA,KAAM,CAAAC,UAAU,CAAGL,OAAO,CAACxF,CAAC,CAAGqB,OAAO,CACtC,KAAM,CAAAyE,UAAU,CAAGN,OAAO,CAACvF,CAAC,CAAGqB,OAAO,CACtC,KAAM,CAAAyE,SAAS,CAAGrE,IAAI,CAACiB,IAAI,CAACkD,UAAU,CAAGA,UAAU,CAAGC,UAAU,CAAGA,UAAU,CAAC,CAE9E,GAAIC,SAAS,CAAG,CAAC,CAAE,CAAE;AACnB,KAAM,CAAAC,WAAW,CAAGH,UAAU,CAAGE,SAAS,CAC1C,KAAM,CAAAE,WAAW,CAAGH,UAAU,CAAGC,SAAS,CAE1C;AACA,KAAM,CAAAG,OAAO,CAAG7E,OAAO,CAAG2E,WAAW,CAAGJ,WAAW,CACnD,KAAM,CAAAO,OAAO,CAAG7E,OAAO,CAAG2E,WAAW,CAAGL,WAAW,CAEnD/F,IAAI,CAAC0F,WAAW,CAACpG,OAAO,CAACW,OAAO,CAAE,CAAEE,CAAC,CAAEkG,OAAO,CAAEjG,CAAC,CAAEkG,OAAQ,CAAC,CAAC,CAE7D;AACA,KAAM,CAAAC,eAAe,CAAGjH,OAAO,CAACW,OAAO,CAAC2F,QAAQ,CAChD,KAAM,CAAAY,UAAU,CAAGD,eAAe,CAACpG,CAAC,CAAGgG,WAAW,CAAGI,eAAe,CAACnG,CAAC,CAAGgG,WAAW,CAEpF,GAAII,UAAU,CAAG,CAAC,CAAE,CAClB;AACA,KAAM,CAAAC,aAAa,CAAGF,eAAe,CAACpG,CAAC,CAAG,CAAC,CAAGqG,UAAU,CAAGL,WAAW,CACtE,KAAM,CAAAO,aAAa,CAAGH,eAAe,CAACnG,CAAC,CAAG,CAAC,CAAGoG,UAAU,CAAGJ,WAAW,CAEtEpG,IAAI,CAACoE,WAAW,CAAC9E,OAAO,CAACW,OAAO,CAAE,CAChCE,CAAC,CAAEsG,aAAa,CAAG/G,eAAe,CAClCU,CAAC,CAAEsG,aAAa,CAAGhH,eACrB,CAAC,CAAC,CACJ,CACF,CACF,CAEA;AACA,GAAImG,KAAK,CAAG,GAAG,CAAE,CACf;AACA,KAAM,CAAAc,YAAY,CAAG9E,IAAI,CAAC4B,MAAM,CAAC,CAAC,CAAG5B,IAAI,CAACC,EAAE,CAAG,CAAC,CAChD,KAAM,CAAA8E,gBAAgB,CAAG,GAAG,CAE5B5G,IAAI,CAAC6G,UAAU,CAACvH,OAAO,CAACW,OAAO,CAAEX,OAAO,CAACW,OAAO,CAACqF,QAAQ,CAAE,CACzDnF,CAAC,CAAE0B,IAAI,CAACE,GAAG,CAAC4E,YAAY,CAAC,CAAGC,gBAAgB,CAAG,MAAM,CACrDxG,CAAC,CAAEyB,IAAI,CAACG,GAAG,CAAC2E,YAAY,CAAC,CAAGC,gBAAgB,CAAG,MACjD,CAAC,CAAC,CACJ,CAEA;AACA,GAAIf,KAAK,CAAG,CAAC,CAAE,CACb,KAAM,CAAAiB,WAAW,CAAIlB,QAAQ,CAACzF,CAAC,CAAG0F,KAAK,CAAI,CAAC,CAC5C,KAAM,CAAAkB,WAAW,CAAInB,QAAQ,CAACxF,CAAC,CAAGyF,KAAK,CAAI,CAAC,CAC5C7F,IAAI,CAACoE,WAAW,CAAC9E,OAAO,CAACW,OAAO,CAAE,CAAEE,CAAC,CAAE2G,WAAW,CAAE1G,CAAC,CAAE2G,WAAY,CAAC,CAAC,CACvE,CACF,CAEA;AACA1H,UAAU,CAACY,OAAO,CAAG+G,qBAAqB,CAACnC,OAAO,CAAC,CACrD,CAAC,CAEH;AACAxF,UAAU,CAACY,OAAO,CAAG+G,qBAAqB,CAACnC,OAAO,CAAC,CAEnD;AACA,MAAO,IAAM,CACX;AACA,GAAIxF,UAAU,CAACY,OAAO,CAAE,CACtBgH,oBAAoB,CAAC5H,UAAU,CAACY,OAAO,CAAC,CAC1C,CAEA;AACAJ,MAAM,CAACqH,IAAI,CAACvG,MAAM,CAAC,CACnBb,KAAK,CAACqH,KAAK,CAAC/H,SAAS,CAACa,OAAO,CAACqE,KAAK,CAAC,CACpC1E,MAAM,CAACuH,KAAK,CAAC/H,SAAS,CAACa,OAAO,CAAC,CAC/BU,MAAM,CAACC,MAAM,CAAG,IAAI,CACpBD,MAAM,CAACyG,OAAO,CAAG,IAAI,CACrBzG,MAAM,CAAC0G,QAAQ,CAAG,CAAC,CAAC,CACtB,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEtI,IAAA,CAACJ,GAAG,EAAC2I,SAAS,CAAEzI,MAAM,CAAC0I,gBAAiB,CACtCC,EAAE,CAAE,CAAE,IAAI,CAACtI,cAAc,EAAI,CAAEuI,SAAS,CAAE,MAAO,CAAC,CAAE,CAAE,CAAAC,QAAA,cACtD3I,IAAA,CAACJ,GAAG,EAAC2I,SAAS,CAAEzI,MAAM,CAAC8I,WAAY,CAAAD,QAAA,cACjC3I,IAAA,CAACJ,GAAG,EAAC2I,SAAS,CAAEzI,MAAM,CAAC+I,uBAAwB,CAAAF,QAAA,cAC7C3I,IAAA,WAAQ8I,GAAG,CAAE1I,SAAU,CAACmI,SAAS,CAAEzI,MAAM,CAACiJ,aAAc,CAAE,CAAC,CACxD,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9I,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import React from 'react';
import { 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  Typography, 
  Box,
  IconButton
} from '@mui/material';
import Iconify from 'components/Iconify/index';

const ConfirmDialog = ({ open, onClose, onConfirm, title, description }) => {
  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="sm"
      PaperProps={{
        sx: {
          borderRadius: '12px',
          boxShadow: '0 8px 24px rgba(0,0,0,0.1)'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        borderBottom: '1px solid #f0f0f0',
        pb: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Iconify icon="material-symbols:warning" width={24} height={24} color="#F44336" sx={{ mr: 1 }} />
          <Typography variant="h6" sx={{ fontFamily: '"Recursive Variable", sans-serif', fontWeight: 600 }}>
            {title}
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <Iconify icon="material-symbols:close" width={20} height={20} />
        </IconButton>
      </DialogTitle>
      
      <DialogContent sx={{ pt: 3, pb: 2 }}>
        <Typography variant="body1" sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>
          {description}
        </Typography>
      </DialogContent>
      
      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #f0f0f0' }}>
        <Button 
          onClick={onClose}
          variant="outlined"
          sx={{ 
            borderRadius: '8px',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            mr: 1
          }}
        >
          Cancel
        </Button>
        <Button 
          onClick={onConfirm}
          variant="contained"
          sx={{ 
            bgcolor: '#F44336',
            '&:hover': {
              bgcolor: '#D32F2F',
            },
            borderRadius: '8px',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600
          }}
        >
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmDialog; 
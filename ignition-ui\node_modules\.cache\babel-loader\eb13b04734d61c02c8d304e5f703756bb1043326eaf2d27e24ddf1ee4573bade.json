{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useParams}from'react-router-dom';import{Container,Box,Tabs,Tab,CircularProgress,Alert}from'@mui/material';import Iconify from'components/Iconify/index';import{mainYellowColor,APIURL}from\"helpers/constants\";import{successSnackbar,errorSnackbar}from'components/Snackbar/index';import{toast}from'react-toastify';import{getHeaders}from\"helpers/functions\";// Components\nimport Header from'./components/Header';import Description from'./components/Description';import Statistics from'./components/Statistics';import Progress from'./components/Progress';import MilestoneList from'./components/MilestoneList';import MilestoneOverview from'./components/MilestoneOverview';import AccessManagement from'./components/AccessManagement';import ChatbotBar from'./components/ChatbotBar';import AgentTab from'./components/AgentTab';// Hooks\nimport usePlanData from'./hooks/usePlanData';import useViewMode from'./hooks/useViewMode';// Dialogs\nimport InviteDialog from'./dialogs/InviteDialog';import DeleteDialog from'./dialogs/DeleteDialog';import OptOutDialog from'./dialogs/OptOutDialog';import ConfirmDialog from'./dialogs/ConfirmDialog';// Services\nimport{updateMilestone,updateTask,updateSubtask,addTask,addSubtask,deleteTask,deleteSubtask,assignMembersToTask}from'../services';// Styles\nimport styles from'./styles.module.scss';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const PlanDetail=()=>{var _planInfo$user_access,_planInfo$user_access2,_planInfo$user_access3;const{param}=useParams();const[activeTab,setActiveTab]=useState(()=>{// Get active tab value from localStorage, default to 'overview' if not found\nreturn localStorage.getItem(`plan_${param}_activeTab`)||'overview';});const[plan,setPlan]=useState(null);const[dialogState,setDialogState]=useState({invite:false,delete:false,optOut:false,deleteTask:false,deleteSubtask:false});const[selectedTaskToDelete]=useState(null);const[selectedSubtaskToDelete]=useState(null);// Custom hooks\nconst{planInfo,loading,error,handleDeletePlan,handleOptOutPlan,handleInviteUser,calculatePlanStats,calculateSubtaskProgress,getSubtaskStatus,calculateTaskProgress,getTaskStatus,calculateMilestoneProgress,getMilestoneStatus}=usePlanData(param);const{viewMode,handleViewModeChange}=useViewMode();// Update plan state when planInfo changes\nuseEffect(()=>{if(planInfo){setPlan(planInfo);}},[planInfo]);// Remove active tab from localStorage when component unmounts\nuseEffect(()=>{return()=>{localStorage.removeItem(`plan_${param}_activeTab`);};},[param]);// Calculate plan statistics\nconst stats=calculatePlanStats?calculatePlanStats(planInfo):null;// Dialog handlers\nconst openDialog=dialogName=>{setDialogState(prev=>({...prev,[dialogName]:true}));};const closeDialog=dialogName=>{setDialogState(prev=>({...prev,[dialogName]:false}));};// Tab change handler\nconst handleTabChange=(_,newValue)=>{setActiveTab(newValue);// Save active tab to localStorage\nlocalStorage.setItem(`plan_${param}_activeTab`,newValue);};// Handle switching to Agent tab from ChatbotBar\nconst handleSwitchToAgent=conversationData=>{console.log('PlanDetail - Switching to agent tab with data:',conversationData);// Debug log\nsetActiveTab('agent');localStorage.setItem(`plan_${param}_activeTab`,'agent');// Store the conversation data for the Agent tab to pick up\nlocalStorage.setItem('pending_agent_message',JSON.stringify(conversationData));};// Refresh plan data\nconst refreshPlanData=async()=>{try{window.location.reload();// Simple refresh for now\n}catch(error){console.error('Error refreshing plan data:',error);}};// Access management handlers\nconst handleAddAccess=async(email,accessLevel)=>{try{const response=await fetch(`${APIURL}/api/plans/${param}/access`,{method:'POST',headers:{...getHeaders(),'Content-Type':'application/json'},body:JSON.stringify({email,access_level:accessLevel})});if(!response.ok){const error=await response.json();throw new Error(error.error||'Failed to add access');}// Refresh plan data\nawait refreshPlanData();toast.success('Access granted successfully');}catch(error){console.error('Error adding access:',error);toast.error(error.message||'Failed to add access');throw error;}};const handleUpdateAccess=async function(accessId,accessLevel){let isHeadOwner=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;try{const response=await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`,{method:'PUT',headers:{...getHeaders(),'Content-Type':'application/json'},body:JSON.stringify({access_level:accessLevel,is_head_owner:isHeadOwner})});if(!response.ok){const error=await response.json();throw new Error(error.error||'Failed to update access');}// Refresh plan data\nawait refreshPlanData();toast.success('Access updated successfully');}catch(error){console.error('Error updating access:',error);toast.error(error.message||'Failed to update access');throw error;}};const handleRemoveAccess=async accessId=>{try{const response=await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`,{method:'DELETE',headers:getHeaders()});if(!response.ok){const error=await response.json();throw new Error(error.error||'Failed to remove access');}// Refresh plan data\nawait refreshPlanData();toast.success('Access removed successfully');}catch(error){console.error('Error removing access:',error);toast.error(error.message||'Failed to remove access');throw error;}};// Handle milestone update\nconst handleUpdateMilestone=async updatedMilestone=>{try{console.log('Updating milestone:',updatedMilestone);await updateMilestone(updatedMilestone);// Update local state\nconst updatedPlan={...plan};const milestoneIndex=updatedPlan.milestones.findIndex(m=>m.id===updatedMilestone.id);if(milestoneIndex!==-1){updatedPlan.milestones[milestoneIndex]={...updatedPlan.milestones[milestoneIndex],...updatedMilestone};setPlan(updatedPlan);}successSnackbar('Milestone updated successfully');}catch(error){console.error('Error updating milestone:',error);errorSnackbar('Failed to update milestone');}};// Handle task update\nconst handleUpdateTask=async updatedTask=>{try{console.log('Updating task:',updatedTask);await updateTask(updatedTask);// Update local state\nconst updatedPlan={...plan};const milestoneIndex=updatedPlan.milestones.findIndex(m=>m.tasks&&m.tasks.some(t=>t.id===updatedTask.id));if(milestoneIndex!==-1){const taskIndex=updatedPlan.milestones[milestoneIndex].tasks.findIndex(t=>t.id===updatedTask.id);if(taskIndex!==-1){updatedPlan.milestones[milestoneIndex].tasks[taskIndex]={...updatedPlan.milestones[milestoneIndex].tasks[taskIndex],...updatedTask};setPlan(updatedPlan);}}successSnackbar('Task updated successfully');}catch(error){console.error('Error updating task:',error);errorSnackbar('Failed to update task');}};// Handle subtask update\nconst handleUpdateSubtask=async updatedSubtask=>{try{console.log('Updating subtask:',updatedSubtask);// Call API to update subtask\nawait updateSubtask(updatedSubtask);// Create a copy of current plan to update\nconst updatedPlan={...plan};// Find the task containing the subtask\nlet taskFound=false;// Update subtask in state\nfor(let i=0;i<updatedPlan.milestones.length;i++){const milestone=updatedPlan.milestones[i];if(!milestone.tasks)continue;for(let j=0;j<milestone.tasks.length;j++){const task=milestone.tasks[j];if(!task.subtasks)continue;// Update subtask in task\nconst subtaskIndex=task.subtasks.findIndex(s=>s.id===updatedSubtask.id);if(subtaskIndex!==-1){// Update subtask\ntask.subtasks[subtaskIndex]={...task.subtasks[subtaskIndex],...updatedSubtask};taskFound=true;break;}}if(taskFound)break;}// Update state with new plan\nsetPlan(updatedPlan);successSnackbar('Subtask updated successfully');}catch(error){console.error('Error updating subtask:',error);errorSnackbar('Failed to update subtask');}};// Handle add task\nconst handleAddTask=async newTask=>{try{console.log('Adding new task:',newTask);const response=await addTask(newTask);console.log('Add task response:',response);// Update local state\nconst updatedPlan={...plan};// Find milestone to add new task\nconst milestoneIndex=updatedPlan.milestones.findIndex(m=>m.id===newTask.milestone);if(milestoneIndex!==-1){// Add new task to milestone\nif(!updatedPlan.milestones[milestoneIndex].tasks){updatedPlan.milestones[milestoneIndex].tasks=[];}// Add the new task with data from response\nconst taskToAdd=response.data||{...newTask,id:Date.now(),// Temporary ID if response doesn't provide one\nsubtasks:[]};updatedPlan.milestones[milestoneIndex].tasks.push(taskToAdd);setPlan(updatedPlan);}successSnackbar('Task added successfully');}catch(error){console.error('Error adding task:',error);errorSnackbar('Failed to add task');}};// Handle add subtask\nconst handleAddSubtask=async newSubtask=>{try{console.log('Adding new subtask:',newSubtask);const response=await addSubtask(newSubtask);console.log('Add subtask response:',response);// Update local state\nconst updatedPlan={...plan};// Find task to add new subtask\nlet taskFound=false;for(let i=0;i<updatedPlan.milestones.length;i++){const milestone=updatedPlan.milestones[i];if(!milestone.tasks)continue;for(let j=0;j<milestone.tasks.length;j++){const task=milestone.tasks[j];if(task.slug===newSubtask.task){// Add new subtask to task\nif(!task.subtasks){task.subtasks=[];}// Add the new subtask with data from response\nconst subtaskToAdd=response.data||{...newSubtask,id:Date.now()// Temporary ID if response doesn't provide one\n};task.subtasks.push(subtaskToAdd);taskFound=true;break;}}if(taskFound)break;}setPlan(updatedPlan);successSnackbar('Subtask added successfully');}catch(error){console.error('Error adding subtask:',error);errorSnackbar('Failed to add subtask');}};// Handle delete task\nconst handleDeleteTask=async taskToDelete=>{try{console.log('Deleting task:',taskToDelete);await deleteTask(taskToDelete.slug);// Update local state\nconst updatedPlan={...plan};// Find milestone containing the task to delete\nconst milestoneIndex=updatedPlan.milestones.findIndex(m=>m.tasks&&m.tasks.some(t=>t.id===taskToDelete.id));if(milestoneIndex!==-1){// Filter out the task to delete\nupdatedPlan.milestones[milestoneIndex].tasks=updatedPlan.milestones[milestoneIndex].tasks.filter(t=>t.id!==taskToDelete.id);setPlan(updatedPlan);}successSnackbar('Task deleted successfully');}catch(error){console.error('Error deleting task:',error);errorSnackbar('Failed to delete task');}};// Handle delete subtask\nconst handleDeleteSubtask=async subtaskToDelete=>{try{console.log('Deleting subtask:',subtaskToDelete);await deleteSubtask(subtaskToDelete.slug);// Update local state\nconst updatedPlan={...plan};// Find task containing the subtask to delete\nlet taskFound=false;for(let i=0;i<updatedPlan.milestones.length;i++){const milestone=updatedPlan.milestones[i];if(!milestone.tasks)continue;for(let j=0;j<milestone.tasks.length;j++){const task=milestone.tasks[j];if(!task.subtasks)continue;// Filter out the subtask to delete\nconst originalLength=task.subtasks.length;task.subtasks=task.subtasks.filter(s=>s.id!==subtaskToDelete.id);if(task.subtasks.length<originalLength){taskFound=true;break;}}if(taskFound)break;}setPlan(updatedPlan);toast.success('Subtask deleted successfully');}catch(error){console.error('Error deleting subtask:',error);toast.error('Failed to delete subtask');}};// Handle assign members to task\nconst handleAssignMembers=async(taskToAssign,memberIds)=>{try{console.log('Assigning members to task:',taskToAssign,memberIds);await assignMembersToTask(taskToAssign.slug,memberIds);// Update local state\nconst updatedPlan={...plan};// Find the task to update\nlet taskFound=false;for(let i=0;i<updatedPlan.milestones.length;i++){const milestone=updatedPlan.milestones[i];if(!milestone.tasks)continue;for(let j=0;j<milestone.tasks.length;j++){const task=milestone.tasks[j];if(task.id===taskToAssign.id){// Update assignees\n// Find user objects for the selected member IDs\nconst assignedMembers=memberIds.map(memberId=>{var _planInfo$invited_use;// Check if it's the plan owner\nif(planInfo.owner&&planInfo.owner.id===memberId){return{id:planInfo.owner.id,first_name:planInfo.owner.first_name,last_name:planInfo.owner.last_name,email:planInfo.owner.email,avatar:planInfo.owner.avatar};}// Check in invited users\nconst invitedUser=(_planInfo$invited_use=planInfo.invited_users)===null||_planInfo$invited_use===void 0?void 0:_planInfo$invited_use.find(user=>user.invited_user_info&&user.invited_user_info.id===memberId);if(invitedUser){return{id:invitedUser.invited_user_info.id,first_name:invitedUser.invited_user_info.first_name,last_name:invitedUser.invited_user_info.last_name,email:invitedUser.email,avatar:invitedUser.invited_user_info.avatar};}return null;}).filter(member=>member!==null);task.assignees=assignedMembers;taskFound=true;break;}}if(taskFound)break;}setPlan(updatedPlan);toast.success('Members assigned successfully');}catch(error){console.error('Error assigning members to task:',error);toast.error('Failed to assign members to task');}};if(error){return/*#__PURE__*/_jsx(Container,{className:styles.container,children:/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mt:4},children:\"An error occurred while loading plan information. Please try again later.\"})});}return/*#__PURE__*/_jsxs(Container,{maxWidth:\"lg\",className:styles.container,sx:{padding:'20px',minHeight:'calc(100vh - 65px)',fontFamily:'\"Recursive Variable\", sans-serif'},children:[loading?/*#__PURE__*/_jsx(Box,{className:styles.loadingContainer,children:/*#__PURE__*/_jsx(CircularProgress,{size:60,sx:{color:mainYellowColor}})}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Header,{planInfo:planInfo,viewMode:viewMode,onViewModeChange:handleViewModeChange,onOpenInviteDialog:()=>openDialog('invite'),onOpenDeleteDialog:()=>openDialog('delete'),onOpenOptOutDialog:()=>openDialog('optOut')}),/*#__PURE__*/_jsx(Box,{sx:{borderBottom:1,borderColor:'divider',mb:0.5,mt:-1},children:/*#__PURE__*/_jsxs(Tabs,{value:activeTab,onChange:handleTabChange,variant:\"scrollable\",scrollButtons:\"auto\",sx:{minHeight:'36px','& .MuiTab-root':{textTransform:'none',fontWeight:600,fontSize:'0.9rem',minWidth:'auto',minHeight:'36px',px:2,py:0.5,fontFamily:'\"Recursive Variable\", sans-serif'},'& .Mui-selected':{color:`${mainYellowColor} !important`},'& .MuiTabs-indicator':{backgroundColor:mainYellowColor,height:'2px'}},children:[/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:dashboard\",width:16,height:16}),iconPosition:\"start\",label:\"Overview\",value:\"overview\",sx:{gap:'4px'}}),/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:view-list\",width:16,height:16}),iconPosition:\"start\",label:\"Project Details\",value:\"milestones\",sx:{gap:'4px'}}),/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:robot\",width:16,height:16}),iconPosition:\"start\",label:\"Agent\",value:\"agent\",sx:{gap:'4px'}}),(planInfo===null||planInfo===void 0?void 0:(_planInfo$user_access=planInfo.user_access_level)===null||_planInfo$user_access===void 0?void 0:_planInfo$user_access.access_level)==='owner'&&/*#__PURE__*/_jsx(Tab,{icon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:security\",width:16,height:16}),iconPosition:\"start\",label:\"Access\",value:\"access\",sx:{gap:'4px'}})]})}),/*#__PURE__*/_jsxs(Box,{className:styles.tabContent,children:[activeTab==='overview'&&/*#__PURE__*/_jsxs(Box,{className:styles.overviewTab,sx:{gap:0.5},children:[/*#__PURE__*/_jsx(Description,{planInfo:planInfo}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:{xs:'column',md:'row'},gap:1,mb:0.5},children:[/*#__PURE__*/_jsx(Statistics,{stats:stats}),/*#__PURE__*/_jsx(Progress,{stats:stats})]}),(planInfo===null||planInfo===void 0?void 0:(_planInfo$user_access2=planInfo.user_access_level)===null||_planInfo$user_access2===void 0?void 0:_planInfo$user_access2.access_level)==='owner'&&/*#__PURE__*/_jsx(AccessManagement,{planInfo:planInfo,userAccessLevel:planInfo===null||planInfo===void 0?void 0:planInfo.user_access_level,onAddAccess:handleAddAccess,onUpdateAccess:handleUpdateAccess,onRemoveAccess:handleRemoveAccess}),/*#__PURE__*/_jsx(MilestoneOverview,{milestones:planInfo===null||planInfo===void 0?void 0:planInfo.milestones,calculateMilestoneProgress:calculateMilestoneProgress,getMilestoneStatus:getMilestoneStatus,calculateTaskProgress:calculateTaskProgress,getTaskStatus:getTaskStatus,calculateSubtaskProgress:calculateSubtaskProgress,getSubtaskStatus:getSubtaskStatus})]}),activeTab==='milestones'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(ChatbotBar,{planInfo:planInfo,onPlanUpdate:updatedPlan=>{// Handle plan updates from AI agent\nconsole.log('Plan updated by AI:',updatedPlan);},onSwitchToAgent:handleSwitchToAgent}),/*#__PURE__*/_jsx(MilestoneList,{milestones:planInfo===null||planInfo===void 0?void 0:planInfo.milestones,viewMode:viewMode,compact:false,showSubtasks:true,calculateMilestoneProgress:calculateMilestoneProgress,getMilestoneStatus:getMilestoneStatus,calculateTaskProgress:calculateTaskProgress,getTaskStatus:getTaskStatus,calculateSubtaskProgress:calculateSubtaskProgress,getSubtaskStatus:getSubtaskStatus,onUpdateMilestone:handleUpdateMilestone,onUpdateTask:handleUpdateTask,onUpdateSubtask:handleUpdateSubtask,onAddTask:handleAddTask,onAddSubtask:handleAddSubtask,onDeleteTask:handleDeleteTask,onDeleteSubtask:handleDeleteSubtask,onAssignMembers:handleAssignMembers,invitedUsers:(planInfo===null||planInfo===void 0?void 0:planInfo.invited_users)||[],planOwner:planInfo===null||planInfo===void 0?void 0:planInfo.owner})]}),activeTab==='agent'&&/*#__PURE__*/_jsx(AgentTab,{planInfo:planInfo,onPlanUpdate:()=>{// Refresh plan data when AI agent makes changes\nconsole.log('Plan updated by AI agent, refreshing data...');refreshPlanData();}}),activeTab==='access'&&(planInfo===null||planInfo===void 0?void 0:(_planInfo$user_access3=planInfo.user_access_level)===null||_planInfo$user_access3===void 0?void 0:_planInfo$user_access3.access_level)==='owner'&&/*#__PURE__*/_jsx(AccessManagement,{planInfo:planInfo,userAccessLevel:planInfo===null||planInfo===void 0?void 0:planInfo.user_access_level,onAddAccess:handleAddAccess,onUpdateAccess:handleUpdateAccess,onRemoveAccess:handleRemoveAccess})]})]}),/*#__PURE__*/_jsx(InviteDialog,{open:dialogState.invite,onClose:()=>closeDialog('invite'),onInvite:handleInviteUser,planInfo:planInfo}),/*#__PURE__*/_jsx(DeleteDialog,{open:dialogState.delete,onClose:()=>closeDialog('delete'),onDelete:handleDeletePlan}),/*#__PURE__*/_jsx(OptOutDialog,{open:dialogState.optOut,onClose:()=>closeDialog('optOut'),onOptOut:handleOptOutPlan}),/*#__PURE__*/_jsx(ConfirmDialog,{open:dialogState.deleteTask,onClose:()=>setDialogState(prev=>({...prev,deleteTask:false})),onConfirm:()=>handleDeleteTask(selectedTaskToDelete),title:\"Delete Task\",description:`Are you sure you want to delete the task \"${selectedTaskToDelete===null||selectedTaskToDelete===void 0?void 0:selectedTaskToDelete.name}\"? This action cannot be undone.`}),/*#__PURE__*/_jsx(ConfirmDialog,{open:dialogState.deleteSubtask,onClose:()=>setDialogState(prev=>({...prev,deleteSubtask:false})),onConfirm:()=>handleDeleteSubtask(selectedSubtaskToDelete),title:\"Delete Subtask\",description:`Are you sure you want to delete the subtask \"${selectedSubtaskToDelete===null||selectedSubtaskToDelete===void 0?void 0:selectedSubtaskToDelete.name}\"? This action cannot be undone.`})]});};export default PlanDetail;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Container", "Box", "Tabs", "Tab", "CircularProgress", "<PERSON><PERSON>", "Iconify", "mainYellowColor", "APIURL", "successSnackbar", "errorSnackbar", "toast", "getHeaders", "Header", "Description", "Statistics", "Progress", "MilestoneList", "MilestoneOverview", "AccessManagement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AgentTab", "usePlanData", "useViewMode", "InviteDialog", "DeleteDialog", "OptOutDialog", "ConfirmDialog", "updateMilestone", "updateTask", "updateSubtask", "addTask", "addSubtask", "deleteTask", "deleteSubtask", "assignMembersToTask", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "PlanDetail", "_planInfo$user_access", "_planInfo$user_access2", "_planInfo$user_access3", "param", "activeTab", "setActiveTab", "localStorage", "getItem", "plan", "setPlan", "dialogState", "setDialogState", "invite", "delete", "optOut", "selectedTaskToDelete", "selectedSubtaskToDelete", "planInfo", "loading", "error", "handleDeletePlan", "handleOptOutPlan", "handleInviteUser", "calculatePlanStats", "calculateSubtaskProgress", "getSubtaskStatus", "calculateTaskProgress", "getTaskStatus", "calculateMilestoneProgress", "getMilestoneStatus", "viewMode", "handleViewModeChange", "removeItem", "stats", "openDialog", "dialogName", "prev", "closeDialog", "handleTabChange", "_", "newValue", "setItem", "handleSwitchToAgent", "conversationData", "console", "log", "JSON", "stringify", "refreshPlanData", "window", "location", "reload", "handleAddAccess", "email", "accessLevel", "response", "fetch", "method", "headers", "body", "access_level", "ok", "json", "Error", "success", "message", "handleUpdateAccess", "accessId", "isHeadOwner", "arguments", "length", "undefined", "is_head_owner", "handleRemoveAccess", "handleUpdateMilestone", "updatedMilestone", "updatedPlan", "milestoneIndex", "milestones", "findIndex", "m", "id", "handleUpdateTask", "updatedTask", "tasks", "some", "t", "taskIndex", "handleUpdateSubtask", "updatedSubtask", "taskFound", "i", "milestone", "j", "task", "subtasks", "subtaskIndex", "s", "handleAddTask", "newTask", "taskToAdd", "data", "Date", "now", "push", "handleAddSubtask", "newSubtask", "slug", "subtaskToAdd", "handleDeleteTask", "taskToDelete", "filter", "handleDeleteSubtask", "subtaskToDelete", "original<PERSON>ength", "handleAssignMembers", "taskToAssign", "memberIds", "assignedMembers", "map", "memberId", "_planInfo$invited_use", "owner", "first_name", "last_name", "avatar", "invitedUser", "invited_users", "find", "user", "invited_user_info", "member", "assignees", "className", "container", "children", "severity", "sx", "mt", "max<PERSON><PERSON><PERSON>", "padding", "minHeight", "fontFamily", "loadingContainer", "size", "color", "onViewModeChange", "onOpenInviteDialog", "onOpenDeleteDialog", "onOpenOptOutDialog", "borderBottom", "borderColor", "mb", "value", "onChange", "variant", "scrollButtons", "textTransform", "fontWeight", "fontSize", "min<PERSON><PERSON><PERSON>", "px", "py", "backgroundColor", "height", "icon", "width", "iconPosition", "label", "gap", "user_access_level", "tab<PERSON>ontent", "overviewTab", "display", "flexDirection", "xs", "md", "userAccessLevel", "onAddAccess", "onUpdateAccess", "onRemoveAccess", "onPlanUpdate", "onSwitchToAgent", "compact", "showSubtasks", "onUpdateMilestone", "onUpdateTask", "onUpdateSubtask", "onAddTask", "onAddSubtask", "onDeleteTask", "onDeleteSubtask", "onAssignMembers", "invitedUsers", "<PERSON><PERSON><PERSON><PERSON>", "open", "onClose", "onInvite", "onDelete", "onOptOut", "onConfirm", "title", "description", "name"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useParams } from 'react-router-dom';\r\nimport { Container, Box, Tabs, Tab, CircularProgress, Alert } from '@mui/material';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\r\nimport { successSnackbar, errorSnackbar } from 'components/Snackbar/index';\r\nimport { toast } from 'react-toastify';\r\nimport { getHeaders } from \"helpers/functions\";\r\n\r\n// Components\r\nimport Header from './components/Header';\r\nimport Description from './components/Description';\r\nimport Statistics from './components/Statistics';\r\nimport Progress from './components/Progress';\r\nimport MilestoneList from './components/MilestoneList';\r\nimport MilestoneOverview from './components/MilestoneOverview';\r\nimport AccessManagement from './components/AccessManagement';\r\nimport ChatbotBar from './components/ChatbotBar';\r\nimport AgentTab from './components/AgentTab';\r\n\r\n// Hooks\r\nimport usePlanData from './hooks/usePlanData';\r\nimport useViewMode from './hooks/useViewMode';\r\n\r\n// Dialogs\r\nimport InviteDialog from './dialogs/InviteDialog';\r\nimport DeleteDialog from './dialogs/DeleteDialog';\r\nimport OptOutDialog from './dialogs/OptOutDialog';\r\nimport ConfirmDialog from './dialogs/ConfirmDialog';\r\n\r\n// Services\r\nimport {\r\n  updateMilestone,\r\n  updateTask,\r\n  updateSubtask,\r\n  addTask,\r\n  addSubtask,\r\n  deleteTask,\r\n  deleteSubtask,\r\n  assignMembersToTask\r\n} from '../services';\r\n\r\n// Styles\r\nimport styles from './styles.module.scss';\r\n\r\nconst PlanDetail = () => {\r\n  const { param } = useParams();\r\n  const [activeTab, setActiveTab] = useState(() => {\r\n    // Get active tab value from localStorage, default to 'overview' if not found\r\n    return localStorage.getItem(`plan_${param}_activeTab`) || 'overview';\r\n  });\r\n  const [plan, setPlan] = useState(null);\r\n  const [dialogState, setDialogState] = useState({\r\n    invite: false,\r\n    delete: false,\r\n    optOut: false,\r\n    deleteTask: false,\r\n    deleteSubtask: false\r\n  });\r\n  const [selectedTaskToDelete, ] = useState(null);\r\n  const [selectedSubtaskToDelete, ] = useState(null);\r\n\r\n  // Custom hooks\r\n  const {\r\n    planInfo,\r\n    loading,\r\n    error,\r\n    handleDeletePlan,\r\n    handleOptOutPlan,\r\n    handleInviteUser,\r\n    calculatePlanStats,\r\n    calculateSubtaskProgress,\r\n    getSubtaskStatus,\r\n    calculateTaskProgress,\r\n    getTaskStatus,\r\n    calculateMilestoneProgress,\r\n    getMilestoneStatus\r\n  } = usePlanData(param);\r\n\r\n  const {\r\n    viewMode,\r\n    handleViewModeChange\r\n  } = useViewMode();\r\n\r\n  // Update plan state when planInfo changes\r\n  useEffect(() => {\r\n    if (planInfo) {\r\n      setPlan(planInfo);\r\n    }\r\n  }, [planInfo]);\r\n\r\n  // Remove active tab from localStorage when component unmounts\r\n  useEffect(() => {\r\n    return () => {\r\n      localStorage.removeItem(`plan_${param}_activeTab`);\r\n    };\r\n  }, [param]);\r\n\r\n  // Calculate plan statistics\r\n  const stats = calculatePlanStats ? calculatePlanStats(planInfo) : null;\r\n\r\n  // Dialog handlers\r\n  const openDialog = (dialogName) => {\r\n    setDialogState(prev => ({ ...prev, [dialogName]: true }));\r\n  };\r\n\r\n  const closeDialog = (dialogName) => {\r\n    setDialogState(prev => ({ ...prev, [dialogName]: false }));\r\n  };\r\n\r\n  // Tab change handler\r\n  const handleTabChange = (_, newValue) => {\r\n    setActiveTab(newValue);\r\n    // Save active tab to localStorage\r\n    localStorage.setItem(`plan_${param}_activeTab`, newValue);\r\n  };\r\n\r\n  // Handle switching to Agent tab from ChatbotBar\r\n  const handleSwitchToAgent = (conversationData) => {\r\n    console.log('PlanDetail - Switching to agent tab with data:', conversationData); // Debug log\r\n    setActiveTab('agent');\r\n    localStorage.setItem(`plan_${param}_activeTab`, 'agent');\r\n    // Store the conversation data for the Agent tab to pick up\r\n    localStorage.setItem('pending_agent_message', JSON.stringify(conversationData));\r\n  };\r\n\r\n  // Refresh plan data\r\n  const refreshPlanData = async () => {\r\n    try {\r\n      window.location.reload(); // Simple refresh for now\r\n    } catch (error) {\r\n      console.error('Error refreshing plan data:', error);\r\n    }\r\n  };\r\n\r\n  // Access management handlers\r\n  const handleAddAccess = async (email, accessLevel) => {\r\n    try {\r\n      const response = await fetch(`${APIURL}/api/plans/${param}/access`, {\r\n        method: 'POST',\r\n        headers: {\r\n          ...getHeaders(),\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify({ email, access_level: accessLevel })\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const error = await response.json();\r\n        throw new Error(error.error || 'Failed to add access');\r\n      }\r\n\r\n      // Refresh plan data\r\n      await refreshPlanData();\r\n      toast.success('Access granted successfully');\r\n    } catch (error) {\r\n      console.error('Error adding access:', error);\r\n      toast.error(error.message || 'Failed to add access');\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const handleUpdateAccess = async (accessId, accessLevel, isHeadOwner = false) => {\r\n    try {\r\n      const response = await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          ...getHeaders(),\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify({\r\n          access_level: accessLevel,\r\n          is_head_owner: isHeadOwner\r\n        })\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const error = await response.json();\r\n        throw new Error(error.error || 'Failed to update access');\r\n      }\r\n\r\n      // Refresh plan data\r\n      await refreshPlanData();\r\n      toast.success('Access updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating access:', error);\r\n      toast.error(error.message || 'Failed to update access');\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const handleRemoveAccess = async (accessId) => {\r\n    try {\r\n      const response = await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`, {\r\n        method: 'DELETE',\r\n        headers: getHeaders()\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const error = await response.json();\r\n        throw new Error(error.error || 'Failed to remove access');\r\n      }\r\n\r\n      // Refresh plan data\r\n      await refreshPlanData();\r\n      toast.success('Access removed successfully');\r\n    } catch (error) {\r\n      console.error('Error removing access:', error);\r\n      toast.error(error.message || 'Failed to remove access');\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Handle milestone update\r\n  const handleUpdateMilestone = async (updatedMilestone) => {\r\n    try {\r\n      console.log('Updating milestone:', updatedMilestone);\r\n      await updateMilestone(updatedMilestone);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === updatedMilestone.id);\r\n\r\n      if (milestoneIndex !== -1) {\r\n        updatedPlan.milestones[milestoneIndex] = {\r\n          ...updatedPlan.milestones[milestoneIndex],\r\n          ...updatedMilestone\r\n        };\r\n        setPlan(updatedPlan);\r\n      }\r\n\r\n      successSnackbar('Milestone updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating milestone:', error);\r\n      errorSnackbar('Failed to update milestone');\r\n    }\r\n  };\r\n\r\n  // Handle task update\r\n  const handleUpdateTask = async (updatedTask) => {\r\n    try {\r\n      console.log('Updating task:', updatedTask);\r\n      await updateTask(updatedTask);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m =>\r\n        m.tasks && m.tasks.some(t => t.id === updatedTask.id)\r\n      );\r\n\r\n      if (milestoneIndex !== -1) {\r\n        const taskIndex = updatedPlan.milestones[milestoneIndex].tasks.findIndex(t => t.id === updatedTask.id);\r\n\r\n        if (taskIndex !== -1) {\r\n          updatedPlan.milestones[milestoneIndex].tasks[taskIndex] = {\r\n            ...updatedPlan.milestones[milestoneIndex].tasks[taskIndex],\r\n            ...updatedTask\r\n          };\r\n          setPlan(updatedPlan);\r\n        }\r\n      }\r\n\r\n      successSnackbar('Task updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating task:', error);\r\n      errorSnackbar('Failed to update task');\r\n    }\r\n  };\r\n\r\n  // Handle subtask update\r\n  const handleUpdateSubtask = async (updatedSubtask) => {\r\n    try {\r\n      console.log('Updating subtask:', updatedSubtask);\r\n\r\n      // Call API to update subtask\r\n      await updateSubtask(updatedSubtask);\r\n\r\n      // Create a copy of current plan to update\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find the task containing the subtask\r\n      let taskFound = false;\r\n\r\n      // Update subtask in state\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n          if (!task.subtasks) continue;\r\n\r\n          // Update subtask in task\r\n          const subtaskIndex = task.subtasks.findIndex(s => s.id === updatedSubtask.id);\r\n          if (subtaskIndex !== -1) {\r\n            // Update subtask\r\n            task.subtasks[subtaskIndex] = {\r\n              ...task.subtasks[subtaskIndex],\r\n              ...updatedSubtask\r\n            };\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      // Update state with new plan\r\n      setPlan(updatedPlan);\r\n\r\n      successSnackbar('Subtask updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating subtask:', error);\r\n      errorSnackbar('Failed to update subtask');\r\n    }\r\n  };\r\n\r\n  // Handle add task\r\n  const handleAddTask = async (newTask) => {\r\n    try {\r\n      console.log('Adding new task:', newTask);\r\n      const response = await addTask(newTask);\r\n      console.log('Add task response:', response);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find milestone to add new task\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === newTask.milestone);\r\n\r\n      if (milestoneIndex !== -1) {\r\n        // Add new task to milestone\r\n        if (!updatedPlan.milestones[milestoneIndex].tasks) {\r\n          updatedPlan.milestones[milestoneIndex].tasks = [];\r\n        }\r\n\r\n        // Add the new task with data from response\r\n        const taskToAdd = response.data || {\r\n          ...newTask,\r\n          id: Date.now(), // Temporary ID if response doesn't provide one\r\n          subtasks: []\r\n        };\r\n\r\n        updatedPlan.milestones[milestoneIndex].tasks.push(taskToAdd);\r\n        setPlan(updatedPlan);\r\n      }\r\n\r\n      successSnackbar('Task added successfully');\r\n    } catch (error) {\r\n      console.error('Error adding task:', error);\r\n      errorSnackbar('Failed to add task');\r\n    }\r\n  };\r\n\r\n  // Handle add subtask\r\n  const handleAddSubtask = async (newSubtask) => {\r\n    try {\r\n      console.log('Adding new subtask:', newSubtask);\r\n      const response = await addSubtask(newSubtask);\r\n      console.log('Add subtask response:', response);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find task to add new subtask\r\n      let taskFound = false;\r\n\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n\r\n          if (task.slug === newSubtask.task) {\r\n            // Add new subtask to task\r\n            if (!task.subtasks) {\r\n              task.subtasks = [];\r\n            }\r\n\r\n            // Add the new subtask with data from response\r\n            const subtaskToAdd = response.data || {\r\n              ...newSubtask,\r\n              id: Date.now() // Temporary ID if response doesn't provide one\r\n            };\r\n\r\n            task.subtasks.push(subtaskToAdd);\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      setPlan(updatedPlan);\r\n      successSnackbar('Subtask added successfully');\r\n    } catch (error) {\r\n      console.error('Error adding subtask:', error);\r\n      errorSnackbar('Failed to add subtask');\r\n    }\r\n  };\r\n\r\n  // Handle delete task\r\n  const handleDeleteTask = async (taskToDelete) => {\r\n    try {\r\n      console.log('Deleting task:', taskToDelete);\r\n      await deleteTask(taskToDelete.slug);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find milestone containing the task to delete\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m =>\r\n        m.tasks && m.tasks.some(t => t.id === taskToDelete.id)\r\n      );\r\n\r\n      if (milestoneIndex !== -1) {\r\n        // Filter out the task to delete\r\n        updatedPlan.milestones[milestoneIndex].tasks = updatedPlan.milestones[milestoneIndex].tasks.filter(\r\n          t => t.id !== taskToDelete.id\r\n        );\r\n\r\n        setPlan(updatedPlan);\r\n      }\r\n\r\n      successSnackbar('Task deleted successfully');\r\n    } catch (error) {\r\n      console.error('Error deleting task:', error);\r\n      errorSnackbar('Failed to delete task');\r\n    }\r\n  };\r\n\r\n  // Handle delete subtask\r\n  const handleDeleteSubtask = async (subtaskToDelete) => {\r\n    try {\r\n      console.log('Deleting subtask:', subtaskToDelete);\r\n      await deleteSubtask(subtaskToDelete.slug);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find task containing the subtask to delete\r\n      let taskFound = false;\r\n\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n          if (!task.subtasks) continue;\r\n\r\n          // Filter out the subtask to delete\r\n          const originalLength = task.subtasks.length;\r\n          task.subtasks = task.subtasks.filter(s => s.id !== subtaskToDelete.id);\r\n\r\n          if (task.subtasks.length < originalLength) {\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      setPlan(updatedPlan);\r\n      toast.success('Subtask deleted successfully');\r\n    } catch (error) {\r\n      console.error('Error deleting subtask:', error);\r\n      toast.error('Failed to delete subtask');\r\n    }\r\n  };\r\n\r\n  // Handle assign members to task\r\n  const handleAssignMembers = async (taskToAssign, memberIds) => {\r\n    try {\r\n      console.log('Assigning members to task:', taskToAssign, memberIds);\r\n      await assignMembersToTask(taskToAssign.slug, memberIds);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find the task to update\r\n      let taskFound = false;\r\n\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n          if (task.id === taskToAssign.id) {\r\n            // Update assignees\r\n            // Find user objects for the selected member IDs\r\n            const assignedMembers = memberIds.map(memberId => {\r\n              // Check if it's the plan owner\r\n              if (planInfo.owner && planInfo.owner.id === memberId) {\r\n                return {\r\n                  id: planInfo.owner.id,\r\n                  first_name: planInfo.owner.first_name,\r\n                  last_name: planInfo.owner.last_name,\r\n                  email: planInfo.owner.email,\r\n                  avatar: planInfo.owner.avatar\r\n                };\r\n              }\r\n\r\n              // Check in invited users\r\n              const invitedUser = planInfo.invited_users?.find(\r\n                user => user.invited_user_info && user.invited_user_info.id === memberId\r\n              );\r\n\r\n              if (invitedUser) {\r\n                return {\r\n                  id: invitedUser.invited_user_info.id,\r\n                  first_name: invitedUser.invited_user_info.first_name,\r\n                  last_name: invitedUser.invited_user_info.last_name,\r\n                  email: invitedUser.email,\r\n                  avatar: invitedUser.invited_user_info.avatar\r\n                };\r\n              }\r\n\r\n              return null;\r\n            }).filter(member => member !== null);\r\n\r\n            task.assignees = assignedMembers;\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      setPlan(updatedPlan);\r\n      toast.success('Members assigned successfully');\r\n    } catch (error) {\r\n      console.error('Error assigning members to task:', error);\r\n      toast.error('Failed to assign members to task');\r\n    }\r\n  };\r\n\r\n  if (error) {\r\n    return (\r\n      <Container className={styles.container}>\r\n        <Alert severity=\"error\" sx={{ mt: 4 }}>\r\n          An error occurred while loading plan information. Please try again later.\r\n        </Alert>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Container\r\n      maxWidth=\"lg\"\r\n      className={styles.container}\r\n      sx={{\r\n        padding: '20px',\r\n        minHeight: 'calc(100vh - 65px)',\r\n        fontFamily: '\"Recursive Variable\", sans-serif'\r\n      }}\r\n    >\r\n      {loading ? (\r\n        <Box className={styles.loadingContainer}>\r\n          <CircularProgress size={60} sx={{ color: mainYellowColor }} />\r\n        </Box>\r\n      ) : (\r\n        <>\r\n          {/* Header Section */}\r\n          <Header\r\n            planInfo={planInfo}\r\n            viewMode={viewMode}\r\n            onViewModeChange={handleViewModeChange}\r\n            onOpenInviteDialog={() => openDialog('invite')}\r\n            onOpenDeleteDialog={() => openDialog('delete')}\r\n            onOpenOptOutDialog={() => openDialog('optOut')}\r\n          />\r\n\r\n          {/* Tabs Navigation */}\r\n          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 0.5, mt: -1 }}>\r\n            <Tabs\r\n              value={activeTab}\r\n              onChange={handleTabChange}\r\n              variant=\"scrollable\"\r\n              scrollButtons=\"auto\"\r\n              sx={{\r\n                minHeight: '36px',\r\n                '& .MuiTab-root': {\r\n                  textTransform: 'none',\r\n                  fontWeight: 600,\r\n                  fontSize: '0.9rem',\r\n                  minWidth: 'auto',\r\n                  minHeight: '36px',\r\n                  px: 2,\r\n                  py: 0.5,\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                },\r\n                '& .Mui-selected': {\r\n                  color: `${mainYellowColor} !important`,\r\n                },\r\n                '& .MuiTabs-indicator': {\r\n                  backgroundColor: mainYellowColor,\r\n                  height: '2px'\r\n                }\r\n              }}\r\n            >\r\n              <Tab\r\n                icon={<Iconify icon=\"material-symbols:dashboard\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Overview\"\r\n                value=\"overview\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              <Tab\r\n                icon={<Iconify icon=\"material-symbols:view-list\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Project Details\"\r\n                value=\"milestones\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              <Tab\r\n                icon={<Iconify icon=\"mdi:robot\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Agent\"\r\n                value=\"agent\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              {planInfo?.user_access_level?.access_level === 'owner' && (\r\n                <Tab\r\n                  icon={<Iconify icon=\"material-symbols:security\" width={16} height={16} />}\r\n                  iconPosition=\"start\"\r\n                  label=\"Access\"\r\n                  value=\"access\"\r\n                  sx={{ gap: '4px' }}\r\n                />\r\n              )}\r\n            </Tabs>\r\n          </Box>\r\n\r\n          {/* Tab Content */}\r\n          <Box className={styles.tabContent}>\r\n            {activeTab === 'overview' && (\r\n              <Box className={styles.overviewTab} sx={{ gap: 0.5 }}>\r\n                <Description planInfo={planInfo} />\r\n                <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 1, mb: 0.5 }}>\r\n                  <Statistics stats={stats} />\r\n                  <Progress stats={stats} />\r\n                </Box>\r\n                {planInfo?.user_access_level?.access_level === 'owner' && (\r\n                  <AccessManagement\r\n                    planInfo={planInfo}\r\n                    userAccessLevel={planInfo?.user_access_level}\r\n                    onAddAccess={handleAddAccess}\r\n                    onUpdateAccess={handleUpdateAccess}\r\n                    onRemoveAccess={handleRemoveAccess}\r\n                  />\r\n                )}\r\n                <MilestoneOverview\r\n                  milestones={planInfo?.milestones}\r\n                  calculateMilestoneProgress={calculateMilestoneProgress}\r\n                  getMilestoneStatus={getMilestoneStatus}\r\n                  calculateTaskProgress={calculateTaskProgress}\r\n                  getTaskStatus={getTaskStatus}\r\n                  calculateSubtaskProgress={calculateSubtaskProgress}\r\n                  getSubtaskStatus={getSubtaskStatus}\r\n                />\r\n              </Box>\r\n            )}\r\n\r\n            {activeTab === 'milestones' && (\r\n              <>\r\n                <ChatbotBar\r\n                  planInfo={planInfo}\r\n                  onPlanUpdate={(updatedPlan) => {\r\n                    // Handle plan updates from AI agent\r\n                    console.log('Plan updated by AI:', updatedPlan);\r\n                  }}\r\n                  onSwitchToAgent={handleSwitchToAgent}\r\n                />\r\n                <MilestoneList\r\n                  milestones={planInfo?.milestones}\r\n                  viewMode={viewMode}\r\n                  compact={false}\r\n                  showSubtasks={true}\r\n                  calculateMilestoneProgress={calculateMilestoneProgress}\r\n                  getMilestoneStatus={getMilestoneStatus}\r\n                  calculateTaskProgress={calculateTaskProgress}\r\n                  getTaskStatus={getTaskStatus}\r\n                  calculateSubtaskProgress={calculateSubtaskProgress}\r\n                  getSubtaskStatus={getSubtaskStatus}\r\n                  onUpdateMilestone={handleUpdateMilestone}\r\n                  onUpdateTask={handleUpdateTask}\r\n                  onUpdateSubtask={handleUpdateSubtask}\r\n                  onAddTask={handleAddTask}\r\n                  onAddSubtask={handleAddSubtask}\r\n                  onDeleteTask={handleDeleteTask}\r\n                  onDeleteSubtask={handleDeleteSubtask}\r\n                  onAssignMembers={handleAssignMembers}\r\n                  invitedUsers={planInfo?.invited_users || []}\r\n                  planOwner={planInfo?.owner}\r\n                />\r\n              </>\r\n            )}\r\n\r\n            {activeTab === 'agent' && (\r\n              <AgentTab\r\n                planInfo={planInfo}\r\n                onPlanUpdate={() => {\r\n                  // Refresh plan data when AI agent makes changes\r\n                  console.log('Plan updated by AI agent, refreshing data...');\r\n                  refreshPlanData();\r\n                }}\r\n              />\r\n            )}\r\n\r\n            {activeTab === 'access' && planInfo?.user_access_level?.access_level === 'owner' && (\r\n              <AccessManagement\r\n                planInfo={planInfo}\r\n                userAccessLevel={planInfo?.user_access_level}\r\n                onAddAccess={handleAddAccess}\r\n                onUpdateAccess={handleUpdateAccess}\r\n                onRemoveAccess={handleRemoveAccess}\r\n              />\r\n            )}\r\n          </Box>\r\n        </>\r\n      )}\r\n\r\n      {/* Dialogs */}\r\n      <InviteDialog\r\n        open={dialogState.invite}\r\n        onClose={() => closeDialog('invite')}\r\n        onInvite={handleInviteUser}\r\n        planInfo={planInfo}\r\n      />\r\n\r\n      <DeleteDialog\r\n        open={dialogState.delete}\r\n        onClose={() => closeDialog('delete')}\r\n        onDelete={handleDeletePlan}\r\n      />\r\n\r\n      <OptOutDialog\r\n        open={dialogState.optOut}\r\n        onClose={() => closeDialog('optOut')}\r\n        onOptOut={handleOptOutPlan}\r\n      />\r\n\r\n      <ConfirmDialog\r\n        open={dialogState.deleteTask}\r\n        onClose={() => setDialogState(prev => ({ ...prev, deleteTask: false }))}\r\n        onConfirm={() => handleDeleteTask(selectedTaskToDelete)}\r\n        title=\"Delete Task\"\r\n        description={`Are you sure you want to delete the task \"${selectedTaskToDelete?.name}\"? This action cannot be undone.`}\r\n      />\r\n\r\n      <ConfirmDialog\r\n        open={dialogState.deleteSubtask}\r\n        onClose={() => setDialogState(prev => ({ ...prev, deleteSubtask: false }))}\r\n        onConfirm={() => handleDeleteSubtask(selectedSubtaskToDelete)}\r\n        title=\"Delete Subtask\"\r\n        description={`Are you sure you want to delete the subtask \"${selectedSubtaskToDelete?.name}\"? This action cannot be undone.`}\r\n      />\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default PlanDetail;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,KAAQ,kBAAkB,CAC5C,OAASC,SAAS,CAAEC,GAAG,CAAEC,IAAI,CAAEC,GAAG,CAAEC,gBAAgB,CAAEC,KAAK,KAAQ,eAAe,CAClF,MAAO,CAAAC,OAAO,KAAM,0BAA0B,CAC9C,OAASC,eAAe,CAAEC,MAAM,KAAQ,mBAAmB,CAC3D,OAASC,eAAe,CAAEC,aAAa,KAAQ,2BAA2B,CAC1E,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,UAAU,KAAQ,mBAAmB,CAE9C;AACA,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,QAAQ,KAAM,uBAAuB,CAC5C,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,iBAAiB,KAAM,gCAAgC,CAC9D,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,QAAQ,KAAM,uBAAuB,CAE5C;AACA,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAE7C;AACA,MAAO,CAAAC,YAAY,KAAM,wBAAwB,CACjD,MAAO,CAAAC,YAAY,KAAM,wBAAwB,CACjD,MAAO,CAAAC,YAAY,KAAM,wBAAwB,CACjD,MAAO,CAAAC,aAAa,KAAM,yBAAyB,CAEnD;AACA,OACEC,eAAe,CACfC,UAAU,CACVC,aAAa,CACbC,OAAO,CACPC,UAAU,CACVC,UAAU,CACVC,aAAa,CACbC,mBAAmB,KACd,aAAa,CAEpB;AACA,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE1C,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACvB,KAAM,CAAEC,KAAM,CAAC,CAAGhD,SAAS,CAAC,CAAC,CAC7B,KAAM,CAACiD,SAAS,CAAEC,YAAY,CAAC,CAAGpD,QAAQ,CAAC,IAAM,CAC/C;AACA,MAAO,CAAAqD,YAAY,CAACC,OAAO,CAAC,QAAQJ,KAAK,YAAY,CAAC,EAAI,UAAU,CACtE,CAAC,CAAC,CACF,KAAM,CAACK,IAAI,CAAEC,OAAO,CAAC,CAAGxD,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACyD,WAAW,CAAEC,cAAc,CAAC,CAAG1D,QAAQ,CAAC,CAC7C2D,MAAM,CAAE,KAAK,CACbC,MAAM,CAAE,KAAK,CACbC,MAAM,CAAE,KAAK,CACbzB,UAAU,CAAE,KAAK,CACjBC,aAAa,CAAE,KACjB,CAAC,CAAC,CACF,KAAM,CAACyB,oBAAoB,CAAG,CAAG9D,QAAQ,CAAC,IAAI,CAAC,CAC/C,KAAM,CAAC+D,uBAAuB,CAAG,CAAG/D,QAAQ,CAAC,IAAI,CAAC,CAElD;AACA,KAAM,CACJgE,QAAQ,CACRC,OAAO,CACPC,KAAK,CACLC,gBAAgB,CAChBC,gBAAgB,CAChBC,gBAAgB,CAChBC,kBAAkB,CAClBC,wBAAwB,CACxBC,gBAAgB,CAChBC,qBAAqB,CACrBC,aAAa,CACbC,0BAA0B,CAC1BC,kBACF,CAAC,CAAGnD,WAAW,CAACyB,KAAK,CAAC,CAEtB,KAAM,CACJ2B,QAAQ,CACRC,oBACF,CAAC,CAAGpD,WAAW,CAAC,CAAC,CAEjB;AACAzB,SAAS,CAAC,IAAM,CACd,GAAI+D,QAAQ,CAAE,CACZR,OAAO,CAACQ,QAAQ,CAAC,CACnB,CACF,CAAC,CAAE,CAACA,QAAQ,CAAC,CAAC,CAEd;AACA/D,SAAS,CAAC,IAAM,CACd,MAAO,IAAM,CACXoD,YAAY,CAAC0B,UAAU,CAAC,QAAQ7B,KAAK,YAAY,CAAC,CACpD,CAAC,CACH,CAAC,CAAE,CAACA,KAAK,CAAC,CAAC,CAEX;AACA,KAAM,CAAA8B,KAAK,CAAGV,kBAAkB,CAAGA,kBAAkB,CAACN,QAAQ,CAAC,CAAG,IAAI,CAEtE;AACA,KAAM,CAAAiB,UAAU,CAAIC,UAAU,EAAK,CACjCxB,cAAc,CAACyB,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,UAAU,EAAG,IAAK,CAAC,CAAC,CAAC,CAC3D,CAAC,CAED,KAAM,CAAAE,WAAW,CAAIF,UAAU,EAAK,CAClCxB,cAAc,CAACyB,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,UAAU,EAAG,KAAM,CAAC,CAAC,CAAC,CAC5D,CAAC,CAED;AACA,KAAM,CAAAG,eAAe,CAAGA,CAACC,CAAC,CAAEC,QAAQ,GAAK,CACvCnC,YAAY,CAACmC,QAAQ,CAAC,CACtB;AACAlC,YAAY,CAACmC,OAAO,CAAC,QAAQtC,KAAK,YAAY,CAAEqC,QAAQ,CAAC,CAC3D,CAAC,CAED;AACA,KAAM,CAAAE,mBAAmB,CAAIC,gBAAgB,EAAK,CAChDC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAEF,gBAAgB,CAAC,CAAE;AACjFtC,YAAY,CAAC,OAAO,CAAC,CACrBC,YAAY,CAACmC,OAAO,CAAC,QAAQtC,KAAK,YAAY,CAAE,OAAO,CAAC,CACxD;AACAG,YAAY,CAACmC,OAAO,CAAC,uBAAuB,CAAEK,IAAI,CAACC,SAAS,CAACJ,gBAAgB,CAAC,CAAC,CACjF,CAAC,CAED;AACA,KAAM,CAAAK,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACFC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAE;AAC5B,CAAE,MAAOhC,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CACF,CAAC,CAED;AACA,KAAM,CAAAiC,eAAe,CAAG,KAAAA,CAAOC,KAAK,CAAEC,WAAW,GAAK,CACpD,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAG5F,MAAM,cAAcuC,KAAK,SAAS,CAAE,CAClEsD,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,GAAG1F,UAAU,CAAC,CAAC,CACf,cAAc,CAAE,kBAClB,CAAC,CACD2F,IAAI,CAAEb,IAAI,CAACC,SAAS,CAAC,CAAEM,KAAK,CAAEO,YAAY,CAAEN,WAAY,CAAC,CAC3D,CAAC,CAAC,CAEF,GAAI,CAACC,QAAQ,CAACM,EAAE,CAAE,CAChB,KAAM,CAAA1C,KAAK,CAAG,KAAM,CAAAoC,QAAQ,CAACO,IAAI,CAAC,CAAC,CACnC,KAAM,IAAI,CAAAC,KAAK,CAAC5C,KAAK,CAACA,KAAK,EAAI,sBAAsB,CAAC,CACxD,CAEA;AACA,KAAM,CAAA6B,eAAe,CAAC,CAAC,CACvBjF,KAAK,CAACiG,OAAO,CAAC,6BAA6B,CAAC,CAC9C,CAAE,MAAO7C,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5CpD,KAAK,CAACoD,KAAK,CAACA,KAAK,CAAC8C,OAAO,EAAI,sBAAsB,CAAC,CACpD,KAAM,CAAA9C,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAA+C,kBAAkB,CAAG,cAAAA,CAAOC,QAAQ,CAAEb,WAAW,CAA0B,IAAxB,CAAAc,WAAW,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC1E,GAAI,CACF,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAG5F,MAAM,cAAcuC,KAAK,WAAWgE,QAAQ,EAAE,CAAE,CAC9EV,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,GAAG1F,UAAU,CAAC,CAAC,CACf,cAAc,CAAE,kBAClB,CAAC,CACD2F,IAAI,CAAEb,IAAI,CAACC,SAAS,CAAC,CACnBa,YAAY,CAAEN,WAAW,CACzBkB,aAAa,CAAEJ,WACjB,CAAC,CACH,CAAC,CAAC,CAEF,GAAI,CAACb,QAAQ,CAACM,EAAE,CAAE,CAChB,KAAM,CAAA1C,KAAK,CAAG,KAAM,CAAAoC,QAAQ,CAACO,IAAI,CAAC,CAAC,CACnC,KAAM,IAAI,CAAAC,KAAK,CAAC5C,KAAK,CAACA,KAAK,EAAI,yBAAyB,CAAC,CAC3D,CAEA;AACA,KAAM,CAAA6B,eAAe,CAAC,CAAC,CACvBjF,KAAK,CAACiG,OAAO,CAAC,6BAA6B,CAAC,CAC9C,CAAE,MAAO7C,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CpD,KAAK,CAACoD,KAAK,CAACA,KAAK,CAAC8C,OAAO,EAAI,yBAAyB,CAAC,CACvD,KAAM,CAAA9C,KAAK,CACb,CACF,CAAC,CAED,KAAM,CAAAsD,kBAAkB,CAAG,KAAO,CAAAN,QAAQ,EAAK,CAC7C,GAAI,CACF,KAAM,CAAAZ,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAG5F,MAAM,cAAcuC,KAAK,WAAWgE,QAAQ,EAAE,CAAE,CAC9EV,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE1F,UAAU,CAAC,CACtB,CAAC,CAAC,CAEF,GAAI,CAACuF,QAAQ,CAACM,EAAE,CAAE,CAChB,KAAM,CAAA1C,KAAK,CAAG,KAAM,CAAAoC,QAAQ,CAACO,IAAI,CAAC,CAAC,CACnC,KAAM,IAAI,CAAAC,KAAK,CAAC5C,KAAK,CAACA,KAAK,EAAI,yBAAyB,CAAC,CAC3D,CAEA;AACA,KAAM,CAAA6B,eAAe,CAAC,CAAC,CACvBjF,KAAK,CAACiG,OAAO,CAAC,6BAA6B,CAAC,CAC9C,CAAE,MAAO7C,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CpD,KAAK,CAACoD,KAAK,CAACA,KAAK,CAAC8C,OAAO,EAAI,yBAAyB,CAAC,CACvD,KAAM,CAAA9C,KAAK,CACb,CACF,CAAC,CAED;AACA,KAAM,CAAAuD,qBAAqB,CAAG,KAAO,CAAAC,gBAAgB,EAAK,CACxD,GAAI,CACF/B,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE8B,gBAAgB,CAAC,CACpD,KAAM,CAAA3F,eAAe,CAAC2F,gBAAgB,CAAC,CAEvC;AACA,KAAM,CAAAC,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAC/B,KAAM,CAAAqE,cAAc,CAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,EAAE,GAAKN,gBAAgB,CAACM,EAAE,CAAC,CAE1F,GAAIJ,cAAc,GAAK,CAAC,CAAC,CAAE,CACzBD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAAG,CACvC,GAAGD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CACzC,GAAGF,gBACL,CAAC,CACDlE,OAAO,CAACmE,WAAW,CAAC,CACtB,CAEA/G,eAAe,CAAC,gCAAgC,CAAC,CACnD,CAAE,MAAOsD,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDrD,aAAa,CAAC,4BAA4B,CAAC,CAC7C,CACF,CAAC,CAED;AACA,KAAM,CAAAoH,gBAAgB,CAAG,KAAO,CAAAC,WAAW,EAAK,CAC9C,GAAI,CACFvC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEsC,WAAW,CAAC,CAC1C,KAAM,CAAAlG,UAAU,CAACkG,WAAW,CAAC,CAE7B;AACA,KAAM,CAAAP,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAC/B,KAAM,CAAAqE,cAAc,CAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,EACvDA,CAAC,CAACI,KAAK,EAAIJ,CAAC,CAACI,KAAK,CAACC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACL,EAAE,GAAKE,WAAW,CAACF,EAAE,CACtD,CAAC,CAED,GAAIJ,cAAc,GAAK,CAAC,CAAC,CAAE,CACzB,KAAM,CAAAU,SAAS,CAAGX,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACL,SAAS,CAACO,CAAC,EAAIA,CAAC,CAACL,EAAE,GAAKE,WAAW,CAACF,EAAE,CAAC,CAEtG,GAAIM,SAAS,GAAK,CAAC,CAAC,CAAE,CACpBX,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACG,SAAS,CAAC,CAAG,CACxD,GAAGX,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACG,SAAS,CAAC,CAC1D,GAAGJ,WACL,CAAC,CACD1E,OAAO,CAACmE,WAAW,CAAC,CACtB,CACF,CAEA/G,eAAe,CAAC,2BAA2B,CAAC,CAC9C,CAAE,MAAOsD,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5CrD,aAAa,CAAC,uBAAuB,CAAC,CACxC,CACF,CAAC,CAED;AACA,KAAM,CAAA0H,mBAAmB,CAAG,KAAO,CAAAC,cAAc,EAAK,CACpD,GAAI,CACF7C,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAE4C,cAAc,CAAC,CAEhD;AACA,KAAM,CAAAvG,aAAa,CAACuG,cAAc,CAAC,CAEnC;AACA,KAAM,CAAAb,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAE/B;AACA,GAAI,CAAAkF,SAAS,CAAG,KAAK,CAErB;AACA,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGf,WAAW,CAACE,UAAU,CAACR,MAAM,CAAEqB,CAAC,EAAE,CAAE,CACtD,KAAM,CAAAC,SAAS,CAAGhB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC,CAC3C,GAAI,CAACC,SAAS,CAACR,KAAK,CAAE,SAEtB,IAAK,GAAI,CAAAS,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGD,SAAS,CAACR,KAAK,CAACd,MAAM,CAAEuB,CAAC,EAAE,CAAE,CAC/C,KAAM,CAAAC,IAAI,CAAGF,SAAS,CAACR,KAAK,CAACS,CAAC,CAAC,CAC/B,GAAI,CAACC,IAAI,CAACC,QAAQ,CAAE,SAEpB;AACA,KAAM,CAAAC,YAAY,CAAGF,IAAI,CAACC,QAAQ,CAAChB,SAAS,CAACkB,CAAC,EAAIA,CAAC,CAAChB,EAAE,GAAKQ,cAAc,CAACR,EAAE,CAAC,CAC7E,GAAIe,YAAY,GAAK,CAAC,CAAC,CAAE,CACvB;AACAF,IAAI,CAACC,QAAQ,CAACC,YAAY,CAAC,CAAG,CAC5B,GAAGF,IAAI,CAACC,QAAQ,CAACC,YAAY,CAAC,CAC9B,GAAGP,cACL,CAAC,CACDC,SAAS,CAAG,IAAI,CAChB,MACF,CACF,CAEA,GAAIA,SAAS,CAAE,MACjB,CAEA;AACAjF,OAAO,CAACmE,WAAW,CAAC,CAEpB/G,eAAe,CAAC,8BAA8B,CAAC,CACjD,CAAE,MAAOsD,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CrD,aAAa,CAAC,0BAA0B,CAAC,CAC3C,CACF,CAAC,CAED;AACA,KAAM,CAAAoI,aAAa,CAAG,KAAO,CAAAC,OAAO,EAAK,CACvC,GAAI,CACFvD,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEsD,OAAO,CAAC,CACxC,KAAM,CAAA5C,QAAQ,CAAG,KAAM,CAAApE,OAAO,CAACgH,OAAO,CAAC,CACvCvD,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAEU,QAAQ,CAAC,CAE3C;AACA,KAAM,CAAAqB,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAE/B;AACA,KAAM,CAAAqE,cAAc,CAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,EAAIA,CAAC,CAACC,EAAE,GAAKkB,OAAO,CAACP,SAAS,CAAC,CAExF,GAAIf,cAAc,GAAK,CAAC,CAAC,CAAE,CACzB;AACA,GAAI,CAACD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAAE,CACjDR,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAAG,EAAE,CACnD,CAEA;AACA,KAAM,CAAAgB,SAAS,CAAG7C,QAAQ,CAAC8C,IAAI,EAAI,CACjC,GAAGF,OAAO,CACVlB,EAAE,CAAEqB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE;AAChBR,QAAQ,CAAE,EACZ,CAAC,CAEDnB,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACoB,IAAI,CAACJ,SAAS,CAAC,CAC5D3F,OAAO,CAACmE,WAAW,CAAC,CACtB,CAEA/G,eAAe,CAAC,yBAAyB,CAAC,CAC5C,CAAE,MAAOsD,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1CrD,aAAa,CAAC,oBAAoB,CAAC,CACrC,CACF,CAAC,CAED;AACA,KAAM,CAAA2I,gBAAgB,CAAG,KAAO,CAAAC,UAAU,EAAK,CAC7C,GAAI,CACF9D,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE6D,UAAU,CAAC,CAC9C,KAAM,CAAAnD,QAAQ,CAAG,KAAM,CAAAnE,UAAU,CAACsH,UAAU,CAAC,CAC7C9D,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEU,QAAQ,CAAC,CAE9C;AACA,KAAM,CAAAqB,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAE/B;AACA,GAAI,CAAAkF,SAAS,CAAG,KAAK,CAErB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGf,WAAW,CAACE,UAAU,CAACR,MAAM,CAAEqB,CAAC,EAAE,CAAE,CACtD,KAAM,CAAAC,SAAS,CAAGhB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC,CAC3C,GAAI,CAACC,SAAS,CAACR,KAAK,CAAE,SAEtB,IAAK,GAAI,CAAAS,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGD,SAAS,CAACR,KAAK,CAACd,MAAM,CAAEuB,CAAC,EAAE,CAAE,CAC/C,KAAM,CAAAC,IAAI,CAAGF,SAAS,CAACR,KAAK,CAACS,CAAC,CAAC,CAE/B,GAAIC,IAAI,CAACa,IAAI,GAAKD,UAAU,CAACZ,IAAI,CAAE,CACjC;AACA,GAAI,CAACA,IAAI,CAACC,QAAQ,CAAE,CAClBD,IAAI,CAACC,QAAQ,CAAG,EAAE,CACpB,CAEA;AACA,KAAM,CAAAa,YAAY,CAAGrD,QAAQ,CAAC8C,IAAI,EAAI,CACpC,GAAGK,UAAU,CACbzB,EAAE,CAAEqB,IAAI,CAACC,GAAG,CAAC,CAAE;AACjB,CAAC,CAEDT,IAAI,CAACC,QAAQ,CAACS,IAAI,CAACI,YAAY,CAAC,CAChClB,SAAS,CAAG,IAAI,CAChB,MACF,CACF,CAEA,GAAIA,SAAS,CAAE,MACjB,CAEAjF,OAAO,CAACmE,WAAW,CAAC,CACpB/G,eAAe,CAAC,4BAA4B,CAAC,CAC/C,CAAE,MAAOsD,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CrD,aAAa,CAAC,uBAAuB,CAAC,CACxC,CACF,CAAC,CAED;AACA,KAAM,CAAA+I,gBAAgB,CAAG,KAAO,CAAAC,YAAY,EAAK,CAC/C,GAAI,CACFlE,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEiE,YAAY,CAAC,CAC3C,KAAM,CAAAzH,UAAU,CAACyH,YAAY,CAACH,IAAI,CAAC,CAEnC;AACA,KAAM,CAAA/B,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAE/B;AACA,KAAM,CAAAqE,cAAc,CAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,EACvDA,CAAC,CAACI,KAAK,EAAIJ,CAAC,CAACI,KAAK,CAACC,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACL,EAAE,GAAK6B,YAAY,CAAC7B,EAAE,CACvD,CAAC,CAED,GAAIJ,cAAc,GAAK,CAAC,CAAC,CAAE,CACzB;AACAD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAAGR,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAAC2B,MAAM,CAChGzB,CAAC,EAAIA,CAAC,CAACL,EAAE,GAAK6B,YAAY,CAAC7B,EAC7B,CAAC,CAEDxE,OAAO,CAACmE,WAAW,CAAC,CACtB,CAEA/G,eAAe,CAAC,2BAA2B,CAAC,CAC9C,CAAE,MAAOsD,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5CrD,aAAa,CAAC,uBAAuB,CAAC,CACxC,CACF,CAAC,CAED;AACA,KAAM,CAAAkJ,mBAAmB,CAAG,KAAO,CAAAC,eAAe,EAAK,CACrD,GAAI,CACFrE,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEoE,eAAe,CAAC,CACjD,KAAM,CAAA3H,aAAa,CAAC2H,eAAe,CAACN,IAAI,CAAC,CAEzC;AACA,KAAM,CAAA/B,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAE/B;AACA,GAAI,CAAAkF,SAAS,CAAG,KAAK,CAErB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGf,WAAW,CAACE,UAAU,CAACR,MAAM,CAAEqB,CAAC,EAAE,CAAE,CACtD,KAAM,CAAAC,SAAS,CAAGhB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC,CAC3C,GAAI,CAACC,SAAS,CAACR,KAAK,CAAE,SAEtB,IAAK,GAAI,CAAAS,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGD,SAAS,CAACR,KAAK,CAACd,MAAM,CAAEuB,CAAC,EAAE,CAAE,CAC/C,KAAM,CAAAC,IAAI,CAAGF,SAAS,CAACR,KAAK,CAACS,CAAC,CAAC,CAC/B,GAAI,CAACC,IAAI,CAACC,QAAQ,CAAE,SAEpB;AACA,KAAM,CAAAmB,cAAc,CAAGpB,IAAI,CAACC,QAAQ,CAACzB,MAAM,CAC3CwB,IAAI,CAACC,QAAQ,CAAGD,IAAI,CAACC,QAAQ,CAACgB,MAAM,CAACd,CAAC,EAAIA,CAAC,CAAChB,EAAE,GAAKgC,eAAe,CAAChC,EAAE,CAAC,CAEtE,GAAIa,IAAI,CAACC,QAAQ,CAACzB,MAAM,CAAG4C,cAAc,CAAE,CACzCxB,SAAS,CAAG,IAAI,CAChB,MACF,CACF,CAEA,GAAIA,SAAS,CAAE,MACjB,CAEAjF,OAAO,CAACmE,WAAW,CAAC,CACpB7G,KAAK,CAACiG,OAAO,CAAC,8BAA8B,CAAC,CAC/C,CAAE,MAAO7C,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CpD,KAAK,CAACoD,KAAK,CAAC,0BAA0B,CAAC,CACzC,CACF,CAAC,CAED;AACA,KAAM,CAAAgG,mBAAmB,CAAG,KAAAA,CAAOC,YAAY,CAAEC,SAAS,GAAK,CAC7D,GAAI,CACFzE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEuE,YAAY,CAAEC,SAAS,CAAC,CAClE,KAAM,CAAA9H,mBAAmB,CAAC6H,YAAY,CAACT,IAAI,CAAEU,SAAS,CAAC,CAEvD;AACA,KAAM,CAAAzC,WAAW,CAAG,CAAE,GAAGpE,IAAK,CAAC,CAE/B;AACA,GAAI,CAAAkF,SAAS,CAAG,KAAK,CAErB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGf,WAAW,CAACE,UAAU,CAACR,MAAM,CAAEqB,CAAC,EAAE,CAAE,CACtD,KAAM,CAAAC,SAAS,CAAGhB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC,CAC3C,GAAI,CAACC,SAAS,CAACR,KAAK,CAAE,SAEtB,IAAK,GAAI,CAAAS,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGD,SAAS,CAACR,KAAK,CAACd,MAAM,CAAEuB,CAAC,EAAE,CAAE,CAC/C,KAAM,CAAAC,IAAI,CAAGF,SAAS,CAACR,KAAK,CAACS,CAAC,CAAC,CAC/B,GAAIC,IAAI,CAACb,EAAE,GAAKmC,YAAY,CAACnC,EAAE,CAAE,CAC/B;AACA;AACA,KAAM,CAAAqC,eAAe,CAAGD,SAAS,CAACE,GAAG,CAACC,QAAQ,EAAI,KAAAC,qBAAA,CAChD;AACA,GAAIxG,QAAQ,CAACyG,KAAK,EAAIzG,QAAQ,CAACyG,KAAK,CAACzC,EAAE,GAAKuC,QAAQ,CAAE,CACpD,MAAO,CACLvC,EAAE,CAAEhE,QAAQ,CAACyG,KAAK,CAACzC,EAAE,CACrB0C,UAAU,CAAE1G,QAAQ,CAACyG,KAAK,CAACC,UAAU,CACrCC,SAAS,CAAE3G,QAAQ,CAACyG,KAAK,CAACE,SAAS,CACnCvE,KAAK,CAAEpC,QAAQ,CAACyG,KAAK,CAACrE,KAAK,CAC3BwE,MAAM,CAAE5G,QAAQ,CAACyG,KAAK,CAACG,MACzB,CAAC,CACH,CAEA;AACA,KAAM,CAAAC,WAAW,EAAAL,qBAAA,CAAGxG,QAAQ,CAAC8G,aAAa,UAAAN,qBAAA,iBAAtBA,qBAAA,CAAwBO,IAAI,CAC9CC,IAAI,EAAIA,IAAI,CAACC,iBAAiB,EAAID,IAAI,CAACC,iBAAiB,CAACjD,EAAE,GAAKuC,QAClE,CAAC,CAED,GAAIM,WAAW,CAAE,CACf,MAAO,CACL7C,EAAE,CAAE6C,WAAW,CAACI,iBAAiB,CAACjD,EAAE,CACpC0C,UAAU,CAAEG,WAAW,CAACI,iBAAiB,CAACP,UAAU,CACpDC,SAAS,CAAEE,WAAW,CAACI,iBAAiB,CAACN,SAAS,CAClDvE,KAAK,CAAEyE,WAAW,CAACzE,KAAK,CACxBwE,MAAM,CAAEC,WAAW,CAACI,iBAAiB,CAACL,MACxC,CAAC,CACH,CAEA,MAAO,KAAI,CACb,CAAC,CAAC,CAACd,MAAM,CAACoB,MAAM,EAAIA,MAAM,GAAK,IAAI,CAAC,CAEpCrC,IAAI,CAACsC,SAAS,CAAGd,eAAe,CAChC5B,SAAS,CAAG,IAAI,CAChB,MACF,CACF,CAEA,GAAIA,SAAS,CAAE,MACjB,CAEAjF,OAAO,CAACmE,WAAW,CAAC,CACpB7G,KAAK,CAACiG,OAAO,CAAC,+BAA+B,CAAC,CAChD,CAAE,MAAO7C,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxDpD,KAAK,CAACoD,KAAK,CAAC,kCAAkC,CAAC,CACjD,CACF,CAAC,CAED,GAAIA,KAAK,CAAE,CACT,mBACEzB,IAAA,CAACtC,SAAS,EAACiL,SAAS,CAAE7I,MAAM,CAAC8I,SAAU,CAAAC,QAAA,cACrC7I,IAAA,CAACjC,KAAK,EAAC+K,QAAQ,CAAC,OAAO,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CAAC,2EAEvC,CAAO,CAAC,CACC,CAAC,CAEhB,CAEA,mBACE3I,KAAA,CAACxC,SAAS,EACRuL,QAAQ,CAAC,IAAI,CACbN,SAAS,CAAE7I,MAAM,CAAC8I,SAAU,CAC5BG,EAAE,CAAE,CACFG,OAAO,CAAE,MAAM,CACfC,SAAS,CAAE,oBAAoB,CAC/BC,UAAU,CAAE,kCACd,CAAE,CAAAP,QAAA,EAEDrH,OAAO,cACNxB,IAAA,CAACrC,GAAG,EAACgL,SAAS,CAAE7I,MAAM,CAACuJ,gBAAiB,CAAAR,QAAA,cACtC7I,IAAA,CAAClC,gBAAgB,EAACwL,IAAI,CAAE,EAAG,CAACP,EAAE,CAAE,CAAEQ,KAAK,CAAEtL,eAAgB,CAAE,CAAE,CAAC,CAC3D,CAAC,cAENiC,KAAA,CAAAE,SAAA,EAAAyI,QAAA,eAEE7I,IAAA,CAACzB,MAAM,EACLgD,QAAQ,CAAEA,QAAS,CACnBa,QAAQ,CAAEA,QAAS,CACnBoH,gBAAgB,CAAEnH,oBAAqB,CACvCoH,kBAAkB,CAAEA,CAAA,GAAMjH,UAAU,CAAC,QAAQ,CAAE,CAC/CkH,kBAAkB,CAAEA,CAAA,GAAMlH,UAAU,CAAC,QAAQ,CAAE,CAC/CmH,kBAAkB,CAAEA,CAAA,GAAMnH,UAAU,CAAC,QAAQ,CAAE,CAChD,CAAC,cAGFxC,IAAA,CAACrC,GAAG,EAACoL,EAAE,CAAE,CAAEa,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAS,CAAEC,EAAE,CAAE,GAAG,CAAEd,EAAE,CAAE,CAAC,CAAE,CAAE,CAAAH,QAAA,cACpE3I,KAAA,CAACtC,IAAI,EACHmM,KAAK,CAAErJ,SAAU,CACjBsJ,QAAQ,CAAEpH,eAAgB,CAC1BqH,OAAO,CAAC,YAAY,CACpBC,aAAa,CAAC,MAAM,CACpBnB,EAAE,CAAE,CACFI,SAAS,CAAE,MAAM,CACjB,gBAAgB,CAAE,CAChBgB,aAAa,CAAE,MAAM,CACrBC,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,QAAQ,CAClBC,QAAQ,CAAE,MAAM,CAChBnB,SAAS,CAAE,MAAM,CACjBoB,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPpB,UAAU,CAAE,kCACd,CAAC,CACD,iBAAiB,CAAE,CACjBG,KAAK,CAAE,GAAGtL,eAAe,aAC3B,CAAC,CACD,sBAAsB,CAAE,CACtBwM,eAAe,CAAExM,eAAe,CAChCyM,MAAM,CAAE,KACV,CACF,CAAE,CAAA7B,QAAA,eAEF7I,IAAA,CAACnC,GAAG,EACF8M,IAAI,cAAE3K,IAAA,CAAChC,OAAO,EAAC2M,IAAI,CAAC,4BAA4B,CAACC,KAAK,CAAE,EAAG,CAACF,MAAM,CAAE,EAAG,CAAE,CAAE,CAC3EG,YAAY,CAAC,OAAO,CACpBC,KAAK,CAAC,UAAU,CAChBf,KAAK,CAAC,UAAU,CAChBhB,EAAE,CAAE,CAAEgC,GAAG,CAAE,KAAM,CAAE,CACpB,CAAC,cACF/K,IAAA,CAACnC,GAAG,EACF8M,IAAI,cAAE3K,IAAA,CAAChC,OAAO,EAAC2M,IAAI,CAAC,4BAA4B,CAACC,KAAK,CAAE,EAAG,CAACF,MAAM,CAAE,EAAG,CAAE,CAAE,CAC3EG,YAAY,CAAC,OAAO,CACpBC,KAAK,CAAC,iBAAiB,CACvBf,KAAK,CAAC,YAAY,CAClBhB,EAAE,CAAE,CAAEgC,GAAG,CAAE,KAAM,CAAE,CACpB,CAAC,cACF/K,IAAA,CAACnC,GAAG,EACF8M,IAAI,cAAE3K,IAAA,CAAChC,OAAO,EAAC2M,IAAI,CAAC,WAAW,CAACC,KAAK,CAAE,EAAG,CAACF,MAAM,CAAE,EAAG,CAAE,CAAE,CAC1DG,YAAY,CAAC,OAAO,CACpBC,KAAK,CAAC,OAAO,CACbf,KAAK,CAAC,OAAO,CACbhB,EAAE,CAAE,CAAEgC,GAAG,CAAE,KAAM,CAAE,CACpB,CAAC,CACD,CAAAxJ,QAAQ,SAARA,QAAQ,kBAAAjB,qBAAA,CAARiB,QAAQ,CAAEyJ,iBAAiB,UAAA1K,qBAAA,iBAA3BA,qBAAA,CAA6B4D,YAAY,IAAK,OAAO,eACpDlE,IAAA,CAACnC,GAAG,EACF8M,IAAI,cAAE3K,IAAA,CAAChC,OAAO,EAAC2M,IAAI,CAAC,2BAA2B,CAACC,KAAK,CAAE,EAAG,CAACF,MAAM,CAAE,EAAG,CAAE,CAAE,CAC1EG,YAAY,CAAC,OAAO,CACpBC,KAAK,CAAC,QAAQ,CACdf,KAAK,CAAC,QAAQ,CACdhB,EAAE,CAAE,CAAEgC,GAAG,CAAE,KAAM,CAAE,CACpB,CACF,EACG,CAAC,CACJ,CAAC,cAGN7K,KAAA,CAACvC,GAAG,EAACgL,SAAS,CAAE7I,MAAM,CAACmL,UAAW,CAAApC,QAAA,EAC/BnI,SAAS,GAAK,UAAU,eACvBR,KAAA,CAACvC,GAAG,EAACgL,SAAS,CAAE7I,MAAM,CAACoL,WAAY,CAACnC,EAAE,CAAE,CAAEgC,GAAG,CAAE,GAAI,CAAE,CAAAlC,QAAA,eACnD7I,IAAA,CAACxB,WAAW,EAAC+C,QAAQ,CAAEA,QAAS,CAAE,CAAC,cACnCrB,KAAA,CAACvC,GAAG,EAACoL,EAAE,CAAE,CAAEoC,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,KAAM,CAAC,CAAEP,GAAG,CAAE,CAAC,CAAEjB,EAAE,CAAE,GAAI,CAAE,CAAAjB,QAAA,eACxF7I,IAAA,CAACvB,UAAU,EAAC8D,KAAK,CAAEA,KAAM,CAAE,CAAC,cAC5BvC,IAAA,CAACtB,QAAQ,EAAC6D,KAAK,CAAEA,KAAM,CAAE,CAAC,EACvB,CAAC,CACL,CAAAhB,QAAQ,SAARA,QAAQ,kBAAAhB,sBAAA,CAARgB,QAAQ,CAAEyJ,iBAAiB,UAAAzK,sBAAA,iBAA3BA,sBAAA,CAA6B2D,YAAY,IAAK,OAAO,eACpDlE,IAAA,CAACnB,gBAAgB,EACf0C,QAAQ,CAAEA,QAAS,CACnBgK,eAAe,CAAEhK,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEyJ,iBAAkB,CAC7CQ,WAAW,CAAE9H,eAAgB,CAC7B+H,cAAc,CAAEjH,kBAAmB,CACnCkH,cAAc,CAAE3G,kBAAmB,CACpC,CACF,cACD/E,IAAA,CAACpB,iBAAiB,EAChBwG,UAAU,CAAE7D,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE6D,UAAW,CACjClD,0BAA0B,CAAEA,0BAA2B,CACvDC,kBAAkB,CAAEA,kBAAmB,CACvCH,qBAAqB,CAAEA,qBAAsB,CAC7CC,aAAa,CAAEA,aAAc,CAC7BH,wBAAwB,CAAEA,wBAAyB,CACnDC,gBAAgB,CAAEA,gBAAiB,CACpC,CAAC,EACC,CACN,CAEArB,SAAS,GAAK,YAAY,eACzBR,KAAA,CAAAE,SAAA,EAAAyI,QAAA,eACE7I,IAAA,CAAClB,UAAU,EACTyC,QAAQ,CAAEA,QAAS,CACnBoK,YAAY,CAAGzG,WAAW,EAAK,CAC7B;AACAhC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE+B,WAAW,CAAC,CACjD,CAAE,CACF0G,eAAe,CAAE5I,mBAAoB,CACtC,CAAC,cACFhD,IAAA,CAACrB,aAAa,EACZyG,UAAU,CAAE7D,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE6D,UAAW,CACjChD,QAAQ,CAAEA,QAAS,CACnByJ,OAAO,CAAE,KAAM,CACfC,YAAY,CAAE,IAAK,CACnB5J,0BAA0B,CAAEA,0BAA2B,CACvDC,kBAAkB,CAAEA,kBAAmB,CACvCH,qBAAqB,CAAEA,qBAAsB,CAC7CC,aAAa,CAAEA,aAAc,CAC7BH,wBAAwB,CAAEA,wBAAyB,CACnDC,gBAAgB,CAAEA,gBAAiB,CACnCgK,iBAAiB,CAAE/G,qBAAsB,CACzCgH,YAAY,CAAExG,gBAAiB,CAC/ByG,eAAe,CAAEnG,mBAAoB,CACrCoG,SAAS,CAAE1F,aAAc,CACzB2F,YAAY,CAAEpF,gBAAiB,CAC/BqF,YAAY,CAAEjF,gBAAiB,CAC/BkF,eAAe,CAAE/E,mBAAoB,CACrCgF,eAAe,CAAE7E,mBAAoB,CACrC8E,YAAY,CAAE,CAAAhL,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE8G,aAAa,GAAI,EAAG,CAC5CmE,SAAS,CAAEjL,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEyG,KAAM,CAC5B,CAAC,EACF,CACH,CAEAtH,SAAS,GAAK,OAAO,eACpBV,IAAA,CAACjB,QAAQ,EACPwC,QAAQ,CAAEA,QAAS,CACnBoK,YAAY,CAAEA,CAAA,GAAM,CAClB;AACAzI,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC,CAC3DG,eAAe,CAAC,CAAC,CACnB,CAAE,CACH,CACF,CAEA5C,SAAS,GAAK,QAAQ,EAAI,CAAAa,QAAQ,SAARA,QAAQ,kBAAAf,sBAAA,CAARe,QAAQ,CAAEyJ,iBAAiB,UAAAxK,sBAAA,iBAA3BA,sBAAA,CAA6B0D,YAAY,IAAK,OAAO,eAC9ElE,IAAA,CAACnB,gBAAgB,EACf0C,QAAQ,CAAEA,QAAS,CACnBgK,eAAe,CAAEhK,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEyJ,iBAAkB,CAC7CQ,WAAW,CAAE9H,eAAgB,CAC7B+H,cAAc,CAAEjH,kBAAmB,CACnCkH,cAAc,CAAE3G,kBAAmB,CACpC,CACF,EACE,CAAC,EACN,CACH,cAGD/E,IAAA,CAACd,YAAY,EACXuN,IAAI,CAAEzL,WAAW,CAACE,MAAO,CACzBwL,OAAO,CAAEA,CAAA,GAAM/J,WAAW,CAAC,QAAQ,CAAE,CACrCgK,QAAQ,CAAE/K,gBAAiB,CAC3BL,QAAQ,CAAEA,QAAS,CACpB,CAAC,cAEFvB,IAAA,CAACb,YAAY,EACXsN,IAAI,CAAEzL,WAAW,CAACG,MAAO,CACzBuL,OAAO,CAAEA,CAAA,GAAM/J,WAAW,CAAC,QAAQ,CAAE,CACrCiK,QAAQ,CAAElL,gBAAiB,CAC5B,CAAC,cAEF1B,IAAA,CAACZ,YAAY,EACXqN,IAAI,CAAEzL,WAAW,CAACI,MAAO,CACzBsL,OAAO,CAAEA,CAAA,GAAM/J,WAAW,CAAC,QAAQ,CAAE,CACrCkK,QAAQ,CAAElL,gBAAiB,CAC5B,CAAC,cAEF3B,IAAA,CAACX,aAAa,EACZoN,IAAI,CAAEzL,WAAW,CAACrB,UAAW,CAC7B+M,OAAO,CAAEA,CAAA,GAAMzL,cAAc,CAACyB,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE/C,UAAU,CAAE,KAAM,CAAC,CAAC,CAAE,CACxEmN,SAAS,CAAEA,CAAA,GAAM3F,gBAAgB,CAAC9F,oBAAoB,CAAE,CACxD0L,KAAK,CAAC,aAAa,CACnBC,WAAW,CAAE,6CAA6C3L,oBAAoB,SAApBA,oBAAoB,iBAApBA,oBAAoB,CAAE4L,IAAI,kCAAmC,CACxH,CAAC,cAEFjN,IAAA,CAACX,aAAa,EACZoN,IAAI,CAAEzL,WAAW,CAACpB,aAAc,CAChC8M,OAAO,CAAEA,CAAA,GAAMzL,cAAc,CAACyB,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE9C,aAAa,CAAE,KAAM,CAAC,CAAC,CAAE,CAC3EkN,SAAS,CAAEA,CAAA,GAAMxF,mBAAmB,CAAChG,uBAAuB,CAAE,CAC9DyL,KAAK,CAAC,gBAAgB,CACtBC,WAAW,CAAE,gDAAgD1L,uBAAuB,SAAvBA,uBAAuB,iBAAvBA,uBAAuB,CAAE2L,IAAI,kCAAmC,CAC9H,CAAC,EACO,CAAC,CAEhB,CAAC,CAED,cAAe,CAAA5M,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
.rightSidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 300px;
  height: 100vh;
  background-color: #27293d;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  border-left: 1px solid #323554;
  transform: translateX(100%);

  &.open {
    transform: translateX(0);
  }
}

.rightSidebarHeader {
  height: 70px;
  padding: 0 1.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #323554;
  background-color: #27293d;
}

.rightSidebarTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem !important;
  font-weight: 600 !important;
  color: #ffffff !important;
  font-family: 'Recursive Variable', sans-serif !important;
}

.closeButton {
  color: #a0aec0 !important;
  padding: 8px !important;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 193, 7, 0.15) !important;
    color: var(--main-yellow-color) !important;
  }
}

.rightSidebarContent {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background-color: #27293d;

  &::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.taskItem {
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  word-break: break-word;
  border: none;
  position: relative;
  overflow: hidden;
  
  &:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-1px);
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: var(--main-yellow-color);
    opacity: 0.9;
  }
  
  /* Task ưu tiên cao */
  &.priority {
    border-left: 4px solid #ff9800;
    
    &::before {
      background-color: #ff9800;
    }
    
    .priorityBadge {
      display: inline-flex;
    }
  }
  
  /* Task khẩn cấp */
  &.urgent {
    border-left: 4px solid #f44336;
    
    &::before {
      background-color: #f44336;
    }
    
    .urgentBadge {
      display: inline-flex;
    }
    
    /* Hiệu ứng nhấp nháy nhẹ cho task khẩn cấp */
    animation: pulseUrgent 2s infinite;
  }
}

/* Hiệu ứng nhấp nháy cho task khẩn cấp */
@keyframes pulseUrgent {
  0% {
    box-shadow: 0 2px 4px rgba(244, 67, 54, 0.1);
  }
  50% {
    box-shadow: 0 2px 12px rgba(244, 67, 54, 0.3);
  }
  100% {
    box-shadow: 0 2px 4px rgba(244, 67, 54, 0.1);
  }
}

.taskHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;
}

.taskTitle {
  font-size: 15px !important;
  font-weight: 600 !important;
  color: #333333 !important;
  line-height: 1.4 !important;
  font-family: 'Recursive Variable', sans-serif !important;
  flex: 1;
}

.taskCheckbox {
  padding: 4px !important;
  margin-left: 2px !important;
  color: #64748b !important;
  
  &:hover {
    background-color: rgba(255, 193, 7, 0.15) !important;
    color: var(--main-yellow-color) !important;
  }
}

.planName {
  font-size: 13px !important;
  color: var(--main-yellow-color) !important;
  margin-bottom: 12px !important;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 400 !important;
  font-family: 'Recursive Variable', sans-serif !important;
  
  &:hover {
    opacity: 0.8;
  }
}

.taskBadges {
  display: flex;
  gap: 6px;
  margin-bottom: 8px;
}

.priorityBadge {
  display: none; /* Mặc định ẩn, chỉ hiển thị khi task có class priority */
  align-items: center;
  gap: 4px;
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Recursive Variable', sans-serif !important;
}

.urgentBadge {
  display: none; /* Mặc định ẩn, chỉ hiển thị khi task có class urgent */
  align-items: center;
  gap: 4px;
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Recursive Variable', sans-serif !important;
}

.taskDateCover {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #64748b;
  flex-wrap: wrap;
  font-family: 'Recursive Variable', sans-serif !important;
  margin-top: 4px;
}

.taskDate {
  font-size: 13px !important;
  color: #64748b !important;
  font-family: 'Recursive Variable', sans-serif !important;
}

/* Hiển thị thời gian khẩn cấp */
.urgentTime {
  color: #f44336 !important;
  font-weight: 600 !important;
}

.noTasksMessage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  color: #a0aec0;
  gap: 1rem;
  text-align: center;
}

.rightSidebarFooter {
  height: 50px;
  padding: 0 1.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #323554;
  background-color: #27293d;
}

.footerText {
  color: #a0aec0 !important;
  font-size: 0.8rem !important;
  font-family: 'Recursive Variable', sans-serif !important;
}

.refreshButton {
  color: #a0aec0 !important;
  padding: 6px !important;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 193, 7, 0.15) !important;
    color: var(--main-yellow-color) !important;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .rightSidebar {
    width: 280px;
  }
} 
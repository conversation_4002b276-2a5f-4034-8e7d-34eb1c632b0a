from rest_framework.permissions import BasePermission
from django.shortcuts import get_object_or_404
from plans.models import Subtask, Task, Plan, Invitation, Milestone, PlanAccess


class canAccessPlan(BasePermission):
    message = "You do not have permission to view this plan detail."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            plan = get_object_or_404(Plan, slug=slug)
            # Check if user has any access level to the plan
            try:
                PlanAccess.objects.get(plan=plan, user=request.user)
                return True
            except PlanAccess.DoesNotExist:
                pass
            # Fallback to old invitation system for backward compatibility
            try:
                Invitation.objects.get(plan=plan, email=request.user.email, accepted=True)
                return True
            except Invitation.DoesNotExist:
                pass
        return False


class CanUpdatePlanMilestone(BasePermission):
    message = "You can not update this milestone."

    def has_permission(self, request, view):
        milestone_id = view.kwargs.get('id', None)
        if milestone_id:
            milestone = get_object_or_404(Milestone, id=milestone_id)
            if milestone.plan.user == request.user:
                return True
            try:
                Invitation.objects.get(plan=milestone.plan, email=request.user.email, accepted=True)
                return True
            except Invitation.DoesNotExist:
                pass
        return False


class IsSubtaskPlanOwner (BasePermission):
    message = "You are not the owner of this plan."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            subtask = get_object_or_404(Subtask, slug=slug)
            if subtask.task.milestone.plan.user == request.user:
                return True
        return False


class IsTaskPlanOwner (BasePermission):
    message = "You are not the owner of this plan."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            task = get_object_or_404(Task, slug=slug)
            if task.milestone.plan.user == request.user:
                return True
        return False


class IsPlanOwner (BasePermission):
    message = "You are not the owner of this plan."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            plan = get_object_or_404(Plan, slug=slug)
            if plan.user == request.user:
                return True
        return False


class canAccessTask(BasePermission):
    message = "You do not have permission to view this task detail."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            task = get_object_or_404(Task, slug=slug)

            if task.milestone and task.milestone.plan:
                # Check if user has any access level to the plan
                try:
                    PlanAccess.objects.get(plan=task.milestone.plan, user=request.user)
                    return True
                except PlanAccess.DoesNotExist:
                    pass
                # Fallback to old invitation system
                try:
                    Invitation.objects.get(plan=task.milestone.plan, email=request.user.email, accepted=True)
                    return True
                except Invitation.DoesNotExist:
                    pass

            if task.user == request.user:
                return True
        return False


class IsOwnerOrEditor(BasePermission):
    """Permission for users who can edit plan content (owners and editors)"""
    message = "You need owner or editor access to perform this action."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            plan = get_object_or_404(Plan, slug=slug)
            try:
                access = PlanAccess.objects.get(plan=plan, user=request.user)
                return access.access_level in [PlanAccess.OWNER, PlanAccess.EDITOR]
            except PlanAccess.DoesNotExist:
                pass
        return False


class IsOwnerOnly(BasePermission):
    """Permission for owners only"""
    message = "You need owner access to perform this action."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            plan = get_object_or_404(Plan, slug=slug)
            try:
                access = PlanAccess.objects.get(plan=plan, user=request.user)
                return access.access_level == PlanAccess.OWNER
            except PlanAccess.DoesNotExist:
                pass
        return False


class IsHeadOwnerOnly(BasePermission):
    """Permission for head owner only"""
    message = "You need head owner access to perform this action."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            plan = get_object_or_404(Plan, slug=slug)
            try:
                access = PlanAccess.objects.get(plan=plan, user=request.user)
                return access.access_level == PlanAccess.OWNER and access.is_head_owner
            except PlanAccess.DoesNotExist:
                pass
        return False

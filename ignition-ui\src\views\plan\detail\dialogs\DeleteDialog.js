import React, { useState } from 'react';
import { 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  Typography, 
  CircularProgress
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import styles from '../styles.module.scss';

const DeleteDialog = ({ open, onClose, onDelete }) => {
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    setLoading(true);
    try {
      const success = await onDelete();
      if (!success) {
        setLoading(false);
      }
      // If successful, the page will navigate away
    } catch (err) {
      console.error('Error deleting plan:', err);
      setLoading(false);
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={loading ? null : onClose}
      maxWidth="sm"
      fullWidth
      className={styles.deleteDialog}
      PaperProps={{
        sx: {
          borderRadius: '12px',
          padding: '8px'
        }
      }}
    >
      <DialogTitle 
        className={styles.dialogTitle}
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1,
          fontFamily: '"Recursive Variable", sans-serif',
          fontSize: '1.3rem',
          fontWeight: 600,
          color: '#333',
          pb: 1
        }}
      >
        <Iconify icon="material-symbols:delete" width={24} height={24} color="#f44336" />
        Delete Plan
      </DialogTitle>
      
      <DialogContent className={styles.dialogContent}>
        <Typography 
          variant="body1" 
          sx={{ 
            mb: 2, 
            color: '#333',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 500
          }}
        >
          Are you sure you want to delete this plan?
        </Typography>
        
        <Typography 
          variant="body2" 
          sx={{ 
            color: '#666',
            fontFamily: '"Recursive Variable", sans-serif'
          }}
        >
          This action cannot be undone. All data associated with this plan, including milestones, tasks, and comments will be permanently deleted.
        </Typography>
      </DialogContent>
      
      <DialogActions className={styles.dialogActions}>
        <Button 
          onClick={onClose}
          disabled={loading}
          sx={{ 
            color: '#666',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600
          }}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleDelete}
          disabled={loading}
          variant="contained"
          color="error"
          startIcon={loading ? <CircularProgress size={16} color="inherit" /> : <Iconify icon="material-symbols:delete" width={16} height={16} />}
          sx={{ 
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600
          }}
        >
          {loading ? 'Deleting...' : 'Delete Plan'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteDialog; 
.container {
  font-family: 'Recursive Variable', sans-serif;
  padding: 20px;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 100px);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
  }
}

.titleSection {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pageTitle {
  font-weight: 700;
  color: #333;
  margin: 0;
}

.actionButtons {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: flex-start;
  }
}

.planActions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tabContent {
  padding-top: 16px;
}

.overviewTab {
  display: flex;
  flex-direction: column;
}

.descriptionCard {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 24px;
  border: 1px solid #f0f0f0;
}

.descriptionText {
  color: #555;
  line-height: 1.6;
  white-space: pre-line;
}

.milestoneCard {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  margin-bottom: 8px;
  transition: all 0.2s ease;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
}

.milestoneHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.milestoneTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.taskList {
  margin-top: 8px;
}

.taskItemContainer {
  margin-bottom: 4px;
}

.taskItem {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  background-color: #f9f9f9;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #f5f5f5;
  }
}

.taskStatus {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 10px;
  flex-shrink: 0;
}

.completed {
  background-color: #4CAF50;
}

.inProgress {
  background-color: #FF9800;
}

.notStarted {
  background-color: #E0E0E0;
}

.taskName {
  flex-grow: 1;
  font-size: 0.9rem;
  color: #444;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.taskMeta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
  color: #777;
  font-size: 0.8rem;
}

.subtaskList {
  margin-top: 8px;
}

.subtaskItem {
  margin-bottom: 4px;
}

.membersCard {
  margin-bottom: 16px;
}

.taskWithSubtasks {
  margin-bottom: 8px;
}

.inviteDialog {
  .dialogTitle {
    font-weight: 600;
    color: #333;
  }

  .dialogContent {
    padding: 16px;
  }

  .dialogActions {
    padding: 16px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}

.deleteDialog {
  .dialogTitle {
    font-weight: 600;
    color: #333;
  }

  .dialogContent {
    padding: 16px;
  }

  .dialogActions {
    padding: 16px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
} 
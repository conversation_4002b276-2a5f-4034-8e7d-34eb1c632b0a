import React from 'react';
import { Chip, Typography, Box, Grid, Paper, CircularProgress } from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";

//--------------------------------------------------------------------------------------------------

const SectionTitle = ({ children, ...props }) => (
  <Typography 
    sx={{
      fontFamily: 'Recursive Variable',
      fontSize: '1.5rem',
      fontWeight: 'bold',
      color: '#0F52BA',
      marginBottom: 2,
      ...props.sx
    }}
    {...props}
  >
    {children}
  </Typography>
);

export const ProfileSkills = ({ skills, skillsLoading, getOrCreateSkillColor }) => (
  <Box>
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <SectionTitle>User Skills</SectionTitle>
      </Grid>

      <Grid item xs={12}>
        <Box sx={{ minHeight: 100 }}>
          {skillsLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
              <CircularProgress sx={{ color: mainYellowColor }} />
            </Box>
          ) : skills.length > 0 ? (
            <Box>
              <Typography
                sx={{
                  fontFamily: 'Recursive Variable',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  mb: 1,
                  color: '#555'
                }}
              >
                Skills list
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {skills.map((skill) => (
                  <Chip
                    key={skill.id}
                    label={skill.name}
                    sx={{
                      bgcolor: getOrCreateSkillColor(skill.id),
                      color: 'white',
                      mb: 1,
                      fontFamily: 'Recursive Variable',
                      fontWeight: 'bold',
                      px: 1
                    }}
                  />
                ))}
              </Box>
            </Box>
          ) : (
            <Paper
              elevation={0}
              sx={{
                p: 2,
                textAlign: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: 2
              }}
            >
              <Iconify
                icon="mdi:information-outline"
                width={32} height={32}
                sx={{ color: '#757575', mb: 1 }}
              />
              <Typography
                sx={{
                  fontFamily: 'Recursive Variable',
                  fontSize: '1.125rem',
                  fontWeight: 'bold',
                  color: '#333',
                  mb: 0.5
                }}
              >
                No skills available
              </Typography>
              <Typography
                sx={{
                  fontFamily: 'Recursive Variable',
                  fontSize: '0.9rem',
                  color: '#666'
                }}
              >
                This user hasn't added any skills yet.
              </Typography>
            </Paper>
          )}
        </Box>
      </Grid>
    </Grid>
  </Box>
);

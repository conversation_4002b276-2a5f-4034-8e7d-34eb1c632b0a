import os
import time
import json
from celery import shared_task
from urllib.error import H<PERSON><PERSON><PERSON>rror, URLError
from openai import OpenAI
from .utils import convert_json_text, save_plan_to_db
from .prompts import get_project_planning_prompt

@shared_task
def call_assistant_api(prompt, language, role, user_id):
    try:
        client = OpenAI(api_key=os.environ['OPENAI_API_KEY'])
        thread = client.beta.threads.create()
        content = get_project_planning_prompt(prompt=prompt, language=language, role=role)

        message = client.beta.threads.messages.create(
            thread_id=thread.id,
            role='user',
            content=content
        )

        run = client.beta.threads.runs.create(
            thread_id=thread.id,
            assistant_id=os.environ['ASSISTANT_ID'],
        )

        while True:
            run_status = client.beta.threads.runs.retrieve(
                thread_id=thread.id,
                run_id=run.id
            )
            if run_status.status == 'completed':
                result_message = client.beta.threads.messages.list(thread_id=thread.id)
                for message in result_message.data:
                    created_plan_data = message.content[0].text.value
                    converted_data = convert_json_text(created_plan_data)
                    plan_data_dict = json.loads(converted_data, strict=False)
                    
                    # Save the plan data to the database
                    return save_plan_to_db(plan_data_dict, user_id)
            time.sleep(5)

    except HTTPError as e:
        print(f"HTTP Error occurred using Assistant API: {e}")
        return {"error": f"HTTP Error occurred using Assistant API: {e}"}
    except URLError as e:
        print(f"URL Error occurred using Assistant API: {e}")
        return {"error": f"URL Error occurred using Assistant API: {e}"}
    except Exception as e:
        print(f"An Error occurred: {e}")
        return {"error": f"An error occurred: {e}"}

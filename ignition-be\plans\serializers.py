from rest_framework import serializers
from django.utils import timezone
from plans.models import Plan, Milestone, Task, Subtask, Invitation, STATUS_CHOICES, Risk, PlanAccess
from users.models import User
from comments.models import Comment
from memos.models import Memos
import os

TASK_TYPE = 1
SUBTASK_TYPE = 2


class UserSerializer(serializers.ModelSerializer):
    avatar = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'email', 'avatar']

    def get_avatar(self, instance):
        url_backend = os.getenv('URL_BACKEND', 'http://127.0.0.1:8000')
        try:
            if instance.avatar and instance.avatar.url:
                return url_backend + instance.avatar.url
            else:
                return url_backend + '/media/avatars/default.png?ver=1'
        except AttributeError:
            return url_backend + '/media/avatars/default.png?ver=1'


class MemoSerializer(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = Comment
        fields = ['id', 'content', 'user', 'type', 'created_at']


class CommentSerializer(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = Comment
        fields = ['id', 'content', 'user', 'type', 'created_at']


class SubtaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = Subtask
        fields = [
            'id', 'name', 'task', 'status', 'start_date', 'end_date',
            'progress', 'created_at', 'slug', 'order'
        ]


class RiskSerializer(serializers.ModelSerializer):
    class Meta:
        model = Risk
        fields = ['id', 'risk', 'mitigation']


class TaskSerializer(serializers.ModelSerializer):
    subtasks = serializers.SerializerMethodField(read_only=True)
    assignees = UserSerializer(many=True, required=False)
    comments = serializers.SerializerMethodField(read_only=True)
    memos = serializers.SerializerMethodField(read_only=True)
    plan_name = serializers.SerializerMethodField(read_only=True)
    plan_slug = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Task
        fields = [
            'id', 'name', 'description', 'milestone', 'status', 'priority', 'progress',
            'start_date', 'end_date', 'subtasks', 'assignees', 'comments',
            'memos', 'plan_name', 'plan_slug', 'slug', 'created_at', 'estimated_duration'
        ]

    def get_subtasks(self, obj):
        subtasks = obj.subtask_set.all()
        serializer = SubtaskSerializer(subtasks, many=True)
        return serializer.data

    def get_comments(self, obj):
        comments = Comment.objects.filter(target_id=obj.id, type=TASK_TYPE)
        serializer = CommentSerializer(comments, many=True)
        return serializer.data

    def get_memos(self, obj):
        request = self.context.get('request')
        if request is not None:
            user = request.user
            memos = Memos.objects.filter(target_id=obj.id, type=TASK_TYPE, user=user)
            serializer = MemoSerializer(memos, many=True)
            return serializer.data
        return []

    def get_plan_name(self, obj):
        if obj.milestone and obj.milestone.plan:
            return obj.milestone.plan.name
        return None

    def get_plan_slug(self, obj):
        if obj.milestone and obj.milestone.plan:
            return obj.milestone.plan.slug
        return None


class MilestoneSerializer(serializers.ModelSerializer):
    tasks = serializers.SerializerMethodField()
    risks = RiskSerializer(many=True, read_only=True)

    class Meta:
        model = Milestone
        fields = ['id', 'description', 'name', 'plan', 'tasks', 'created_at', 'estimated_duration', 'success_criteria', 'risks']

    def get_tasks(self, obj):
        tasks = obj.task_set.all()
        serializer = TaskSerializer(tasks, many=True)
        return serializer.data


class PlanAccessSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    granted_by = UserSerializer(read_only=True)

    class Meta:
        model = PlanAccess
        fields = ['id', 'user', 'access_level', 'is_head_owner', 'granted_by', 'created_at']


class InvitationSerializer(serializers.ModelSerializer):
    invited_by = serializers.StringRelatedField()
    plan = serializers.StringRelatedField()
    invited_user_info = serializers.SerializerMethodField()

    class Meta:
        model = Invitation
        fields = ['email', 'plan', 'invited_by', 'access_level', 'accepted', 'created_at', 'accepted_at', 'invited_user_info']

    def get_invited_user_info(self, obj):
        try:
            user = User.objects.get(email=obj.email)
            return UserSerializer(user).data
        except User.DoesNotExist:
            return None


class PlanViewSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    milestones = serializers.SerializerMethodField()
    invitations = serializers.SerializerMethodField()
    access_levels = serializers.SerializerMethodField()
    user_access_level = serializers.SerializerMethodField()
    start_date = serializers.SerializerMethodField()
    end_date = serializers.SerializerMethodField()
    total_comments = serializers.SerializerMethodField()
    total_memos = serializers.SerializerMethodField()

    class Meta:
        model = Plan
        fields = [
            'id', 'name', 'description', 'user', 'milestones', 'slug',
            'created_at', 'invitations', 'access_levels', 'user_access_level',
            'start_date', 'end_date', 'total_comments', 'total_memos', 'status'
        ]

    def get_milestones(self, obj):
        milestones = obj.milestone_set.all()
        serializer = MilestoneSerializer(milestones, many=True)
        return serializer.data

    def get_invitations(self, obj):
        invitations = obj.invitations.all()
        serializer = InvitationSerializer(invitations, many=True)
        return serializer.data

    def get_access_levels(self, obj):
        access_levels = obj.access_levels.all()
        serializer = PlanAccessSerializer(access_levels, many=True)
        return serializer.data

    def get_user_access_level(self, obj):
        request = self.context.get('request')
        if request and request.user:
            try:
                access = PlanAccess.objects.get(plan=obj, user=request.user)
                return {
                    'access_level': access.access_level,
                    'is_head_owner': access.is_head_owner
                }
            except PlanAccess.DoesNotExist:
                return None
        return None

    def get_start_date(self, obj):
        tasks = Task.objects.filter(milestone__plan=obj).exclude(start_date=None).values_list('start_date', flat=True)

        if tasks:
            return min(tasks)
        return None

    def get_end_date(self, obj):
        tasks = Task.objects.filter(milestone__plan=obj).exclude(end_date=None).values_list('end_date', flat=True)

        if tasks:
            return max(tasks)
        return None

    def get_total_comments(self, obj):
        tasks = Task.objects.filter(milestone__plan=obj).values_list('id', flat=True)
        total_comments = Comment.objects.filter(type=TASK_TYPE, target_id__in=tasks).count()
        return total_comments

    def get_total_memos(self, obj):
        tasks = Task.objects.filter(milestone__plan=obj).values_list('id', flat=True)
        total_memos = Memos.objects.filter(type=TASK_TYPE, target_id__in=tasks).count()
        return total_memos


class TaskUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ['name', 'description', 'milestone', 'status', 'progress', 'start_date', 'end_date', 'slug']

    def validate_status(self, value):
        valid_statuses = [status for status, _ in STATUS_CHOICES]

        if value not in valid_statuses:
            raise serializers.ValidationError("Invalid status value.")
        return value

    def validate_progress(self, value):
        if value < 0 or value > 100:
            raise serializers.ValidationError("Progress must be between 0 and 100.")
        return value

    def validate(self, data):
        start_date = data.get('start_date')
        end_date = data.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("Start date must be before end date.")
        return data


class SubtaskUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Subtask
        fields = ['name', 'description', 'task', 'status', 'progress', 'start_date', 'end_date', 'slug']

    def validate_status(self, value):
        valid_statuses = [status for status, _ in STATUS_CHOICES]

        if value not in valid_statuses:
            raise serializers.ValidationError("Invalid status value.")
        return value

    def validate_progress(self, value):
        if value < 0 or value > 100:
            raise serializers.ValidationError("Progress must be between 0 and 100.")
        return value

    def validate(self, data):
        start_date = data.get('start_date')
        end_date = data.get('end_date')

        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("Start date must be before end date.")
        return data


class TaskAssignSerializer(serializers.ModelSerializer):
    assignees = serializers.PrimaryKeyRelatedField(queryset=User.objects.all(), many=True)

    class Meta:
        model = Task
        fields = ['assignees']


class CheckUserExistsSerializer(serializers.Serializer):
    email = serializers.EmailField()


class AddInvitationSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(required=True)

    class Meta:
        model = Invitation
        fields = ['email']


class CheckInvitationSerializer(serializers.ModelSerializer):
    invited_by = serializers.EmailField(source='invited_by.email', read_only=True)
    plan_name = serializers.CharField(source='plan.name', read_only=True)
    plan_slug = serializers.CharField(source='plan.slug', read_only=True)

    class Meta:
        model = Invitation
        fields = ['id', 'plan_name', 'invited_by', 'email', 'accepted', 'plan_slug']


class InvitationUpdateSerializer(serializers.ModelSerializer):
    accepted = serializers.ChoiceField(choices=[(0, 'Pending'), (1, 'Accepted'), (-1, 'Rejected')])

    class Meta:
        model = Invitation
        fields = ['accepted']


class UpdateTaskStatusTodoSerializer(serializers.ModelSerializer):

    class Meta:
        model = Task
        fields = ['status', 'start_date', 'end_date', 'name']

    def validate_status(self, value):
        valid_statuses = [choice[0] for choice in STATUS_CHOICES]
        if value not in valid_statuses:
            raise serializers.ValidationError(f"Invalid status. Valid choices are: {valid_statuses}")
        return value

class UpdateTaskStartEndDateTodoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ['start_date', 'end_date']

from django.shortcuts import render
from rest_framework.permissions import IsAuthenticated
from .models import Memos
from .permissions import IsMemosOwner
from .serializers import MemosSerializer, MemosListWithUserSerializer
from rest_framework.views import APIView
from rest_framework.response import Response
from starlette import status
from drf_yasg.utils import swagger_auto_schema
from django.shortcuts import get_object_or_404

class addMemosView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=MemosSerializer, responses={201: 'Memo created successfully'})
    def post(self, request):
        serializer = MemosSerializer(data=request.data)
        if serializer.is_valid():
            content = serializer.validated_data['content']
            type = serializer.validated_data.get('type')
            target_id = serializer.validated_data.get('target_id')
            memos = Memos.objects.create(
                content=content,
                type = type,
                user=request.user,
                target_id=target_id,
            )
            return Response(MemosSerializer(memos).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class deleteMemosView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, id):
        memos = get_object_or_404(Memos, pk=id)
        self.check_object_permissions(request, memos)
        memos.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class updateMemosView(APIView):
    permission_classes = [IsAuthenticated, IsMemosOwner]

    @swagger_auto_schema(request_body=MemosSerializer, responses={200: 'Login success'})
    def put(self, request, id):
        user = request.user
        memos = get_object_or_404(Memos, id=id)
        serializer = MemosSerializer(id, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(status=status.HTTP_200_OK)
        else:
            return Response({
                "message": serializer.errors,
            }, status=status.HTTP_400_BAD_REQUEST)


class getMemosView(APIView):
    permission_classes = [IsAuthenticated, IsMemosOwner]
    def get(self, request, target_id, type):
        memos = Memos.objects.filter(target_id=target_id, type=type)
        serializer = MemosListWithUserSerializer(memos, many=True)
        return Response(serializer.data ,status = status.HTTP_200_OK)
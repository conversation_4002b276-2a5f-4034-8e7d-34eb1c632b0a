import React from 'react';
import { Dialog, Box, Typography, Button } from '@mui/material';
import InputBase from 'components/Input/InputBase';
import styles from '../styles.module.scss';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";

//--------------------------------------------------------------------------------------------------

const InviteDialogComponent = ({ open, onClose, email, handleEmailChange, handleSendEmail, inviteError, isSending }) => {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <Box className={styles.noteDialogContainer}>
        <Typography variant="h5" className={styles.noteTitle}>Invite Plan via Email</Typography>
        <Box className={styles.subtaskInputContainer}>
          <InputBase
            id="email-input"
            value={email}
            placeholder="Enter email address"
            handleChange={handleEmailChange}
            errorText={inviteError}
            required
            sx={{ flexGrow: 1, marginRight: 2 }}
          />
        </Box>
        <Box>
          <Button onClick={handleSendEmail} variant="contained" color="primary" className={styles.addBtnBase}  disabled={isSending}>
            <span><Iconify icon={isSending? 'eos-icons:bubble-loading' : "tabler:send"} width={24} height={24} color={mainYellowColor} /></span>
            Send
          </Button>
        </Box>
      </Box>
    </Dialog>
  );
};

export default InviteDialogComponent;

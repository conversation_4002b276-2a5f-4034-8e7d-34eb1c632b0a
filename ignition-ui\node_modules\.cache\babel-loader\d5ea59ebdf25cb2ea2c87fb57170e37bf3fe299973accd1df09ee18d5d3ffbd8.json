{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\AgentTab.js\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Typography, Paper, TextField, IconButton, Avatar, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport { useLocation } from 'react-router-dom';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\nimport { getHeaders } from \"helpers/functions\";\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AgentTab = _ref => {\n  _s();\n  let {\n    planInfo,\n    onPlanUpdate\n  } = _ref;\n  const [conversations, setConversations] = useState([]);\n  const [currentMessage, setCurrentMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const [, setError] = useState(null);\n  const location = useLocation();\n\n  // Load conversations from localStorage\n  useEffect(() => {\n    var _location$state;\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n    const planConversations = savedConversations.filter(conv => conv.planId === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n    setConversations(planConversations);\n\n    // Check for pending message from ChatbotBar\n    const pendingMessage = localStorage.getItem('pending_agent_message');\n    if (pendingMessage) {\n      try {\n        var _messageData$planInfo;\n        const messageData = JSON.parse(pendingMessage);\n        if (((_messageData$planInfo = messageData.planInfo) === null || _messageData$planInfo === void 0 ? void 0 : _messageData$planInfo.id) === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id)) {\n          handleSendMessage(messageData.message);\n        }\n        // Clear the pending message\n        localStorage.removeItem('pending_agent_message');\n      } catch (error) {\n        console.error('Error processing pending message:', error);\n        localStorage.removeItem('pending_agent_message');\n      }\n    }\n\n    // If coming from chatbot bar via navigation state, add the initial message\n    if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.message) {\n      handleSendMessage(location.state.message);\n    }\n  }, [planInfo === null || planInfo === void 0 ? void 0 : planInfo.id, location.state]);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [conversations]);\n  const handleSendMessage = async function () {\n    let messageText = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : currentMessage;\n    if (!messageText.trim() || isLoading) return;\n    setIsLoading(true);\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageText.trim(),\n      timestamp: new Date().toISOString()\n    };\n    const newConversations = [...conversations, userMessage];\n    setConversations(newConversations);\n    setCurrentMessage('');\n    try {\n      // Call the new AI agent chat endpoint with plan context\n      const response = await axios.post(`${APIURL}/api/assistant/agent-chat`, {\n        message: messageText.trim(),\n        plan_slug: planInfo.slug\n      }, {\n        headers: getHeaders()\n      });\n      const aiResponseData = response.data;\n\n      // Create AI response message\n      const aiResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: aiResponseData.message || 'I received your message but had trouble generating a response.',\n        timestamp: new Date().toISOString(),\n        actions: aiResponseData.actions || [],\n        metadata: aiResponseData.metadata || {}\n      };\n      const updatedConversations = [...newConversations, aiResponse];\n      setConversations(updatedConversations);\n\n      // Execute any actions returned by the AI\n      if (aiResponseData.actions && aiResponseData.actions.length > 0) {\n        let actionResults = [];\n        for (const action of aiResponseData.actions) {\n          try {\n            const result = await executeAIAction(action);\n            actionResults.push(result);\n          } catch (error) {\n            console.error('Error executing AI action:', error);\n            actionResults.push({\n              success: false,\n              error: error.message || 'Unknown error'\n            });\n          }\n        }\n\n        // If any actions were successful, trigger plan refresh\n        if (actionResults.some(result => result.success) && onPlanUpdate) {\n          onPlanUpdate();\n        }\n\n        // Add action results to the conversation if there were any issues\n        const failedActions = actionResults.filter(result => !result.success);\n        if (failedActions.length > 0) {\n          const errorMessage = {\n            id: Date.now() + 2,\n            type: 'assistant',\n            content: `Note: I encountered some issues executing the requested actions:\\n${failedActions.map(result => `• ${result.error}`).join('\\n')}`,\n            timestamp: new Date().toISOString(),\n            isError: true\n          };\n          setConversations(prev => [...prev, errorMessage]);\n        }\n      }\n\n      // Save to localStorage\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n      const planConversations = updatedConversations.map(conv => ({\n        ...conv,\n        planId: planInfo === null || planInfo === void 0 ? void 0 : planInfo.id,\n        planName: planInfo === null || planInfo === void 0 ? void 0 : planInfo.name\n      }));\n      localStorage.setItem('agent_conversations', JSON.stringify([...otherPlanConversations, ...planConversations]));\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error processing message:', error);\n      const errorResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: `Sorry, I encountered an error while processing your request: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || error.message || 'Unknown error'}. Please try again.`,\n        timestamp: new Date().toISOString(),\n        isError: true\n      };\n      setConversations([...newConversations, errorResponse]);\n      setError(error.message || 'Unknown error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const executeAIAction = async action => {\n    try {\n      const response = await axios.post(`${APIURL}/api/assistant/plan-action`, {\n        action: action.type,\n        plan_slug: planInfo.slug,\n        data: action.data || {},\n        message: `AI-generated action: ${action.type}`\n      }, {\n        headers: getHeaders()\n      });\n      return {\n        success: true,\n        data: response.data,\n        action: action\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Error executing AI action:', error);\n      return {\n        success: false,\n        error: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || error.message || 'Unknown error',\n        action: action\n      };\n    }\n  };\n  const generateAIResponse = (message, planInfo) => {\n    const lowerMessage = message.toLowerCase();\n    const milestones = (planInfo === null || planInfo === void 0 ? void 0 : planInfo.milestones) || [];\n    const totalTasks = milestones.reduce((acc, milestone) => {\n      var _milestone$tasks;\n      return acc + (((_milestone$tasks = milestone.tasks) === null || _milestone$tasks === void 0 ? void 0 : _milestone$tasks.length) || 0);\n    }, 0);\n    const totalSubtasks = milestones.reduce((acc, milestone) => {\n      var _milestone$tasks2;\n      return acc + ((_milestone$tasks2 = milestone.tasks) === null || _milestone$tasks2 === void 0 ? void 0 : _milestone$tasks2.reduce((taskAcc, task) => {\n        var _task$subtasks;\n        return taskAcc + (((_task$subtasks = task.subtasks) === null || _task$subtasks === void 0 ? void 0 : _task$subtasks.length) || 0);\n      }, 0)) || 0;\n    }, 0);\n\n    // Analyze project context\n    const projectContext = {\n      name: (planInfo === null || planInfo === void 0 ? void 0 : planInfo.name) || 'your project',\n      milestoneCount: milestones.length,\n      taskCount: totalTasks,\n      subtaskCount: totalSubtasks,\n      milestoneNames: milestones.map(m => m.name).slice(0, 3) // First 3 milestone names\n    };\n\n    // Advanced intent recognition and response generation\n    if (lowerMessage.includes('milestone') && (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new'))) {\n      return `I can help you add a new milestone to \"${projectContext.name}\". Currently, you have ${projectContext.milestoneCount} milestones: ${projectContext.milestoneNames.join(', ')}${projectContext.milestoneCount > 3 ? '...' : ''}.\n\nTo add a new milestone, I'll need:\n1. **Milestone name** - What should we call it?\n2. **Description** - What's the main objective?\n3. **Position** - Should it come before/after a specific milestone?\n4. **Tasks** - Any initial tasks to include?\n\nPlease provide these details, or just tell me the milestone name and I'll help structure the rest!`;\n    }\n    if (lowerMessage.includes('progress') || lowerMessage.includes('status') || lowerMessage.includes('overview')) {\n      const progressDetails = milestones.map(milestone => {\n        var _milestone$tasks3, _milestone$tasks4, _milestone$tasks4$fil;\n        const taskCount = ((_milestone$tasks3 = milestone.tasks) === null || _milestone$tasks3 === void 0 ? void 0 : _milestone$tasks3.length) || 0;\n        const completedTasks = ((_milestone$tasks4 = milestone.tasks) === null || _milestone$tasks4 === void 0 ? void 0 : (_milestone$tasks4$fil = _milestone$tasks4.filter(task => task.status === 'completed')) === null || _milestone$tasks4$fil === void 0 ? void 0 : _milestone$tasks4$fil.length) || 0;\n        return `• ${milestone.name}: ${completedTasks}/${taskCount} tasks completed`;\n      }).join('\\n');\n      return `Here's your project progress for \"${projectContext.name}\":\n\n📊 **Overall Statistics:**\n• ${projectContext.milestoneCount} milestones\n• ${projectContext.taskCount} total tasks\n• ${projectContext.subtaskCount} total subtasks\n\n📋 **Milestone Progress:**\n${progressDetails}\n\nWould you like me to:\n• Show detailed progress for a specific milestone?\n• Identify overdue or at-risk tasks?\n• Suggest next actions to move the project forward?`;\n    }\n    if (lowerMessage.includes('complete') || lowerMessage.includes('done') || lowerMessage.includes('finish')) {\n      const availableTasks = milestones.flatMap(milestone => {\n        var _milestone$tasks5;\n        return ((_milestone$tasks5 = milestone.tasks) === null || _milestone$tasks5 === void 0 ? void 0 : _milestone$tasks5.filter(task => task.status !== 'completed').map(task => `\"${task.name}\" in ${milestone.name}`)) || [];\n      }).slice(0, 5);\n      return `I can mark tasks as completed for you! Here are some pending tasks I found:\n\n${availableTasks.map(task => `• ${task}`).join('\\n')}\n\nTo mark a task as complete, just tell me:\n• \"Mark [task name] as completed\"\n• \"Complete the [task name] task\"\n• Or simply \"Done with [task name]\"\n\nWhich task would you like to mark as completed?`;\n    }\n    if (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new')) {\n      if (lowerMessage.includes('task')) {\n        return `I can add new tasks to any of your milestones. You currently have ${projectContext.milestoneCount} milestones:\n\n${projectContext.milestoneNames.map((name, i) => `${i + 1}. ${name}`).join('\\n')}\n\nTo add a task, tell me:\n• **Which milestone** to add it to\n• **Task name** and description\n• **Any subtasks** to include\n\nExample: \"Add task 'Set up development environment' to the first milestone with subtasks for installing tools and configuring settings\"\n\nWhat task would you like to add?`;\n      }\n      return `I can help you add new content to \"${projectContext.name}\". I can create:\n\n🎯 **Milestones** - Major project phases\n📋 **Tasks** - Specific work items within milestones\n✅ **Subtasks** - Detailed steps for tasks\n📝 **Descriptions** - Enhanced details for any item\n\nWhat would you like to add? Just describe it naturally, like:\n• \"Add a milestone for user testing\"\n• \"Create a task for database setup in the development milestone\"\n• \"Add subtasks for the API integration task\"`;\n    }\n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\n      return `I can help you remove items from your project. For safety, I'll always confirm before deleting anything.\n\nI can remove:\n• **Tasks** that are no longer needed\n• **Subtasks** that are redundant\n• **Completed items** to clean up the project\n• **Duplicate entries**\n\n⚠️ **Note:** I cannot delete milestones as they're core to your project structure.\n\nWhat would you like to remove? Please be specific about the item name and location.`;\n    }\n    if (lowerMessage.includes('update') || lowerMessage.includes('change') || lowerMessage.includes('edit') || lowerMessage.includes('modify')) {\n      return `I can update various aspects of your project \"${projectContext.name}\":\n\n📝 **Content Updates:**\n• Task and subtask descriptions\n• Milestone objectives\n• Due dates and priorities\n• Task assignments\n\n🔄 **Status Changes:**\n• Mark items as in-progress, completed, or blocked\n• Update milestone phases\n• Change task priorities\n\n📊 **Structural Changes:**\n• Move tasks between milestones\n• Reorder items\n• Split large tasks into smaller ones\n\nWhat would you like to update? Describe the change you want to make.`;\n    }\n    if (lowerMessage.includes('help') || lowerMessage.includes('what can you do') || lowerMessage.includes('capabilities')) {\n      return `I'm your AI Project Assistant for \"${projectContext.name}\"! Here's what I can do:\n\n🎯 **Project Management:**\n• Add/remove tasks, subtasks, and milestones\n• Update descriptions, statuses, and priorities\n• Mark items as completed or in-progress\n• Move tasks between milestones\n\n📊 **Project Analysis:**\n• Show progress reports and statistics\n• Identify bottlenecks and overdue items\n• Suggest next actions and optimizations\n• Generate project summaries\n\n🔍 **Smart Search:**\n• Find specific tasks or milestones\n• Filter by status, assignee, or due date\n• Locate related items across the project\n\n💡 **Recommendations:**\n• Suggest task breakdowns\n• Recommend milestone structures\n• Identify missing dependencies\n• Propose timeline optimizations\n\nJust tell me what you want to do in natural language - I'll understand and help you get it done!`;\n    }\n\n    // Default intelligent response\n    return `I understand you want to \"${message}\".\n\nBased on your project \"${projectContext.name}\" with ${projectContext.milestoneCount} milestones and ${projectContext.taskCount} tasks, I can help you:\n\n🎯 **Quick Actions:**\n• \"Show me project progress\"\n• \"Add a new task to [milestone name]\"\n• \"Mark [task name] as completed\"\n• \"Update the description for [item name]\"\n\n💡 **Smart Suggestions:**\n• \"What should I work on next?\"\n• \"Show me overdue items\"\n• \"Help me organize this milestone\"\n• \"Create a timeline for this project\"\n\nWhat specific action would you like me to take? I'm here to make managing your project easier!`;\n  };\n  const extractActions = message => {\n    const actions = [];\n    const lowerMessage = message.toLowerCase();\n\n    // Enhanced action extraction with context\n    const actionPatterns = [{\n      pattern: /(complete|done|finish|mark.*complete)/,\n      type: 'complete_task',\n      confidence: 0.9,\n      description: 'Mark task as completed'\n    }, {\n      pattern: /(add|create|new).*milestone/,\n      type: 'add_milestone',\n      confidence: 0.9,\n      description: 'Add new milestone'\n    }, {\n      pattern: /(add|create|new).*task/,\n      type: 'add_task',\n      confidence: 0.9,\n      description: 'Add new task'\n    }, {\n      pattern: /(add|create|new).*subtask/,\n      type: 'add_subtask',\n      confidence: 0.9,\n      description: 'Add new subtask'\n    }, {\n      pattern: /(delete|remove).*task/,\n      type: 'delete_task',\n      confidence: 0.8,\n      description: 'Delete task'\n    }, {\n      pattern: /(update|change|edit|modify)/,\n      type: 'update_item',\n      confidence: 0.8,\n      description: 'Update item details'\n    }, {\n      pattern: /(progress|status|overview)/,\n      type: 'show_progress',\n      confidence: 0.9,\n      description: 'Show project progress'\n    }, {\n      pattern: /(move|transfer).*task/,\n      type: 'move_task',\n      confidence: 0.8,\n      description: 'Move task between milestones'\n    }, {\n      pattern: /(assign|delegate)/,\n      type: 'assign_task',\n      confidence: 0.8,\n      description: 'Assign task to team member'\n    }, {\n      pattern: /(due date|deadline|schedule)/,\n      type: 'set_deadline',\n      confidence: 0.8,\n      description: 'Set or update due date'\n    }];\n\n    // Extract entities (task names, milestone names, etc.)\n    const entities = {\n      taskNames: [],\n      milestoneNames: [],\n      dates: [],\n      priorities: []\n    };\n\n    // Look for quoted text (likely task/milestone names)\n    const quotedText = message.match(/\"([^\"]+)\"/g);\n    if (quotedText) {\n      entities.taskNames = quotedText.map(q => q.replace(/\"/g, ''));\n    }\n\n    // Look for date patterns\n    const datePatterns = message.match(/\\b\\d{1,2}\\/\\d{1,2}\\/\\d{4}\\b|\\b\\d{1,2}-\\d{1,2}-\\d{4}\\b|tomorrow|today|next week|next month/gi);\n    if (datePatterns) {\n      entities.dates = datePatterns;\n    }\n\n    // Look for priority keywords\n    const priorityPatterns = message.match(/\\b(high|low|medium|urgent|critical|normal)\\s*priority\\b/gi);\n    if (priorityPatterns) {\n      entities.priorities = priorityPatterns;\n    }\n\n    // Match actions against patterns\n    actionPatterns.forEach(_ref2 => {\n      let {\n        pattern,\n        type,\n        confidence,\n        description\n      } = _ref2;\n      if (pattern.test(lowerMessage)) {\n        actions.push({\n          type,\n          confidence,\n          description,\n          entities: entities,\n          originalMessage: message\n        });\n      }\n    });\n    return actions;\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const formatTimestamp = timestamp => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '70vh',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '12px 12px 0 0',\n        border: '1px solid #f0f0f0',\n        borderBottom: 'none',\n        backgroundColor: '#fafafa'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: mainYellowColor,\n            width: 40,\n            height: 40\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 24,\n            height: 24,\n            color: \"#fff\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontWeight: 600,\n              color: '#333'\n            },\n            children: \"AI Project Agent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            },\n            children: [\"Managing: \", planInfo === null || planInfo === void 0 ? void 0 : planInfo.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Beta\",\n          size: \"small\",\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            color: mainYellowColor,\n            fontWeight: 600,\n            ml: 'auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        flex: 1,\n        border: '1px solid #f0f0f0',\n        borderTop: 'none',\n        borderBottom: 'none',\n        overflow: 'auto',\n        p: 2,\n        backgroundColor: '#fff'\n      },\n      children: conversations.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            width: 60,\n            height: 60,\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 32,\n            height: 32,\n            color: mainYellowColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            fontWeight: 600,\n            color: '#333',\n            mb: 1\n          },\n          children: \"Welcome to AI Project Agent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: '#666',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            maxWidth: 400\n          },\n          children: \"I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        children: [conversations.map(message => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxWidth: '70%',\n              display: 'flex',\n              flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: message.type === 'user' ? '#666' : '#fff'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Paper, {\n                elevation: 0,\n                sx: {\n                  p: 1.5,\n                  borderRadius: '12px',\n                  backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\n                  color: message.type === 'user' ? '#fff' : '#333',\n                  border: message.isError ? '1px solid #f44336' : 'none'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontFamily: '\"Recursive Variable\", sans-serif',\n                    lineHeight: 1.5,\n                    whiteSpace: 'pre-line'\n                  },\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 23\n                }, this), message.type === 'assistant' && message.actions && message.actions.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mt: 1.5,\n                    display: 'flex',\n                    flexWrap: 'wrap',\n                    gap: 1\n                  },\n                  children: message.actions.slice(0, 3).map((action, actionIndex) => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: action.description,\n                    size: \"small\",\n                    onClick: () => {\n                      // Handle quick action\n                      setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);\n                      if (inputRef.current) {\n                        inputRef.current.focus();\n                      }\n                    },\n                    sx: {\n                      backgroundColor: '#fff',\n                      border: `1px solid ${mainYellowColor}`,\n                      color: mainYellowColor,\n                      fontSize: '0.7rem',\n                      height: '24px',\n                      cursor: 'pointer',\n                      '&:hover': {\n                        backgroundColor: `${mainYellowColor}10`\n                      }\n                    }\n                  }, actionIndex, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 680,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: '#999',\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  fontSize: '0.7rem',\n                  mt: 0.5,\n                  display: 'block',\n                  textAlign: message.type === 'user' ? 'right' : 'left'\n                },\n                children: formatTimestamp(message.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 15\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: \"#fff\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 0,\n              sx: {\n                p: 1.5,\n                borderRadius: '12px',\n                backgroundColor: '#f5f5f5',\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 16,\n                sx: {\n                  color: mainYellowColor\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  color: '#666'\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '0 0 12px 12px',\n        border: '1px solid #f0f0f0',\n        borderTop: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          alignItems: 'flex-end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          inputRef: inputRef,\n          value: currentMessage,\n          onChange: e => setCurrentMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask me anything about your project...\",\n          multiline: true,\n          maxRows: 3,\n          fullWidth: true,\n          variant: \"outlined\",\n          disabled: isLoading,\n          sx: {\n            '& .MuiOutlinedInput-root': {\n              borderRadius: '8px',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Send message\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => handleSendMessage(),\n            disabled: !currentMessage.trim() || isLoading,\n            sx: {\n              backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n              color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\n              '&:hover': {\n                backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:send\",\n              width: 20,\n              height: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 776,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 767,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 508,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentTab, \"rQuF4l2OLxvb/G3jCFGXzIq4svw=\", false, function () {\n  return [useLocation];\n});\n_c = AgentTab;\nexport default AgentTab;\nvar _c;\n$RefreshReg$(_c, \"AgentTab\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Typography", "Paper", "TextField", "IconButton", "Avatar", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "useLocation", "Iconify", "mainYellowColor", "APIURL", "getHeaders", "axios", "jsxDEV", "_jsxDEV", "AgentTab", "_ref", "_s", "planInfo", "onPlanUpdate", "conversations", "setConversations", "currentMessage", "setCurrentMessage", "isLoading", "setIsLoading", "messagesEndRef", "inputRef", "setError", "location", "_location$state", "savedConversations", "JSON", "parse", "localStorage", "getItem", "planConversations", "filter", "conv", "planId", "id", "pendingMessage", "_messageData$planInfo", "messageData", "handleSendMessage", "message", "removeItem", "error", "console", "state", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "messageText", "arguments", "length", "undefined", "trim", "userMessage", "Date", "now", "type", "content", "timestamp", "toISOString", "newConversations", "response", "post", "plan_slug", "slug", "headers", "aiResponseData", "data", "aiResponse", "actions", "metadata", "updatedConversations", "actionResults", "action", "result", "executeAIAction", "push", "success", "some", "failedActions", "errorMessage", "map", "join", "isError", "prev", "allConversations", "otherPlanConversations", "planName", "name", "setItem", "stringify", "_error$response", "_error$response$data", "errorResponse", "_error$response2", "_error$response2$data", "generateAIResponse", "lowerMessage", "toLowerCase", "milestones", "totalTasks", "reduce", "acc", "milestone", "_milestone$tasks", "tasks", "totalSubtasks", "_milestone$tasks2", "taskAcc", "task", "_task$subtasks", "subtasks", "projectContext", "milestoneCount", "taskCount", "subtaskCount", "milestoneNames", "m", "slice", "includes", "progressDetails", "_milestone$tasks3", "_milestone$tasks4", "_milestone$tasks4$fil", "completedTasks", "status", "availableTasks", "flatMap", "_milestone$tasks5", "i", "extractActions", "actionPatterns", "pattern", "confidence", "description", "entities", "taskNames", "dates", "priorities", "quotedText", "match", "q", "replace", "datePatterns", "priorityPatterns", "for<PERSON>ach", "_ref2", "test", "originalMessage", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "formatTimestamp", "toLocaleTimeString", "hour", "minute", "sx", "height", "display", "flexDirection", "children", "elevation", "p", "borderRadius", "border", "borderBottom", "backgroundColor", "alignItems", "gap", "width", "icon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontFamily", "fontWeight", "label", "size", "ml", "flex", "borderTop", "overflow", "justifyContent", "textAlign", "mb", "max<PERSON><PERSON><PERSON>", "lineHeight", "whiteSpace", "mt", "flexWrap", "actionIndex", "onClick", "focus", "fontSize", "cursor", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "multiline", "maxRows", "fullWidth", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/AgentTab.js"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  TextField,\r\n  IconButton,\r\n  Avatar,\r\n  CircularProgress,\r\n  Chip,\r\n  Tooltip\r\n} from '@mui/material';\r\nimport { useLocation } from 'react-router-dom';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\r\nimport { getHeaders } from \"helpers/functions\";\r\nimport axios from 'axios';\r\n\r\nconst AgentTab = ({ planInfo, onPlanUpdate }) => {\r\n  const [conversations, setConversations] = useState([]);\r\n  const [currentMessage, setCurrentMessage] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const messagesEndRef = useRef(null);\r\n  const inputRef = useRef(null);\r\n  const [ , setError] = useState(null);\r\n  const location = useLocation();\r\n\r\n  // Load conversations from localStorage\r\n  useEffect(() => {\r\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n    const planConversations = savedConversations.filter(conv => conv.planId === planInfo?.id);\r\n    setConversations(planConversations);\r\n\r\n    // Check for pending message from ChatbotBar\r\n    const pendingMessage = localStorage.getItem('pending_agent_message');\r\n    if (pendingMessage) {\r\n      try {\r\n        const messageData = JSON.parse(pendingMessage);\r\n        if (messageData.planInfo?.id === planInfo?.id) {\r\n          handleSendMessage(messageData.message);\r\n        }\r\n        // Clear the pending message\r\n        localStorage.removeItem('pending_agent_message');\r\n      } catch (error) {\r\n        console.error('Error processing pending message:', error);\r\n        localStorage.removeItem('pending_agent_message');\r\n      }\r\n    }\r\n\r\n    // If coming from chatbot bar via navigation state, add the initial message\r\n    if (location.state?.message) {\r\n      handleSendMessage(location.state.message);\r\n    }\r\n  }, [planInfo?.id, location.state]);\r\n\r\n  // Auto scroll to bottom\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  }, [conversations]);\r\n\r\n\r\n\r\n  const handleSendMessage = async (messageText = currentMessage) => {\r\n    if (!messageText.trim() || isLoading) return;\r\n\r\n    setIsLoading(true);\r\n    const userMessage = {\r\n      id: Date.now(),\r\n      type: 'user',\r\n      content: messageText.trim(),\r\n      timestamp: new Date().toISOString()\r\n    };\r\n\r\n    const newConversations = [...conversations, userMessage];\r\n    setConversations(newConversations);\r\n    setCurrentMessage('');\r\n\r\n    try {\r\n      // Call the new AI agent chat endpoint with plan context\r\n      const response = await axios.post(\r\n        `${APIURL}/api/assistant/agent-chat`,\r\n        {\r\n          message: messageText.trim(),\r\n          plan_slug: planInfo.slug\r\n        },\r\n        { headers: getHeaders() }\r\n      );\r\n\r\n      const aiResponseData = response.data;\r\n\r\n      // Create AI response message\r\n      const aiResponse = {\r\n        id: Date.now() + 1,\r\n        type: 'assistant',\r\n        content: aiResponseData.message || 'I received your message but had trouble generating a response.',\r\n        timestamp: new Date().toISOString(),\r\n        actions: aiResponseData.actions || [],\r\n        metadata: aiResponseData.metadata || {}\r\n      };\r\n\r\n      const updatedConversations = [...newConversations, aiResponse];\r\n      setConversations(updatedConversations);\r\n\r\n      // Execute any actions returned by the AI\r\n      if (aiResponseData.actions && aiResponseData.actions.length > 0) {\r\n        let actionResults = [];\r\n        for (const action of aiResponseData.actions) {\r\n          try {\r\n            const result = await executeAIAction(action);\r\n            actionResults.push(result);\r\n          } catch (error) {\r\n            console.error('Error executing AI action:', error);\r\n            actionResults.push({\r\n              success: false,\r\n              error: error.message || 'Unknown error'\r\n            });\r\n          }\r\n        }\r\n\r\n        // If any actions were successful, trigger plan refresh\r\n        if (actionResults.some(result => result.success) && onPlanUpdate) {\r\n          onPlanUpdate();\r\n        }\r\n\r\n        // Add action results to the conversation if there were any issues\r\n        const failedActions = actionResults.filter(result => !result.success);\r\n        if (failedActions.length > 0) {\r\n          const errorMessage = {\r\n            id: Date.now() + 2,\r\n            type: 'assistant',\r\n            content: `Note: I encountered some issues executing the requested actions:\\n${failedActions.map(result => `• ${result.error}`).join('\\n')}`,\r\n            timestamp: new Date().toISOString(),\r\n            isError: true\r\n          };\r\n          setConversations(prev => [...prev, errorMessage]);\r\n        }\r\n      }\r\n\r\n      // Save to localStorage\r\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== planInfo?.id);\r\n      const planConversations = updatedConversations.map(conv => ({\r\n        ...conv,\r\n        planId: planInfo?.id,\r\n        planName: planInfo?.name\r\n      }));\r\n\r\n      localStorage.setItem('agent_conversations', JSON.stringify([\r\n        ...otherPlanConversations,\r\n        ...planConversations\r\n      ]));\r\n\r\n    } catch (error) {\r\n      console.error('Error processing message:', error);\r\n      const errorResponse = {\r\n        id: Date.now() + 1,\r\n        type: 'assistant',\r\n        content: `Sorry, I encountered an error while processing your request: ${error.response?.data?.error || error.message || 'Unknown error'}. Please try again.`,\r\n        timestamp: new Date().toISOString(),\r\n        isError: true\r\n      };\r\n      setConversations([...newConversations, errorResponse]);\r\n      setError(error.message || 'Unknown error occurred');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const executeAIAction = async (action) => {\r\n    try {\r\n      const response = await axios.post(\r\n        `${APIURL}/api/assistant/plan-action`,\r\n        {\r\n          action: action.type,\r\n          plan_slug: planInfo.slug,\r\n          data: action.data || {},\r\n          message: `AI-generated action: ${action.type}`\r\n        },\r\n        { headers: getHeaders() }\r\n      );\r\n\r\n      return {\r\n        success: true,\r\n        data: response.data,\r\n        action: action\r\n      };\r\n    } catch (error) {\r\n      console.error('Error executing AI action:', error);\r\n      return {\r\n        success: false,\r\n        error: error.response?.data?.error || error.message || 'Unknown error',\r\n        action: action\r\n      };\r\n    }\r\n  };\r\n\r\n  const generateAIResponse = (message, planInfo) => {\r\n    const lowerMessage = message.toLowerCase();\r\n    const milestones = planInfo?.milestones || [];\r\n    const totalTasks = milestones.reduce((acc, milestone) => acc + (milestone.tasks?.length || 0), 0);\r\n    const totalSubtasks = milestones.reduce((acc, milestone) =>\r\n      acc + milestone.tasks?.reduce((taskAcc, task) => taskAcc + (task.subtasks?.length || 0), 0) || 0, 0);\r\n\r\n    // Analyze project context\r\n    const projectContext = {\r\n      name: planInfo?.name || 'your project',\r\n      milestoneCount: milestones.length,\r\n      taskCount: totalTasks,\r\n      subtaskCount: totalSubtasks,\r\n      milestoneNames: milestones.map(m => m.name).slice(0, 3), // First 3 milestone names\r\n    };\r\n\r\n    // Advanced intent recognition and response generation\r\n    if (lowerMessage.includes('milestone') && (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new'))) {\r\n      return `I can help you add a new milestone to \"${projectContext.name}\". Currently, you have ${projectContext.milestoneCount} milestones: ${projectContext.milestoneNames.join(', ')}${projectContext.milestoneCount > 3 ? '...' : ''}.\r\n\r\nTo add a new milestone, I'll need:\r\n1. **Milestone name** - What should we call it?\r\n2. **Description** - What's the main objective?\r\n3. **Position** - Should it come before/after a specific milestone?\r\n4. **Tasks** - Any initial tasks to include?\r\n\r\nPlease provide these details, or just tell me the milestone name and I'll help structure the rest!`;\r\n    }\r\n\r\n    if (lowerMessage.includes('progress') || lowerMessage.includes('status') || lowerMessage.includes('overview')) {\r\n      const progressDetails = milestones.map(milestone => {\r\n        const taskCount = milestone.tasks?.length || 0;\r\n        const completedTasks = milestone.tasks?.filter(task => task.status === 'completed')?.length || 0;\r\n        return `• ${milestone.name}: ${completedTasks}/${taskCount} tasks completed`;\r\n      }).join('\\n');\r\n\r\n      return `Here's your project progress for \"${projectContext.name}\":\r\n\r\n📊 **Overall Statistics:**\r\n• ${projectContext.milestoneCount} milestones\r\n• ${projectContext.taskCount} total tasks\r\n• ${projectContext.subtaskCount} total subtasks\r\n\r\n📋 **Milestone Progress:**\r\n${progressDetails}\r\n\r\nWould you like me to:\r\n• Show detailed progress for a specific milestone?\r\n• Identify overdue or at-risk tasks?\r\n• Suggest next actions to move the project forward?`;\r\n    }\r\n\r\n    if (lowerMessage.includes('complete') || lowerMessage.includes('done') || lowerMessage.includes('finish')) {\r\n      const availableTasks = milestones.flatMap(milestone =>\r\n        milestone.tasks?.filter(task => task.status !== 'completed').map(task =>\r\n          `\"${task.name}\" in ${milestone.name}`\r\n        ) || []\r\n      ).slice(0, 5);\r\n\r\n      return `I can mark tasks as completed for you! Here are some pending tasks I found:\r\n\r\n${availableTasks.map(task => `• ${task}`).join('\\n')}\r\n\r\nTo mark a task as complete, just tell me:\r\n• \"Mark [task name] as completed\"\r\n• \"Complete the [task name] task\"\r\n• Or simply \"Done with [task name]\"\r\n\r\nWhich task would you like to mark as completed?`;\r\n    }\r\n\r\n    if (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new')) {\r\n      if (lowerMessage.includes('task')) {\r\n        return `I can add new tasks to any of your milestones. You currently have ${projectContext.milestoneCount} milestones:\r\n\r\n${projectContext.milestoneNames.map((name, i) => `${i + 1}. ${name}`).join('\\n')}\r\n\r\nTo add a task, tell me:\r\n• **Which milestone** to add it to\r\n• **Task name** and description\r\n• **Any subtasks** to include\r\n\r\nExample: \"Add task 'Set up development environment' to the first milestone with subtasks for installing tools and configuring settings\"\r\n\r\nWhat task would you like to add?`;\r\n      }\r\n\r\n      return `I can help you add new content to \"${projectContext.name}\". I can create:\r\n\r\n🎯 **Milestones** - Major project phases\r\n📋 **Tasks** - Specific work items within milestones\r\n✅ **Subtasks** - Detailed steps for tasks\r\n📝 **Descriptions** - Enhanced details for any item\r\n\r\nWhat would you like to add? Just describe it naturally, like:\r\n• \"Add a milestone for user testing\"\r\n• \"Create a task for database setup in the development milestone\"\r\n• \"Add subtasks for the API integration task\"`;\r\n    }\r\n\r\n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\r\n      return `I can help you remove items from your project. For safety, I'll always confirm before deleting anything.\r\n\r\nI can remove:\r\n• **Tasks** that are no longer needed\r\n• **Subtasks** that are redundant\r\n• **Completed items** to clean up the project\r\n• **Duplicate entries**\r\n\r\n⚠️ **Note:** I cannot delete milestones as they're core to your project structure.\r\n\r\nWhat would you like to remove? Please be specific about the item name and location.`;\r\n    }\r\n\r\n    if (lowerMessage.includes('update') || lowerMessage.includes('change') || lowerMessage.includes('edit') || lowerMessage.includes('modify')) {\r\n      return `I can update various aspects of your project \"${projectContext.name}\":\r\n\r\n📝 **Content Updates:**\r\n• Task and subtask descriptions\r\n• Milestone objectives\r\n• Due dates and priorities\r\n• Task assignments\r\n\r\n🔄 **Status Changes:**\r\n• Mark items as in-progress, completed, or blocked\r\n• Update milestone phases\r\n• Change task priorities\r\n\r\n📊 **Structural Changes:**\r\n• Move tasks between milestones\r\n• Reorder items\r\n• Split large tasks into smaller ones\r\n\r\nWhat would you like to update? Describe the change you want to make.`;\r\n    }\r\n\r\n    if (lowerMessage.includes('help') || lowerMessage.includes('what can you do') || lowerMessage.includes('capabilities')) {\r\n      return `I'm your AI Project Assistant for \"${projectContext.name}\"! Here's what I can do:\r\n\r\n🎯 **Project Management:**\r\n• Add/remove tasks, subtasks, and milestones\r\n• Update descriptions, statuses, and priorities\r\n• Mark items as completed or in-progress\r\n• Move tasks between milestones\r\n\r\n📊 **Project Analysis:**\r\n• Show progress reports and statistics\r\n• Identify bottlenecks and overdue items\r\n• Suggest next actions and optimizations\r\n• Generate project summaries\r\n\r\n🔍 **Smart Search:**\r\n• Find specific tasks or milestones\r\n• Filter by status, assignee, or due date\r\n• Locate related items across the project\r\n\r\n💡 **Recommendations:**\r\n• Suggest task breakdowns\r\n• Recommend milestone structures\r\n• Identify missing dependencies\r\n• Propose timeline optimizations\r\n\r\nJust tell me what you want to do in natural language - I'll understand and help you get it done!`;\r\n    }\r\n\r\n    // Default intelligent response\r\n    return `I understand you want to \"${message}\".\r\n\r\nBased on your project \"${projectContext.name}\" with ${projectContext.milestoneCount} milestones and ${projectContext.taskCount} tasks, I can help you:\r\n\r\n🎯 **Quick Actions:**\r\n• \"Show me project progress\"\r\n• \"Add a new task to [milestone name]\"\r\n• \"Mark [task name] as completed\"\r\n• \"Update the description for [item name]\"\r\n\r\n💡 **Smart Suggestions:**\r\n• \"What should I work on next?\"\r\n• \"Show me overdue items\"\r\n• \"Help me organize this milestone\"\r\n• \"Create a timeline for this project\"\r\n\r\nWhat specific action would you like me to take? I'm here to make managing your project easier!`;\r\n  };\r\n\r\n  const extractActions = (message) => {\r\n    const actions = [];\r\n    const lowerMessage = message.toLowerCase();\r\n\r\n    // Enhanced action extraction with context\r\n    const actionPatterns = [\r\n      {\r\n        pattern: /(complete|done|finish|mark.*complete)/,\r\n        type: 'complete_task',\r\n        confidence: 0.9,\r\n        description: 'Mark task as completed'\r\n      },\r\n      {\r\n        pattern: /(add|create|new).*milestone/,\r\n        type: 'add_milestone',\r\n        confidence: 0.9,\r\n        description: 'Add new milestone'\r\n      },\r\n      {\r\n        pattern: /(add|create|new).*task/,\r\n        type: 'add_task',\r\n        confidence: 0.9,\r\n        description: 'Add new task'\r\n      },\r\n      {\r\n        pattern: /(add|create|new).*subtask/,\r\n        type: 'add_subtask',\r\n        confidence: 0.9,\r\n        description: 'Add new subtask'\r\n      },\r\n      {\r\n        pattern: /(delete|remove).*task/,\r\n        type: 'delete_task',\r\n        confidence: 0.8,\r\n        description: 'Delete task'\r\n      },\r\n      {\r\n        pattern: /(update|change|edit|modify)/,\r\n        type: 'update_item',\r\n        confidence: 0.8,\r\n        description: 'Update item details'\r\n      },\r\n      {\r\n        pattern: /(progress|status|overview)/,\r\n        type: 'show_progress',\r\n        confidence: 0.9,\r\n        description: 'Show project progress'\r\n      },\r\n      {\r\n        pattern: /(move|transfer).*task/,\r\n        type: 'move_task',\r\n        confidence: 0.8,\r\n        description: 'Move task between milestones'\r\n      },\r\n      {\r\n        pattern: /(assign|delegate)/,\r\n        type: 'assign_task',\r\n        confidence: 0.8,\r\n        description: 'Assign task to team member'\r\n      },\r\n      {\r\n        pattern: /(due date|deadline|schedule)/,\r\n        type: 'set_deadline',\r\n        confidence: 0.8,\r\n        description: 'Set or update due date'\r\n      }\r\n    ];\r\n\r\n    // Extract entities (task names, milestone names, etc.)\r\n    const entities = {\r\n      taskNames: [],\r\n      milestoneNames: [],\r\n      dates: [],\r\n      priorities: []\r\n    };\r\n\r\n    // Look for quoted text (likely task/milestone names)\r\n    const quotedText = message.match(/\"([^\"]+)\"/g);\r\n    if (quotedText) {\r\n      entities.taskNames = quotedText.map(q => q.replace(/\"/g, ''));\r\n    }\r\n\r\n    // Look for date patterns\r\n    const datePatterns = message.match(/\\b\\d{1,2}\\/\\d{1,2}\\/\\d{4}\\b|\\b\\d{1,2}-\\d{1,2}-\\d{4}\\b|tomorrow|today|next week|next month/gi);\r\n    if (datePatterns) {\r\n      entities.dates = datePatterns;\r\n    }\r\n\r\n    // Look for priority keywords\r\n    const priorityPatterns = message.match(/\\b(high|low|medium|urgent|critical|normal)\\s*priority\\b/gi);\r\n    if (priorityPatterns) {\r\n      entities.priorities = priorityPatterns;\r\n    }\r\n\r\n    // Match actions against patterns\r\n    actionPatterns.forEach(({ pattern, type, confidence, description }) => {\r\n      if (pattern.test(lowerMessage)) {\r\n        actions.push({\r\n          type,\r\n          confidence,\r\n          description,\r\n          entities: entities,\r\n          originalMessage: message\r\n        });\r\n      }\r\n    });\r\n\r\n    return actions;\r\n  };\r\n\r\n  const handleKeyPress = (e) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const formatTimestamp = (timestamp) => {\r\n    return new Date(timestamp).toLocaleTimeString('en-US', {\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>\r\n      {/* Header */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          p: 2,\r\n          borderRadius: '12px 12px 0 0',\r\n          border: '1px solid #f0f0f0',\r\n          borderBottom: 'none',\r\n          backgroundColor: '#fafafa'\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n          <Avatar\r\n            sx={{\r\n              backgroundColor: mainYellowColor,\r\n              width: 40,\r\n              height: 40\r\n            }}\r\n          >\r\n            <Iconify icon=\"mdi:robot\" width={24} height={24} color=\"#fff\" />\r\n          </Avatar>\r\n          <Box>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                color: '#333'\r\n              }}\r\n            >\r\n              AI Project Agent\r\n            </Typography>\r\n            <Typography\r\n              variant=\"caption\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }}\r\n            >\r\n              Managing: {planInfo?.name}\r\n            </Typography>\r\n          </Box>\r\n          <Chip\r\n            label=\"Beta\"\r\n            size=\"small\"\r\n            sx={{\r\n              backgroundColor: `${mainYellowColor}20`,\r\n              color: mainYellowColor,\r\n              fontWeight: 600,\r\n              ml: 'auto'\r\n            }}\r\n          />\r\n        </Box>\r\n      </Paper>\r\n\r\n      {/* Messages Area */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          flex: 1,\r\n          border: '1px solid #f0f0f0',\r\n          borderTop: 'none',\r\n          borderBottom: 'none',\r\n          overflow: 'auto',\r\n          p: 2,\r\n          backgroundColor: '#fff'\r\n        }}\r\n      >\r\n        {conversations.length === 0 ? (\r\n          <Box\r\n            sx={{\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              height: '100%',\r\n              textAlign: 'center'\r\n            }}\r\n          >\r\n            <Avatar\r\n              sx={{\r\n                backgroundColor: `${mainYellowColor}20`,\r\n                width: 60,\r\n                height: 60,\r\n                mb: 2\r\n              }}\r\n            >\r\n              <Iconify icon=\"mdi:robot\" width={32} height={32} color={mainYellowColor} />\r\n            </Avatar>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                color: '#333',\r\n                mb: 1\r\n              }}\r\n            >\r\n              Welcome to AI Project Agent\r\n            </Typography>\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                maxWidth: 400\r\n              }}\r\n            >\r\n              I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\r\n            </Typography>\r\n          </Box>\r\n        ) : (\r\n          <Box>\r\n            {conversations.map((message) => (\r\n              <Box\r\n                key={message.id}\r\n                sx={{\r\n                  display: 'flex',\r\n                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\r\n                  mb: 2\r\n                }}\r\n              >\r\n                <Box\r\n                  sx={{\r\n                    maxWidth: '70%',\r\n                    display: 'flex',\r\n                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\r\n                    alignItems: 'flex-start',\r\n                    gap: 1\r\n                  }}\r\n                >\r\n                  <Avatar\r\n                    sx={{\r\n                      width: 32,\r\n                      height: 32,\r\n                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\r\n                    }}\r\n                  >\r\n                    <Iconify\r\n                      icon={message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\"}\r\n                      width={18}\r\n                      height={18}\r\n                      color={message.type === 'user' ? '#666' : '#fff'}\r\n                    />\r\n                  </Avatar>\r\n                  <Box>\r\n                    <Paper\r\n                      elevation={0}\r\n                      sx={{\r\n                        p: 1.5,\r\n                        borderRadius: '12px',\r\n                        backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\r\n                        color: message.type === 'user' ? '#fff' : '#333',\r\n                        border: message.isError ? '1px solid #f44336' : 'none'\r\n                      }}\r\n                    >\r\n                      <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                          fontFamily: '\"Recursive Variable\", sans-serif',\r\n                          lineHeight: 1.5,\r\n                          whiteSpace: 'pre-line'\r\n                        }}\r\n                      >\r\n                        {message.content}\r\n                      </Typography>\r\n\r\n                      {/* Quick Action Buttons for AI responses */}\r\n                      {message.type === 'assistant' && message.actions && message.actions.length > 0 && (\r\n                        <Box sx={{ mt: 1.5, display: 'flex', flexWrap: 'wrap', gap: 1 }}>\r\n                          {message.actions.slice(0, 3).map((action, actionIndex) => (\r\n                            <Chip\r\n                              key={actionIndex}\r\n                              label={action.description}\r\n                              size=\"small\"\r\n                              onClick={() => {\r\n                                // Handle quick action\r\n                                setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);\r\n                                if (inputRef.current) {\r\n                                  inputRef.current.focus();\r\n                                }\r\n                              }}\r\n                              sx={{\r\n                                backgroundColor: '#fff',\r\n                                border: `1px solid ${mainYellowColor}`,\r\n                                color: mainYellowColor,\r\n                                fontSize: '0.7rem',\r\n                                height: '24px',\r\n                                cursor: 'pointer',\r\n                                '&:hover': {\r\n                                  backgroundColor: `${mainYellowColor}10`\r\n                                }\r\n                              }}\r\n                            />\r\n                          ))}\r\n                        </Box>\r\n                      )}\r\n                    </Paper>\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        color: '#999',\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        fontSize: '0.7rem',\r\n                        mt: 0.5,\r\n                        display: 'block',\r\n                        textAlign: message.type === 'user' ? 'right' : 'left'\r\n                      }}\r\n                    >\r\n                      {formatTimestamp(message.timestamp)}\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n            ))}\r\n            {isLoading && (\r\n              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>\r\n                  <Avatar\r\n                    sx={{\r\n                      width: 32,\r\n                      height: 32,\r\n                      backgroundColor: mainYellowColor\r\n                    }}\r\n                  >\r\n                    <Iconify icon=\"mdi:robot\" width={18} height={18} color=\"#fff\" />\r\n                  </Avatar>\r\n                  <Paper\r\n                    elevation={0}\r\n                    sx={{\r\n                      p: 1.5,\r\n                      borderRadius: '12px',\r\n                      backgroundColor: '#f5f5f5',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: 1\r\n                    }}\r\n                  >\r\n                    <CircularProgress size={16} sx={{ color: mainYellowColor }} />\r\n                    <Typography\r\n                      variant=\"body2\"\r\n                      sx={{\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        color: '#666'\r\n                      }}\r\n                    >\r\n                      Thinking...\r\n                    </Typography>\r\n                  </Paper>\r\n                </Box>\r\n              </Box>\r\n            )}\r\n            <div ref={messagesEndRef} />\r\n          </Box>\r\n        )}\r\n      </Paper>\r\n\r\n      {/* Input Area */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          p: 2,\r\n          borderRadius: '0 0 12px 12px',\r\n          border: '1px solid #f0f0f0',\r\n          borderTop: 'none'\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\r\n          <TextField\r\n            inputRef={inputRef}\r\n            value={currentMessage}\r\n            onChange={(e) => setCurrentMessage(e.target.value)}\r\n            onKeyPress={handleKeyPress}\r\n            placeholder=\"Ask me anything about your project...\"\r\n            multiline\r\n            maxRows={3}\r\n            fullWidth\r\n            variant=\"outlined\"\r\n            disabled={isLoading}\r\n            sx={{\r\n              '& .MuiOutlinedInput-root': {\r\n                borderRadius: '8px',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }\r\n            }}\r\n          />\r\n          <Tooltip title=\"Send message\">\r\n            <IconButton\r\n              onClick={() => handleSendMessage()}\r\n              disabled={!currentMessage.trim() || isLoading}\r\n              sx={{\r\n                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\r\n                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\r\n                '&:hover': {\r\n                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\r\n                }\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:send\" width={20} height={20} />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Box>\r\n      </Paper>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default AgentTab;\r\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,EAAEC,MAAM,QAAQ,mBAAmB;AAC3D,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGC,IAAA,IAAgC;EAAAC,EAAA;EAAA,IAA/B;IAAEC,QAAQ;IAAEC;EAAa,CAAC,GAAAH,IAAA;EAC1C,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM+B,cAAc,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM8B,QAAQ,GAAG9B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM,GAAI+B,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACpC,MAAMkC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;;EAE9B;EACAX,SAAS,CAAC,MAAM;IAAA,IAAAkC,eAAA;IACd,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;IAC1F,MAAMC,iBAAiB,GAAGL,kBAAkB,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKrB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAC;IACzFnB,gBAAgB,CAACe,iBAAiB,CAAC;;IAEnC;IACA,MAAMK,cAAc,GAAGP,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;IACpE,IAAIM,cAAc,EAAE;MAClB,IAAI;QAAA,IAAAC,qBAAA;QACF,MAAMC,WAAW,GAAGX,IAAI,CAACC,KAAK,CAACQ,cAAc,CAAC;QAC9C,IAAI,EAAAC,qBAAA,GAAAC,WAAW,CAACzB,QAAQ,cAAAwB,qBAAA,uBAApBA,qBAAA,CAAsBF,EAAE,OAAKtB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,GAAE;UAC7CI,iBAAiB,CAACD,WAAW,CAACE,OAAO,CAAC;QACxC;QACA;QACAX,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC;MAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDb,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC;MAClD;IACF;;IAEA;IACA,KAAAhB,eAAA,GAAID,QAAQ,CAACoB,KAAK,cAAAnB,eAAA,eAAdA,eAAA,CAAgBe,OAAO,EAAE;MAC3BD,iBAAiB,CAACf,QAAQ,CAACoB,KAAK,CAACJ,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAAC3B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAEX,QAAQ,CAACoB,KAAK,CAAC,CAAC;;EAElC;EACArD,SAAS,CAAC,MAAM;IAAA,IAAAsD,qBAAA;IACd,CAAAA,qBAAA,GAAAxB,cAAc,CAACyB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACjC,aAAa,CAAC,CAAC;EAInB,MAAMwB,iBAAiB,GAAG,eAAAA,CAAA,EAAwC;IAAA,IAAjCU,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGjC,cAAc;IAC3D,IAAI,CAACgC,WAAW,CAACI,IAAI,CAAC,CAAC,IAAIlC,SAAS,EAAE;IAEtCC,YAAY,CAAC,IAAI,CAAC;IAClB,MAAMkC,WAAW,GAAG;MAClBnB,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAET,WAAW,CAACI,IAAI,CAAC,CAAC;MAC3BM,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACpC,CAAC;IAED,MAAMC,gBAAgB,GAAG,CAAC,GAAG9C,aAAa,EAAEuC,WAAW,CAAC;IACxDtC,gBAAgB,CAAC6C,gBAAgB,CAAC;IAClC3C,iBAAiB,CAAC,EAAE,CAAC;IAErB,IAAI;MACF;MACA,MAAM4C,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,IAAI,CAC/B,GAAG1D,MAAM,2BAA2B,EACpC;QACEmC,OAAO,EAAES,WAAW,CAACI,IAAI,CAAC,CAAC;QAC3BW,SAAS,EAAEnD,QAAQ,CAACoD;MACtB,CAAC,EACD;QAAEC,OAAO,EAAE5D,UAAU,CAAC;MAAE,CAC1B,CAAC;MAED,MAAM6D,cAAc,GAAGL,QAAQ,CAACM,IAAI;;MAEpC;MACA,MAAMC,UAAU,GAAG;QACjBlC,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAES,cAAc,CAAC3B,OAAO,IAAI,gEAAgE;QACnGmB,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QACnCU,OAAO,EAAEH,cAAc,CAACG,OAAO,IAAI,EAAE;QACrCC,QAAQ,EAAEJ,cAAc,CAACI,QAAQ,IAAI,CAAC;MACxC,CAAC;MAED,MAAMC,oBAAoB,GAAG,CAAC,GAAGX,gBAAgB,EAAEQ,UAAU,CAAC;MAC9DrD,gBAAgB,CAACwD,oBAAoB,CAAC;;MAEtC;MACA,IAAIL,cAAc,CAACG,OAAO,IAAIH,cAAc,CAACG,OAAO,CAACnB,MAAM,GAAG,CAAC,EAAE;QAC/D,IAAIsB,aAAa,GAAG,EAAE;QACtB,KAAK,MAAMC,MAAM,IAAIP,cAAc,CAACG,OAAO,EAAE;UAC3C,IAAI;YACF,MAAMK,MAAM,GAAG,MAAMC,eAAe,CAACF,MAAM,CAAC;YAC5CD,aAAa,CAACI,IAAI,CAACF,MAAM,CAAC;UAC5B,CAAC,CAAC,OAAOjC,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;YAClD+B,aAAa,CAACI,IAAI,CAAC;cACjBC,OAAO,EAAE,KAAK;cACdpC,KAAK,EAAEA,KAAK,CAACF,OAAO,IAAI;YAC1B,CAAC,CAAC;UACJ;QACF;;QAEA;QACA,IAAIiC,aAAa,CAACM,IAAI,CAACJ,MAAM,IAAIA,MAAM,CAACG,OAAO,CAAC,IAAIhE,YAAY,EAAE;UAChEA,YAAY,CAAC,CAAC;QAChB;;QAEA;QACA,MAAMkE,aAAa,GAAGP,aAAa,CAACzC,MAAM,CAAC2C,MAAM,IAAI,CAACA,MAAM,CAACG,OAAO,CAAC;QACrE,IAAIE,aAAa,CAAC7B,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAM8B,YAAY,GAAG;YACnB9C,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;YAClBC,IAAI,EAAE,WAAW;YACjBC,OAAO,EAAE,qEAAqEsB,aAAa,CAACE,GAAG,CAACP,MAAM,IAAI,KAAKA,MAAM,CAACjC,KAAK,EAAE,CAAC,CAACyC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC3IxB,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;YACnCwB,OAAO,EAAE;UACX,CAAC;UACDpE,gBAAgB,CAACqE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEJ,YAAY,CAAC,CAAC;QACnD;MACF;;MAEA;MACA,MAAMK,gBAAgB,GAAG3D,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;MACxF,MAAMyD,sBAAsB,GAAGD,gBAAgB,CAACtD,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKrB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAC;MAC5F,MAAMJ,iBAAiB,GAAGyC,oBAAoB,CAACU,GAAG,CAACjD,IAAI,KAAK;QAC1D,GAAGA,IAAI;QACPC,MAAM,EAAErB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE;QACpBqD,QAAQ,EAAE3E,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4E;MACtB,CAAC,CAAC,CAAC;MAEH5D,YAAY,CAAC6D,OAAO,CAAC,qBAAqB,EAAE/D,IAAI,CAACgE,SAAS,CAAC,CACzD,GAAGJ,sBAAsB,EACzB,GAAGxD,iBAAiB,CACrB,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOW,KAAK,EAAE;MAAA,IAAAkD,eAAA,EAAAC,oBAAA;MACdlD,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMoD,aAAa,GAAG;QACpB3D,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,gEAAgE,EAAAkC,eAAA,GAAAlD,KAAK,CAACoB,QAAQ,cAAA8B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBxB,IAAI,cAAAyB,oBAAA,uBAApBA,oBAAA,CAAsBnD,KAAK,KAAIA,KAAK,CAACF,OAAO,IAAI,eAAe,qBAAqB;QAC7JmB,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QACnCwB,OAAO,EAAE;MACX,CAAC;MACDpE,gBAAgB,CAAC,CAAC,GAAG6C,gBAAgB,EAAEiC,aAAa,CAAC,CAAC;MACtDvE,QAAQ,CAACmB,KAAK,CAACF,OAAO,IAAI,wBAAwB,CAAC;IACrD,CAAC,SAAS;MACRpB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMwD,eAAe,GAAG,MAAOF,MAAM,IAAK;IACxC,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,IAAI,CAC/B,GAAG1D,MAAM,4BAA4B,EACrC;QACEqE,MAAM,EAAEA,MAAM,CAACjB,IAAI;QACnBO,SAAS,EAAEnD,QAAQ,CAACoD,IAAI;QACxBG,IAAI,EAAEM,MAAM,CAACN,IAAI,IAAI,CAAC,CAAC;QACvB5B,OAAO,EAAE,wBAAwBkC,MAAM,CAACjB,IAAI;MAC9C,CAAC,EACD;QAAES,OAAO,EAAE5D,UAAU,CAAC;MAAE,CAC1B,CAAC;MAED,OAAO;QACLwE,OAAO,EAAE,IAAI;QACbV,IAAI,EAAEN,QAAQ,CAACM,IAAI;QACnBM,MAAM,EAAEA;MACV,CAAC;IACH,CAAC,CAAC,OAAOhC,KAAK,EAAE;MAAA,IAAAqD,gBAAA,EAAAC,qBAAA;MACdrD,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO;QACLoC,OAAO,EAAE,KAAK;QACdpC,KAAK,EAAE,EAAAqD,gBAAA,GAAArD,KAAK,CAACoB,QAAQ,cAAAiC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3B,IAAI,cAAA4B,qBAAA,uBAApBA,qBAAA,CAAsBtD,KAAK,KAAIA,KAAK,CAACF,OAAO,IAAI,eAAe;QACtEkC,MAAM,EAAEA;MACV,CAAC;IACH;EACF,CAAC;EAED,MAAMuB,kBAAkB,GAAGA,CAACzD,OAAO,EAAE3B,QAAQ,KAAK;IAChD,MAAMqF,YAAY,GAAG1D,OAAO,CAAC2D,WAAW,CAAC,CAAC;IAC1C,MAAMC,UAAU,GAAG,CAAAvF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuF,UAAU,KAAI,EAAE;IAC7C,MAAMC,UAAU,GAAGD,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS;MAAA,IAAAC,gBAAA;MAAA,OAAKF,GAAG,IAAI,EAAAE,gBAAA,GAAAD,SAAS,CAACE,KAAK,cAAAD,gBAAA,uBAAfA,gBAAA,CAAiBtD,MAAM,KAAI,CAAC,CAAC;IAAA,GAAE,CAAC,CAAC;IACjG,MAAMwD,aAAa,GAAGP,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS;MAAA,IAAAI,iBAAA;MAAA,OACrDL,GAAG,KAAAK,iBAAA,GAAGJ,SAAS,CAACE,KAAK,cAAAE,iBAAA,uBAAfA,iBAAA,CAAiBN,MAAM,CAAC,CAACO,OAAO,EAAEC,IAAI;QAAA,IAAAC,cAAA;QAAA,OAAKF,OAAO,IAAI,EAAAE,cAAA,GAAAD,IAAI,CAACE,QAAQ,cAAAD,cAAA,uBAAbA,cAAA,CAAe5D,MAAM,KAAI,CAAC,CAAC;MAAA,GAAE,CAAC,CAAC,KAAI,CAAC;IAAA,GAAE,CAAC,CAAC;;IAEtG;IACA,MAAM8D,cAAc,GAAG;MACrBxB,IAAI,EAAE,CAAA5E,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4E,IAAI,KAAI,cAAc;MACtCyB,cAAc,EAAEd,UAAU,CAACjD,MAAM;MACjCgE,SAAS,EAAEd,UAAU;MACrBe,YAAY,EAAET,aAAa;MAC3BU,cAAc,EAAEjB,UAAU,CAAClB,GAAG,CAACoC,CAAC,IAAIA,CAAC,CAAC7B,IAAI,CAAC,CAAC8B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE;IAC3D,CAAC;;IAED;IACA,IAAIrB,YAAY,CAACsB,QAAQ,CAAC,WAAW,CAAC,KAAKtB,YAAY,CAACsB,QAAQ,CAAC,KAAK,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3I,OAAO,0CAA0CP,cAAc,CAACxB,IAAI,0BAA0BwB,cAAc,CAACC,cAAc,gBAAgBD,cAAc,CAACI,cAAc,CAAClC,IAAI,CAAC,IAAI,CAAC,GAAG8B,cAAc,CAACC,cAAc,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE;AAC1O;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mGAAmG;IAC/F;IAEA,IAAIhB,YAAY,CAACsB,QAAQ,CAAC,UAAU,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC7G,MAAMC,eAAe,GAAGrB,UAAU,CAAClB,GAAG,CAACsB,SAAS,IAAI;QAAA,IAAAkB,iBAAA,EAAAC,iBAAA,EAAAC,qBAAA;QAClD,MAAMT,SAAS,GAAG,EAAAO,iBAAA,GAAAlB,SAAS,CAACE,KAAK,cAAAgB,iBAAA,uBAAfA,iBAAA,CAAiBvE,MAAM,KAAI,CAAC;QAC9C,MAAM0E,cAAc,GAAG,EAAAF,iBAAA,GAAAnB,SAAS,CAACE,KAAK,cAAAiB,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB3F,MAAM,CAAC8E,IAAI,IAAIA,IAAI,CAACgB,MAAM,KAAK,WAAW,CAAC,cAAAF,qBAAA,uBAA5DA,qBAAA,CAA8DzE,MAAM,KAAI,CAAC;QAChG,OAAO,KAAKqD,SAAS,CAACf,IAAI,KAAKoC,cAAc,IAAIV,SAAS,kBAAkB;MAC9E,CAAC,CAAC,CAAChC,IAAI,CAAC,IAAI,CAAC;MAEb,OAAO,qCAAqC8B,cAAc,CAACxB,IAAI;AACrE;AACA;AACA,IAAIwB,cAAc,CAACC,cAAc;AACjC,IAAID,cAAc,CAACE,SAAS;AAC5B,IAAIF,cAAc,CAACG,YAAY;AAC/B;AACA;AACA,EAAEK,eAAe;AACjB;AACA;AACA;AACA;AACA,oDAAoD;IAChD;IAEA,IAAIvB,YAAY,CAACsB,QAAQ,CAAC,UAAU,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,MAAM,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACzG,MAAMO,cAAc,GAAG3B,UAAU,CAAC4B,OAAO,CAACxB,SAAS;QAAA,IAAAyB,iBAAA;QAAA,OACjD,EAAAA,iBAAA,GAAAzB,SAAS,CAACE,KAAK,cAAAuB,iBAAA,uBAAfA,iBAAA,CAAiBjG,MAAM,CAAC8E,IAAI,IAAIA,IAAI,CAACgB,MAAM,KAAK,WAAW,CAAC,CAAC5C,GAAG,CAAC4B,IAAI,IACnE,IAAIA,IAAI,CAACrB,IAAI,QAAQe,SAAS,CAACf,IAAI,EACrC,CAAC,KAAI,EAAE;MAAA,CACT,CAAC,CAAC8B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAEb,OAAO;AACb;AACA,EAAEQ,cAAc,CAAC7C,GAAG,CAAC4B,IAAI,IAAI,KAAKA,IAAI,EAAE,CAAC,CAAC3B,IAAI,CAAC,IAAI,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD;IAC5C;IAEA,IAAIe,YAAY,CAACsB,QAAQ,CAAC,KAAK,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,KAAK,CAAC,EAAE;MACnG,IAAItB,YAAY,CAACsB,QAAQ,CAAC,MAAM,CAAC,EAAE;QACjC,OAAO,qEAAqEP,cAAc,CAACC,cAAc;AACjH;AACA,EAAED,cAAc,CAACI,cAAc,CAACnC,GAAG,CAAC,CAACO,IAAI,EAAEyC,CAAC,KAAK,GAAGA,CAAC,GAAG,CAAC,KAAKzC,IAAI,EAAE,CAAC,CAACN,IAAI,CAAC,IAAI,CAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;MAC3B;MAEA,OAAO,sCAAsC8B,cAAc,CAACxB,IAAI;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C;IAC1C;IAEA,IAAIS,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACtE,OAAO;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF;IAChF;IAEA,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,MAAM,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC1I,OAAO,iDAAiDP,cAAc,CAACxB,IAAI;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE;IACjE;IAEA,IAAIS,YAAY,CAACsB,QAAQ,CAAC,MAAM,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,iBAAiB,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,cAAc,CAAC,EAAE;MACtH,OAAO,sCAAsCP,cAAc,CAACxB,IAAI;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iGAAiG;IAC7F;;IAEA;IACA,OAAO,6BAA6BjD,OAAO;AAC/C;AACA,yBAAyByE,cAAc,CAACxB,IAAI,UAAUwB,cAAc,CAACC,cAAc,mBAAmBD,cAAc,CAACE,SAAS;AAC9H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+FAA+F;EAC7F,CAAC;EAED,MAAMgB,cAAc,GAAI3F,OAAO,IAAK;IAClC,MAAM8B,OAAO,GAAG,EAAE;IAClB,MAAM4B,YAAY,GAAG1D,OAAO,CAAC2D,WAAW,CAAC,CAAC;;IAE1C;IACA,MAAMiC,cAAc,GAAG,CACrB;MACEC,OAAO,EAAE,uCAAuC;MAChD5E,IAAI,EAAE,eAAe;MACrB6E,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,6BAA6B;MACtC5E,IAAI,EAAE,eAAe;MACrB6E,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,wBAAwB;MACjC5E,IAAI,EAAE,UAAU;MAChB6E,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,2BAA2B;MACpC5E,IAAI,EAAE,aAAa;MACnB6E,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,uBAAuB;MAChC5E,IAAI,EAAE,aAAa;MACnB6E,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,6BAA6B;MACtC5E,IAAI,EAAE,aAAa;MACnB6E,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,4BAA4B;MACrC5E,IAAI,EAAE,eAAe;MACrB6E,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,uBAAuB;MAChC5E,IAAI,EAAE,WAAW;MACjB6E,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,mBAAmB;MAC5B5E,IAAI,EAAE,aAAa;MACnB6E,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,8BAA8B;MACvC5E,IAAI,EAAE,cAAc;MACpB6E,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,CACF;;IAED;IACA,MAAMC,QAAQ,GAAG;MACfC,SAAS,EAAE,EAAE;MACbpB,cAAc,EAAE,EAAE;MAClBqB,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE;IACd,CAAC;;IAED;IACA,MAAMC,UAAU,GAAGpG,OAAO,CAACqG,KAAK,CAAC,YAAY,CAAC;IAC9C,IAAID,UAAU,EAAE;MACdJ,QAAQ,CAACC,SAAS,GAAGG,UAAU,CAAC1D,GAAG,CAAC4D,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC/D;;IAEA;IACA,MAAMC,YAAY,GAAGxG,OAAO,CAACqG,KAAK,CAAC,6FAA6F,CAAC;IACjI,IAAIG,YAAY,EAAE;MAChBR,QAAQ,CAACE,KAAK,GAAGM,YAAY;IAC/B;;IAEA;IACA,MAAMC,gBAAgB,GAAGzG,OAAO,CAACqG,KAAK,CAAC,2DAA2D,CAAC;IACnG,IAAII,gBAAgB,EAAE;MACpBT,QAAQ,CAACG,UAAU,GAAGM,gBAAgB;IACxC;;IAEA;IACAb,cAAc,CAACc,OAAO,CAACC,KAAA,IAAgD;MAAA,IAA/C;QAAEd,OAAO;QAAE5E,IAAI;QAAE6E,UAAU;QAAEC;MAAY,CAAC,GAAAY,KAAA;MAChE,IAAId,OAAO,CAACe,IAAI,CAAClD,YAAY,CAAC,EAAE;QAC9B5B,OAAO,CAACO,IAAI,CAAC;UACXpB,IAAI;UACJ6E,UAAU;UACVC,WAAW;UACXC,QAAQ,EAAEA,QAAQ;UAClBa,eAAe,EAAE7G;QACnB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAO8B,OAAO;EAChB,CAAC;EAED,MAAMgF,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBnH,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMoH,eAAe,GAAIhG,SAAS,IAAK;IACrC,OAAO,IAAIJ,IAAI,CAACI,SAAS,CAAC,CAACiG,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACErJ,OAAA,CAAChB,GAAG;IAACsK,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpE1J,OAAA,CAACd,KAAK;MACJyK,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,MAAM;QACpBC,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,eAEF1J,OAAA,CAAChB,GAAG;QAACsK,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAES,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACzD1J,OAAA,CAACX,MAAM;UACLiK,EAAE,EAAE;YACFU,eAAe,EAAErK,eAAe;YAChCwK,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE;UACV,CAAE;UAAAG,QAAA,eAEF1J,OAAA,CAACN,OAAO;YAAC0K,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACTzK,OAAA,CAAChB,GAAG;UAAA0K,QAAA,gBACF1J,OAAA,CAACf,UAAU;YACTyL,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFqB,UAAU,EAAE,kCAAkC;cAC9CC,UAAU,EAAE,GAAG;cACfP,KAAK,EAAE;YACT,CAAE;YAAAX,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzK,OAAA,CAACf,UAAU;YACTyL,OAAO,EAAC,SAAS;YACjBpB,EAAE,EAAE;cACFe,KAAK,EAAE,MAAM;cACbM,UAAU,EAAE;YACd,CAAE;YAAAjB,QAAA,GACH,YACW,EAACtJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4E,IAAI;UAAA;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNzK,OAAA,CAACT,IAAI;UACHsL,KAAK,EAAC,MAAM;UACZC,IAAI,EAAC,OAAO;UACZxB,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGrK,eAAe,IAAI;YACvC0K,KAAK,EAAE1K,eAAe;YACtBiL,UAAU,EAAE,GAAG;YACfG,EAAE,EAAE;UACN;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRzK,OAAA,CAACd,KAAK;MACJyK,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACF0B,IAAI,EAAE,CAAC;QACPlB,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE,MAAM;QACjBlB,YAAY,EAAE,MAAM;QACpBmB,QAAQ,EAAE,MAAM;QAChBtB,CAAC,EAAE,CAAC;QACJI,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,EAEDpJ,aAAa,CAACoC,MAAM,KAAK,CAAC,gBACzB1C,OAAA,CAAChB,GAAG;QACFsK,EAAE,EAAE;UACFE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBQ,UAAU,EAAE,QAAQ;UACpBkB,cAAc,EAAE,QAAQ;UACxB5B,MAAM,EAAE,MAAM;UACd6B,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,gBAEF1J,OAAA,CAACX,MAAM;UACLiK,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGrK,eAAe,IAAI;YACvCwK,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE,EAAE;YACV8B,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEF1J,OAAA,CAACN,OAAO;YAAC0K,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAE1K;UAAgB;YAAA2K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACTzK,OAAA,CAACf,UAAU;UACTyL,OAAO,EAAC,IAAI;UACZpB,EAAE,EAAE;YACFqB,UAAU,EAAE,kCAAkC;YAC9CC,UAAU,EAAE,GAAG;YACfP,KAAK,EAAE,MAAM;YACbgB,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzK,OAAA,CAACf,UAAU;UACTyL,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YACFe,KAAK,EAAE,MAAM;YACbM,UAAU,EAAE,kCAAkC;YAC9CW,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAENzK,OAAA,CAAChB,GAAG;QAAA0K,QAAA,GACDpJ,aAAa,CAACmE,GAAG,CAAE1C,OAAO,iBACzB/B,OAAA,CAAChB,GAAG;UAEFsK,EAAE,EAAE;YACFE,OAAO,EAAE,MAAM;YACf2B,cAAc,EAAEpJ,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,YAAY;YACnEqI,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEF1J,OAAA,CAAChB,GAAG;YACFsK,EAAE,EAAE;cACFgC,QAAQ,EAAE,KAAK;cACf9B,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE1H,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,KAAK;cAC9DiH,UAAU,EAAE,YAAY;cACxBC,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBAEF1J,OAAA,CAACX,MAAM;cACLiK,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAEjI,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,SAAS,GAAGrD;cACzD,CAAE;cAAA+J,QAAA,eAEF1J,OAAA,CAACN,OAAO;gBACN0K,IAAI,EAAErI,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,yBAAyB,GAAG,WAAY;gBACxEmH,KAAK,EAAE,EAAG;gBACVZ,MAAM,EAAE,EAAG;gBACXc,KAAK,EAAEtI,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG;cAAO;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTzK,OAAA,CAAChB,GAAG;cAAA0K,QAAA,gBACF1J,OAAA,CAACd,KAAK;gBACJyK,SAAS,EAAE,CAAE;gBACbL,EAAE,EAAE;kBACFM,CAAC,EAAE,GAAG;kBACNC,YAAY,EAAE,MAAM;kBACpBG,eAAe,EAAEjI,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAGrD,eAAe,GAAG,SAAS;kBACtE0K,KAAK,EAAEtI,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;kBAChD8G,MAAM,EAAE/H,OAAO,CAAC4C,OAAO,GAAG,mBAAmB,GAAG;gBAClD,CAAE;gBAAA+E,QAAA,gBAEF1J,OAAA,CAACf,UAAU;kBACTyL,OAAO,EAAC,OAAO;kBACfpB,EAAE,EAAE;oBACFqB,UAAU,EAAE,kCAAkC;oBAC9CY,UAAU,EAAE,GAAG;oBACfC,UAAU,EAAE;kBACd,CAAE;kBAAA9B,QAAA,EAED3H,OAAO,CAACkB;gBAAO;kBAAAqH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAGZ1I,OAAO,CAACiB,IAAI,KAAK,WAAW,IAAIjB,OAAO,CAAC8B,OAAO,IAAI9B,OAAO,CAAC8B,OAAO,CAACnB,MAAM,GAAG,CAAC,iBAC5E1C,OAAA,CAAChB,GAAG;kBAACsK,EAAE,EAAE;oBAAEmC,EAAE,EAAE,GAAG;oBAAEjC,OAAO,EAAE,MAAM;oBAAEkC,QAAQ,EAAE,MAAM;oBAAExB,GAAG,EAAE;kBAAE,CAAE;kBAAAR,QAAA,EAC7D3H,OAAO,CAAC8B,OAAO,CAACiD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACrC,GAAG,CAAC,CAACR,MAAM,EAAE0H,WAAW,kBACnD3L,OAAA,CAACT,IAAI;oBAEHsL,KAAK,EAAE5G,MAAM,CAAC6D,WAAY;oBAC1BgD,IAAI,EAAC,OAAO;oBACZc,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACAnL,iBAAiB,CAACwD,MAAM,CAAC2E,eAAe,IAAI,UAAU3E,MAAM,CAAC6D,WAAW,CAACpC,WAAW,CAAC,CAAC,EAAE,CAAC;sBACzF,IAAI7E,QAAQ,CAACwB,OAAO,EAAE;wBACpBxB,QAAQ,CAACwB,OAAO,CAACwJ,KAAK,CAAC,CAAC;sBAC1B;oBACF,CAAE;oBACFvC,EAAE,EAAE;sBACFU,eAAe,EAAE,MAAM;sBACvBF,MAAM,EAAE,aAAanK,eAAe,EAAE;sBACtC0K,KAAK,EAAE1K,eAAe;sBACtBmM,QAAQ,EAAE,QAAQ;sBAClBvC,MAAM,EAAE,MAAM;sBACdwC,MAAM,EAAE,SAAS;sBACjB,SAAS,EAAE;wBACT/B,eAAe,EAAE,GAAGrK,eAAe;sBACrC;oBACF;kBAAE,GApBGgM,WAAW;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqBjB,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACRzK,OAAA,CAACf,UAAU;gBACTyL,OAAO,EAAC,SAAS;gBACjBpB,EAAE,EAAE;kBACFe,KAAK,EAAE,MAAM;kBACbM,UAAU,EAAE,kCAAkC;kBAC9CmB,QAAQ,EAAE,QAAQ;kBAClBL,EAAE,EAAE,GAAG;kBACPjC,OAAO,EAAE,OAAO;kBAChB4B,SAAS,EAAErJ,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG;gBACjD,CAAE;gBAAA0G,QAAA,EAEDR,eAAe,CAACnH,OAAO,CAACmB,SAAS;cAAC;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjGD1I,OAAO,CAACL,EAAE;UAAA4I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkGZ,CACN,CAAC,EACD/J,SAAS,iBACRV,OAAA,CAAChB,GAAG;UAACsK,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,YAAY;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eAChE1J,OAAA,CAAChB,GAAG;YAACsK,EAAE,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAES,UAAU,EAAE,YAAY;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAR,QAAA,gBAC7D1J,OAAA,CAACX,MAAM;cACLiK,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAErK;cACnB,CAAE;cAAA+J,QAAA,eAEF1J,OAAA,CAACN,OAAO;gBAAC0K,IAAI,EAAC,WAAW;gBAACD,KAAK,EAAE,EAAG;gBAACZ,MAAM,EAAE,EAAG;gBAACc,KAAK,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACTzK,OAAA,CAACd,KAAK;cACJyK,SAAS,EAAE,CAAE;cACbL,EAAE,EAAE;gBACFM,CAAC,EAAE,GAAG;gBACNC,YAAY,EAAE,MAAM;gBACpBG,eAAe,EAAE,SAAS;gBAC1BR,OAAO,EAAE,MAAM;gBACfS,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,gBAEF1J,OAAA,CAACV,gBAAgB;gBAACwL,IAAI,EAAE,EAAG;gBAACxB,EAAE,EAAE;kBAAEe,KAAK,EAAE1K;gBAAgB;cAAE;gBAAA2K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DzK,OAAA,CAACf,UAAU;gBACTyL,OAAO,EAAC,OAAO;gBACfpB,EAAE,EAAE;kBACFqB,UAAU,EAAE,kCAAkC;kBAC9CN,KAAK,EAAE;gBACT,CAAE;gBAAAX,QAAA,EACH;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eACDzK,OAAA;UAAKgM,GAAG,EAAEpL;QAAe;UAAA0J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGRzK,OAAA,CAACd,KAAK;MACJyK,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE;MACb,CAAE;MAAAvB,QAAA,eAEF1J,OAAA,CAAChB,GAAG;QAACsK,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEU,GAAG,EAAE,CAAC;UAAED,UAAU,EAAE;QAAW,CAAE;QAAAP,QAAA,gBAC3D1J,OAAA,CAACb,SAAS;UACR0B,QAAQ,EAAEA,QAAS;UACnBoL,KAAK,EAAEzL,cAAe;UACtB0L,QAAQ,EAAGpD,CAAC,IAAKrI,iBAAiB,CAACqI,CAAC,CAACqD,MAAM,CAACF,KAAK,CAAE;UACnDG,UAAU,EAAEvD,cAAe;UAC3BwD,WAAW,EAAC,uCAAuC;UACnDC,SAAS;UACTC,OAAO,EAAE,CAAE;UACXC,SAAS;UACT9B,OAAO,EAAC,UAAU;UAClB+B,QAAQ,EAAE/L,SAAU;UACpB4I,EAAE,EAAE;YACF,0BAA0B,EAAE;cAC1BO,YAAY,EAAE,KAAK;cACnBc,UAAU,EAAE;YACd;UACF;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFzK,OAAA,CAACR,OAAO;UAACkN,KAAK,EAAC,cAAc;UAAAhD,QAAA,eAC3B1J,OAAA,CAACZ,UAAU;YACTwM,OAAO,EAAEA,CAAA,KAAM9J,iBAAiB,CAAC,CAAE;YACnC2K,QAAQ,EAAE,CAACjM,cAAc,CAACoC,IAAI,CAAC,CAAC,IAAIlC,SAAU;YAC9C4I,EAAE,EAAE;cACFU,eAAe,EAAExJ,cAAc,CAACoC,IAAI,CAAC,CAAC,IAAI,CAAClC,SAAS,GAAGf,eAAe,GAAG,SAAS;cAClF0K,KAAK,EAAE7J,cAAc,CAACoC,IAAI,CAAC,CAAC,IAAI,CAAClC,SAAS,GAAG,MAAM,GAAG,MAAM;cAC5D,SAAS,EAAE;gBACTsJ,eAAe,EAAExJ,cAAc,CAACoC,IAAI,CAAC,CAAC,IAAI,CAAClC,SAAS,GAAG,SAAS,GAAG;cACrE;YACF,CAAE;YAAAgJ,QAAA,eAEF1J,OAAA,CAACN,OAAO;cAAC0K,IAAI,EAAC,uBAAuB;cAACD,KAAK,EAAE,EAAG;cAACZ,MAAM,EAAE;YAAG;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACtK,EAAA,CA1xBIF,QAAQ;EAAA,QAOKR,WAAW;AAAA;AAAAkN,EAAA,GAPxB1M,QAAQ;AA4xBd,eAAeA,QAAQ;AAAC,IAAA0M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
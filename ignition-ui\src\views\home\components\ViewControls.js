import React from 'react';
import { Box, ToggleButtonGroup, ToggleButton, Tooltip } from '@mui/material';
import Iconify from 'components/Iconify/index';
import styles from "../styles.module.scss";

const ViewControls = ({ viewMode, groupBy, onViewModeChange, onGroupByChange }) => {
  return (
    <Box className={styles.actionButtons}>
      <ToggleButtonGroup
        value={viewMode}
        exclusive
        onChange={onViewModeChange}
        aria-label="view mode"
        size="small"
        className={styles.viewToggle}
        sx={{
          backgroundColor: '#f5f5f5',
          borderRadius: '8px',
          '& .MuiToggleButton-root': {
            padding: '6px',
            '&.Mui-selected': {
              backgroundColor: '#333',
              color: 'white'
            }
          }
        }}
      >
        <ToggleButton value="list" aria-label="list view">
          <Iconify icon="material-symbols:view-list" width={20} height={20} />
        </ToggleButton>
        <ToggleButton value="grid" aria-label="grid view">
          <Iconify icon="material-symbols:grid-view" width={20} height={20} />
        </ToggleButton>
      </ToggleButtonGroup>

      <ToggleButtonGroup
        value={groupBy}
        exclusive
        onChange={onGroupByChange}
        aria-label="group by"
        size="small"
        className={styles.groupToggle}
        sx={{
          backgroundColor: '#f5f5f5',
          borderRadius: '8px',
          '& .MuiToggleButton-root': {
            padding: '6px',
            '&.Mui-selected': {
              backgroundColor: '#333',
              color: 'white'
            }
          }
        }}
      >
        <ToggleButton value="none" aria-label="no grouping">
          <Tooltip title="No grouping" arrow>
            <Iconify icon="material-symbols:sort" width={20} height={20} />
          </Tooltip>
        </ToggleButton>
        <ToggleButton value="status" aria-label="group by status">
          <Tooltip title="Group by status" arrow>
            <Iconify icon="material-symbols:check-circle-outline" width={20} height={20} />
          </Tooltip>
        </ToggleButton>
        <ToggleButton value="date" aria-label="group by date">
          <Tooltip title="Group by date" arrow>
            <Iconify icon="material-symbols:date-range" width={20} height={20} />
          </Tooltip>
        </ToggleButton>
      </ToggleButtonGroup>
    </Box>
  );
};

export default ViewControls;

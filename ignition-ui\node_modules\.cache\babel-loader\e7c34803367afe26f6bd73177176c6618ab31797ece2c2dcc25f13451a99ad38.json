{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\AgentTab.js\",\n  _s = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Typography, Paper, TextField, IconButton, Avatar, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport { useLocation } from 'react-router-dom';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\nimport { getHeaders } from \"helpers/functions\";\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AgentTab = _ref => {\n  _s();\n  let {\n    planInfo,\n    onPlanUpdate\n  } = _ref;\n  const [conversations, setConversations] = useState([]);\n  const [currentMessage, setCurrentMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const [, setError] = useState(null);\n  const location = useLocation();\n\n  // Load conversations from localStorage\n  useEffect(() => {\n    var _location$state;\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n    const planConversations = savedConversations.filter(conv => conv.planId === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n    setConversations(planConversations);\n\n    // Check for pending message from ChatbotBar\n    const pendingMessage = localStorage.getItem('pending_agent_message');\n    if (pendingMessage) {\n      try {\n        var _messageData$planInfo;\n        const messageData = JSON.parse(pendingMessage);\n        if (((_messageData$planInfo = messageData.planInfo) === null || _messageData$planInfo === void 0 ? void 0 : _messageData$planInfo.id) === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id)) {\n          handleSendMessage(messageData.message);\n        }\n        // Clear the pending message\n        localStorage.removeItem('pending_agent_message');\n      } catch (error) {\n        console.error('Error processing pending message:', error);\n        localStorage.removeItem('pending_agent_message');\n      }\n    }\n\n    // If coming from chatbot bar via navigation state, add the initial message\n    if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.message) {\n      handleSendMessage(location.state.message);\n    }\n  }, [planInfo === null || planInfo === void 0 ? void 0 : planInfo.id, location.state]);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [conversations]);\n  const handleSendMessage = async function () {\n    let messageText = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : currentMessage;\n    if (!messageText.trim() || isLoading) return;\n    setIsLoading(true);\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageText.trim(),\n      timestamp: new Date().toISOString()\n    };\n    const newConversations = [...conversations, userMessage];\n    setConversations(newConversations);\n    setCurrentMessage('');\n    try {\n      // Call the new AI agent chat endpoint with plan context\n      const response = await axios.post(`${APIURL}/api/assistant/agent-chat`, {\n        message: messageText.trim(),\n        plan_slug: planInfo.slug\n      }, {\n        headers: getHeaders()\n      });\n      const aiResponseData = response.data;\n\n      // Create AI response message\n      const aiResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: aiResponseData.message || 'I received your message but had trouble generating a response.',\n        timestamp: new Date().toISOString(),\n        actions: aiResponseData.actions || [],\n        metadata: aiResponseData.metadata || {}\n      };\n      const updatedConversations = [...newConversations, aiResponse];\n      setConversations(updatedConversations);\n\n      // Execute any actions returned by the AI\n      if (aiResponseData.actions && aiResponseData.actions.length > 0) {\n        let actionResults = [];\n        for (const action of aiResponseData.actions) {\n          try {\n            const result = await executeAIAction(action);\n            actionResults.push(result);\n          } catch (error) {\n            console.error('Error executing AI action:', error);\n            actionResults.push({\n              success: false,\n              error: error.message || 'Unknown error'\n            });\n          }\n        }\n\n        // If any actions were successful, trigger plan refresh and show success message\n        const successfulActions = actionResults.filter(result => result.success);\n        if (successfulActions.length > 0 && onPlanUpdate) {\n          onPlanUpdate();\n\n          // Add success confirmation message\n          const successMessage = {\n            id: Date.now() + 2,\n            type: 'assistant',\n            content: `✅ **All done!** I've successfully made the following changes to your project:\\n\\n${successfulActions.map(result => {\n              var _result$data$result, _action$data, _result$data$result2, _action$data2, _result$data$result3, _action$data3, _result$data$result4, _action$data4, _result$data$result5, _action$data5, _result$data$result6, _action$data6;\n              const action = result.action;\n              const actionType = action.action || action.type;\n              switch (actionType) {\n                case 'add_milestone':\n                  return `• Added milestone: \"${((_result$data$result = result.data.result) === null || _result$data$result === void 0 ? void 0 : _result$data$result.milestone_name) || ((_action$data = action.data) === null || _action$data === void 0 ? void 0 : _action$data.name)}\"`;\n                case 'add_task':\n                  return `• Added task: \"${((_result$data$result2 = result.data.result) === null || _result$data$result2 === void 0 ? void 0 : _result$data$result2.task_name) || ((_action$data2 = action.data) === null || _action$data2 === void 0 ? void 0 : _action$data2.name)}\"`;\n                case 'add_subtask':\n                  return `• Added subtask: \"${((_result$data$result3 = result.data.result) === null || _result$data$result3 === void 0 ? void 0 : _result$data$result3.subtask_name) || ((_action$data3 = action.data) === null || _action$data3 === void 0 ? void 0 : _action$data3.name)}\"`;\n                case 'update_milestone':\n                  return `• Updated milestone: \"${((_result$data$result4 = result.data.result) === null || _result$data$result4 === void 0 ? void 0 : _result$data$result4.milestone_name) || ((_action$data4 = action.data) === null || _action$data4 === void 0 ? void 0 : _action$data4.name)}\"`;\n                case 'update_task':\n                  return `• Updated task: \"${((_result$data$result5 = result.data.result) === null || _result$data$result5 === void 0 ? void 0 : _result$data$result5.task_name) || ((_action$data5 = action.data) === null || _action$data5 === void 0 ? void 0 : _action$data5.name)}\"`;\n                case 'complete_task':\n                  return `• Completed task: \"${((_result$data$result6 = result.data.result) === null || _result$data$result6 === void 0 ? void 0 : _result$data$result6.task_name) || ((_action$data6 = action.data) === null || _action$data6 === void 0 ? void 0 : _action$data6.name)}\"`;\n                default:\n                  return `• Completed action: ${actionType}`;\n              }\n            }).join('\\n')}\\n\\n**Check it out** in your project details above! 🎉`,\n            timestamp: new Date().toISOString(),\n            isSuccess: true\n          };\n          setConversations(prev => [...prev, successMessage]);\n        }\n\n        // Add action results to the conversation if there were any issues\n        const failedActions = actionResults.filter(result => !result.success);\n        if (failedActions.length > 0) {\n          const errorMessage = {\n            id: Date.now() + 3,\n            type: 'assistant',\n            content: `Note: I encountered some issues executing the requested actions:\\n${failedActions.map(result => `• ${result.error}`).join('\\n')}`,\n            timestamp: new Date().toISOString(),\n            isError: true\n          };\n          setConversations(prev => [...prev, errorMessage]);\n        }\n      }\n\n      // Save to localStorage\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n      const planConversations = updatedConversations.map(conv => ({\n        ...conv,\n        planId: planInfo === null || planInfo === void 0 ? void 0 : planInfo.id,\n        planName: planInfo === null || planInfo === void 0 ? void 0 : planInfo.name\n      }));\n      localStorage.setItem('agent_conversations', JSON.stringify([...otherPlanConversations, ...planConversations]));\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error processing message:', error);\n      const errorResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: `Sorry, I encountered an error while processing your request: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || error.message || 'Unknown error'}. Please try again.`,\n        timestamp: new Date().toISOString(),\n        isError: true\n      };\n      setConversations([...newConversations, errorResponse]);\n      setError(error.message || 'Unknown error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const executeAIAction = async action => {\n    try {\n      const response = await axios.post(`${APIURL}/api/assistant/plan-action`, {\n        action: action.action || action.type,\n        // Handle both formats\n        plan_slug: planInfo.slug,\n        data: action.data || {},\n        message: `AI-generated action: ${action.action || action.type}`\n      }, {\n        headers: getHeaders()\n      });\n      return {\n        success: true,\n        data: response.data,\n        action: action\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('Error executing AI action:', error);\n      return {\n        success: false,\n        error: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || error.message || 'Unknown error',\n        action: action\n      };\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const formatTimestamp = timestamp => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '70vh',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '12px 12px 0 0',\n        border: '1px solid #f0f0f0',\n        borderBottom: 'none',\n        backgroundColor: '#fafafa'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: mainYellowColor,\n            width: 40,\n            height: 40\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 24,\n            height: 24,\n            color: \"#fff\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontWeight: 600,\n              color: '#333'\n            },\n            children: \"AI Project Agent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            },\n            children: [\"Managing: \", planInfo === null || planInfo === void 0 ? void 0 : planInfo.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Beta\",\n          size: \"small\",\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            color: mainYellowColor,\n            fontWeight: 600,\n            ml: 'auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        flex: 1,\n        border: '1px solid #f0f0f0',\n        borderTop: 'none',\n        borderBottom: 'none',\n        overflow: 'auto',\n        p: 2,\n        backgroundColor: '#fff'\n      },\n      children: conversations.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            width: 60,\n            height: 60,\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 32,\n            height: 32,\n            color: mainYellowColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            fontWeight: 600,\n            color: '#333',\n            mb: 1\n          },\n          children: \"Welcome to AI Project Agent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: '#666',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            maxWidth: 400\n          },\n          children: \"I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        children: [conversations.map(message => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxWidth: '70%',\n              display: 'flex',\n              flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: message.type === 'user' ? '#666' : '#fff'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Paper, {\n                elevation: 0,\n                sx: {\n                  p: 1.5,\n                  borderRadius: '12px',\n                  backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\n                  color: message.type === 'user' ? '#fff' : '#333',\n                  border: message.isError ? '1px solid #f44336' : 'none'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontFamily: '\"Recursive Variable\", sans-serif',\n                    lineHeight: 1.5,\n                    whiteSpace: 'pre-line',\n                    '& strong': {\n                      fontWeight: 600\n                    }\n                  },\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 23\n                }, this), message.type === 'assistant' && message.actions && message.actions.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mt: 1.5,\n                    display: 'flex',\n                    flexWrap: 'wrap',\n                    gap: 1\n                  },\n                  children: message.actions.slice(0, 3).map((action, actionIndex) => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: action.description,\n                    size: \"small\",\n                    onClick: () => {\n                      // Handle quick action\n                      setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);\n                      if (inputRef.current) {\n                        inputRef.current.focus();\n                      }\n                    },\n                    sx: {\n                      backgroundColor: '#fff',\n                      border: `1px solid ${mainYellowColor}`,\n                      color: mainYellowColor,\n                      fontSize: '0.7rem',\n                      height: '24px',\n                      cursor: 'pointer',\n                      '&:hover': {\n                        backgroundColor: `${mainYellowColor}10`\n                      }\n                    }\n                  }, actionIndex, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: '#999',\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  fontSize: '0.7rem',\n                  mt: 0.5,\n                  display: 'block',\n                  textAlign: message.type === 'user' ? 'right' : 'left'\n                },\n                children: formatTimestamp(message.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 15\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: \"#fff\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 0,\n              sx: {\n                p: 1.5,\n                borderRadius: '12px',\n                backgroundColor: '#f5f5f5',\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 16,\n                sx: {\n                  color: mainYellowColor\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  color: '#666'\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '0 0 12px 12px',\n        border: '1px solid #f0f0f0',\n        borderTop: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          alignItems: 'flex-end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          inputRef: inputRef,\n          value: currentMessage,\n          onChange: e => setCurrentMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask me anything about your project...\",\n          multiline: true,\n          maxRows: 3,\n          fullWidth: true,\n          variant: \"outlined\",\n          disabled: isLoading,\n          sx: {\n            '& .MuiOutlinedInput-root': {\n              borderRadius: '8px',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Send message\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => handleSendMessage(),\n            disabled: !currentMessage.trim() || isLoading,\n            sx: {\n              backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n              color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\n              '&:hover': {\n                backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:send\",\n              width: 20,\n              height: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 246,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentTab, \"rQuF4l2OLxvb/G3jCFGXzIq4svw=\", false, function () {\n  return [useLocation];\n});\n_c = AgentTab;\nexport default AgentTab;\nvar _c;\n$RefreshReg$(_c, \"AgentTab\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Typography", "Paper", "TextField", "IconButton", "Avatar", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "useLocation", "Iconify", "mainYellowColor", "APIURL", "getHeaders", "axios", "jsxDEV", "_jsxDEV", "AgentTab", "_ref", "_s", "planInfo", "onPlanUpdate", "conversations", "setConversations", "currentMessage", "setCurrentMessage", "isLoading", "setIsLoading", "messagesEndRef", "inputRef", "setError", "location", "_location$state", "savedConversations", "JSON", "parse", "localStorage", "getItem", "planConversations", "filter", "conv", "planId", "id", "pendingMessage", "_messageData$planInfo", "messageData", "handleSendMessage", "message", "removeItem", "error", "console", "state", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "messageText", "arguments", "length", "undefined", "trim", "userMessage", "Date", "now", "type", "content", "timestamp", "toISOString", "newConversations", "response", "post", "plan_slug", "slug", "headers", "aiResponseData", "data", "aiResponse", "actions", "metadata", "updatedConversations", "actionResults", "action", "result", "executeAIAction", "push", "success", "successfulActions", "successMessage", "map", "_result$data$result", "_action$data", "_result$data$result2", "_action$data2", "_result$data$result3", "_action$data3", "_result$data$result4", "_action$data4", "_result$data$result5", "_action$data5", "_result$data$result6", "_action$data6", "actionType", "milestone_name", "name", "task_name", "subtask_name", "join", "isSuccess", "prev", "failedActions", "errorMessage", "isError", "allConversations", "otherPlanConversations", "planName", "setItem", "stringify", "_error$response", "_error$response$data", "errorResponse", "_error$response2", "_error$response2$data", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "formatTimestamp", "toLocaleTimeString", "hour", "minute", "sx", "height", "display", "flexDirection", "children", "elevation", "p", "borderRadius", "border", "borderBottom", "backgroundColor", "alignItems", "gap", "width", "icon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontFamily", "fontWeight", "label", "size", "ml", "flex", "borderTop", "overflow", "justifyContent", "textAlign", "mb", "max<PERSON><PERSON><PERSON>", "lineHeight", "whiteSpace", "mt", "flexWrap", "slice", "actionIndex", "description", "onClick", "originalMessage", "toLowerCase", "focus", "fontSize", "cursor", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "multiline", "maxRows", "fullWidth", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/AgentTab.js"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  TextField,\r\n  IconButton,\r\n  Avatar,\r\n  CircularProgress,\r\n  Chip,\r\n  Tooltip\r\n} from '@mui/material';\r\nimport { useLocation } from 'react-router-dom';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor, APIURL } from \"helpers/constants\";\r\nimport { getHeaders } from \"helpers/functions\";\r\nimport axios from 'axios';\r\n\r\nconst AgentTab = ({ planInfo, onPlanUpdate }) => {\r\n  const [conversations, setConversations] = useState([]);\r\n  const [currentMessage, setCurrentMessage] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const messagesEndRef = useRef(null);\r\n  const inputRef = useRef(null);\r\n  const [ , setError] = useState(null);\r\n  const location = useLocation();\r\n\r\n  // Load conversations from localStorage\r\n  useEffect(() => {\r\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n    const planConversations = savedConversations.filter(conv => conv.planId === planInfo?.id);\r\n    setConversations(planConversations);\r\n\r\n    // Check for pending message from ChatbotBar\r\n    const pendingMessage = localStorage.getItem('pending_agent_message');\r\n    if (pendingMessage) {\r\n      try {\r\n        const messageData = JSON.parse(pendingMessage);\r\n        if (messageData.planInfo?.id === planInfo?.id) {\r\n          handleSendMessage(messageData.message);\r\n        }\r\n        // Clear the pending message\r\n        localStorage.removeItem('pending_agent_message');\r\n      } catch (error) {\r\n        console.error('Error processing pending message:', error);\r\n        localStorage.removeItem('pending_agent_message');\r\n      }\r\n    }\r\n\r\n    // If coming from chatbot bar via navigation state, add the initial message\r\n    if (location.state?.message) {\r\n      handleSendMessage(location.state.message);\r\n    }\r\n  }, [planInfo?.id, location.state]);\r\n\r\n  // Auto scroll to bottom\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  }, [conversations]);\r\n\r\n\r\n\r\n  const handleSendMessage = async (messageText = currentMessage) => {\r\n    if (!messageText.trim() || isLoading) return;\r\n\r\n    setIsLoading(true);\r\n    const userMessage = {\r\n      id: Date.now(),\r\n      type: 'user',\r\n      content: messageText.trim(),\r\n      timestamp: new Date().toISOString()\r\n    };\r\n\r\n    const newConversations = [...conversations, userMessage];\r\n    setConversations(newConversations);\r\n    setCurrentMessage('');\r\n\r\n    try {\r\n      // Call the new AI agent chat endpoint with plan context\r\n      const response = await axios.post(\r\n        `${APIURL}/api/assistant/agent-chat`,\r\n        {\r\n          message: messageText.trim(),\r\n          plan_slug: planInfo.slug\r\n        },\r\n        { headers: getHeaders() }\r\n      );\r\n\r\n      const aiResponseData = response.data;\r\n\r\n      // Create AI response message\r\n      const aiResponse = {\r\n        id: Date.now() + 1,\r\n        type: 'assistant',\r\n        content: aiResponseData.message || 'I received your message but had trouble generating a response.',\r\n        timestamp: new Date().toISOString(),\r\n        actions: aiResponseData.actions || [],\r\n        metadata: aiResponseData.metadata || {}\r\n      };\r\n\r\n      const updatedConversations = [...newConversations, aiResponse];\r\n      setConversations(updatedConversations);\r\n\r\n      // Execute any actions returned by the AI\r\n      if (aiResponseData.actions && aiResponseData.actions.length > 0) {\r\n        let actionResults = [];\r\n        for (const action of aiResponseData.actions) {\r\n          try {\r\n            const result = await executeAIAction(action);\r\n            actionResults.push(result);\r\n          } catch (error) {\r\n            console.error('Error executing AI action:', error);\r\n            actionResults.push({\r\n              success: false,\r\n              error: error.message || 'Unknown error'\r\n            });\r\n          }\r\n        }\r\n\r\n        // If any actions were successful, trigger plan refresh and show success message\r\n        const successfulActions = actionResults.filter(result => result.success);\r\n        if (successfulActions.length > 0 && onPlanUpdate) {\r\n          onPlanUpdate();\r\n\r\n          // Add success confirmation message\r\n          const successMessage = {\r\n            id: Date.now() + 2,\r\n            type: 'assistant',\r\n            content: `✅ **All done!** I've successfully made the following changes to your project:\\n\\n${successfulActions.map(result => {\r\n              const action = result.action;\r\n              const actionType = action.action || action.type;\r\n\r\n              switch(actionType) {\r\n                case 'add_milestone':\r\n                  return `• Added milestone: \"${result.data.result?.milestone_name || action.data?.name}\"`;\r\n                case 'add_task':\r\n                  return `• Added task: \"${result.data.result?.task_name || action.data?.name}\"`;\r\n                case 'add_subtask':\r\n                  return `• Added subtask: \"${result.data.result?.subtask_name || action.data?.name}\"`;\r\n                case 'update_milestone':\r\n                  return `• Updated milestone: \"${result.data.result?.milestone_name || action.data?.name}\"`;\r\n                case 'update_task':\r\n                  return `• Updated task: \"${result.data.result?.task_name || action.data?.name}\"`;\r\n                case 'complete_task':\r\n                  return `• Completed task: \"${result.data.result?.task_name || action.data?.name}\"`;\r\n                default:\r\n                  return `• Completed action: ${actionType}`;\r\n              }\r\n            }).join('\\n')}\\n\\n**Check it out** in your project details above! 🎉`,\r\n            timestamp: new Date().toISOString(),\r\n            isSuccess: true\r\n          };\r\n          setConversations(prev => [...prev, successMessage]);\r\n        }\r\n\r\n        // Add action results to the conversation if there were any issues\r\n        const failedActions = actionResults.filter(result => !result.success);\r\n        if (failedActions.length > 0) {\r\n          const errorMessage = {\r\n            id: Date.now() + 3,\r\n            type: 'assistant',\r\n            content: `Note: I encountered some issues executing the requested actions:\\n${failedActions.map(result => `• ${result.error}`).join('\\n')}`,\r\n            timestamp: new Date().toISOString(),\r\n            isError: true\r\n          };\r\n          setConversations(prev => [...prev, errorMessage]);\r\n        }\r\n      }\r\n\r\n      // Save to localStorage\r\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\r\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== planInfo?.id);\r\n      const planConversations = updatedConversations.map(conv => ({\r\n        ...conv,\r\n        planId: planInfo?.id,\r\n        planName: planInfo?.name\r\n      }));\r\n\r\n      localStorage.setItem('agent_conversations', JSON.stringify([\r\n        ...otherPlanConversations,\r\n        ...planConversations\r\n      ]));\r\n\r\n    } catch (error) {\r\n      console.error('Error processing message:', error);\r\n      const errorResponse = {\r\n        id: Date.now() + 1,\r\n        type: 'assistant',\r\n        content: `Sorry, I encountered an error while processing your request: ${error.response?.data?.error || error.message || 'Unknown error'}. Please try again.`,\r\n        timestamp: new Date().toISOString(),\r\n        isError: true\r\n      };\r\n      setConversations([...newConversations, errorResponse]);\r\n      setError(error.message || 'Unknown error occurred');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const executeAIAction = async (action) => {\r\n    try {\r\n      const response = await axios.post(\r\n        `${APIURL}/api/assistant/plan-action`,\r\n        {\r\n          action: action.action || action.type, // Handle both formats\r\n          plan_slug: planInfo.slug,\r\n          data: action.data || {},\r\n          message: `AI-generated action: ${action.action || action.type}`\r\n        },\r\n        { headers: getHeaders() }\r\n      );\r\n\r\n      return {\r\n        success: true,\r\n        data: response.data,\r\n        action: action\r\n      };\r\n    } catch (error) {\r\n      console.error('Error executing AI action:', error);\r\n      return {\r\n        success: false,\r\n        error: error.response?.data?.error || error.message || 'Unknown error',\r\n        action: action\r\n      };\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleKeyPress = (e) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const formatTimestamp = (timestamp) => {\r\n    return new Date(timestamp).toLocaleTimeString('en-US', {\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>\r\n      {/* Header */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          p: 2,\r\n          borderRadius: '12px 12px 0 0',\r\n          border: '1px solid #f0f0f0',\r\n          borderBottom: 'none',\r\n          backgroundColor: '#fafafa'\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n          <Avatar\r\n            sx={{\r\n              backgroundColor: mainYellowColor,\r\n              width: 40,\r\n              height: 40\r\n            }}\r\n          >\r\n            <Iconify icon=\"mdi:robot\" width={24} height={24} color=\"#fff\" />\r\n          </Avatar>\r\n          <Box>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                color: '#333'\r\n              }}\r\n            >\r\n              AI Project Agent\r\n            </Typography>\r\n            <Typography\r\n              variant=\"caption\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }}\r\n            >\r\n              Managing: {planInfo?.name}\r\n            </Typography>\r\n          </Box>\r\n          <Chip\r\n            label=\"Beta\"\r\n            size=\"small\"\r\n            sx={{\r\n              backgroundColor: `${mainYellowColor}20`,\r\n              color: mainYellowColor,\r\n              fontWeight: 600,\r\n              ml: 'auto'\r\n            }}\r\n          />\r\n        </Box>\r\n      </Paper>\r\n\r\n      {/* Messages Area */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          flex: 1,\r\n          border: '1px solid #f0f0f0',\r\n          borderTop: 'none',\r\n          borderBottom: 'none',\r\n          overflow: 'auto',\r\n          p: 2,\r\n          backgroundColor: '#fff'\r\n        }}\r\n      >\r\n        {conversations.length === 0 ? (\r\n          <Box\r\n            sx={{\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              height: '100%',\r\n              textAlign: 'center'\r\n            }}\r\n          >\r\n            <Avatar\r\n              sx={{\r\n                backgroundColor: `${mainYellowColor}20`,\r\n                width: 60,\r\n                height: 60,\r\n                mb: 2\r\n              }}\r\n            >\r\n              <Iconify icon=\"mdi:robot\" width={32} height={32} color={mainYellowColor} />\r\n            </Avatar>\r\n            <Typography\r\n              variant=\"h6\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                color: '#333',\r\n                mb: 1\r\n              }}\r\n            >\r\n              Welcome to AI Project Agent\r\n            </Typography>\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                color: '#666',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                maxWidth: 400\r\n              }}\r\n            >\r\n              I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\r\n            </Typography>\r\n          </Box>\r\n        ) : (\r\n          <Box>\r\n            {conversations.map((message) => (\r\n              <Box\r\n                key={message.id}\r\n                sx={{\r\n                  display: 'flex',\r\n                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\r\n                  mb: 2\r\n                }}\r\n              >\r\n                <Box\r\n                  sx={{\r\n                    maxWidth: '70%',\r\n                    display: 'flex',\r\n                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\r\n                    alignItems: 'flex-start',\r\n                    gap: 1\r\n                  }}\r\n                >\r\n                  <Avatar\r\n                    sx={{\r\n                      width: 32,\r\n                      height: 32,\r\n                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\r\n                    }}\r\n                  >\r\n                    <Iconify\r\n                      icon={message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\"}\r\n                      width={18}\r\n                      height={18}\r\n                      color={message.type === 'user' ? '#666' : '#fff'}\r\n                    />\r\n                  </Avatar>\r\n                  <Box>\r\n                    <Paper\r\n                      elevation={0}\r\n                      sx={{\r\n                        p: 1.5,\r\n                        borderRadius: '12px',\r\n                        backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\r\n                        color: message.type === 'user' ? '#fff' : '#333',\r\n                        border: message.isError ? '1px solid #f44336' : 'none'\r\n                      }}\r\n                    >\r\n                      <Typography\r\n                        variant=\"body2\"\r\n                        sx={{\r\n                          fontFamily: '\"Recursive Variable\", sans-serif',\r\n                          lineHeight: 1.5,\r\n                          whiteSpace: 'pre-line',\r\n                          '& strong': {\r\n                            fontWeight: 600\r\n                          }\r\n                        }}\r\n                      >\r\n                        {message.content}\r\n                      </Typography>\r\n\r\n                      {/* Quick Action Buttons for AI responses */}\r\n                      {message.type === 'assistant' && message.actions && message.actions.length > 0 && (\r\n                        <Box sx={{ mt: 1.5, display: 'flex', flexWrap: 'wrap', gap: 1 }}>\r\n                          {message.actions.slice(0, 3).map((action, actionIndex) => (\r\n                            <Chip\r\n                              key={actionIndex}\r\n                              label={action.description}\r\n                              size=\"small\"\r\n                              onClick={() => {\r\n                                // Handle quick action\r\n                                setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);\r\n                                if (inputRef.current) {\r\n                                  inputRef.current.focus();\r\n                                }\r\n                              }}\r\n                              sx={{\r\n                                backgroundColor: '#fff',\r\n                                border: `1px solid ${mainYellowColor}`,\r\n                                color: mainYellowColor,\r\n                                fontSize: '0.7rem',\r\n                                height: '24px',\r\n                                cursor: 'pointer',\r\n                                '&:hover': {\r\n                                  backgroundColor: `${mainYellowColor}10`\r\n                                }\r\n                              }}\r\n                            />\r\n                          ))}\r\n                        </Box>\r\n                      )}\r\n                    </Paper>\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        color: '#999',\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        fontSize: '0.7rem',\r\n                        mt: 0.5,\r\n                        display: 'block',\r\n                        textAlign: message.type === 'user' ? 'right' : 'left'\r\n                      }}\r\n                    >\r\n                      {formatTimestamp(message.timestamp)}\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n            ))}\r\n            {isLoading && (\r\n              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>\r\n                  <Avatar\r\n                    sx={{\r\n                      width: 32,\r\n                      height: 32,\r\n                      backgroundColor: mainYellowColor\r\n                    }}\r\n                  >\r\n                    <Iconify icon=\"mdi:robot\" width={18} height={18} color=\"#fff\" />\r\n                  </Avatar>\r\n                  <Paper\r\n                    elevation={0}\r\n                    sx={{\r\n                      p: 1.5,\r\n                      borderRadius: '12px',\r\n                      backgroundColor: '#f5f5f5',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: 1\r\n                    }}\r\n                  >\r\n                    <CircularProgress size={16} sx={{ color: mainYellowColor }} />\r\n                    <Typography\r\n                      variant=\"body2\"\r\n                      sx={{\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        color: '#666'\r\n                      }}\r\n                    >\r\n                      Thinking...\r\n                    </Typography>\r\n                  </Paper>\r\n                </Box>\r\n              </Box>\r\n            )}\r\n            <div ref={messagesEndRef} />\r\n          </Box>\r\n        )}\r\n      </Paper>\r\n\r\n      {/* Input Area */}\r\n      <Paper\r\n        elevation={0}\r\n        sx={{\r\n          p: 2,\r\n          borderRadius: '0 0 12px 12px',\r\n          border: '1px solid #f0f0f0',\r\n          borderTop: 'none'\r\n        }}\r\n      >\r\n        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\r\n          <TextField\r\n            inputRef={inputRef}\r\n            value={currentMessage}\r\n            onChange={(e) => setCurrentMessage(e.target.value)}\r\n            onKeyPress={handleKeyPress}\r\n            placeholder=\"Ask me anything about your project...\"\r\n            multiline\r\n            maxRows={3}\r\n            fullWidth\r\n            variant=\"outlined\"\r\n            disabled={isLoading}\r\n            sx={{\r\n              '& .MuiOutlinedInput-root': {\r\n                borderRadius: '8px',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }\r\n            }}\r\n          />\r\n          <Tooltip title=\"Send message\">\r\n            <IconButton\r\n              onClick={() => handleSendMessage()}\r\n              disabled={!currentMessage.trim() || isLoading}\r\n              sx={{\r\n                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\r\n                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\r\n                '&:hover': {\r\n                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\r\n                }\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:send\" width={20} height={20} />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Box>\r\n      </Paper>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default AgentTab;\r\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,EAAEC,MAAM,QAAQ,mBAAmB;AAC3D,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGC,IAAA,IAAgC;EAAAC,EAAA;EAAA,IAA/B;IAAEC,QAAQ;IAAEC;EAAa,CAAC,GAAAH,IAAA;EAC1C,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM+B,cAAc,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM8B,QAAQ,GAAG9B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM,GAAI+B,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACpC,MAAMkC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;;EAE9B;EACAX,SAAS,CAAC,MAAM;IAAA,IAAAkC,eAAA;IACd,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;IAC1F,MAAMC,iBAAiB,GAAGL,kBAAkB,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKrB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAC;IACzFnB,gBAAgB,CAACe,iBAAiB,CAAC;;IAEnC;IACA,MAAMK,cAAc,GAAGP,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;IACpE,IAAIM,cAAc,EAAE;MAClB,IAAI;QAAA,IAAAC,qBAAA;QACF,MAAMC,WAAW,GAAGX,IAAI,CAACC,KAAK,CAACQ,cAAc,CAAC;QAC9C,IAAI,EAAAC,qBAAA,GAAAC,WAAW,CAACzB,QAAQ,cAAAwB,qBAAA,uBAApBA,qBAAA,CAAsBF,EAAE,OAAKtB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,GAAE;UAC7CI,iBAAiB,CAACD,WAAW,CAACE,OAAO,CAAC;QACxC;QACA;QACAX,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC;MAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDb,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC;MAClD;IACF;;IAEA;IACA,KAAAhB,eAAA,GAAID,QAAQ,CAACoB,KAAK,cAAAnB,eAAA,eAAdA,eAAA,CAAgBe,OAAO,EAAE;MAC3BD,iBAAiB,CAACf,QAAQ,CAACoB,KAAK,CAACJ,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAAC3B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAEX,QAAQ,CAACoB,KAAK,CAAC,CAAC;;EAElC;EACArD,SAAS,CAAC,MAAM;IAAA,IAAAsD,qBAAA;IACd,CAAAA,qBAAA,GAAAxB,cAAc,CAACyB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACjC,aAAa,CAAC,CAAC;EAInB,MAAMwB,iBAAiB,GAAG,eAAAA,CAAA,EAAwC;IAAA,IAAjCU,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGjC,cAAc;IAC3D,IAAI,CAACgC,WAAW,CAACI,IAAI,CAAC,CAAC,IAAIlC,SAAS,EAAE;IAEtCC,YAAY,CAAC,IAAI,CAAC;IAClB,MAAMkC,WAAW,GAAG;MAClBnB,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAET,WAAW,CAACI,IAAI,CAAC,CAAC;MAC3BM,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACpC,CAAC;IAED,MAAMC,gBAAgB,GAAG,CAAC,GAAG9C,aAAa,EAAEuC,WAAW,CAAC;IACxDtC,gBAAgB,CAAC6C,gBAAgB,CAAC;IAClC3C,iBAAiB,CAAC,EAAE,CAAC;IAErB,IAAI;MACF;MACA,MAAM4C,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,IAAI,CAC/B,GAAG1D,MAAM,2BAA2B,EACpC;QACEmC,OAAO,EAAES,WAAW,CAACI,IAAI,CAAC,CAAC;QAC3BW,SAAS,EAAEnD,QAAQ,CAACoD;MACtB,CAAC,EACD;QAAEC,OAAO,EAAE5D,UAAU,CAAC;MAAE,CAC1B,CAAC;MAED,MAAM6D,cAAc,GAAGL,QAAQ,CAACM,IAAI;;MAEpC;MACA,MAAMC,UAAU,GAAG;QACjBlC,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAES,cAAc,CAAC3B,OAAO,IAAI,gEAAgE;QACnGmB,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QACnCU,OAAO,EAAEH,cAAc,CAACG,OAAO,IAAI,EAAE;QACrCC,QAAQ,EAAEJ,cAAc,CAACI,QAAQ,IAAI,CAAC;MACxC,CAAC;MAED,MAAMC,oBAAoB,GAAG,CAAC,GAAGX,gBAAgB,EAAEQ,UAAU,CAAC;MAC9DrD,gBAAgB,CAACwD,oBAAoB,CAAC;;MAEtC;MACA,IAAIL,cAAc,CAACG,OAAO,IAAIH,cAAc,CAACG,OAAO,CAACnB,MAAM,GAAG,CAAC,EAAE;QAC/D,IAAIsB,aAAa,GAAG,EAAE;QACtB,KAAK,MAAMC,MAAM,IAAIP,cAAc,CAACG,OAAO,EAAE;UAC3C,IAAI;YACF,MAAMK,MAAM,GAAG,MAAMC,eAAe,CAACF,MAAM,CAAC;YAC5CD,aAAa,CAACI,IAAI,CAACF,MAAM,CAAC;UAC5B,CAAC,CAAC,OAAOjC,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;YAClD+B,aAAa,CAACI,IAAI,CAAC;cACjBC,OAAO,EAAE,KAAK;cACdpC,KAAK,EAAEA,KAAK,CAACF,OAAO,IAAI;YAC1B,CAAC,CAAC;UACJ;QACF;;QAEA;QACA,MAAMuC,iBAAiB,GAAGN,aAAa,CAACzC,MAAM,CAAC2C,MAAM,IAAIA,MAAM,CAACG,OAAO,CAAC;QACxE,IAAIC,iBAAiB,CAAC5B,MAAM,GAAG,CAAC,IAAIrC,YAAY,EAAE;UAChDA,YAAY,CAAC,CAAC;;UAEd;UACA,MAAMkE,cAAc,GAAG;YACrB7C,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;YAClBC,IAAI,EAAE,WAAW;YACjBC,OAAO,EAAE,oFAAoFqB,iBAAiB,CAACE,GAAG,CAACN,MAAM,IAAI;cAAA,IAAAO,mBAAA,EAAAC,YAAA,EAAAC,oBAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,aAAA,EAAAC,oBAAA,EAAAC,aAAA;cAC3H,MAAMnB,MAAM,GAAGC,MAAM,CAACD,MAAM;cAC5B,MAAMoB,UAAU,GAAGpB,MAAM,CAACA,MAAM,IAAIA,MAAM,CAACjB,IAAI;cAE/C,QAAOqC,UAAU;gBACf,KAAK,eAAe;kBAClB,OAAO,uBAAuB,EAAAZ,mBAAA,GAAAP,MAAM,CAACP,IAAI,CAACO,MAAM,cAAAO,mBAAA,uBAAlBA,mBAAA,CAAoBa,cAAc,OAAAZ,YAAA,GAAIT,MAAM,CAACN,IAAI,cAAAe,YAAA,uBAAXA,YAAA,CAAaa,IAAI,IAAG;gBAC1F,KAAK,UAAU;kBACb,OAAO,kBAAkB,EAAAZ,oBAAA,GAAAT,MAAM,CAACP,IAAI,CAACO,MAAM,cAAAS,oBAAA,uBAAlBA,oBAAA,CAAoBa,SAAS,OAAAZ,aAAA,GAAIX,MAAM,CAACN,IAAI,cAAAiB,aAAA,uBAAXA,aAAA,CAAaW,IAAI,IAAG;gBAChF,KAAK,aAAa;kBAChB,OAAO,qBAAqB,EAAAV,oBAAA,GAAAX,MAAM,CAACP,IAAI,CAACO,MAAM,cAAAW,oBAAA,uBAAlBA,oBAAA,CAAoBY,YAAY,OAAAX,aAAA,GAAIb,MAAM,CAACN,IAAI,cAAAmB,aAAA,uBAAXA,aAAA,CAAaS,IAAI,IAAG;gBACtF,KAAK,kBAAkB;kBACrB,OAAO,yBAAyB,EAAAR,oBAAA,GAAAb,MAAM,CAACP,IAAI,CAACO,MAAM,cAAAa,oBAAA,uBAAlBA,oBAAA,CAAoBO,cAAc,OAAAN,aAAA,GAAIf,MAAM,CAACN,IAAI,cAAAqB,aAAA,uBAAXA,aAAA,CAAaO,IAAI,IAAG;gBAC5F,KAAK,aAAa;kBAChB,OAAO,oBAAoB,EAAAN,oBAAA,GAAAf,MAAM,CAACP,IAAI,CAACO,MAAM,cAAAe,oBAAA,uBAAlBA,oBAAA,CAAoBO,SAAS,OAAAN,aAAA,GAAIjB,MAAM,CAACN,IAAI,cAAAuB,aAAA,uBAAXA,aAAA,CAAaK,IAAI,IAAG;gBAClF,KAAK,eAAe;kBAClB,OAAO,sBAAsB,EAAAJ,oBAAA,GAAAjB,MAAM,CAACP,IAAI,CAACO,MAAM,cAAAiB,oBAAA,uBAAlBA,oBAAA,CAAoBK,SAAS,OAAAJ,aAAA,GAAInB,MAAM,CAACN,IAAI,cAAAyB,aAAA,uBAAXA,aAAA,CAAaG,IAAI,IAAG;gBACpF;kBACE,OAAO,uBAAuBF,UAAU,EAAE;cAC9C;YACF,CAAC,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,wDAAwD;YACrExC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;YACnCwC,SAAS,EAAE;UACb,CAAC;UACDpF,gBAAgB,CAACqF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAErB,cAAc,CAAC,CAAC;QACrD;;QAEA;QACA,MAAMsB,aAAa,GAAG7B,aAAa,CAACzC,MAAM,CAAC2C,MAAM,IAAI,CAACA,MAAM,CAACG,OAAO,CAAC;QACrE,IAAIwB,aAAa,CAACnD,MAAM,GAAG,CAAC,EAAE;UAC5B,MAAMoD,YAAY,GAAG;YACnBpE,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;YAClBC,IAAI,EAAE,WAAW;YACjBC,OAAO,EAAE,qEAAqE4C,aAAa,CAACrB,GAAG,CAACN,MAAM,IAAI,KAAKA,MAAM,CAACjC,KAAK,EAAE,CAAC,CAACyD,IAAI,CAAC,IAAI,CAAC,EAAE;YAC3IxC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;YACnC4C,OAAO,EAAE;UACX,CAAC;UACDxF,gBAAgB,CAACqF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEE,YAAY,CAAC,CAAC;QACnD;MACF;;MAEA;MACA,MAAME,gBAAgB,GAAG9E,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;MACxF,MAAM4E,sBAAsB,GAAGD,gBAAgB,CAACzE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKrB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAC;MAC5F,MAAMJ,iBAAiB,GAAGyC,oBAAoB,CAACS,GAAG,CAAChD,IAAI,KAAK;QAC1D,GAAGA,IAAI;QACPC,MAAM,EAAErB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE;QACpBwE,QAAQ,EAAE9F,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmF;MACtB,CAAC,CAAC,CAAC;MAEHnE,YAAY,CAAC+E,OAAO,CAAC,qBAAqB,EAAEjF,IAAI,CAACkF,SAAS,CAAC,CACzD,GAAGH,sBAAsB,EACzB,GAAG3E,iBAAiB,CACrB,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOW,KAAK,EAAE;MAAA,IAAAoE,eAAA,EAAAC,oBAAA;MACdpE,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMsE,aAAa,GAAG;QACpB7E,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,gEAAgE,EAAAoD,eAAA,GAAApE,KAAK,CAACoB,QAAQ,cAAAgD,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB1C,IAAI,cAAA2C,oBAAA,uBAApBA,oBAAA,CAAsBrE,KAAK,KAAIA,KAAK,CAACF,OAAO,IAAI,eAAe,qBAAqB;QAC7JmB,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QACnC4C,OAAO,EAAE;MACX,CAAC;MACDxF,gBAAgB,CAAC,CAAC,GAAG6C,gBAAgB,EAAEmD,aAAa,CAAC,CAAC;MACtDzF,QAAQ,CAACmB,KAAK,CAACF,OAAO,IAAI,wBAAwB,CAAC;IACrD,CAAC,SAAS;MACRpB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMwD,eAAe,GAAG,MAAOF,MAAM,IAAK;IACxC,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMvD,KAAK,CAACwD,IAAI,CAC/B,GAAG1D,MAAM,4BAA4B,EACrC;QACEqE,MAAM,EAAEA,MAAM,CAACA,MAAM,IAAIA,MAAM,CAACjB,IAAI;QAAE;QACtCO,SAAS,EAAEnD,QAAQ,CAACoD,IAAI;QACxBG,IAAI,EAAEM,MAAM,CAACN,IAAI,IAAI,CAAC,CAAC;QACvB5B,OAAO,EAAE,wBAAwBkC,MAAM,CAACA,MAAM,IAAIA,MAAM,CAACjB,IAAI;MAC/D,CAAC,EACD;QAAES,OAAO,EAAE5D,UAAU,CAAC;MAAE,CAC1B,CAAC;MAED,OAAO;QACLwE,OAAO,EAAE,IAAI;QACbV,IAAI,EAAEN,QAAQ,CAACM,IAAI;QACnBM,MAAM,EAAEA;MACV,CAAC;IACH,CAAC,CAAC,OAAOhC,KAAK,EAAE;MAAA,IAAAuE,gBAAA,EAAAC,qBAAA;MACdvE,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO;QACLoC,OAAO,EAAE,KAAK;QACdpC,KAAK,EAAE,EAAAuE,gBAAA,GAAAvE,KAAK,CAACoB,QAAQ,cAAAmD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7C,IAAI,cAAA8C,qBAAA,uBAApBA,qBAAA,CAAsBxE,KAAK,KAAIA,KAAK,CAACF,OAAO,IAAI,eAAe;QACtEkC,MAAM,EAAEA;MACV,CAAC;IACH;EACF,CAAC;EAID,MAAMyC,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBhF,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMiF,eAAe,GAAI7D,SAAS,IAAK;IACrC,OAAO,IAAIJ,IAAI,CAACI,SAAS,CAAC,CAAC8D,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACElH,OAAA,CAAChB,GAAG;IAACmI,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpEvH,OAAA,CAACd,KAAK;MACJsI,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,MAAM;QACpBC,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,eAEFvH,OAAA,CAAChB,GAAG;QAACmI,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAES,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACzDvH,OAAA,CAACX,MAAM;UACL8H,EAAE,EAAE;YACFU,eAAe,EAAElI,eAAe;YAChCqI,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE;UACV,CAAE;UAAAG,QAAA,eAEFvH,OAAA,CAACN,OAAO;YAACuI,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACTtI,OAAA,CAAChB,GAAG;UAAAuI,QAAA,gBACFvH,OAAA,CAACf,UAAU;YACTsJ,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFqB,UAAU,EAAE,kCAAkC;cAC9CC,UAAU,EAAE,GAAG;cACfP,KAAK,EAAE;YACT,CAAE;YAAAX,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtI,OAAA,CAACf,UAAU;YACTsJ,OAAO,EAAC,SAAS;YACjBpB,EAAE,EAAE;cACFe,KAAK,EAAE,MAAM;cACbM,UAAU,EAAE;YACd,CAAE;YAAAjB,QAAA,GACH,YACW,EAACnH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmF,IAAI;UAAA;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNtI,OAAA,CAACT,IAAI;UACHmJ,KAAK,EAAC,MAAM;UACZC,IAAI,EAAC,OAAO;UACZxB,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGlI,eAAe,IAAI;YACvCuI,KAAK,EAAEvI,eAAe;YACtB8I,UAAU,EAAE,GAAG;YACfG,EAAE,EAAE;UACN;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRtI,OAAA,CAACd,KAAK;MACJsI,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACF0B,IAAI,EAAE,CAAC;QACPlB,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE,MAAM;QACjBlB,YAAY,EAAE,MAAM;QACpBmB,QAAQ,EAAE,MAAM;QAChBtB,CAAC,EAAE,CAAC;QACJI,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,EAEDjH,aAAa,CAACoC,MAAM,KAAK,CAAC,gBACzB1C,OAAA,CAAChB,GAAG;QACFmI,EAAE,EAAE;UACFE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBQ,UAAU,EAAE,QAAQ;UACpBkB,cAAc,EAAE,QAAQ;UACxB5B,MAAM,EAAE,MAAM;UACd6B,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,gBAEFvH,OAAA,CAACX,MAAM;UACL8H,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGlI,eAAe,IAAI;YACvCqI,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE,EAAE;YACV8B,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEFvH,OAAA,CAACN,OAAO;YAACuI,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAEvI;UAAgB;YAAAwI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACTtI,OAAA,CAACf,UAAU;UACTsJ,OAAO,EAAC,IAAI;UACZpB,EAAE,EAAE;YACFqB,UAAU,EAAE,kCAAkC;YAC9CC,UAAU,EAAE,GAAG;YACfP,KAAK,EAAE,MAAM;YACbgB,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtI,OAAA,CAACf,UAAU;UACTsJ,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YACFe,KAAK,EAAE,MAAM;YACbM,UAAU,EAAE,kCAAkC;YAC9CW,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAENtI,OAAA,CAAChB,GAAG;QAAAuI,QAAA,GACDjH,aAAa,CAACkE,GAAG,CAAEzC,OAAO,iBACzB/B,OAAA,CAAChB,GAAG;UAEFmI,EAAE,EAAE;YACFE,OAAO,EAAE,MAAM;YACf2B,cAAc,EAAEjH,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,YAAY;YACnEkG,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEFvH,OAAA,CAAChB,GAAG;YACFmI,EAAE,EAAE;cACFgC,QAAQ,EAAE,KAAK;cACf9B,OAAO,EAAE,MAAM;cACfC,aAAa,EAAEvF,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,KAAK;cAC9D8E,UAAU,EAAE,YAAY;cACxBC,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBAEFvH,OAAA,CAACX,MAAM;cACL8H,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAE9F,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,SAAS,GAAGrD;cACzD,CAAE;cAAA4H,QAAA,eAEFvH,OAAA,CAACN,OAAO;gBACNuI,IAAI,EAAElG,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,yBAAyB,GAAG,WAAY;gBACxEgF,KAAK,EAAE,EAAG;gBACVZ,MAAM,EAAE,EAAG;gBACXc,KAAK,EAAEnG,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG;cAAO;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTtI,OAAA,CAAChB,GAAG;cAAAuI,QAAA,gBACFvH,OAAA,CAACd,KAAK;gBACJsI,SAAS,EAAE,CAAE;gBACbL,EAAE,EAAE;kBACFM,CAAC,EAAE,GAAG;kBACNC,YAAY,EAAE,MAAM;kBACpBG,eAAe,EAAE9F,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAGrD,eAAe,GAAG,SAAS;kBACtEuI,KAAK,EAAEnG,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;kBAChD2E,MAAM,EAAE5F,OAAO,CAACgE,OAAO,GAAG,mBAAmB,GAAG;gBAClD,CAAE;gBAAAwB,QAAA,gBAEFvH,OAAA,CAACf,UAAU;kBACTsJ,OAAO,EAAC,OAAO;kBACfpB,EAAE,EAAE;oBACFqB,UAAU,EAAE,kCAAkC;oBAC9CY,UAAU,EAAE,GAAG;oBACfC,UAAU,EAAE,UAAU;oBACtB,UAAU,EAAE;sBACVZ,UAAU,EAAE;oBACd;kBACF,CAAE;kBAAAlB,QAAA,EAEDxF,OAAO,CAACkB;gBAAO;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAGZvG,OAAO,CAACiB,IAAI,KAAK,WAAW,IAAIjB,OAAO,CAAC8B,OAAO,IAAI9B,OAAO,CAAC8B,OAAO,CAACnB,MAAM,GAAG,CAAC,iBAC5E1C,OAAA,CAAChB,GAAG;kBAACmI,EAAE,EAAE;oBAAEmC,EAAE,EAAE,GAAG;oBAAEjC,OAAO,EAAE,MAAM;oBAAEkC,QAAQ,EAAE,MAAM;oBAAExB,GAAG,EAAE;kBAAE,CAAE;kBAAAR,QAAA,EAC7DxF,OAAO,CAAC8B,OAAO,CAAC2F,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChF,GAAG,CAAC,CAACP,MAAM,EAAEwF,WAAW,kBACnDzJ,OAAA,CAACT,IAAI;oBAEHmJ,KAAK,EAAEzE,MAAM,CAACyF,WAAY;oBAC1Bf,IAAI,EAAC,OAAO;oBACZgB,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACAlJ,iBAAiB,CAACwD,MAAM,CAAC2F,eAAe,IAAI,UAAU3F,MAAM,CAACyF,WAAW,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC;sBACzF,IAAIhJ,QAAQ,CAACwB,OAAO,EAAE;wBACpBxB,QAAQ,CAACwB,OAAO,CAACyH,KAAK,CAAC,CAAC;sBAC1B;oBACF,CAAE;oBACF3C,EAAE,EAAE;sBACFU,eAAe,EAAE,MAAM;sBACvBF,MAAM,EAAE,aAAahI,eAAe,EAAE;sBACtCuI,KAAK,EAAEvI,eAAe;sBACtBoK,QAAQ,EAAE,QAAQ;sBAClB3C,MAAM,EAAE,MAAM;sBACd4C,MAAM,EAAE,SAAS;sBACjB,SAAS,EAAE;wBACTnC,eAAe,EAAE,GAAGlI,eAAe;sBACrC;oBACF;kBAAE,GApBG8J,WAAW;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqBjB,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACRtI,OAAA,CAACf,UAAU;gBACTsJ,OAAO,EAAC,SAAS;gBACjBpB,EAAE,EAAE;kBACFe,KAAK,EAAE,MAAM;kBACbM,UAAU,EAAE,kCAAkC;kBAC9CuB,QAAQ,EAAE,QAAQ;kBAClBT,EAAE,EAAE,GAAG;kBACPjC,OAAO,EAAE,OAAO;kBAChB4B,SAAS,EAAElH,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG;gBACjD,CAAE;gBAAAuE,QAAA,EAEDR,eAAe,CAAChF,OAAO,CAACmB,SAAS;cAAC;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GApGDvG,OAAO,CAACL,EAAE;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqGZ,CACN,CAAC,EACD5H,SAAS,iBACRV,OAAA,CAAChB,GAAG;UAACmI,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,YAAY;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eAChEvH,OAAA,CAAChB,GAAG;YAACmI,EAAE,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAES,UAAU,EAAE,YAAY;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAR,QAAA,gBAC7DvH,OAAA,CAACX,MAAM;cACL8H,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAElI;cACnB,CAAE;cAAA4H,QAAA,eAEFvH,OAAA,CAACN,OAAO;gBAACuI,IAAI,EAAC,WAAW;gBAACD,KAAK,EAAE,EAAG;gBAACZ,MAAM,EAAE,EAAG;gBAACc,KAAK,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACTtI,OAAA,CAACd,KAAK;cACJsI,SAAS,EAAE,CAAE;cACbL,EAAE,EAAE;gBACFM,CAAC,EAAE,GAAG;gBACNC,YAAY,EAAE,MAAM;gBACpBG,eAAe,EAAE,SAAS;gBAC1BR,OAAO,EAAE,MAAM;gBACfS,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,gBAEFvH,OAAA,CAACV,gBAAgB;gBAACqJ,IAAI,EAAE,EAAG;gBAACxB,EAAE,EAAE;kBAAEe,KAAK,EAAEvI;gBAAgB;cAAE;gBAAAwI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DtI,OAAA,CAACf,UAAU;gBACTsJ,OAAO,EAAC,OAAO;gBACfpB,EAAE,EAAE;kBACFqB,UAAU,EAAE,kCAAkC;kBAC9CN,KAAK,EAAE;gBACT,CAAE;gBAAAX,QAAA,EACH;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eACDtI,OAAA;UAAKiK,GAAG,EAAErJ;QAAe;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGRtI,OAAA,CAACd,KAAK;MACJsI,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE;MACb,CAAE;MAAAvB,QAAA,eAEFvH,OAAA,CAAChB,GAAG;QAACmI,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEU,GAAG,EAAE,CAAC;UAAED,UAAU,EAAE;QAAW,CAAE;QAAAP,QAAA,gBAC3DvH,OAAA,CAACb,SAAS;UACR0B,QAAQ,EAAEA,QAAS;UACnBqJ,KAAK,EAAE1J,cAAe;UACtB2J,QAAQ,EAAGxD,CAAC,IAAKlG,iBAAiB,CAACkG,CAAC,CAACyD,MAAM,CAACF,KAAK,CAAE;UACnDG,UAAU,EAAE3D,cAAe;UAC3B4D,WAAW,EAAC,uCAAuC;UACnDC,SAAS;UACTC,OAAO,EAAE,CAAE;UACXC,SAAS;UACTlC,OAAO,EAAC,UAAU;UAClBmC,QAAQ,EAAEhK,SAAU;UACpByG,EAAE,EAAE;YACF,0BAA0B,EAAE;cAC1BO,YAAY,EAAE,KAAK;cACnBc,UAAU,EAAE;YACd;UACF;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFtI,OAAA,CAACR,OAAO;UAACmL,KAAK,EAAC,cAAc;UAAApD,QAAA,eAC3BvH,OAAA,CAACZ,UAAU;YACTuK,OAAO,EAAEA,CAAA,KAAM7H,iBAAiB,CAAC,CAAE;YACnC4I,QAAQ,EAAE,CAAClK,cAAc,CAACoC,IAAI,CAAC,CAAC,IAAIlC,SAAU;YAC9CyG,EAAE,EAAE;cACFU,eAAe,EAAErH,cAAc,CAACoC,IAAI,CAAC,CAAC,IAAI,CAAClC,SAAS,GAAGf,eAAe,GAAG,SAAS;cAClFuI,KAAK,EAAE1H,cAAc,CAACoC,IAAI,CAAC,CAAC,IAAI,CAAClC,SAAS,GAAG,MAAM,GAAG,MAAM;cAC5D,SAAS,EAAE;gBACTmH,eAAe,EAAErH,cAAc,CAACoC,IAAI,CAAC,CAAC,IAAI,CAAClC,SAAS,GAAG,SAAS,GAAG;cACrE;YACF,CAAE;YAAA6G,QAAA,eAEFvH,OAAA,CAACN,OAAO;cAACuI,IAAI,EAAC,uBAAuB;cAACD,KAAK,EAAE,EAAG;cAACZ,MAAM,EAAE;YAAG;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACnI,EAAA,CAvhBIF,QAAQ;EAAA,QAOKR,WAAW;AAAA;AAAAmL,EAAA,GAPxB3K,QAAQ;AAyhBd,eAAeA,QAAQ;AAAC,IAAA2K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
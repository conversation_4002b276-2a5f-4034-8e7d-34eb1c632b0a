{"ast": null, "code": "import Home from\"views/home/<USER>\";import Connect<PERSON>riend from\"views/users/connect_friend/index\";import CalendarComponent from\"views/calendar/index.js\";import CreatePlan from\"views/plan/create.js\";import PlanDetail from\"views/plan/detail\";import Login from\"views/auth/Login.js\";import Register from\"views/auth/Register.js\";import ForgotPassword from\"views/auth/ForgotPassword.js\";import Activate from\"views/auth/Activate.js\";import Reset from\"views/auth/Reset.js\";import GoogleAuthHandle from\"views/auth/GoogleAuthHandle\";import Profile from\"views/users/profile/index.js\";import OtherProfile from\"views/users/other_profile/index\";import TodoComponent from\"views/todo/index.js\";import Notifications from\"views/notifications/index.js\";import AcceptInvitation from\"views/plan/invitation\";import RegisterByInvitation from\"views/auth/RegisterByInvitation\";import PrivacyPolicies from\"views/public/policies\";import{AUTH_PAGE_KEY,ADMIN_PAGE_KEY,PUBLIC_PAGE_KEY}from\"helpers/constants\";import{jsx as _jsx}from\"react/jsx-runtime\";var routes=[{path:\"/\",layout:ADMIN_PAGE_KEY,name:\"HOME\",component:/*#__PURE__*/_jsx(Home,{}),private:true},{path:\"/plan/create\",layout:ADMIN_PAGE_KEY,name:\"CREATE_A_PLAN\",component:/*#__PURE__*/_jsx(CreatePlan,{}),private:true},{path:\"/plan/:param\",layout:ADMIN_PAGE_KEY,name:\"PLAN_INFOMATION\",component:/*#__PURE__*/_jsx(PlanDetail,{}),private:true},{path:\"/connect\",layout:ADMIN_PAGE_KEY,name:\"CONTACT_DIRECTORY\",component:/*#__PURE__*/_jsx(ConnectFriend,{}),private:true},{path:\"/profile\",layout:ADMIN_PAGE_KEY,name:\"CURRENT_USER_PROFILE\",component:/*#__PURE__*/_jsx(Profile,{}),private:true},{path:\"/other/profile/:param\",layout:ADMIN_PAGE_KEY,name:\"OTHER_USER_PROFILE\",component:/*#__PURE__*/_jsx(OtherProfile,{}),private:true},{path:\"/my/tasks/calendar\",layout:ADMIN_PAGE_KEY,name:\"MY_TASKS_CALENDAR\",component:/*#__PURE__*/_jsx(CalendarComponent,{}),private:true},{path:\"/my/tasks/table\",layout:ADMIN_PAGE_KEY,name:\"MY_TASKS_TABLE\",component:/*#__PURE__*/_jsx(TodoComponent,{}),private:true},{path:\"/notifications\",layout:ADMIN_PAGE_KEY,name:\"MY_NOTIFICATIONS\",component:/*#__PURE__*/_jsx(Notifications,{}),private:true},// No Auth page\n{path:\"/login\",layout:AUTH_PAGE_KEY,name:\"LOGIN\",component:/*#__PURE__*/_jsx(Login,{}),private:false},{path:\"/register\",layout:AUTH_PAGE_KEY,name:\"REGISTER\",component:/*#__PURE__*/_jsx(Register,{}),private:false},{path:\"/register-by-invitation/:signedId/:email\",layout:AUTH_PAGE_KEY,name:\"REGISTER_BY_INVITATION\",component:/*#__PURE__*/_jsx(RegisterByInvitation,{}),private:false},{path:\"/activate/:param1/:param2\",layout:AUTH_PAGE_KEY,name:\"ACTIVATE\",component:/*#__PURE__*/_jsx(Activate,{}),private:false},{path:\"/reset/:uid/:token\",layout:AUTH_PAGE_KEY,name:\"RESET_PASSWORD\",component:/*#__PURE__*/_jsx(Reset,{}),private:false},{path:\"/forgot-password\",layout:AUTH_PAGE_KEY,name:\"FORGOT_PASSWORD\",component:/*#__PURE__*/_jsx(ForgotPassword,{}),private:false},{path:\"/google\",layout:AUTH_PAGE_KEY,name:\"GOOGLE_AUTH_HANDLE\",component:/*#__PURE__*/_jsx(GoogleAuthHandle,{}),private:false},// Both types\n{path:\"/accept-invitation/:param\",layout:PUBLIC_PAGE_KEY,name:\"ACCEPT_INVITATION\",component:/*#__PURE__*/_jsx(AcceptInvitation,{}),private:true},{path:\"/policies\",layout:PUBLIC_PAGE_KEY,name:\"POLICIES\",component:/*#__PURE__*/_jsx(PrivacyPolicies,{}),private:false}];export default routes;", "map": {"version": 3, "names": ["Home", "ConnectFriend", "CalendarComponent", "CreatePlan", "PlanDetail", "<PERSON><PERSON>", "Register", "ForgotPassword", "Activate", "Reset", "GoogleAuthHandle", "Profile", "OtherProfile", "TodoComponent", "Notifications", "AcceptInvitation", "RegisterByInvitation", "PrivacyPolicies", "AUTH_PAGE_KEY", "ADMIN_PAGE_KEY", "PUBLIC_PAGE_KEY", "jsx", "_jsx", "routes", "path", "layout", "name", "component", "private"], "sources": ["C:/ignition/ignition-ui/src/routes/index.js"], "sourcesContent": ["import Home from \"views/home/<USER>\";\r\nimport Connect<PERSON>riend from \"views/users/connect_friend/index\";\r\nimport CalendarComponent from \"views/calendar/index.js\";\r\nimport CreatePlan from \"views/plan/create.js\";\r\nimport PlanDetail from \"views/plan/detail\";\r\nimport Login from \"views/auth/Login.js\";\r\nimport Register from \"views/auth/Register.js\";\r\nimport ForgotPassword from \"views/auth/ForgotPassword.js\";\r\nimport Activate from \"views/auth/Activate.js\";\r\nimport Reset from \"views/auth/Reset.js\";\r\nimport GoogleAuthHandle from \"views/auth/GoogleAuthHandle\";\r\nimport Profile from \"views/users/profile/index.js\";\r\nimport OtherProfile from \"views/users/other_profile/index\";\r\nimport TodoComponent from \"views/todo/index.js\";\r\nimport Notifications from \"views/notifications/index.js\";\r\nimport AcceptInvitation from \"views/plan/invitation\";\r\nimport RegisterByInvitation from \"views/auth/RegisterByInvitation\";\r\nimport PrivacyPolicies from \"views/public/policies\"\r\n\r\nimport { AUTH_PAGE_KEY, ADMIN_PAGE_KEY, PUBLIC_PAGE_KEY } from \"helpers/constants\";\r\n\r\nvar routes = [\r\n  {\r\n    path: \"/\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"HOME\",\r\n    component: <Home />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/plan/create\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"CREATE_A_PLAN\",\r\n    component: <CreatePlan />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/plan/:param\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"PLAN_INFOMATION\",\r\n    component: <PlanDetail />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/connect\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"CONTACT_DIRECTORY\",\r\n    component: <ConnectFriend />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/profile\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"CURRENT_USER_PROFILE\",\r\n    component: <Profile />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/other/profile/:param\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"OTHER_USER_PROFILE\",\r\n    component: <OtherProfile />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/my/tasks/calendar\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"MY_TASKS_CALENDAR\",\r\n    component: <CalendarComponent />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/my/tasks/table\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"MY_TASKS_TABLE\",\r\n    component: <TodoComponent />,\r\n    private: true,\r\n  },\r\n  {\r\n    path: \"/notifications\",\r\n    layout: ADMIN_PAGE_KEY,\r\n    name: \"MY_NOTIFICATIONS\",\r\n    component: <Notifications />,\r\n    private: true,\r\n  },\r\n  // No Auth page\r\n  {\r\n    path: \"/login\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"LOGIN\",\r\n    component: <Login />,\r\n    private: false,\r\n  },\r\n  {\r\n    path: \"/register\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"REGISTER\",\r\n    component: <Register />,\r\n    private: false,\r\n  },\r\n  {\r\n    path: \"/register-by-invitation/:signedId/:email\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"REGISTER_BY_INVITATION\",\r\n    component: <RegisterByInvitation />,\r\n    private: false,\r\n  },\r\n  {\r\n    path: \"/activate/:param1/:param2\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"ACTIVATE\",\r\n    component: <Activate />,\r\n    private: false,\r\n  },\r\n  {\r\n    path: \"/reset/:uid/:token\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"RESET_PASSWORD\",\r\n    component: <Reset />,\r\n    private: false,\r\n  },\r\n  {\r\n    path: \"/forgot-password\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"FORGOT_PASSWORD\",\r\n    component: <ForgotPassword />,\r\n    private: false,\r\n  },\r\n  {\r\n    path: \"/google\",\r\n    layout: AUTH_PAGE_KEY,\r\n    name: \"GOOGLE_AUTH_HANDLE\",\r\n    component: <GoogleAuthHandle />,\r\n    private: false,\r\n  },\r\n  // Both types\r\n  {\r\n    path: \"/accept-invitation/:param\",\r\n    layout: PUBLIC_PAGE_KEY,\r\n    name: \"ACCEPT_INVITATION\",\r\n    component: <AcceptInvitation />,\r\n    private: true,\r\n  },\r\n  {\r\n    path:\"/policies\",\r\n    layout: PUBLIC_PAGE_KEY,\r\n    name: \"POLICIES\",\r\n    component: <PrivacyPolicies />,\r\n    private: false,\r\n  }\r\n];\r\n\r\nexport default routes;\r\n\r\n\r\n"], "mappings": "AAAA,MAAO,CAAAA,IAAI,KAAM,qBAAqB,CACtC,MAAO,CAAAC,aAAa,KAAM,kCAAkC,CAC5D,MAAO,CAAAC,iBAAiB,KAAM,yBAAyB,CACvD,MAAO,CAAAC,UAAU,KAAM,sBAAsB,CAC7C,MAAO,CAAAC,UAAU,KAAM,mBAAmB,CAC1C,MAAO,CAAAC,KAAK,KAAM,qBAAqB,CACvC,MAAO,CAAAC,QAAQ,KAAM,wBAAwB,CAC7C,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CACzD,MAAO,CAAAC,QAAQ,KAAM,wBAAwB,CAC7C,MAAO,CAAAC,KAAK,KAAM,qBAAqB,CACvC,MAAO,CAAAC,gBAAgB,KAAM,6BAA6B,CAC1D,MAAO,CAAAC,OAAO,KAAM,8BAA8B,CAClD,MAAO,CAAAC,YAAY,KAAM,iCAAiC,CAC1D,MAAO,CAAAC,aAAa,KAAM,qBAAqB,CAC/C,MAAO,CAAAC,aAAa,KAAM,8BAA8B,CACxD,MAAO,CAAAC,gBAAgB,KAAM,uBAAuB,CACpD,MAAO,CAAAC,oBAAoB,KAAM,iCAAiC,CAClE,MAAO,CAAAC,eAAe,KAAM,uBAAuB,CAEnD,OAASC,aAAa,CAAEC,cAAc,CAAEC,eAAe,KAAQ,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEnF,GAAI,CAAAC,MAAM,CAAG,CACX,CACEC,IAAI,CAAE,GAAG,CACTC,MAAM,CAAEN,cAAc,CACtBO,IAAI,CAAE,MAAM,CACZC,SAAS,cAAEL,IAAA,CAACtB,IAAI,GAAE,CAAC,CACnB4B,OAAO,CAAE,IACX,CAAC,CACD,CACEJ,IAAI,CAAE,cAAc,CACpBC,MAAM,CAAEN,cAAc,CACtBO,IAAI,CAAE,eAAe,CACrBC,SAAS,cAAEL,IAAA,CAACnB,UAAU,GAAE,CAAC,CACzByB,OAAO,CAAE,IACX,CAAC,CACD,CACEJ,IAAI,CAAE,cAAc,CACpBC,MAAM,CAAEN,cAAc,CACtBO,IAAI,CAAE,iBAAiB,CACvBC,SAAS,cAAEL,IAAA,CAAClB,UAAU,GAAE,CAAC,CACzBwB,OAAO,CAAE,IACX,CAAC,CACD,CACEJ,IAAI,CAAE,UAAU,CAChBC,MAAM,CAAEN,cAAc,CACtBO,IAAI,CAAE,mBAAmB,CACzBC,SAAS,cAAEL,IAAA,CAACrB,aAAa,GAAE,CAAC,CAC5B2B,OAAO,CAAE,IACX,CAAC,CACD,CACEJ,IAAI,CAAE,UAAU,CAChBC,MAAM,CAAEN,cAAc,CACtBO,IAAI,CAAE,sBAAsB,CAC5BC,SAAS,cAAEL,IAAA,CAACX,OAAO,GAAE,CAAC,CACtBiB,OAAO,CAAE,IACX,CAAC,CACD,CACEJ,IAAI,CAAE,uBAAuB,CAC7BC,MAAM,CAAEN,cAAc,CACtBO,IAAI,CAAE,oBAAoB,CAC1BC,SAAS,cAAEL,IAAA,CAACV,YAAY,GAAE,CAAC,CAC3BgB,OAAO,CAAE,IACX,CAAC,CACD,CACEJ,IAAI,CAAE,oBAAoB,CAC1BC,MAAM,CAAEN,cAAc,CACtBO,IAAI,CAAE,mBAAmB,CACzBC,SAAS,cAAEL,IAAA,CAACpB,iBAAiB,GAAE,CAAC,CAChC0B,OAAO,CAAE,IACX,CAAC,CACD,CACEJ,IAAI,CAAE,iBAAiB,CACvBC,MAAM,CAAEN,cAAc,CACtBO,IAAI,CAAE,gBAAgB,CACtBC,SAAS,cAAEL,IAAA,CAACT,aAAa,GAAE,CAAC,CAC5Be,OAAO,CAAE,IACX,CAAC,CACD,CACEJ,IAAI,CAAE,gBAAgB,CACtBC,MAAM,CAAEN,cAAc,CACtBO,IAAI,CAAE,kBAAkB,CACxBC,SAAS,cAAEL,IAAA,CAACR,aAAa,GAAE,CAAC,CAC5Bc,OAAO,CAAE,IACX,CAAC,CACD;AACA,CACEJ,IAAI,CAAE,QAAQ,CACdC,MAAM,CAAEP,aAAa,CACrBQ,IAAI,CAAE,OAAO,CACbC,SAAS,cAAEL,IAAA,CAACjB,KAAK,GAAE,CAAC,CACpBuB,OAAO,CAAE,KACX,CAAC,CACD,CACEJ,IAAI,CAAE,WAAW,CACjBC,MAAM,CAAEP,aAAa,CACrBQ,IAAI,CAAE,UAAU,CAChBC,SAAS,cAAEL,IAAA,CAAChB,QAAQ,GAAE,CAAC,CACvBsB,OAAO,CAAE,KACX,CAAC,CACD,CACEJ,IAAI,CAAE,0CAA0C,CAChDC,MAAM,CAAEP,aAAa,CACrBQ,IAAI,CAAE,wBAAwB,CAC9BC,SAAS,cAAEL,IAAA,CAACN,oBAAoB,GAAE,CAAC,CACnCY,OAAO,CAAE,KACX,CAAC,CACD,CACEJ,IAAI,CAAE,2BAA2B,CACjCC,MAAM,CAAEP,aAAa,CACrBQ,IAAI,CAAE,UAAU,CAChBC,SAAS,cAAEL,IAAA,CAACd,QAAQ,GAAE,CAAC,CACvBoB,OAAO,CAAE,KACX,CAAC,CACD,CACEJ,IAAI,CAAE,oBAAoB,CAC1BC,MAAM,CAAEP,aAAa,CACrBQ,IAAI,CAAE,gBAAgB,CACtBC,SAAS,cAAEL,IAAA,CAACb,KAAK,GAAE,CAAC,CACpBmB,OAAO,CAAE,KACX,CAAC,CACD,CACEJ,IAAI,CAAE,kBAAkB,CACxBC,MAAM,CAAEP,aAAa,CACrBQ,IAAI,CAAE,iBAAiB,CACvBC,SAAS,cAAEL,IAAA,CAACf,cAAc,GAAE,CAAC,CAC7BqB,OAAO,CAAE,KACX,CAAC,CACD,CACEJ,IAAI,CAAE,SAAS,CACfC,MAAM,CAAEP,aAAa,CACrBQ,IAAI,CAAE,oBAAoB,CAC1BC,SAAS,cAAEL,IAAA,CAACZ,gBAAgB,GAAE,CAAC,CAC/BkB,OAAO,CAAE,KACX,CAAC,CACD;AACA,CACEJ,IAAI,CAAE,2BAA2B,CACjCC,MAAM,CAAEL,eAAe,CACvBM,IAAI,CAAE,mBAAmB,CACzBC,SAAS,cAAEL,IAAA,CAACP,gBAAgB,GAAE,CAAC,CAC/Ba,OAAO,CAAE,IACX,CAAC,CACD,CACEJ,IAAI,CAAC,WAAW,CAChBC,MAAM,CAAEL,eAAe,CACvBM,IAAI,CAAE,UAAU,CAChBC,SAAS,cAAEL,IAAA,CAACL,eAAe,GAAE,CAAC,CAC9BW,OAAO,CAAE,KACX,CAAC,CACF,CAED,cAAe,CAAAL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
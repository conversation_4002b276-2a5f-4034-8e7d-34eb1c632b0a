import React, { useState } from 'react';
import { Box, Typography, Card, CardContent, Chip, LinearProgress, Collapse, Tooltip } from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import dayjs from 'dayjs';

const MilestoneOverview = ({ 
  milestones,
  calculateMilestoneProgress,
  getMilestoneStatus,
  calculateTaskProgress,
  getTaskStatus,
  calculateSubtaskProgress,
  getSubtaskStatus
}) => {
  const [expandedMilestones, setExpandedMilestones] = useState({});

  const toggleMilestone = (milestoneId) => {
    setExpandedMilestones(prev => ({
      ...prev,
      [milestoneId]: !prev[milestoneId]
    }));
  };

  if (!milestones || milestones.length === 0) {
    return (
      <Box 
        sx={{ 
          textAlign: 'center', 
          py: 4, 
          backgroundColor: '#f9f9f9',
          borderRadius: '12px',
          border: '1px dashed #ddd'
        }}
      >
        <Iconify 
          icon="material-symbols:flag" 
          width={48} 
          height={48} 
          sx={{ color: '#999', mb: 2 }} 
        />
        <Typography variant="body1" sx={{ color: '#666', fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.9rem' }}>
          No milestones found
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {milestones.map((milestone, index) => {
        const milestoneId = milestone.id || `milestone-${index}`;
        const isExpanded = expandedMilestones[milestoneId] !== false; // Default to expanded
        const progress = calculateMilestoneProgress(milestone);
        const status = getMilestoneStatus(milestone);
        const completedTasks = milestone.tasks?.filter(task => getTaskStatus(task).label === 'Completed').length || 0;
        const totalTasks = milestone.tasks?.length || 0;
        const daysLeft = milestone.end_date ? dayjs(milestone.end_date).diff(dayjs(), 'day') : null;
        const isOverdue = daysLeft !== null && daysLeft < 0;

        return (
          <Card 
            key={milestoneId}
            elevation={0}
            sx={{ 
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              backgroundColor: '#fff',
              transition: 'all 0.2s ease',
              overflow: 'visible',
              '&:hover': {
                borderColor: '#e0e0e0',
                boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
              }
            }}
          >
            <CardContent sx={{ p: '0 !important' }}>
              {/* Milestone Header - Always visible */}
              <Box 
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  p: 2.5,
                  cursor: 'pointer',
                  borderBottom: isExpanded ? '1px solid #f0f0f0' : 'none',
                  borderTopLeftRadius: '12px',
                  borderTopRightRadius: '12px',
                  backgroundColor: isExpanded ? '#fff' : '#fafafa',
                  transition: 'background-color 0.2s ease'
                }}
                onClick={() => toggleMilestone(milestoneId)}
              >
                <Box 
                  sx={{ 
                    width: 16, 
                    height: 16, 
                    borderRadius: '50%', 
                    backgroundColor: status.color,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mr: 2,
                    flexShrink: 0
                  }}
                >
                  <Iconify 
                    icon={isExpanded ? "material-symbols:keyboard-arrow-down" : "material-symbols:keyboard-arrow-right"} 
                    width={14} 
                    height={14} 
                    sx={{ color: '#fff' }} 
                  />
                </Box>
                
                <Box sx={{ flexGrow: 1 }}>
                  <Typography 
                    variant="subtitle1" 
                    sx={{ 
                      fontWeight: 600,
                      color: '#333',
                      fontFamily: '"Recursive Variable", sans-serif',
                      fontSize: '1rem',
                      lineHeight: 1.3
                    }}
                  >
                    {milestone.name}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexShrink: 0 }}>
                  {daysLeft !== null && (
                    <Tooltip title={isOverdue ? "Overdue" : "Days remaining"}>
                      <Chip
                        icon={<Iconify icon="material-symbols:calendar-today" width={14} height={14} />}
                        label={isOverdue ? `${Math.abs(daysLeft)} days overdue` : `${daysLeft} days left`}
                        size="small"
                        sx={{
                          height: '22px',
                          fontSize: '0.75rem',
                          backgroundColor: isOverdue ? '#ffebee' : '#e8f5e9',
                          color: isOverdue ? '#d32f2f' : '#2e7d32',
                          fontWeight: 500,
                          fontFamily: '"Recursive Variable", sans-serif'
                        }}
                      />
                    </Tooltip>
                  )}
                  
                  <Chip 
                    label={`${progress}%`}
                    size="small"
                    sx={{ 
                      height: '22px',
                      fontSize: '0.75rem',
                      backgroundColor: progress === 100 ? '#e8f5e9' : `${status.color}15`,
                      color: progress === 100 ? '#2e7d32' : status.color,
                      fontWeight: 600,
                      fontFamily: '"Recursive Variable", sans-serif',
                      minWidth: 45
                    }}
                  />
                </Box>
              </Box>
              
              {/* Milestone Content - Expandable */}
              <Collapse in={isExpanded}>
                <Box sx={{ p: 2.5, pt: 2 }}>
                  
                  {/* Milestone Meta Info */}
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2, alignItems: 'center' }}>
                    <Chip 
                      icon={<Iconify icon="material-symbols:flag" width={14} height={14} />}
                      label={status.label}
                      size="small"
                      sx={{ 
                        height: '22px',
                        fontSize: '0.75rem',
                        backgroundColor: `${status.color}15`,
                        color: status.color,
                        fontWeight: 600,
                        fontFamily: '"Recursive Variable", sans-serif'
                      }}
                    />
                    
                    {milestone.start_date && milestone.end_date && (
                      <Typography 
                        variant="caption" 
                        sx={{ 
                          color: '#666',
                          fontSize: '0.8rem',
                          fontFamily: '"Recursive Variable", sans-serif',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5
                        }}
                      >
                        <Iconify icon="material-symbols:calendar-month" width={16} height={16} />
                        {dayjs(milestone.start_date).format('MMM D')} - {dayjs(milestone.end_date).format('MMM D, YYYY')}
                      </Typography>
                    )}
                    
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        color: '#666',
                        fontSize: '0.8rem',
                        fontFamily: '"Recursive Variable", sans-serif',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5
                      }}
                    >
                      <Iconify icon="material-symbols:task" width={16} height={16} />
                      {completedTasks}/{totalTasks} tasks completed
                    </Typography>
                  </Box>

                  {/* Progress Bar */}
                  <Box sx={{ mb: 3 }}>
                    <LinearProgress 
                      variant="determinate" 
                      value={progress} 
                      sx={{ 
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: '#f0f0f0',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: progress === 100 ? '#4CAF50' : mainYellowColor
                        }
                      }}
                    />
                  </Box>

                  {/* Tasks List */}
                  {milestone.tasks && milestone.tasks.length > 0 ? (
                    <Box 
                      sx={{ 
                        display: 'flex', 
                        flexDirection: 'column',
                        gap: 1.5
                      }}
                    >
                      {milestone.tasks.map((task, taskIndex) => {
                        const taskProgress = calculateTaskProgress(task);
                        const taskStatus = getTaskStatus(task);
                        const hasSubtasks = task.subtasks && task.subtasks.length > 0;
                        const completedSubtasks = task.subtasks?.filter(s => getSubtaskStatus(s).label === 'Completed').length || 0;
                        const isTaskCompleted = taskStatus.label === 'Completed';

                        return (
                          <Box 
                            key={task.id || taskIndex}
                            sx={{ 
                              display: 'flex',
                              flexDirection: 'column',
                              gap: 1,
                              backgroundColor: isTaskCompleted ? '#f8fff8' : '#fafafa',
                              borderRadius: '8px',
                              p: 1.5,
                              border: `1px solid ${isTaskCompleted ? '#e6f7e6' : '#f0f0f0'}`,
                              transition: 'all 0.2s ease',
                              '&:hover': {
                                borderColor: isTaskCompleted ? '#c8e6c9' : '#e0e0e0',
                                backgroundColor: isTaskCompleted ? '#f0fff0' : '#f5f5f5',
                                boxShadow: '0 1px 4px rgba(0,0,0,0.03)'
                              }
                            }}
                          >
                            {/* Task Header */}
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                              <Box 
                                sx={{ 
                                  width: 10,
                                  height: 10,
                                  borderRadius: '50%',
                                  backgroundColor: taskStatus.color,
                                  flexShrink: 0
                                }}
                              />
                              <Typography 
                                variant="body2"
                                sx={{ 
                                  flexGrow: 1,
                                  color: isTaskCompleted ? '#4CAF50' : '#333',
                                  fontFamily: '"Recursive Variable", sans-serif',
                                  fontSize: '0.875rem',
                                  fontWeight: 500,
                                  lineHeight: 1.3
                                }}
                              >
                                {task.name}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                                {hasSubtasks && (
                                  <Tooltip title={`${completedSubtasks} of ${task.subtasks.length} subtasks completed`}>
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        fontSize: '0.75rem',
                                        color: completedSubtasks === task.subtasks.length ? '#2e7d32' : '#666',
                                        fontWeight: 500,
                                        fontFamily: '"Recursive Variable", sans-serif',
                                        backgroundColor: completedSubtasks === task.subtasks.length ? '#e8f5e9' : '#f5f5f5',
                                        px: 1,
                                        py: 0.5,
                                        borderRadius: '4px',
                                        border: `1px solid ${completedSubtasks === task.subtasks.length ? '#c8e6c9' : '#e0e0e0'}`
                                      }}
                                    >
                                      {completedSubtasks}/{task.subtasks.length} subtasks completed
                                    </Typography>
                                  </Tooltip>
                                )}
                                <Tooltip title={`${taskProgress}% completed`}>
                                  <Box sx={{ position: 'relative', width: 28, height: 28 }}>
                                    <Box
                                      sx={{
                                        position: 'absolute',
                                        top: 0,
                                        left: 0,
                                        width: 28,
                                        height: 28,
                                        borderRadius: '50%',
                                        backgroundColor: '#f0f0f0'
                                      }}
                                    />
                                    <Box
                                      sx={{
                                        position: 'absolute',
                                        top: 0,
                                        left: 0,
                                        width: 28,
                                        height: 28,
                                        borderRadius: '50%',
                                        background: `conic-gradient(${taskProgress === 100 ? '#4CAF50' : taskStatus.color} ${taskProgress}%, transparent 0)`,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                      }}
                                    >
                                      <Box
                                        sx={{
                                          width: 20,
                                          height: 20,
                                          borderRadius: '50%',
                                          backgroundColor: '#fff',
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'center'
                                        }}
                                      >
                                        {taskProgress === 100 ? (
                                          <Iconify icon="material-symbols:check-small" width={14} height={14} sx={{ color: '#4CAF50' }} />
                                        ) : (
                                          <Typography
                                            variant="caption"
                                            sx={{
                                              fontSize: '0.65rem',
                                              fontWeight: 600,
                                              color: taskProgress > 0 ? (taskProgress >= 50 ? '#4CAF50' : mainYellowColor) : '#999',
                                              lineHeight: 1
                                            }}
                                          >
                                            {taskProgress}%
                                          </Typography>
                                        )}
                                      </Box>
                                    </Box>
                                  </Box>
                                </Tooltip>
                              </Box>
                            </Box>
                          </Box>
                        );
                      })}
                    </Box>
                  ) : (
                    <Box sx={{ textAlign: 'center', py: 2, color: '#999' }}>
                      <Typography variant="body2">No tasks in this milestone</Typography>
                    </Box>
                  )}
                </Box>
              </Collapse>
            </CardContent>
          </Card>
        );
      })}
    </Box>
  );
};

export default MilestoneOverview; 
import axios from 'axios';
import { getHeaders } from "helpers/functions";
import { APIURL } from "helpers/constants";

export const fetchPlanInfo = async (param) => {
  try {
    const headers = getHeaders();
    const response = await axios.get(`${APIURL}/api/plans/${param}`, { headers });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const checkUserExistence = async (email) => {
  try {
    const formData = new FormData();
    formData.append('email', email);
    const response = await axios.post(`${APIURL}/api/check-user`, formData, {
      headers: getHeaders(),
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const sendInvitation = async (plan, email) => {
  try {
    const formData = new FormData();
    formData.append('email', email);
    const response = await axios.post(`${APIURL}/api/plans/${plan.slug}/invite`, formData, {
      headers: getHeaders(),
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getInvitedUsers = async (planSlug) => {
  try {
    console.log('Calling API to get invited users for plan:', planSlug);
    const response = await axios.get(`${APIURL}/api/plans/${planSlug}/invited-users`, {
      headers: getHeaders(),
    });
    console.log('Full API Response:', response);
    
    // Kiểm tra cấu trúc response
    if (response.data && response.data.data) {
      return response.data;
    } else if (Array.isArray(response.data)) {
      return { data: response.data };
    } else {
      console.warn('Unexpected API response structure:', response.data);
      return { data: [] };
    }
  } catch (error) {
    console.error('Error fetching invited users:', error);
    throw new Error('Error fetching invited users: ' + error.message);
  }
};

export const deletePlan = async (planSlug) => {
  try {
    const response = await axios.delete(`${APIURL}/api/plan/delete/${planSlug}`, {
      headers: getHeaders(),
    });
    return response.data;
  } catch (error) {
    throw new Error('Error deleting plan of user: ' + error.message);
  }
}

export const optOutPlan = async (planSlug) => {
  try {
    const response = await axios.delete(`${APIURL}/api/plan/opt-out/${planSlug}`, {
      headers: getHeaders(),
    });
    return response.data;
  } catch (error) {
    throw new Error('Error opting out of plan: ' + error.message);
  }
};

export const updateMilestone = async (milestone) => {
  console.log('API call - updateMilestone:', milestone);
  try {
    const formData = new FormData();
    formData.append('name', milestone.name);
    
    const headers = getHeaders();
    console.log('Headers:', headers);
    const url = `${APIURL}/api/milestones/${milestone.id}/update`;
    console.log('API URL:', url);
    
    const response = await axios.put(url, formData, {
      headers: headers,
    });
    console.log('API Response:', response);
    return response.data;
  } catch (error) {
    console.error('API Error in updateMilestone:', error);
    console.error('Error details:', error.response?.data || error.message);
    throw new Error('Error updating milestone: ' + (error.response?.data?.message || error.message));
  }
};

export const updateTask = async (task) => {
  try {
    console.log('Updating task:', task);
    const formData = new FormData();
    
    // Thêm các trường cần thiết
    formData.append('name', task.name);
    
    // Thêm start_date và end_date nếu có
    if (task.start_date) {
      formData.append('start_date', task.start_date);
    }
    
    if (task.end_date) {
      formData.append('end_date', task.end_date);
    }
    
    // Thêm các trường khác nếu cần
    if (task.status) {
      formData.append('status', task.status);
    }
    
    // Đảm bảo progress luôn là số nguyên
    const progress = task.progress !== null && task.progress !== undefined ? task.progress : 0;
    formData.append('progress', progress);
    
    const response = await axios.put(
      `${APIURL}/api/tasks/update/${task.slug}`,
      formData,
      { headers: getHeaders() }
    );
    
    console.log('Update task response:', response);
    return response.data;
  } catch (error) {
    console.error('Error updating task:', error);
    throw error;
  }
};

export const updateSubtask = async (subtask) => {
  console.log('API call - updateSubtask:', subtask);
  try {
    const formData = new FormData();
    formData.append('name', subtask.name);
    
    formData.append('status', subtask.status);
    if (subtask.progress !== undefined) {
      formData.append('progress', subtask.progress);
    }
    
    if (subtask.start_date) {
      formData.append('start_date', subtask.start_date);
    }
    if (subtask.end_date) {
      formData.append('end_date', subtask.end_date);
    }
    
    const headers = getHeaders();
    console.log('Headers:', headers);
    const url = `${APIURL}/api/subtasks/update/${subtask.slug}`;
    console.log('API URL:', url);
    console.log('FormData being sent:', {
      name: subtask.name,
      status: subtask.status,
      progress: subtask.progress,
      start_date: subtask.start_date,
      end_date: subtask.end_date
    });
    
    const response = await axios.put(url, formData, {
      headers: headers,
    });
    console.log('API Response:', response);
    return response.data;
  } catch (error) {
    console.error('API Error in updateSubtask:', error);
    console.error('Error details:', error.response?.data || error.message);
    throw new Error('Error updating subtask: ' + (error.response?.data?.message || error.message));
  }
};

export const addTask = async (task) => {
  console.log('API call - addTask:', task);
  try {
    const formData = new FormData();
    formData.append('name', task.name);
    
    formData.append('status', task.status || 1);
    formData.append('progress', task.progress || 0);
    
    if (task.description) {
      formData.append('description', task.description);
    }
    if (task.start_date) {
      formData.append('start_date', task.start_date);
    }
    if (task.end_date) {
      formData.append('end_date', task.end_date);
    }
    
    const headers = getHeaders();
    console.log('Headers:', headers);
    const url = `${APIURL}/api/milestones/${task.milestone}/tasks/add`;
    console.log('API URL:', url);
    console.log('FormData being sent:', {
      name: task.name,
      status: task.status || 1,
      progress: task.progress || 0,
      description: task.description,
      start_date: task.start_date,
      end_date: task.end_date
    });
    
    const response = await axios.post(url, formData, {
      headers: headers,
    });
    console.log('API Response:', response);
    return response.data;
  } catch (error) {
    console.error('API Error in addTask:', error);
    console.error('Error details:', error.response?.data || error.message);
    throw new Error('Error adding task: ' + (error.response?.data?.message || error.message));
  }
};

export const addSubtask = async (subtask) => {
  console.log('API call - addSubtask:', subtask);
  try {
    const formData = new FormData();
    formData.append('name', subtask.name);
    
    formData.append('status', subtask.status || 1);
    formData.append('progress', subtask.progress || 0);
    
    if (subtask.start_date) {
      formData.append('start_date', subtask.start_date);
    }
    if (subtask.end_date) {
      formData.append('end_date', subtask.end_date);
    }
    
    const headers = getHeaders();
    console.log('Headers:', headers);
    const url = `${APIURL}/api/tasks/${subtask.task}/add-subtask`;
    console.log('API URL:', url);
    console.log('FormData being sent:', {
      name: subtask.name,
      status: subtask.status || 1,
      progress: subtask.progress || 0,
      start_date: subtask.start_date,
      end_date: subtask.end_date
    });
    
    const response = await axios.post(url, formData, {
      headers: headers,
    });
    console.log('API Response:', response);
    return response.data;
  } catch (error) {
    console.error('API Error in addSubtask:', error);
    console.error('Error details:', error.response?.data || error.message);
    throw new Error('Error adding subtask: ' + (error.response?.data?.message || error.message));
  }
};

export const deleteTask = async (taskSlug) => {
  console.log('API call - deleteTask:', taskSlug);
  try {
    const headers = getHeaders();
    console.log('Headers:', headers);
    const url = `${APIURL}/api/tasks/${taskSlug}/delete`;
    console.log('API URL:', url);
    
    const response = await axios.delete(url, {
      headers: headers,
    });
    console.log('API Response:', response);
    return response.data;
  } catch (error) {
    console.error('API Error in deleteTask:', error);
    console.error('Error details:', error.response?.data || error.message);
    throw new Error('Error deleting task: ' + (error.response?.data?.message || error.message));
  }
};

export const deleteSubtask = async (subtaskSlug) => {
  console.log('API call - deleteSubtask:', subtaskSlug);
  try {
    const headers = getHeaders();
    console.log('Headers:', headers);
    const url = `${APIURL}/api/subtasks/${subtaskSlug}/delete`;
    console.log('API URL:', url);
    
    const response = await axios.delete(url, {
      headers: headers,
    });
    console.log('API Response:', response);
    return response.data;
  } catch (error) {
    console.error('API Error in deleteSubtask:', error);
    console.error('Error details:', error.response?.data || error.message);
    throw new Error('Error deleting subtask: ' + (error.response?.data?.message || error.message));
  }
};

// Comment APIs
export const getComments = async (taskId) => {
  try {
    const response = await axios.get(`${APIURL}/api/comments/target/${taskId}/type/1`, {
      headers: getHeaders()
    });
    console.log('Get comments response:', response);
    return response;
  } catch (error) {
    console.error('Error fetching comments:', error);
    throw error;
  }
};

export const addComment = async (taskId, content) => {
  try {
    const formData = new FormData();
    formData.append('content', content);
    formData.append('target_id', taskId);
    formData.append('type', 1);

    const response = await axios.post(`${APIURL}/api/comments/add`, 
      formData,
      { headers: getHeaders() }
    );
    console.log('Add comment response:', response);
    return response;
  } catch (error) {
    console.error('Error adding comment:', error);
    throw error;
  }
};

export const updateComment = async (commentId, content) => {
  try {
    const formData = new FormData();
    formData.append('content', content);
    formData.append('type', 1);

    const response = await axios.put(`${APIURL}/api/comments/edit/${commentId}`, 
      formData,
      { headers: getHeaders() }
    );
    console.log('Update comment response:', response);
    return response;
  } catch (error) {
    console.error('Error updating comment:', error);
    throw error;
  }
};

export const deleteComment = async (commentId) => {
  try {
    const response = await axios.delete(`${APIURL}/api/comments/delete/${commentId}`, {
      headers: getHeaders()
    });
    console.log('Delete comment response:', response);
    return response;
  } catch (error) {
    console.error('Error deleting comment:', error);
    throw error;
  }
};

export const assignMembersToTask = async (taskSlug, memberIds) => {
  try {
    const response = await axios.put(
      `${APIURL}/api/tasks/${taskSlug}/assign`,
      { assignees: memberIds },
      { headers: getHeaders() }
    );
    console.log('Assign members response:', response);
    return response.data;
  } catch (error) {
    console.error('Error assigning members to task:', error);
    throw new Error('Error assigning members to task: ' + (error.response?.data?.message || error.message));
  }
};
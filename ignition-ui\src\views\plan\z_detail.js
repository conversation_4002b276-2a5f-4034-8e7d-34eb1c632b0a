/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, List, Box, Typography, Button, Chip, Avatar, Skeleton, Grid, Tooltip, Link } from '@mui/material';
import usePlanDetail from 'hooks/usePlanDetail';
import MilestoneComponent from 'components/Plan/MilestoneComponent';
import ConfirmDialogComponent from 'components/Dialog/confirm';
import Iconify from 'components/Iconify/index';
import InviteDialogComponent from './dialog/invite';
import { mainYellowColor } from "helpers/constants";
import { successSnackbar, errorSnackbar } from 'components/Snackbar';
import { checkUserExistence, sendInvitation, getInvitedUsers, deletePlan } from './services';
import styles from './styles.module.scss';

//-------------------------------------------------------------------------------------------------

const PlanDetail = () => {
  const navigate = useNavigate();
  const { param } = useParams();
  const {
    planInfo,
    loading,
    setSelectedUsers,
    error
  } = usePlanDetail(param);

  const [email, setEmail] = useState('');
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [emailDialogOpen, setEmailDialogOpen] = useState(false);
  const [invitedUsers, setInvitedUsers] = useState([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [inviteError, setInviteError] = useState('');
  const currentUser = useSelector((state) => state.user);
  const [isSending, setIsSending] = useState(false);

  useEffect(() => {
    fetchInvitedUsers();
  }, [param]);


  const fetchInvitedUsers = async () => {
    try {
      const invitedUsersList = await getInvitedUsers(param);
      setInvitedUsers(invitedUsersList);
    } catch (error) {
      console.error('Error fetching invited users:', error.message);
    }
  };

  const handleEmailChange = (value) => {
    setEmail(value);
  };

  const handleSendEmail = async () => {
    setIsSending(true);
    try {
      const result = await checkUserExistence(email);
      if (result.exists) {
        await fetchInvitedUsers();
        await sendInvitation(planInfo, email);
        setEmail('');
        setEmailDialogOpen(false);
        successSnackbar(`${email} has been added to the plan.`);
      } else {
        setInviteDialogOpen(true);
      }
    } catch (error) {
      if (error?.response && error?.response?.data?.message) {
        setInviteError(error.response.data.message)
      } else {
        errorSnackbar(`Failed to send invitation to ${email}`);
      }
    } finally {
      setIsSending(false);
    }
  };

  const handleInviteUser = async () => {
    try {
      await sendInvitation(planInfo, email);
      await fetchInvitedUsers();
      setInviteDialogOpen(false);
      setEmail('');
      setEmailDialogOpen(false);
      successSnackbar(`Invitation sent to ${email}`);
    } catch (error) {
      console.error('Error sending invitation:', error.message);
      errorSnackbar(`Failed to send invitation to ${email}`);
    }
  };

  const handleDeletePlan = async () => {
    try {
      await deletePlan(planInfo.slug);
      successSnackbar('Plan has been deleted successfully.');
      setDeleteDialogOpen(false);
      navigate('/d/api/plan/delete/<slug:slug>');
    } catch (error) {
      errorSnackbar('Failed to delete the plan.');
      setDeleteDialogOpen(false);
    }
  };

  if (error) {
    return (
      <Box className={styles.mainPage}>
        <Grid container className={styles.noDataPlanMain}>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
            <Iconify icon="bxs:error" width={80} height={80} color={"#ed4337"} />
            <Typography variant="h6" className={styles.title}>
              Something went wrong!
            </Typography>
            <Typography variant="body2" className={styles.subTitle}>
              It seems like you are at a forbidden/nonexistant page.
            </Typography>
            <Typography variant="body2" className={styles.subTitle}>
              Please return to the previous page.
            </Typography>
          </Box>
        </Grid>
      </Box>
    )
  }

  return (
    <>
      {loading ? (
        <Container sx={{ padding: '30px 0 0' }}>
          <Box sx={{ paddingBottom: '24px' }}>
            <Box className={styles.headerPlan}>
              <Skeleton variant="rectangular" width="94%" height={100} sx={{ marginTop: 2 }} />
            </Box>
            <Box>
              <Skeleton variant="text" width="94%" height={40} sx={{ marginTop: 1 }} />
              <Skeleton variant="text" width="94%" height={40} sx={{ marginTop: 1 }} />
              <Skeleton variant="text" width="94%" height={40} sx={{ marginTop: 1 }} />
            </Box>
            <Box sx={{ mt: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Skeleton variant="text" width={500} height={30} />
              <Skeleton variant="circular" width={24} height={24} />
            </Box>
            <Box sx={{ mt: 2 }}>
              <Skeleton variant="rectangular" width="94%" height={60} />
              <Skeleton variant="rectangular" width="94%" height={60} />
              <Skeleton variant="rectangular" width="94%" height={60} />
            </Box>
          </Box>
          <List style={{ padding: 0 }}>
            {[...Array(4)].map((_, index) => (
              <Box key={index} sx={{ mb: 2 }}>
                <Skeleton variant="rectangular" width="100%" height={100} />
              </Box>
            ))}
          </List>
        </Container>
      ) : (
        <Container className={styles.planDetailContainer} sx={{ padding: '30px 0 0' }}>
          <Box sx={{ paddingBottom: '24px' }}>
            <Box className={styles.headerPlan}>
              <Iconify icon="ant-design:project-outlined" width={50} height={50} color={mainYellowColor} />
              <Typography variant="h4" component="h1" gutterBottom>
                {planInfo?.name}
              </Typography>
            </Box>
            <Box className={styles.bodyPlan}>
              <Iconify icon="fluent:text-description-32-filled" width={32} height={32} color={mainYellowColor} />
              <Typography variant="subtitle1" component="p">
                {planInfo?.description}
              </Typography>
            </Box>
            <Box style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Box style={{ marginTop: '20px !important' }}>
                <Typography variant="h6" component="h2" gutterBottom className={styles.invitedUser} sx={{ marginTop: '50px' }}>
                  <Iconify icon="fluent:share-48-filled" width={30} height={30} color={mainYellowColor} />
                  <span>Invited Users</span>
                </Typography>
                <Box sx={{ mt: 0, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {invitedUsers.length === 0 ? (
                    <Typography variant="body1" className={styles.noInviteUser}>
                      No users have been invited to this plan yet.
                    </Typography>
                  ) : (
                    <Box sx={{ mt: 0, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                      {invitedUsers.map((user, index) => (
                        <Tooltip key={index} title={user.email} arrow>
                          {user.accepted === 1  ? (
                            <Tooltip key={index} title={user.email} arrow>
                              <Link href={`/d/other/profile/${user.email}`}>
                                <Chip
                                  key={index}
                                  avatar={<Avatar src={user.invited_user_info?.avatar || ''} />}
                                  label={user?.invited_user_info ? user?.invited_user_info?.first_name : user.email}
                                  variant="outlined"
                                  sx={{ backgroundColor: '#333', color: 'white', fontWeight: 'bold' }}
                                />
                              </Link>
                            </Tooltip>
                          ) : (
                            <Chip
                              key={index}
                              avatar={<Avatar src={user.invited_user_info?.avatar || ''} />}
                              label={user?.invited_user_info ? user?.invited_user_info?.first_name : user.email}
                              variant="outlined"
                              sx={{ backgroundColor: '#333', color: 'white', fontWeight: 'bold', opacity: '0.75' }}
                            />
                          )}
                        </Tooltip>
                      ))}
                    </Box>
                  )}
                </Box>
              </Box>
              <Box>
                {planInfo?.user?.id === currentUser.id && (
                  <Box className="d-flex">
                    <Button onClick={() => console.log("editActionBtn")} className={styles.editActionBtn}>
                      <span><Iconify icon="raphael:edit" width={24} height={20} color={mainYellowColor} /></span>
                      Edit
                    </Button>
                    <Button onClick={() => setDeleteDialogOpen(true)} className={styles.deleteActionBtn}>
                      <span><Iconify icon="eva:trash-2-outline" width={23} height={20} color="red" /></span>
                      Delete
                    </Button>
                  </Box>
                )}
                <Box>
                  {planInfo?.user?.id === currentUser.id && (
                    <Button onClick={() => setEmailDialogOpen(true)} className={styles.inviteActionBtn}>
                      <span><Iconify icon="mdi:email-outline" width={28} height={28} color={mainYellowColor} /></span>
                      Invite via Email
                    </Button>
                  )}
                  {planInfo?.user.id !== currentUser.id && (
                    <Box className={styles.invitedPlanLabel}>
                      Collaborating
                    </Box>
                  )}
                </Box>
              </Box>
            </Box>
          </Box>
          <List style={{ padding: 0 }}>
            {planInfo?.milestones.map((milestone, milestoneIndex) => (
              <MilestoneComponent
                key={milestone.id}
                milestone={milestone}
                setSelectedUsers={setSelectedUsers}
                users={invitedUsers}
              />
            ))}
          </List>
        </Container>
      )}

      <InviteDialogComponent
        open={emailDialogOpen}
        onClose={() => setEmailDialogOpen(false)}
        email={email}
        isSending={isSending}
        handleEmailChange={handleEmailChange}
        handleSendEmail={handleSendEmail}
        inviteError={inviteError}
      />
      <ConfirmDialogComponent
        open={inviteDialogOpen}
        onClose={() => setInviteDialogOpen(false)}
        onConfirm={handleInviteUser}
        title="User not found!"
        description={`The email "${email}" does not exist in the system. Would you like to send an invitation?`}
      />
      <ConfirmDialogComponent
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        onConfirm={handleDeletePlan}
        title="Delete Plan"
        description="Are you sure you want to delete this plan? This action cannot be undone."
      />
    </>
  );
};

export default PlanDetail;

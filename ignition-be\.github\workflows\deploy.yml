name: Deploy to Production

on:
  push:
    branches:
      - master

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.5.4
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add SSH known hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H api.ignitionai.site >> ~/.ssh/known_hosts

      - name: Deploy to server
        run: |
          ssh <EMAIL> "bash ~/deploy.sh" 
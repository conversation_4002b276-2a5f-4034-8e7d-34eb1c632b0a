import React, { useState } from 'react';
import { 
  <PERSON>alog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  TextField, 
  Typography, 
  CircularProgress
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import styles from '../styles.module.scss';

const InviteDialog = ({ open, onClose, onInvite, planInfo }) => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleEmailChange = (e) => {
    setEmail(e.target.value);
    setError('');
  };

  const validateEmail = (email) => {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  };

  const handleInvite = async () => {
    if (!email) {
      setError('Please enter an email address');
      return;
    }

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setLoading(true);
    try {
      const success = await onInvite(email);
      if (success) {
        setEmail('');
        onClose();
      }
    } catch (err) {
      console.error('Error inviting user:', err);
      setError('Failed to send invitation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !loading) {
      handleInvite();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={loading ? null : onClose}
      maxWidth="sm"
      fullWidth
      className={styles.inviteDialog}
      PaperProps={{
        sx: {
          borderRadius: '12px',
          padding: '8px'
        }
      }}
    >
      <DialogTitle 
        className={styles.dialogTitle}
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1,
          fontFamily: '"Recursive Variable", sans-serif',
          fontSize: '1.3rem',
          fontWeight: 600,
          color: '#333',
          pb: 1
        }}
      >
        <Iconify icon="material-symbols:person-add" width={24} height={24} color={mainYellowColor} />
        Invite to Plan
      </DialogTitle>
      
      <DialogContent className={styles.dialogContent}>
        <Typography 
          variant="body2" 
          sx={{ 
            mb: 2, 
            color: '#555',
            fontFamily: '"Recursive Variable", sans-serif'
          }}
        >
          Invite someone to collaborate on "{planInfo?.name}". They will receive an email invitation.
        </Typography>
        
        <TextField
          fullWidth
          label="Email Address"
          variant="outlined"
          value={email}
          onChange={handleEmailChange}
          onKeyDown={handleKeyDown}
          error={!!error}
          helperText={error}
          disabled={loading}
          placeholder="<EMAIL>"
          sx={{
            mb: 1,
            '& .MuiOutlinedInput-root': {
              borderRadius: '8px',
              fontFamily: '"Recursive Variable", sans-serif'
            },
            '& .MuiInputLabel-root': {
              fontFamily: '"Recursive Variable", sans-serif'
            }
          }}
        />
      </DialogContent>
      
      <DialogActions className={styles.dialogActions}>
        <Button 
          onClick={onClose}
          disabled={loading}
          sx={{ 
            color: '#666',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600
          }}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleInvite}
          disabled={loading || !email}
          variant="contained"
          startIcon={loading ? <CircularProgress size={16} color="inherit" /> : <Iconify icon="material-symbols:send" width={16} height={16} />}
          sx={{ 
            backgroundColor: mainYellowColor,
            color: '#333',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            '&:hover': {
              backgroundColor: '#e0a800'
            }
          }}
        >
          {loading ? 'Sending...' : 'Send Invitation'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default InviteDialog; 
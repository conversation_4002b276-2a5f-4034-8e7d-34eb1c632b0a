{"ast": null, "code": "import React,{useState,useEffect,useRef,useCallback}from'react';import axios from'axios';import{Button,Container,Box,Typography,Tooltip,IconButton,Paper,Divider}from'@mui/material';import LinearProgress from'@mui/material/LinearProgress';import{getHeaders}from\"helpers/functions\";import{APIURL}from\"helpers/constants\";import{useNavigate}from'react-router-dom';import TextAreaBase from'components/Input/TextAreaBase';import Iconify from'components/Iconify/index';import HexagonBallLoading from'components/Loading/HexagonBallLoading';import styles from'./styles.module.scss';//--------------------------------------------------------------------------------------------------\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CreatePlan=()=>{const[promptInput,setPromptInput]=useState('');const[language]=useState('English');// Default to English\nconst[plannerRole]=useState('Project Manager');// Default role\nconst[loading,setLoading]=useState(false);const[error,setError]=useState('');const navigate=useNavigate();const[planSlug,setPlanSlug]=useState('');const[isPlanCreated,setIsPlanCreated]=useState(false);const[planId,setPlanId]=useState(null);const[planStatus,setPlanStatus]=useState('');const[statusMessage,setStatusMessage]=useState('');const pollingIntervalRef=useRef(null);// Hàm kiểm tra trạng thái kế hoạch\nconst checkPlanStatus=useCallback(async()=>{if(!planId)return;try{const response=await axios.get(`${APIURL}/api/assistant/plan-status/${planId}`,{headers:getHeaders()});const{status,slug}=response.data;setPlanStatus(status);// Handle different statuses\nswitch(status){case'pending':setStatusMessage('Preparing to create plan...');break;case'processing':setStatusMessage('Creating plan, please wait...');break;case'completed':setStatusMessage('Plan has been created successfully!');// Just use the slug from status API response\nsetPlanSlug(slug);setIsPlanCreated(true);setLoading(false);// Stop polling when plan is completed\nclearInterval(pollingIntervalRef.current);break;case'failed':// Show specific error message from backend\nconst errorMessage=response.data.description||'An error occurred while creating the plan. Please try again.';setError(errorMessage);setStatusMessage('Plan creation failed');setLoading(false);clearInterval(pollingIntervalRef.current);break;default:setStatusMessage('Processing...');}}catch(error){var _error$response,_error$response2;console.error(\"Error checking plan status:\",error);// Handle specific error cases\nif(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)===404){setError('Plan not found. It may have been cleaned up due to a creation error. Please try creating your plan again.');}else if(((_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.status)===403){setError('Access denied. Please log in again and try creating your plan.');}else{setError('Unable to check plan creation status. This might be due to network issues. Please check your connection and try again.');}setStatusMessage('Plan creation error');setLoading(false);clearInterval(pollingIntervalRef.current);}},[planId]);// Stop polling when component unmounts\nuseEffect(()=>{return()=>{if(pollingIntervalRef.current){clearInterval(pollingIntervalRef.current);}};},[]);// Set up polling when planId changes\nuseEffect(()=>{if(planId&&(planStatus==='pending'||planStatus==='processing')){// Stop old interval if exists\nif(pollingIntervalRef.current){clearInterval(pollingIntervalRef.current);}// Check immediately\ncheckPlanStatus();// Set up new interval\npollingIntervalRef.current=setInterval(checkPlanStatus,10000);// Check every 10 seconds\nreturn()=>{if(pollingIntervalRef.current){clearInterval(pollingIntervalRef.current);}};}},[planId,planStatus,checkPlanStatus]);const handleCreate=async()=>{if(!promptInput.trim()){setError('Please describe what you want from this project.');return;}setLoading(true);setStatusMessage('Starting to create plan...');try{const formData=new FormData();formData.append(\"prompt\",promptInput);formData.append(\"language\",language);formData.append(\"role\",plannerRole);const response=await axios.post(`${APIURL}/api/assistant/create-planner-by-chat`,formData,{headers:getHeaders()});// Save plan_id for polling\nsetPlanId(response.data.plan_id);setPlanStatus('pending');// Polling will be set up automatically through useEffect\n}catch(error){console.error(\"Plan generation faced an error\",error);setStatusMessage('An error occurred while creating the plan. Please try again.');setLoading(false);}};const handleNavigateToPlan=()=>{navigate(\"/d/plan/\"+planSlug,{replace:true});};const handleResetForm=()=>{setLoading(false);setIsPlanCreated(false);setPlanId(null);setPlanStatus('');setStatusMessage('');if(pollingIntervalRef.current){clearInterval(pollingIntervalRef.current);}};return/*#__PURE__*/_jsx(Container,{className:styles.mainCreateContainer,children:/*#__PURE__*/_jsxs(Box,{className:styles.boxWrapper,children:[!loading&&!isPlanCreated&&/*#__PURE__*/_jsxs(Paper,{elevation:3,className:styles.paperContent,children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',textAlign:'center',mb:5,pt:2},children:[/*#__PURE__*/_jsx(Box,{sx:{backgroundColor:'rgba(240, 165, 0, 0.1)',borderRadius:'50%',p:2,mb:3,display:'flex',justifyContent:'center',alignItems:'center',width:100,height:100,boxShadow:'0 4px 20px rgba(240, 165, 0, 0.2)'},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:rocket-launch\",width:60,height:60,color:\"#F0A500\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",className:styles.paperTitle,sx:{mb:2},children:\"Welcome to the Ignition\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",className:styles.paperBodyContent,sx:{maxWidth:'700px'},children:\"This form will help you create a new project plan. Please describe what you want from your project in detail, and then click \\\"Generate\\\" to create your plan.\"})]}),/*#__PURE__*/_jsx(Divider,{sx:{mb:5,borderColor:'rgba(0,0,0,0.1)'}}),/*#__PURE__*/_jsxs(Box,{className:styles.formSection,children:[/*#__PURE__*/_jsxs(Box,{className:styles.boxInputContent,children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",justifyContent:\"space-between\",children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",className:styles.titleInput,sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:target\",width:24,height:24,color:\"#F0A500\",style:{marginRight:'12px'}}),\"What do you want out from this project?\"]}),/*#__PURE__*/_jsx(Tooltip,{title:/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"What is this field?\"}),/*#__PURE__*/_jsx(\"br\",{}),\"This field is for describing your project. Please provide as much detail as possible to help the AI understand your requirements.\",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"strong\",{children:\"What should you include?\"}),/*#__PURE__*/_jsx(\"br\",{}),\"- Brief description of the project.\",/*#__PURE__*/_jsx(\"br\",{}),\"- Key objectives and goals.\",/*#__PURE__*/_jsx(\"br\",{}),\"- Any specific tasks or milestones.\",/*#__PURE__*/_jsx(\"br\",{}),\"The more detailed you are, the better the generated plan will be.\"]}),children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",children:/*#__PURE__*/_jsx(Iconify,{icon:\"octicon:info-16\",width:18,height:18,color:\"#F0A500\"})})})]}),/*#__PURE__*/_jsx(TextAreaBase,{id:\"prompt\",value:promptInput,handleChange:setPromptInput,minRows:5,multiline:true,placeholder:\"E.g., I need a comprehensive testing plan for a new mobile app. The app has user authentication, profile management, content browsing, and payment features. I want to ensure all features work correctly across different devices and platforms...\",errorText:error&&!promptInput?error:'',required:true,sx:{'& .MuiOutlinedInput-root':{borderRadius:'12px','&.Mui-focused fieldset':{borderColor:'#F0A500',borderWidth:'2px'}}}}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{display:'block',mt:1,color:'#666',fontStyle:'italic'},children:\"Suggestion: Be specific about your project scope, timeline, and expected outcomes. Include any technical requirements or constraints.\"})]}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleCreate,fullWidth:true,className:styles.genPlanBtn,startIcon:/*#__PURE__*/_jsx(Iconify,{icon:\"mingcute:ai-line\",width:24,height:24}),sx:{mt:5,height:'60px',borderRadius:'16px',boxShadow:'0 6px 16px rgba(240, 165, 0, 0.3)',fontSize:'1.2rem',fontWeight:700},children:\"GENERATE\"})]})]}),(loading||isPlanCreated)&&/*#__PURE__*/_jsxs(Box,{className:styles.loadingBox,children:[loading&&/*#__PURE__*/_jsxs(Box,{className:styles.loadingContainer,children:[/*#__PURE__*/_jsx(Box,{className:styles.hexagonContainer,children:/*#__PURE__*/_jsx(HexagonBallLoading,{fromCreatePlan:true})}),/*#__PURE__*/_jsxs(Paper,{elevation:4,className:styles.loadingCard,children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,right:0,height:'4px',bgcolor:'rgba(240, 165, 0, 0.2)'},children:/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,height:'100%',width:'30%',bgcolor:'#F0A500',animation:'loadingProgress 2s infinite ease-in-out'}})}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",align:\"center\",className:styles.titleGenerating,children:statusMessage||'Creating plan, please wait...'}),/*#__PURE__*/_jsx(Box,{sx:{width:'100%',mt:4,mb:3,display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(Box,{sx:{position:'relative',width:'80%'},children:/*#__PURE__*/_jsx(LinearProgress,{color:\"inherit\",sx:{height:8,borderRadius:4,bgcolor:'rgba(240, 165, 0, 0.15)','& .MuiLinearProgress-bar':{bgcolor:'#F0A500'}}})})}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mt:3,color:'#666',fontStyle:'italic',px:2},children:\"This may take a minute or two. We're crafting a detailed plan for you.\"}),error&&/*#__PURE__*/_jsx(Box,{sx:{mt:4,p:3,bgcolor:'rgba(244, 67, 54, 0.1)',borderRadius:'12px',border:'1px solid rgba(244, 67, 54, 0.2)'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'flex-start',gap:2},children:[/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:error-outline\",width:24,height:24,color:\"#f44336\"}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{color:'#f44336',fontWeight:600,mb:1},children:\"Plan Creation Failed\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{color:'#666',mb:3},children:error}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2,flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(Button,{variant:\"contained\",onClick:handleResetForm,startIcon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:refresh\",width:20,height:20}),sx:{bgcolor:'#f44336','&:hover':{bgcolor:'#d32f2f'},borderRadius:'8px',textTransform:'none',fontWeight:600},children:\"Try Again\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",onClick:()=>navigate('/d/'),sx:{borderColor:'#f44336',color:'#f44336','&:hover':{borderColor:'#d32f2f',bgcolor:'rgba(244, 67, 54, 0.04)'},borderRadius:'8px',textTransform:'none',fontWeight:600},children:\"Go to Dashboard\"})]})]})]})})]})]}),isPlanCreated&&/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',maxWidth:'500px',p:5,bgcolor:'white',borderRadius:'16px',boxShadow:'0 8px 32px rgba(0, 0, 0, 0.1)'},children:[/*#__PURE__*/_jsx(Box,{sx:{backgroundColor:'rgba(76, 175, 80, 0.1)',borderRadius:'50%',p:2,mb:3,display:'flex',justifyContent:'center',alignItems:'center',width:100,height:100,margin:'0 auto',boxShadow:'0 4px 20px rgba(76, 175, 80, 0.2)'},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:check-circle\",width:64,height:64,color:\"#4CAF50\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",align:\"center\",className:styles.titleGenerating,sx:{color:'#333',animation:'none'},children:\"Plan has been created successfully!\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mt:3,mb:5,color:'#555'},children:\"Would you like to view your new plan or create another one?\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'center',gap:3,flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(Button,{variant:\"contained\",className:styles.gotoDetailPageBtn,onClick:handleNavigateToPlan,startIcon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:visibility-outline\",width:22,height:22}),sx:{borderRadius:'12px',boxShadow:'0 4px 12px rgba(240, 165, 0, 0.3)',padding:'12px 28px',fontSize:'1.1rem',fontWeight:600},children:\"View Plan\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",className:styles.reRunGenBtn,onClick:handleResetForm,startIcon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:refresh\",width:22,height:22}),sx:{borderRadius:'12px',boxShadow:'0 4px 12px rgba(0, 0, 0, 0.15)',padding:'12px 28px',fontSize:'1.1rem',fontWeight:600},children:\"Create Another\"})]})]})]})]})});};export default CreatePlan;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "axios", "<PERSON><PERSON>", "Container", "Box", "Typography", "<PERSON><PERSON><PERSON>", "IconButton", "Paper", "Divider", "LinearProgress", "getHeaders", "APIURL", "useNavigate", "TextAreaBase", "Iconify", "HexagonBallLoading", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CreatePlan", "promptInput", "setPromptInput", "language", "plannerRole", "loading", "setLoading", "error", "setError", "navigate", "planSlug", "setPlanSlug", "isPlanCreated", "setIsPlanCreated", "planId", "setPlanId", "planStatus", "setPlanStatus", "statusMessage", "setStatusMessage", "pollingIntervalRef", "checkPlanStatus", "response", "get", "headers", "status", "slug", "data", "clearInterval", "current", "errorMessage", "description", "_error$response", "_error$response2", "console", "setInterval", "handleCreate", "trim", "formData", "FormData", "append", "post", "plan_id", "handleNavigateToPlan", "replace", "handleResetForm", "className", "mainCreateContainer", "children", "boxWrapper", "elevation", "paperContent", "sx", "display", "flexDirection", "alignItems", "textAlign", "mb", "pt", "backgroundColor", "borderRadius", "p", "justifyContent", "width", "height", "boxShadow", "icon", "color", "variant", "paperTitle", "paperBodyContent", "max<PERSON><PERSON><PERSON>", "borderColor", "formSection", "boxInputContent", "titleInput", "style", "marginRight", "title", "size", "id", "value", "handleChange", "minRows", "multiline", "placeholder", "errorText", "required", "borderWidth", "mt", "fontStyle", "onClick", "fullWidth", "genPlanBtn", "startIcon", "fontSize", "fontWeight", "loadingBox", "loadingContainer", "hexagonContainer", "fromCreatePlan", "loadingCard", "position", "top", "left", "right", "bgcolor", "animation", "align", "titleGenerating", "px", "border", "gap", "flexWrap", "textTransform", "margin", "gotoDetailPageBtn", "padding", "reRunGenBtn"], "sources": ["C:/ignition/ignition-ui/src/views/plan/create.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\r\nimport axios from 'axios';\r\nimport { Button, Container, Box, Typography, Tooltip, IconButton, Paper, Divider } from '@mui/material';\r\nimport LinearProgress from '@mui/material/LinearProgress';\r\nimport { getHeaders } from \"helpers/functions\";\r\nimport { APIURL } from \"helpers/constants\";\r\nimport { useNavigate } from 'react-router-dom';\r\nimport TextAreaBase from 'components/Input/TextAreaBase';\r\nimport Iconify from 'components/Iconify/index';\r\nimport HexagonBallLoading from 'components/Loading/HexagonBallLoading';\r\nimport styles from './styles.module.scss';\r\n\r\n//--------------------------------------------------------------------------------------------------\r\n\r\nconst CreatePlan = () => {\r\n  const [promptInput, setPromptInput] = useState('');\r\n  const [language] = useState('English'); // Default to English\r\n  const [plannerRole] = useState('Project Manager'); // Default role\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const navigate = useNavigate();\r\n  const [planSlug, setPlanSlug] = useState('');\r\n  const [isPlanCreated, setIsPlanCreated] = useState(false);\r\n  const [planId, setPlanId] = useState(null);\r\n  const [planStatus, setPlanStatus] = useState('');\r\n  const [statusMessage, setStatusMessage] = useState('');\r\n  const pollingIntervalRef = useRef(null);\r\n\r\n  // Hàm kiểm tra trạng thái kế hoạch\r\n  const checkPlanStatus = useCallback(async () => {\r\n    if (!planId) return;\r\n\r\n    try {\r\n      const response = await axios.get(`${APIURL}/api/assistant/plan-status/${planId}`, {\r\n        headers: getHeaders()\r\n      });\r\n\r\n      const { status, slug } = response.data;\r\n      setPlanStatus(status);\r\n\r\n      // Handle different statuses\r\n      switch (status) {\r\n        case 'pending':\r\n          setStatusMessage('Preparing to create plan...');\r\n          break;\r\n        case 'processing':\r\n          setStatusMessage('Creating plan, please wait...');\r\n          break;\r\n        case 'completed':\r\n          setStatusMessage('Plan has been created successfully!');\r\n          \r\n          // Just use the slug from status API response\r\n          setPlanSlug(slug);\r\n          setIsPlanCreated(true);\r\n          setLoading(false);\r\n          // Stop polling when plan is completed\r\n          clearInterval(pollingIntervalRef.current);\r\n          break;\r\n        case 'failed':\r\n          // Show specific error message from backend\r\n          const errorMessage = response.data.description || 'An error occurred while creating the plan. Please try again.';\r\n          setError(errorMessage);\r\n          setStatusMessage('Plan creation failed');\r\n          setLoading(false);\r\n          clearInterval(pollingIntervalRef.current);\r\n          break;\r\n        default:\r\n          setStatusMessage('Processing...');\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error checking plan status:\", error);\r\n\r\n      // Handle specific error cases\r\n      if (error.response?.status === 404) {\r\n        setError('Plan not found. It may have been cleaned up due to a creation error. Please try creating your plan again.');\r\n      } else if (error.response?.status === 403) {\r\n        setError('Access denied. Please log in again and try creating your plan.');\r\n      } else {\r\n        setError('Unable to check plan creation status. This might be due to network issues. Please check your connection and try again.');\r\n      }\r\n\r\n      setStatusMessage('Plan creation error');\r\n      setLoading(false);\r\n      clearInterval(pollingIntervalRef.current);\r\n    }\r\n  }, [planId]);\r\n\r\n  // Stop polling when component unmounts\r\n  useEffect(() => {\r\n    return () => {\r\n      if (pollingIntervalRef.current) {\r\n        clearInterval(pollingIntervalRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Set up polling when planId changes\r\n  useEffect(() => {\r\n    if (planId && (planStatus === 'pending' || planStatus === 'processing')) {\r\n      // Stop old interval if exists\r\n      if (pollingIntervalRef.current) {\r\n        clearInterval(pollingIntervalRef.current);\r\n      }\r\n\r\n      // Check immediately\r\n      checkPlanStatus();\r\n\r\n      // Set up new interval\r\n      pollingIntervalRef.current = setInterval(checkPlanStatus, 10000); // Check every 10 seconds\r\n\r\n      return () => {\r\n        if (pollingIntervalRef.current) {\r\n          clearInterval(pollingIntervalRef.current);\r\n        }\r\n      };\r\n    }\r\n  }, [planId, planStatus, checkPlanStatus]);\r\n\r\n  const handleCreate = async () => {\r\n    if (!promptInput.trim()) {\r\n      setError('Please describe what you want from this project.');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setStatusMessage('Starting to create plan...');\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append(\"prompt\", promptInput);\r\n      formData.append(\"language\", language);\r\n      formData.append(\"role\", plannerRole);\r\n\r\n      const response = await axios.post(`${APIURL}/api/assistant/create-planner-by-chat`,\r\n        formData,\r\n        { headers: getHeaders() }\r\n      );\r\n\r\n      // Save plan_id for polling\r\n      setPlanId(response.data.plan_id);\r\n      setPlanStatus('pending');\r\n\r\n      // Polling will be set up automatically through useEffect\r\n\r\n    } catch (error) {\r\n      console.error(\"Plan generation faced an error\", error);\r\n      setStatusMessage('An error occurred while creating the plan. Please try again.');\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleNavigateToPlan = () => {\r\n    navigate(\"/d/plan/\" + planSlug, { replace: true });\r\n  };\r\n\r\n  const handleResetForm = () => {\r\n    setLoading(false);\r\n    setIsPlanCreated(false);\r\n    setPlanId(null);\r\n    setPlanStatus('');\r\n    setStatusMessage('');\r\n    if (pollingIntervalRef.current) {\r\n      clearInterval(pollingIntervalRef.current);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container className={styles.mainCreateContainer}>\r\n      <Box className={styles.boxWrapper}>\r\n        {!loading && !isPlanCreated && (\r\n          <Paper elevation={3} className={styles.paperContent}>\r\n            {/* Header Section with Rocket Icon */}\r\n            <Box sx={{ \r\n              display: 'flex', \r\n              flexDirection: 'column', \r\n              alignItems: 'center',\r\n              textAlign: 'center',\r\n              mb: 5,\r\n              pt: 2\r\n            }}>\r\n              <Box sx={{ \r\n                backgroundColor: 'rgba(240, 165, 0, 0.1)', \r\n                borderRadius: '50%', \r\n                p: 2,\r\n                mb: 3,\r\n                display: 'flex',\r\n                justifyContent: 'center',\r\n                alignItems: 'center',\r\n                width: 100,\r\n                height: 100,\r\n                boxShadow: '0 4px 20px rgba(240, 165, 0, 0.2)'\r\n              }}>\r\n                <Iconify icon=\"mdi:rocket-launch\" width={60} height={60} color=\"#F0A500\" />\r\n              </Box>\r\n              <Typography variant=\"h4\" className={styles.paperTitle} sx={{ mb: 2 }}>\r\n                Welcome to the Ignition\r\n              </Typography>\r\n              <Typography variant=\"body1\" className={styles.paperBodyContent} sx={{ maxWidth: '700px' }}>\r\n                This form will help you create a new project plan. Please describe what you want from your project in detail, and then click \"Generate\" to create your plan.\r\n              </Typography>\r\n            </Box>\r\n\r\n            <Divider sx={{ mb: 5, borderColor: 'rgba(0,0,0,0.1)' }} />\r\n\r\n            {/* Form Section */}\r\n            <Box className={styles.formSection}>\r\n              <Box className={styles.boxInputContent}>\r\n                <Box display=\"flex\" alignItems=\"center\" justifyContent='space-between'>\r\n                  <Typography variant=\"h6\" className={styles.titleInput} sx={{ display: 'flex', alignItems: 'center' }}>\r\n                    <Iconify icon=\"mdi:target\" width={24} height={24} color=\"#F0A500\" style={{ marginRight: '12px' }} />\r\n                    What do you want out from this project?\r\n                  </Typography>\r\n                  <Tooltip title={<>\r\n                    <strong>What is this field?</strong><br />\r\n                    This field is for describing your project. Please provide as much detail as possible to help the AI understand your requirements.<br />\r\n                    <strong>What should you include?</strong><br />\r\n                    - Brief description of the project.<br />\r\n                    - Key objectives and goals.<br />\r\n                    - Any specific tasks or milestones.<br />\r\n                    The more detailed you are, the better the generated plan will be.\r\n                  </>}>\r\n                    <IconButton size=\"small\">\r\n                      <Iconify icon=\"octicon:info-16\" width={18} height={18} color=\"#F0A500\" />\r\n                    </IconButton>\r\n                  </Tooltip>\r\n                </Box>\r\n                <TextAreaBase\r\n                  id=\"prompt\"\r\n                  value={promptInput}\r\n                  handleChange={setPromptInput}\r\n                  minRows={5}\r\n                  multiline\r\n                  placeholder=\"E.g., I need a comprehensive testing plan for a new mobile app. The app has user authentication, profile management, content browsing, and payment features. I want to ensure all features work correctly across different devices and platforms...\"\r\n                  errorText={error && !promptInput ? error : ''}\r\n                  required\r\n                  sx={{ \r\n                    '& .MuiOutlinedInput-root': {\r\n                      borderRadius: '12px',\r\n                      '&.Mui-focused fieldset': {\r\n                        borderColor: '#F0A500',\r\n                        borderWidth: '2px'\r\n                      }\r\n                    }\r\n                  }}\r\n                />\r\n                <Typography variant=\"caption\" sx={{ display: 'block', mt: 1, color: '#666', fontStyle: 'italic' }}>\r\n                  Suggestion: Be specific about your project scope, timeline, and expected outcomes. Include any technical requirements or constraints.\r\n                </Typography>\r\n              </Box>\r\n\r\n              <Button\r\n                variant=\"contained\" \r\n                onClick={handleCreate} \r\n                fullWidth \r\n                className={styles.genPlanBtn}\r\n                startIcon={<Iconify icon=\"mingcute:ai-line\" width={24} height={24} />}\r\n                sx={{ \r\n                  mt: 5,\r\n                  height: '60px',\r\n                  borderRadius: '16px',\r\n                  boxShadow: '0 6px 16px rgba(240, 165, 0, 0.3)',\r\n                  fontSize: '1.2rem',\r\n                  fontWeight: 700\r\n                }}\r\n              >\r\n                GENERATE\r\n              </Button>\r\n            </Box>\r\n          </Paper>\r\n        )}\r\n\r\n        {(loading || isPlanCreated) && (\r\n          <Box className={styles.loadingBox}>\r\n            {loading && (\r\n              <Box className={styles.loadingContainer}>\r\n                <Box className={styles.hexagonContainer}>\r\n                  <HexagonBallLoading fromCreatePlan={true} />\r\n                </Box>\r\n\r\n                <Paper elevation={4} className={styles.loadingCard}>\r\n                  <Box sx={{\r\n                    position: 'absolute',\r\n                    top: 0,\r\n                    left: 0,\r\n                    right: 0,\r\n                    height: '4px',\r\n                    bgcolor: 'rgba(240, 165, 0, 0.2)'\r\n                  }}>\r\n                    <Box sx={{\r\n                      position: 'absolute',\r\n                      top: 0,\r\n                      left: 0,\r\n                      height: '100%',\r\n                      width: '30%',\r\n                      bgcolor: '#F0A500',\r\n                      animation: 'loadingProgress 2s infinite ease-in-out'\r\n                    }} />\r\n                  </Box>\r\n\r\n                  <Typography variant=\"h5\" align=\"center\" className={styles.titleGenerating}>\r\n                    {statusMessage || 'Creating plan, please wait...'}\r\n                  </Typography>\r\n                  \r\n                  <Box sx={{ \r\n                    width: '100%', \r\n                    mt: 4, \r\n                    mb: 3, \r\n                    display: 'flex', \r\n                    justifyContent: 'center'\r\n                  }}>\r\n                    <Box sx={{ position: 'relative', width: '80%' }}>\r\n                      <LinearProgress \r\n                        color=\"inherit\" \r\n                        sx={{ \r\n                          height: 8, \r\n                          borderRadius: 4,\r\n                          bgcolor: 'rgba(240, 165, 0, 0.15)',\r\n                          '& .MuiLinearProgress-bar': {\r\n                            bgcolor: '#F0A500',\r\n                          }\r\n                        }} \r\n                      />\r\n                    </Box>\r\n                  </Box>\r\n                  \r\n                  <Typography variant=\"body2\" sx={{\r\n                    mt: 3,\r\n                    color: '#666',\r\n                    fontStyle: 'italic',\r\n                    px: 2\r\n                  }}>\r\n                    This may take a minute or two. We're crafting a detailed plan for you.\r\n                  </Typography>\r\n\r\n                  {/* Error Display */}\r\n                  {error && (\r\n                    <Box sx={{\r\n                      mt: 4,\r\n                      p: 3,\r\n                      bgcolor: 'rgba(244, 67, 54, 0.1)',\r\n                      borderRadius: '12px',\r\n                      border: '1px solid rgba(244, 67, 54, 0.2)'\r\n                    }}>\r\n                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>\r\n                        <Iconify icon=\"material-symbols:error-outline\" width={24} height={24} color=\"#f44336\" />\r\n                        <Box>\r\n                          <Typography variant=\"h6\" sx={{ color: '#f44336', fontWeight: 600, mb: 1 }}>\r\n                            Plan Creation Failed\r\n                          </Typography>\r\n                          <Typography variant=\"body2\" sx={{ color: '#666', mb: 3 }}>\r\n                            {error}\r\n                          </Typography>\r\n                          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>\r\n                            <Button\r\n                              variant=\"contained\"\r\n                              onClick={handleResetForm}\r\n                              startIcon={<Iconify icon=\"material-symbols:refresh\" width={20} height={20} />}\r\n                              sx={{\r\n                                bgcolor: '#f44336',\r\n                                '&:hover': { bgcolor: '#d32f2f' },\r\n                                borderRadius: '8px',\r\n                                textTransform: 'none',\r\n                                fontWeight: 600\r\n                              }}\r\n                            >\r\n                              Try Again\r\n                            </Button>\r\n                            <Button\r\n                              variant=\"outlined\"\r\n                              onClick={() => navigate('/d/')}\r\n                              sx={{\r\n                                borderColor: '#f44336',\r\n                                color: '#f44336',\r\n                                '&:hover': { borderColor: '#d32f2f', bgcolor: 'rgba(244, 67, 54, 0.04)' },\r\n                                borderRadius: '8px',\r\n                                textTransform: 'none',\r\n                                fontWeight: 600\r\n                              }}\r\n                            >\r\n                              Go to Dashboard\r\n                            </Button>\r\n                          </Box>\r\n                        </Box>\r\n                      </Box>\r\n                    </Box>\r\n                  )}\r\n                </Paper>\r\n              </Box>\r\n            )}\r\n\r\n            {isPlanCreated && (\r\n              <Box sx={{ textAlign: 'center', maxWidth: '500px', p: 5, bgcolor: 'white', borderRadius: '16px', boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)' }}>\r\n                <Box sx={{ \r\n                  backgroundColor: 'rgba(76, 175, 80, 0.1)', \r\n                  borderRadius: '50%', \r\n                  p: 2,\r\n                  mb: 3,\r\n                  display: 'flex',\r\n                  justifyContent: 'center',\r\n                  alignItems: 'center',\r\n                  width: 100,\r\n                  height: 100,\r\n                  margin: '0 auto',\r\n                  boxShadow: '0 4px 20px rgba(76, 175, 80, 0.2)'\r\n                }}>\r\n                  <Iconify icon=\"mdi:check-circle\" width={64} height={64} color=\"#4CAF50\" />\r\n                </Box>\r\n                <Typography variant=\"h5\" align=\"center\" className={styles.titleGenerating} sx={{ color: '#333', animation: 'none' }}>\r\n                  Plan has been created successfully!\r\n                </Typography>\r\n                <Typography variant=\"body1\" sx={{ mt: 3, mb: 5, color: '#555' }}>\r\n                  Would you like to view your new plan or create another one?\r\n                </Typography>\r\n                <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, flexWrap: 'wrap' }}>\r\n                  <Button \r\n                    variant=\"contained\"\r\n                    className={styles.gotoDetailPageBtn} \r\n                    onClick={handleNavigateToPlan}\r\n                    startIcon={<Iconify icon=\"material-symbols:visibility-outline\" width={22} height={22} />}\r\n                    sx={{ \r\n                      borderRadius: '12px',\r\n                      boxShadow: '0 4px 12px rgba(240, 165, 0, 0.3)',\r\n                      padding: '12px 28px',\r\n                      fontSize: '1.1rem',\r\n                      fontWeight: 600\r\n                    }}\r\n                  >\r\n                    View Plan\r\n                  </Button>\r\n                  <Button \r\n                    variant=\"contained\"\r\n                    className={styles.reRunGenBtn} \r\n                    onClick={handleResetForm}\r\n                    startIcon={<Iconify icon=\"material-symbols:refresh\" width={22} height={22} />}\r\n                    sx={{ \r\n                      borderRadius: '12px',\r\n                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\r\n                      padding: '12px 28px',\r\n                      fontSize: '1.1rem',\r\n                      fontWeight: 600\r\n                    }}\r\n                  >\r\n                    Create Another\r\n                  </Button>\r\n                </Box>\r\n              </Box>\r\n            )}\r\n          </Box>\r\n        )}\r\n      </Box>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default CreatePlan;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,SAAS,CAAEC,GAAG,CAAEC,UAAU,CAAEC,OAAO,CAAEC,UAAU,CAAEC,KAAK,CAAEC,OAAO,KAAQ,eAAe,CACvG,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CACzD,OAASC,UAAU,KAAQ,mBAAmB,CAC9C,OAASC,MAAM,KAAQ,mBAAmB,CAC1C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,YAAY,KAAM,+BAA+B,CACxD,MAAO,CAAAC,OAAO,KAAM,0BAA0B,CAC9C,MAAO,CAAAC,kBAAkB,KAAM,uCAAuC,CACtE,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CAEzC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEA,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC8B,QAAQ,CAAC,CAAG9B,QAAQ,CAAC,SAAS,CAAC,CAAE;AACxC,KAAM,CAAC+B,WAAW,CAAC,CAAG/B,QAAQ,CAAC,iBAAiB,CAAC,CAAE;AACnD,KAAM,CAACgC,OAAO,CAAEC,UAAU,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACkC,KAAK,CAAEC,QAAQ,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAAoC,QAAQ,CAAGpB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACqB,QAAQ,CAAEC,WAAW,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACuC,aAAa,CAAEC,gBAAgB,CAAC,CAAGxC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACyC,MAAM,CAAEC,SAAS,CAAC,CAAG1C,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAAC2C,UAAU,CAAEC,aAAa,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC6C,aAAa,CAAEC,gBAAgB,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAA+C,kBAAkB,CAAG7C,MAAM,CAAC,IAAI,CAAC,CAEvC;AACA,KAAM,CAAA8C,eAAe,CAAG7C,WAAW,CAAC,SAAY,CAC9C,GAAI,CAACsC,MAAM,CAAE,OAEb,GAAI,CACF,KAAM,CAAAQ,QAAQ,CAAG,KAAM,CAAA7C,KAAK,CAAC8C,GAAG,CAAC,GAAGnC,MAAM,8BAA8B0B,MAAM,EAAE,CAAE,CAChFU,OAAO,CAAErC,UAAU,CAAC,CACtB,CAAC,CAAC,CAEF,KAAM,CAAEsC,MAAM,CAAEC,IAAK,CAAC,CAAGJ,QAAQ,CAACK,IAAI,CACtCV,aAAa,CAACQ,MAAM,CAAC,CAErB;AACA,OAAQA,MAAM,EACZ,IAAK,SAAS,CACZN,gBAAgB,CAAC,6BAA6B,CAAC,CAC/C,MACF,IAAK,YAAY,CACfA,gBAAgB,CAAC,+BAA+B,CAAC,CACjD,MACF,IAAK,WAAW,CACdA,gBAAgB,CAAC,qCAAqC,CAAC,CAEvD;AACAR,WAAW,CAACe,IAAI,CAAC,CACjBb,gBAAgB,CAAC,IAAI,CAAC,CACtBP,UAAU,CAAC,KAAK,CAAC,CACjB;AACAsB,aAAa,CAACR,kBAAkB,CAACS,OAAO,CAAC,CACzC,MACF,IAAK,QAAQ,CACX;AACA,KAAM,CAAAC,YAAY,CAAGR,QAAQ,CAACK,IAAI,CAACI,WAAW,EAAI,8DAA8D,CAChHvB,QAAQ,CAACsB,YAAY,CAAC,CACtBX,gBAAgB,CAAC,sBAAsB,CAAC,CACxCb,UAAU,CAAC,KAAK,CAAC,CACjBsB,aAAa,CAACR,kBAAkB,CAACS,OAAO,CAAC,CACzC,MACF,QACEV,gBAAgB,CAAC,eAAe,CAAC,CACrC,CACF,CAAE,MAAOZ,KAAK,CAAE,KAAAyB,eAAA,CAAAC,gBAAA,CACdC,OAAO,CAAC3B,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CAEnD;AACA,GAAI,EAAAyB,eAAA,CAAAzB,KAAK,CAACe,QAAQ,UAAAU,eAAA,iBAAdA,eAAA,CAAgBP,MAAM,IAAK,GAAG,CAAE,CAClCjB,QAAQ,CAAC,2GAA2G,CAAC,CACvH,CAAC,IAAM,IAAI,EAAAyB,gBAAA,CAAA1B,KAAK,CAACe,QAAQ,UAAAW,gBAAA,iBAAdA,gBAAA,CAAgBR,MAAM,IAAK,GAAG,CAAE,CACzCjB,QAAQ,CAAC,gEAAgE,CAAC,CAC5E,CAAC,IAAM,CACLA,QAAQ,CAAC,wHAAwH,CAAC,CACpI,CAEAW,gBAAgB,CAAC,qBAAqB,CAAC,CACvCb,UAAU,CAAC,KAAK,CAAC,CACjBsB,aAAa,CAACR,kBAAkB,CAACS,OAAO,CAAC,CAC3C,CACF,CAAC,CAAE,CAACf,MAAM,CAAC,CAAC,CAEZ;AACAxC,SAAS,CAAC,IAAM,CACd,MAAO,IAAM,CACX,GAAI8C,kBAAkB,CAACS,OAAO,CAAE,CAC9BD,aAAa,CAACR,kBAAkB,CAACS,OAAO,CAAC,CAC3C,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACAvD,SAAS,CAAC,IAAM,CACd,GAAIwC,MAAM,GAAKE,UAAU,GAAK,SAAS,EAAIA,UAAU,GAAK,YAAY,CAAC,CAAE,CACvE;AACA,GAAII,kBAAkB,CAACS,OAAO,CAAE,CAC9BD,aAAa,CAACR,kBAAkB,CAACS,OAAO,CAAC,CAC3C,CAEA;AACAR,eAAe,CAAC,CAAC,CAEjB;AACAD,kBAAkB,CAACS,OAAO,CAAGM,WAAW,CAACd,eAAe,CAAE,KAAK,CAAC,CAAE;AAElE,MAAO,IAAM,CACX,GAAID,kBAAkB,CAACS,OAAO,CAAE,CAC9BD,aAAa,CAACR,kBAAkB,CAACS,OAAO,CAAC,CAC3C,CACF,CAAC,CACH,CACF,CAAC,CAAE,CAACf,MAAM,CAAEE,UAAU,CAAEK,eAAe,CAAC,CAAC,CAEzC,KAAM,CAAAe,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAACnC,WAAW,CAACoC,IAAI,CAAC,CAAC,CAAE,CACvB7B,QAAQ,CAAC,kDAAkD,CAAC,CAC5D,OACF,CAEAF,UAAU,CAAC,IAAI,CAAC,CAChBa,gBAAgB,CAAC,4BAA4B,CAAC,CAE9C,GAAI,CACF,KAAM,CAAAmB,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,CAAEvC,WAAW,CAAC,CACtCqC,QAAQ,CAACE,MAAM,CAAC,UAAU,CAAErC,QAAQ,CAAC,CACrCmC,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEpC,WAAW,CAAC,CAEpC,KAAM,CAAAkB,QAAQ,CAAG,KAAM,CAAA7C,KAAK,CAACgE,IAAI,CAAC,GAAGrD,MAAM,uCAAuC,CAChFkD,QAAQ,CACR,CAAEd,OAAO,CAAErC,UAAU,CAAC,CAAE,CAC1B,CAAC,CAED;AACA4B,SAAS,CAACO,QAAQ,CAACK,IAAI,CAACe,OAAO,CAAC,CAChCzB,aAAa,CAAC,SAAS,CAAC,CAExB;AAEF,CAAE,MAAOV,KAAK,CAAE,CACd2B,OAAO,CAAC3B,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtDY,gBAAgB,CAAC,8DAA8D,CAAC,CAChFb,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAqC,oBAAoB,CAAGA,CAAA,GAAM,CACjClC,QAAQ,CAAC,UAAU,CAAGC,QAAQ,CAAE,CAAEkC,OAAO,CAAE,IAAK,CAAC,CAAC,CACpD,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5BvC,UAAU,CAAC,KAAK,CAAC,CACjBO,gBAAgB,CAAC,KAAK,CAAC,CACvBE,SAAS,CAAC,IAAI,CAAC,CACfE,aAAa,CAAC,EAAE,CAAC,CACjBE,gBAAgB,CAAC,EAAE,CAAC,CACpB,GAAIC,kBAAkB,CAACS,OAAO,CAAE,CAC9BD,aAAa,CAACR,kBAAkB,CAACS,OAAO,CAAC,CAC3C,CACF,CAAC,CAED,mBACElC,IAAA,CAAChB,SAAS,EAACmE,SAAS,CAAErD,MAAM,CAACsD,mBAAoB,CAAAC,QAAA,cAC/CnD,KAAA,CAACjB,GAAG,EAACkE,SAAS,CAAErD,MAAM,CAACwD,UAAW,CAAAD,QAAA,EAC/B,CAAC3C,OAAO,EAAI,CAACO,aAAa,eACzBf,KAAA,CAACb,KAAK,EAACkE,SAAS,CAAE,CAAE,CAACJ,SAAS,CAAErD,MAAM,CAAC0D,YAAa,CAAAH,QAAA,eAElDnD,KAAA,CAACjB,GAAG,EAACwE,EAAE,CAAE,CACPC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBC,SAAS,CAAE,QAAQ,CACnBC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAE,CAAAV,QAAA,eACArD,IAAA,CAACf,GAAG,EAACwE,EAAE,CAAE,CACPO,eAAe,CAAE,wBAAwB,CACzCC,YAAY,CAAE,KAAK,CACnBC,CAAC,CAAE,CAAC,CACJJ,EAAE,CAAE,CAAC,CACLJ,OAAO,CAAE,MAAM,CACfS,cAAc,CAAE,QAAQ,CACxBP,UAAU,CAAE,QAAQ,CACpBQ,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,SAAS,CAAE,mCACb,CAAE,CAAAjB,QAAA,cACArD,IAAA,CAACJ,OAAO,EAAC2E,IAAI,CAAC,mBAAmB,CAACH,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACG,KAAK,CAAC,SAAS,CAAE,CAAC,CACxE,CAAC,cACNxE,IAAA,CAACd,UAAU,EAACuF,OAAO,CAAC,IAAI,CAACtB,SAAS,CAAErD,MAAM,CAAC4E,UAAW,CAACjB,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,CAAC,yBAEtE,CAAY,CAAC,cACbrD,IAAA,CAACd,UAAU,EAACuF,OAAO,CAAC,OAAO,CAACtB,SAAS,CAAErD,MAAM,CAAC6E,gBAAiB,CAAClB,EAAE,CAAE,CAAEmB,QAAQ,CAAE,OAAQ,CAAE,CAAAvB,QAAA,CAAC,gKAE3F,CAAY,CAAC,EACV,CAAC,cAENrD,IAAA,CAACV,OAAO,EAACmE,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAC,CAAEe,WAAW,CAAE,iBAAkB,CAAE,CAAE,CAAC,cAG1D3E,KAAA,CAACjB,GAAG,EAACkE,SAAS,CAAErD,MAAM,CAACgF,WAAY,CAAAzB,QAAA,eACjCnD,KAAA,CAACjB,GAAG,EAACkE,SAAS,CAAErD,MAAM,CAACiF,eAAgB,CAAA1B,QAAA,eACrCnD,KAAA,CAACjB,GAAG,EAACyE,OAAO,CAAC,MAAM,CAACE,UAAU,CAAC,QAAQ,CAACO,cAAc,CAAC,eAAe,CAAAd,QAAA,eACpEnD,KAAA,CAAChB,UAAU,EAACuF,OAAO,CAAC,IAAI,CAACtB,SAAS,CAAErD,MAAM,CAACkF,UAAW,CAACvB,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAP,QAAA,eACnGrD,IAAA,CAACJ,OAAO,EAAC2E,IAAI,CAAC,YAAY,CAACH,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACG,KAAK,CAAC,SAAS,CAACS,KAAK,CAAE,CAAEC,WAAW,CAAE,MAAO,CAAE,CAAE,CAAC,0CAEtG,EAAY,CAAC,cACblF,IAAA,CAACb,OAAO,EAACgG,KAAK,cAAEjF,KAAA,CAAAE,SAAA,EAAAiD,QAAA,eACdrD,IAAA,WAAAqD,QAAA,CAAQ,qBAAmB,CAAQ,CAAC,cAAArD,IAAA,QAAK,CAAC,oIACuF,cAAAA,IAAA,QAAK,CAAC,cACvIA,IAAA,WAAAqD,QAAA,CAAQ,0BAAwB,CAAQ,CAAC,cAAArD,IAAA,QAAK,CAAC,sCACZ,cAAAA,IAAA,QAAK,CAAC,8BACd,cAAAA,IAAA,QAAK,CAAC,sCACE,cAAAA,IAAA,QAAK,CAAC,oEAE3C,EAAE,CAAE,CAAAqD,QAAA,cACFrD,IAAA,CAACZ,UAAU,EAACgG,IAAI,CAAC,OAAO,CAAA/B,QAAA,cACtBrD,IAAA,CAACJ,OAAO,EAAC2E,IAAI,CAAC,iBAAiB,CAACH,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACG,KAAK,CAAC,SAAS,CAAE,CAAC,CAC/D,CAAC,CACN,CAAC,EACP,CAAC,cACNxE,IAAA,CAACL,YAAY,EACX0F,EAAE,CAAC,QAAQ,CACXC,KAAK,CAAEhF,WAAY,CACnBiF,YAAY,CAAEhF,cAAe,CAC7BiF,OAAO,CAAE,CAAE,CACXC,SAAS,MACTC,WAAW,CAAC,qPAAqP,CACjQC,SAAS,CAAE/E,KAAK,EAAI,CAACN,WAAW,CAAGM,KAAK,CAAG,EAAG,CAC9CgF,QAAQ,MACRnC,EAAE,CAAE,CACF,0BAA0B,CAAE,CAC1BQ,YAAY,CAAE,MAAM,CACpB,wBAAwB,CAAE,CACxBY,WAAW,CAAE,SAAS,CACtBgB,WAAW,CAAE,KACf,CACF,CACF,CAAE,CACH,CAAC,cACF7F,IAAA,CAACd,UAAU,EAACuF,OAAO,CAAC,SAAS,CAAChB,EAAE,CAAE,CAAEC,OAAO,CAAE,OAAO,CAAEoC,EAAE,CAAE,CAAC,CAAEtB,KAAK,CAAE,MAAM,CAAEuB,SAAS,CAAE,QAAS,CAAE,CAAA1C,QAAA,CAAC,uIAEnG,CAAY,CAAC,EACV,CAAC,cAENrD,IAAA,CAACjB,MAAM,EACL0F,OAAO,CAAC,WAAW,CACnBuB,OAAO,CAAEvD,YAAa,CACtBwD,SAAS,MACT9C,SAAS,CAAErD,MAAM,CAACoG,UAAW,CAC7BC,SAAS,cAAEnG,IAAA,CAACJ,OAAO,EAAC2E,IAAI,CAAC,kBAAkB,CAACH,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAE,CACtEZ,EAAE,CAAE,CACFqC,EAAE,CAAE,CAAC,CACLzB,MAAM,CAAE,MAAM,CACdJ,YAAY,CAAE,MAAM,CACpBK,SAAS,CAAE,mCAAmC,CAC9C8B,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,GACd,CAAE,CAAAhD,QAAA,CACH,UAED,CAAQ,CAAC,EACN,CAAC,EACD,CACR,CAEA,CAAC3C,OAAO,EAAIO,aAAa,gBACxBf,KAAA,CAACjB,GAAG,EAACkE,SAAS,CAAErD,MAAM,CAACwG,UAAW,CAAAjD,QAAA,EAC/B3C,OAAO,eACNR,KAAA,CAACjB,GAAG,EAACkE,SAAS,CAAErD,MAAM,CAACyG,gBAAiB,CAAAlD,QAAA,eACtCrD,IAAA,CAACf,GAAG,EAACkE,SAAS,CAAErD,MAAM,CAAC0G,gBAAiB,CAAAnD,QAAA,cACtCrD,IAAA,CAACH,kBAAkB,EAAC4G,cAAc,CAAE,IAAK,CAAE,CAAC,CACzC,CAAC,cAENvG,KAAA,CAACb,KAAK,EAACkE,SAAS,CAAE,CAAE,CAACJ,SAAS,CAAErD,MAAM,CAAC4G,WAAY,CAAArD,QAAA,eACjDrD,IAAA,CAACf,GAAG,EAACwE,EAAE,CAAE,CACPkD,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,CAAC,CACRzC,MAAM,CAAE,KAAK,CACb0C,OAAO,CAAE,wBACX,CAAE,CAAA1D,QAAA,cACArD,IAAA,CAACf,GAAG,EAACwE,EAAE,CAAE,CACPkD,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPxC,MAAM,CAAE,MAAM,CACdD,KAAK,CAAE,KAAK,CACZ2C,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,yCACb,CAAE,CAAE,CAAC,CACF,CAAC,cAENhH,IAAA,CAACd,UAAU,EAACuF,OAAO,CAAC,IAAI,CAACwC,KAAK,CAAC,QAAQ,CAAC9D,SAAS,CAAErD,MAAM,CAACoH,eAAgB,CAAA7D,QAAA,CACvE9B,aAAa,EAAI,+BAA+B,CACvC,CAAC,cAEbvB,IAAA,CAACf,GAAG,EAACwE,EAAE,CAAE,CACPW,KAAK,CAAE,MAAM,CACb0B,EAAE,CAAE,CAAC,CACLhC,EAAE,CAAE,CAAC,CACLJ,OAAO,CAAE,MAAM,CACfS,cAAc,CAAE,QAClB,CAAE,CAAAd,QAAA,cACArD,IAAA,CAACf,GAAG,EAACwE,EAAE,CAAE,CAAEkD,QAAQ,CAAE,UAAU,CAAEvC,KAAK,CAAE,KAAM,CAAE,CAAAf,QAAA,cAC9CrD,IAAA,CAACT,cAAc,EACbiF,KAAK,CAAC,SAAS,CACff,EAAE,CAAE,CACFY,MAAM,CAAE,CAAC,CACTJ,YAAY,CAAE,CAAC,CACf8C,OAAO,CAAE,yBAAyB,CAClC,0BAA0B,CAAE,CAC1BA,OAAO,CAAE,SACX,CACF,CAAE,CACH,CAAC,CACC,CAAC,CACH,CAAC,cAEN/G,IAAA,CAACd,UAAU,EAACuF,OAAO,CAAC,OAAO,CAAChB,EAAE,CAAE,CAC9BqC,EAAE,CAAE,CAAC,CACLtB,KAAK,CAAE,MAAM,CACbuB,SAAS,CAAE,QAAQ,CACnBoB,EAAE,CAAE,CACN,CAAE,CAAA9D,QAAA,CAAC,wEAEH,CAAY,CAAC,CAGZzC,KAAK,eACJZ,IAAA,CAACf,GAAG,EAACwE,EAAE,CAAE,CACPqC,EAAE,CAAE,CAAC,CACL5B,CAAC,CAAE,CAAC,CACJ6C,OAAO,CAAE,wBAAwB,CACjC9C,YAAY,CAAE,MAAM,CACpBmD,MAAM,CAAE,kCACV,CAAE,CAAA/D,QAAA,cACAnD,KAAA,CAACjB,GAAG,EAACwE,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,YAAY,CAAEyD,GAAG,CAAE,CAAE,CAAE,CAAAhE,QAAA,eAC7DrD,IAAA,CAACJ,OAAO,EAAC2E,IAAI,CAAC,gCAAgC,CAACH,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACG,KAAK,CAAC,SAAS,CAAE,CAAC,cACxFtE,KAAA,CAACjB,GAAG,EAAAoE,QAAA,eACFrD,IAAA,CAACd,UAAU,EAACuF,OAAO,CAAC,IAAI,CAAChB,EAAE,CAAE,CAAEe,KAAK,CAAE,SAAS,CAAE6B,UAAU,CAAE,GAAG,CAAEvC,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,CAAC,sBAE3E,CAAY,CAAC,cACbrD,IAAA,CAACd,UAAU,EAACuF,OAAO,CAAC,OAAO,CAAChB,EAAE,CAAE,CAAEe,KAAK,CAAE,MAAM,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAT,QAAA,CACtDzC,KAAK,CACI,CAAC,cACbV,KAAA,CAACjB,GAAG,EAACwE,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAE2D,GAAG,CAAE,CAAC,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAjE,QAAA,eACrDrD,IAAA,CAACjB,MAAM,EACL0F,OAAO,CAAC,WAAW,CACnBuB,OAAO,CAAE9C,eAAgB,CACzBiD,SAAS,cAAEnG,IAAA,CAACJ,OAAO,EAAC2E,IAAI,CAAC,0BAA0B,CAACH,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAE,CAC9EZ,EAAE,CAAE,CACFsD,OAAO,CAAE,SAAS,CAClB,SAAS,CAAE,CAAEA,OAAO,CAAE,SAAU,CAAC,CACjC9C,YAAY,CAAE,KAAK,CACnBsD,aAAa,CAAE,MAAM,CACrBlB,UAAU,CAAE,GACd,CAAE,CAAAhD,QAAA,CACH,WAED,CAAQ,CAAC,cACTrD,IAAA,CAACjB,MAAM,EACL0F,OAAO,CAAC,UAAU,CAClBuB,OAAO,CAAEA,CAAA,GAAMlF,QAAQ,CAAC,KAAK,CAAE,CAC/B2C,EAAE,CAAE,CACFoB,WAAW,CAAE,SAAS,CACtBL,KAAK,CAAE,SAAS,CAChB,SAAS,CAAE,CAAEK,WAAW,CAAE,SAAS,CAAEkC,OAAO,CAAE,yBAA0B,CAAC,CACzE9C,YAAY,CAAE,KAAK,CACnBsD,aAAa,CAAE,MAAM,CACrBlB,UAAU,CAAE,GACd,CAAE,CAAAhD,QAAA,CACH,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,EACI,CAAC,EACL,CACN,CAEApC,aAAa,eACZf,KAAA,CAACjB,GAAG,EAACwE,EAAE,CAAE,CAAEI,SAAS,CAAE,QAAQ,CAAEe,QAAQ,CAAE,OAAO,CAAEV,CAAC,CAAE,CAAC,CAAE6C,OAAO,CAAE,OAAO,CAAE9C,YAAY,CAAE,MAAM,CAAEK,SAAS,CAAE,+BAAgC,CAAE,CAAAjB,QAAA,eAC5IrD,IAAA,CAACf,GAAG,EAACwE,EAAE,CAAE,CACPO,eAAe,CAAE,wBAAwB,CACzCC,YAAY,CAAE,KAAK,CACnBC,CAAC,CAAE,CAAC,CACJJ,EAAE,CAAE,CAAC,CACLJ,OAAO,CAAE,MAAM,CACfS,cAAc,CAAE,QAAQ,CACxBP,UAAU,CAAE,QAAQ,CACpBQ,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXmD,MAAM,CAAE,QAAQ,CAChBlD,SAAS,CAAE,mCACb,CAAE,CAAAjB,QAAA,cACArD,IAAA,CAACJ,OAAO,EAAC2E,IAAI,CAAC,kBAAkB,CAACH,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACG,KAAK,CAAC,SAAS,CAAE,CAAC,CACvE,CAAC,cACNxE,IAAA,CAACd,UAAU,EAACuF,OAAO,CAAC,IAAI,CAACwC,KAAK,CAAC,QAAQ,CAAC9D,SAAS,CAAErD,MAAM,CAACoH,eAAgB,CAACzD,EAAE,CAAE,CAAEe,KAAK,CAAE,MAAM,CAAEwC,SAAS,CAAE,MAAO,CAAE,CAAA3D,QAAA,CAAC,qCAErH,CAAY,CAAC,cACbrD,IAAA,CAACd,UAAU,EAACuF,OAAO,CAAC,OAAO,CAAChB,EAAE,CAAE,CAAEqC,EAAE,CAAE,CAAC,CAAEhC,EAAE,CAAE,CAAC,CAAEU,KAAK,CAAE,MAAO,CAAE,CAAAnB,QAAA,CAAC,6DAEjE,CAAY,CAAC,cACbnD,KAAA,CAACjB,GAAG,EAACwE,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAES,cAAc,CAAE,QAAQ,CAAEkD,GAAG,CAAE,CAAC,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAjE,QAAA,eAC/ErD,IAAA,CAACjB,MAAM,EACL0F,OAAO,CAAC,WAAW,CACnBtB,SAAS,CAAErD,MAAM,CAAC2H,iBAAkB,CACpCzB,OAAO,CAAEhD,oBAAqB,CAC9BmD,SAAS,cAAEnG,IAAA,CAACJ,OAAO,EAAC2E,IAAI,CAAC,qCAAqC,CAACH,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAE,CACzFZ,EAAE,CAAE,CACFQ,YAAY,CAAE,MAAM,CACpBK,SAAS,CAAE,mCAAmC,CAC9CoD,OAAO,CAAE,WAAW,CACpBtB,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,GACd,CAAE,CAAAhD,QAAA,CACH,WAED,CAAQ,CAAC,cACTrD,IAAA,CAACjB,MAAM,EACL0F,OAAO,CAAC,WAAW,CACnBtB,SAAS,CAAErD,MAAM,CAAC6H,WAAY,CAC9B3B,OAAO,CAAE9C,eAAgB,CACzBiD,SAAS,cAAEnG,IAAA,CAACJ,OAAO,EAAC2E,IAAI,CAAC,0BAA0B,CAACH,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAE,CAC9EZ,EAAE,CAAE,CACFQ,YAAY,CAAE,MAAM,CACpBK,SAAS,CAAE,gCAAgC,CAC3CoD,OAAO,CAAE,WAAW,CACpBtB,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,GACd,CAAE,CAAAhD,QAAA,CACH,gBAED,CAAQ,CAAC,EACN,CAAC,EACH,CACN,EACE,CACN,EACE,CAAC,CACG,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAhD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
/* eslint-disable jsx-a11y/anchor-is-valid */
import { Col, Nav, NavItem, NavLink } from "reactstrap";

const Footer = () => {
  return (
    <footer className="footer">
      <div className="d-flex align-items-center justify-content-between">
        <Col xl="6">
          <div className="copyright text-xl-left">
            © {new Date().getFullYear()}{" "}
            <a className="ml-1" style={{ color: 'gray' }}
              target="_blank">
              Ignition Team
            </a>
          </div>
        </Col>

        <Col xl="6">
          <Nav className="nav-footer justify-content-end">
            <NavItem>
              <NavLink
                href="https://www.creative-tim.com?ref=adr-admin-footer"
                rel="noopener noreferrer"
                target="_blank">
                Ignition
              </NavLink>
            </NavItem>

            <NavItem>
              <NavLink
                href="https://www.creative-tim.com/presentation?ref=adr-admin-footer"
                rel="noopener noreferrer"
                target="_blank">
                About Us
              </NavLink>
            </NavItem>

            <NavItem>
              <NavLink
                href="http://blog.creative-tim.com?ref=adr-admin-footer"
                rel="noopener noreferrer"
                target="_blank">
                Blog
              </NavLink>
            </NavItem>
          </Nav>
        </Col>
      </div>
    </footer>
  );
};

export default Footer;

# Generated by Django 5.0.1 on 2024-02-03 02:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assistant', '0002_message'),
    ]

    operations = [
        migrations.CreateModel(
            name='Run',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.CharField(max_length=255, null=True)),
                ('status', models.CharField(max_length=255, null=True)),
                ('thread', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='assistant.thread')),
            ],
            options={
                'db_table': 'runs',
            },
        ),
    ]

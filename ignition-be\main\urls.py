from django.contrib import admin
from drf_yasg import openapi
from django.urls import path, include
from django.conf.urls.static import static
from drf_yasg.views import get_schema_view
from users.views import GoogleLoginApi, UserLoginView, CreateUserView, CreateInvitedUserView, ActivateUserView, \
    PasswordResetRequestView, UserProfileView, UserUpdateProfileView, PasswordResetView, \
    OtherUserProfileView, UserNotificationView, MarkAllNotificationsAsReadView, \
    MarkNotificationAsReadView, DeleteNotificationView, UsersInRelatedPlansView, \
    CheckInvitationSignedIdView
from assistant.views import CreateAssitantThreadView, CreateRunAssistantGetResultView, \
    AddMessageView, CreateProjectPlannerByChatView, CreateProjectPlannerByAssistantView, \
    PlanStatusView, AIProvidersInfoView, AIAgentActionView
from rest_framework_simplejwt.views import TokenRefreshView
from plans.views import DeletePlanView, PlanDetailInfoView, PlanDetailInformationBySlugView, \
    UpdateSubTaskView, UpdateTaskView, getPlanUsersView, retrievePlanThroughUserAccountView, \
    AssignTaskView, AddSubtaskView, DeleteSubtaskView, UpdateSubtaskOrderView, UpdateTaskOrderView, \
    AddTaskToMilestoneView, DeleteTaskView, UpdateMilestoneNameView, AddMilestoneToPlanView, CheckUserExistsView, \
    SendInvitationView, ListInvitedUsersView, DeletePlanView, CheckInvitationView, UpdateInvitationStatusView, \
    OptOutPlanView, PlanAccessManagementView, PlanAccessDetailView
from comments.views import addCommentView, deleteCommentView, updateCommentView, getCommentView
from skills.views import UserSkillsView, AddSkillForUserView, deleteSkillsView
from memos.views import addMemosView, deleteMemosView, updateMemosView
from django.conf import settings
from tasks.views import AddTaskForUserView, DeleteTaskForUserView, HandleTaskCompletionForUserView, \
    getMyTaskView, getMyTaskForTodoView, UpdateTaskStatusTodoView, UpdateTaskStartEndDateTodoView
import os
from dotenv import load_dotenv
load_dotenv()


schema_view = get_schema_view(
    openapi.Info(
        title="Ignition API schema",
        default_version='v1',
    ),
    public=True,
    permission_classes=(),
    url=os.getenv('SWAGGER_CLIENT_URL'),
)

urlpatterns = [
    path('admin/', admin.site.urls),

    # FOR ASSISTANT
    path('api/assistant/create-thread', CreateAssitantThreadView.as_view(), name='assistant-create-thread'),
    path('api/assistant/add-message', AddMessageView.as_view(), name='assistant-add-message'),
    path('api/assistant/create-assistant-thread-get-result', CreateRunAssistantGetResultView.as_view(), name='create-run-assistant-get-result'),
    path('api/assistant/create-planner-by-chat', CreateProjectPlannerByChatView.as_view(), name='create-planner-chat'),
    path('api/assistant/create-planner-by-assistant', CreateProjectPlannerByAssistantView.as_view(), name='create-planner-assistant'),
    path('api/assistant/plan-status/<int:plan_id>', PlanStatusView.as_view(), name='plan-status'),
    path('api/assistant/providers-info', AIProvidersInfoView.as_view(), name='ai-providers-info'),
    path('api/assistant/plan-action', AIAgentActionView.as_view(), name='ai-agent-action'),

    # FOR USER
    path('api/auth/google', GoogleLoginApi.as_view(), name="login-with-google"),
    path("api/user/login", UserLoginView.as_view(), name="login-api"),
    path('api/user/create', CreateUserView.as_view(), name="create_user"),
    path('api/user/activate', ActivateUserView.as_view(), name='activate_user'),
    path('api/user/forgot-password', PasswordResetRequestView.as_view(), name='forgot-password'),
    path('api/user/view-profile', UserProfileView.as_view(), name='view-profile'),
    path('api/user/update-profile', UserUpdateProfileView.as_view(), name='update-profile'),
    path('api/user/token-refresh', TokenRefreshView.as_view(), name='token_refresh'),
    path('api/user/password-reset/<str:uid>/<str:token>/', PasswordResetView.as_view(), name='password-reset'),
    path('api/user/connect/friends', UsersInRelatedPlansView.as_view(), name='send_follow_request'),
    path('api/other-profile/<str:email>', OtherUserProfileView.as_view(), name='view_other_profile'),
    path('api/notifications', UserNotificationView.as_view(), name='get_user_notifications'),
    path('api/notifications/update/<int:noti_id>', MarkNotificationAsReadView.as_view(), name='update_notification'),
    path('api/notifications/update-all', MarkAllNotificationsAsReadView.as_view(), name='update_all_notifications'),
    path('api/notifications/delete/<int:noti_id>', DeleteNotificationView.as_view(), name = 'delete_notification'),
    path('api/user/create-through-invitation', CreateInvitedUserView.as_view(), name='create_invited_user'),
    path('api/user/check-signed-id', CheckInvitationSignedIdView.as_view(), name='check_signed_id'),

    # SWAGGER
    path('docs/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),

    # FOR PLANS
    path('api/retrieve/myplan', retrievePlanThroughUserAccountView.as_view(), name='retrieve-plan-through-user'),
    path('api/plans/<int:pk>', PlanDetailInfoView.as_view(), name='view-plan'),
    path('api/plans/<slug:slug>', PlanDetailInformationBySlugView.as_view(), name='view-plan-slug'),
    path('api/subtasks/update/<slug:slug>', UpdateSubTaskView.as_view(), name='update-subtask'),
    path('api/tasks/update/<slug:slug>', UpdateTaskView.as_view(), name='update-task'),
    path('api/plans/users/<slug:slug>', getPlanUsersView.as_view(), name='get-plan-users'),
    path('api/tasks/<slug:slug>/assign', AssignTaskView.as_view(), name='assign-task-for-user'),
    path('api/plan/delete/<slug:slug>', DeletePlanView.as_view(), name='delete-plan'),
    path('api/plan/opt-out/<slug:slug>', OptOutPlanView.as_view(), name='opt-out-plan'),
    path('api/plan/check-invitation', CheckInvitationView.as_view(), name='check-invitation'),
    path('api/plan/update-invitation-status/<int:id>', UpdateInvitationStatusView.as_view(), name='update-invitation-status'),

    # Access level management
    path('api/plans/<slug:slug>/access', PlanAccessManagementView.as_view(), name='plan-access-management'),
    path('api/plans/<slug:slug>/access/<int:access_id>', PlanAccessDetailView.as_view(), name='plan-access-detail'),

    # FOR SKILLS
    path('api/skills/add', AddSkillForUserView.as_view(), name='create-skill'),
    path('api/skills/user', UserSkillsView.as_view(), name='user-skills'),
    path('api/skills/delete/<int:id>', deleteSkillsView.as_view(), name='delete-skill'),

    # FOR COMMENTS
    path('api/comments/add', addCommentView.as_view(), name='add-comment'),
    path('api/comments/delete/<int:id>', deleteCommentView.as_view(), name='delete-comment'),
    path('api/comments/target/<int:target_id>/type/<int:type>', getCommentView.as_view(), name='get-comments'),
    path('api/comments/edit/<int:id>', updateCommentView.as_view(), name='edit-comment'),

    # FOR MEMOS
    path('api/memos/add', addMemosView.as_view(), name='add-memos'),
    path('api/memos/delete/<int:id>', deleteMemosView.as_view(), name='delete-memos'),
    path('api/memos/edit/<int:id>', updateMemosView.as_view(), name='edit-memos'),

    # FOR MILESTONE
    path('api/milestones/<int:id>/tasks/add', AddTaskToMilestoneView.as_view(), name='add-task-to-milestone'),
    path('api/milestones/<int:id>/tasks/order', UpdateTaskOrderView.as_view(), name='update-task-order'),
    path('api/milestones/<int:id>/tasks/order', UpdateTaskOrderView.as_view(), name='update-task-order'),
    path('api/milestones/<int:id>/update', UpdateMilestoneNameView.as_view(), name='update-milestone-name'),
    path('api/plans/<slug:slug>/milestones/add', AddMilestoneToPlanView.as_view(), name='add-milestone-to-plan'),
    path('api/check-user', CheckUserExistsView.as_view(), name='check-user'),
    path('api/plans/<slug:slug>/invite', SendInvitationView.as_view(), name='send-invitation'),
    path('api/plans/<slug:slug>/invited-users', ListInvitedUsersView.as_view(), name='send-invitation'),

    # FOR TASK/SUBTASK
    path('api/task/add', AddTaskForUserView.as_view(), name='add-task'),
    path('api/task/delete/<slug:slug>', DeleteTaskForUserView.as_view(), name='todo-delete-task'),
    path('api/retrieve/mytask', getMyTaskView.as_view(), name='retrieve-tasks'),
    path('api/task/<int:task_id>/complete', HandleTaskCompletionForUserView.as_view(), name='complete-task'),
    path('api/tasks/<slug:slug>/delete', DeleteTaskView.as_view(), name='delete-task'),
    path('api/tasks/todo-by-status', getMyTaskForTodoView.as_view(), name='task-todo'),
    path('api/tasks/<slug:slug>/update-todo-status', UpdateTaskStatusTodoView.as_view(), name='update-task-todo-status'),
    # path('api/tasks/<slug:slug>/delete-todo-status', UpdateTaskStatusTodoView.as_view(), name='update-task-todo-status'),
    path('api/tasks/<slug:slug>/update-start-end-date', UpdateTaskStartEndDateTodoView.as_view(), name='update-task-start-end-date'),

    path('api/tasks/<slug:slug>/add-subtask', AddSubtaskView.as_view(), name='add-subtask'),
    path('api/subtasks/<slug:slug>/delete', DeleteSubtaskView.as_view(), name='delete-subtask'),
    path('api/subtasks/<slug:slug>/order', UpdateSubtaskOrderView.as_view(), name='update-subtask-order'),
    path('healthcheck/', include('healthcheck.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

import React, { useState } from 'react';
import { 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  Typography, 
  CircularProgress
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import styles from '../styles.module.scss';

const OptOutDialog = ({ open, onClose, onOptOut }) => {
  const [loading, setLoading] = useState(false);

  const handleOptOut = async () => {
    setLoading(true);
    try {
      const success = await onOptOut();
      if (!success) {
        setLoading(false);
      }
      // If successful, the page will navigate away
    } catch (err) {
      console.error('Error opting out of plan:', err);
      setLoading(false);
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={loading ? null : onClose}
      maxWidth="sm"
      fullWidth
      className={styles.deleteDialog}
      PaperProps={{
        sx: {
          borderRadius: '12px',
          padding: '8px'
        }
      }}
    >
      <DialogTitle 
        className={styles.dialogTitle}
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1,
          fontFamily: '"Recursive Variable", sans-serif',
          fontSize: '1.3rem',
          fontWeight: 600,
          color: '#333',
          pb: 1
        }}
      >
        <Iconify icon="material-symbols:logout" width={24} height={24} color="#f44336" />
        Opt Out of Plan
      </DialogTitle>
      
      <DialogContent className={styles.dialogContent}>
        <Typography 
          variant="body1"
          sx={{ 
            fontFamily: '"Recursive Variable", sans-serif',
            color: '#666',
            lineHeight: 1.6,
            fontSize: '1rem'
          }}
        >
          Are you sure you want to opt out of this plan? You will no longer have access to this plan and its content. 
          This action cannot be undone.
        </Typography>
        
        <Typography 
          variant="body2"
          sx={{ 
            fontFamily: '"Recursive Variable", sans-serif',
            color: '#999',
            lineHeight: 1.5,
            fontSize: '0.9rem',
            mt: 2,
            fontStyle: 'italic'
          }}
        >
          Note: You can be re-invited to this plan by the plan owner if needed.
        </Typography>
      </DialogContent>
      
      <DialogActions className={styles.dialogActions}>
        <Button 
          onClick={onClose}
          disabled={loading}
          sx={{ 
            color: '#666',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600
          }}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleOptOut}
          disabled={loading}
          variant="contained"
          color="error"
          startIcon={loading ? <CircularProgress size={16} color="inherit" /> : <Iconify icon="material-symbols:logout" width={16} height={16} />}
          sx={{ 
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600
          }}
        >
          {loading ? 'Opting Out...' : 'Opt Out'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default OptOutDialog;
